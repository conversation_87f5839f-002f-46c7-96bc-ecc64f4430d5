import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'

/**
 * Simple unpaid amounts calculation using created_at and Final Payment logic
 * Logic: All attendance records created after the last "Final Payment" are unpaid
 */
export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { firmId, groupId } = query

    if (!firmId || !groupId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: firmId and groupId are required'
      })
    }

    // Get Supabase configuration
    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found for this firm'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // 1. Get labor profiles for this group
    const { data: profiles, error: profilesError } = await supabase
      .from('labor_profiles')
      .select('id, name')
      .eq('group_id', groupId)

    if (profilesError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to fetch labor profiles: ${profilesError.message}`
      })
    }

    if (!profiles || profiles.length === 0) {
      return {
        success: true,
        data: [],
        message: 'No labor profiles found for this group'
      }
    }

    const laborIds = profiles.map(p => p.id)

    // 2. Find the last "Final Payment" for this group
    const { data: lastFinalPayment, error: paymentError } = await supabase
      .from('labor_payments')
      .select('created_at')
      .eq('group_id', groupId)
      .eq('payment_type', 'Final Payment')
      .order('created_at', { ascending: false })
      .limit(1)

    if (paymentError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to fetch final payment: ${paymentError.message}`
      })
    }

    // 3. Get attendance records created after the last final payment
    let attendanceQuery = supabase
      .from('attendance_records')
      .select(`
        *,
        labor_profiles!inner(id, name)
      `)
      .in('labor_id', laborIds)

    // If there's a last final payment, only get records created after it
    if (lastFinalPayment && lastFinalPayment.length > 0) {
      const lastFinalDate = lastFinalPayment[0].created_at
      attendanceQuery = attendanceQuery.gt('created_at', lastFinalDate)
      console.log('Filtering attendance records created after:', lastFinalDate)
    } else {
      console.log('No final payment found, getting all attendance records')
    }

    const { data: attendanceRecords, error: attendanceError } = await attendanceQuery
      .order('created_at', { ascending: true })

    if (attendanceError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to fetch attendance records: ${attendanceError.message}`
      })
    }

    if (!attendanceRecords || attendanceRecords.length === 0) {
      return {
        success: true,
        data: [],
        message: lastFinalPayment && lastFinalPayment.length > 0 
          ? 'No new attendance records since last final payment'
          : 'No attendance records found'
      }
    }

    // 4. Calculate total earnings by labor profile
    const laborEarnings = {}
    let totalUnpaidAmount = 0

    attendanceRecords.forEach(record => {
      const laborId = record.labor_id
      const laborName = record.labor_profiles.name
      const earnings = parseFloat(record.total_earnings || 0)

      if (!laborEarnings[laborId]) {
        laborEarnings[laborId] = {
          laborId,
          laborName,
          totalEarnings: 0,
          recordCount: 0,
          records: []
        }
      }

      laborEarnings[laborId].totalEarnings += earnings
      laborEarnings[laborId].recordCount += 1
      laborEarnings[laborId].records.push({
        date: record.attendance_date,
        earnings: earnings,
        created_at: record.created_at
      })

      totalUnpaidAmount += earnings
    })

    // 5. Get total payments made for this group (after last final payment if any)
    let paymentsQuery = supabase
      .from('labor_payments')
      .select('amount, payment_date, created_at, payment_type')
      .eq('group_id', groupId)

    if (lastFinalPayment && lastFinalPayment.length > 0) {
      const lastFinalDate = lastFinalPayment[0].created_at
      paymentsQuery = paymentsQuery.gt('created_at', lastFinalDate)
    }

    const { data: payments, error: paymentsError } = await paymentsQuery
      .order('created_at', { ascending: true })

    if (paymentsError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Failed to fetch payments: ${paymentsError.message}`
      })
    }

    const totalPayments = payments?.reduce((sum, payment) => sum + parseFloat(payment.amount || 0), 0) || 0
    const unpaidAmount = totalUnpaidAmount - totalPayments

    // 6. Format the result
    const result = {
      period: {
        description: lastFinalPayment && lastFinalPayment.length > 0 
          ? `Records created after ${new Date(lastFinalPayment[0].created_at).toLocaleDateString()}`
          : 'All attendance records',
        recordCount: attendanceRecords.length,
        lastFinalPayment: lastFinalPayment && lastFinalPayment.length > 0 ? lastFinalPayment[0].created_at : null
      },
      totalEarnings: totalUnpaidAmount,
      totalPayments: totalPayments,
      unpaidAmount: Math.max(0, unpaidAmount), // Don't show negative amounts
      type: unpaidAmount <= 0 ? 'settled' : 'unpaid',
      laborBreakdown: Object.values(laborEarnings),
      paymentsInPeriod: payments || [],
      metadata: {
        attendanceRecordsCount: attendanceRecords.length,
        paymentsCount: payments?.length || 0,
        laborProfilesCount: profiles.length
      }
    }

    console.log('Simple calculation result:', result)

    return {
      success: true,
      data: [result],
      message: `Found ${attendanceRecords.length} attendance records and ${payments?.length || 0} payments`
    }

  } catch (error) {
    console.error('Simple unpaid calculation error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to calculate unpaid amounts'
    })
  }
})
