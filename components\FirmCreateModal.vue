<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white/20 backdrop-blur-xl rounded-lg w-full max-w-2xl mx-auto border-2 border-white/30" @click.stop>
      <!-- Header with gradient background -->
      <div class="bg-gradient-to-r from-blue-500/50 to-purple-500/50 p-4 rounded-t-lg">
        <h2 class="text-xl font-bold text-white">Create New Firm</h2>
      </div>

      <!-- Form content -->
      <div class="p-6">
        <h3 class="text-lg font-medium text-white/90 mb-4">Please enter firm details</h3>

        <form class="grid grid-cols-2 gap-x-6 gap-y-4">
          <!-- Firm Name -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Firm Name*</label>
            <input v-model="newFirm.name" type="text"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter firm name" required>
          </div>

          <!-- Business Type -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Business Type*</label>
            <select v-model="newFirm.businessType"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10">
              <option value="" disabled class="text-black">Select business type</option>
              <option value="Retail" class="text-black">Retail</option>
              <option value="Manufacturing" class="text-black">Manufacturing</option>
              <option value="Services" class="text-black">Services</option>
              <option value="Construction" class="text-black">Construction</option>
              <option value="IT" class="text-black">IT</option>
              <option value="Other" class="text-black">Other</option>
            </select>
          </div>

          <!-- Address -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Address*</label>
            <input v-model="newFirm.address" type="text"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter address" required>
          </div>

          <!-- State -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">State</label>
            <input v-model="newFirm.state" type="text"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter state">
          </div>

          <!-- Contact Person -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Contact Person*</label>
            <input v-model="newFirm.contactPerson" type="text"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter contact person name" required>
          </div>

          <!-- Contact Number -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Contact Number*</label>
            <input v-model="newFirm.contactNo" type="text"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter contact number" required>
          </div>

          <!-- Email -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Email*</label>
            <input v-model="newFirm.email" type="email"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter email" required>
          </div>

          <!-- GST Number -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">GST Number*</label>
            <input v-model="newFirm.gstNo" type="text"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
              placeholder="Enter GST number" required>
          </div>

          <!-- Description -->
          <div>
            <label class="block text-base font-medium text-white/90 mb-2">Description (Optional)</label>
            <textarea v-model="newFirm.description"
              class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300"
              placeholder="Enter description" rows="2"></textarea>
          </div>

          <!-- Error message -->
          <div v-if="errorMessage" class="col-span-2 bg-red-500/30 text-white p-3 rounded-lg mt-2 text-sm">
            <p><strong>Error:</strong> {{ errorMessage }}</p>
          </div>

          <!-- Note about approval - spans both columns -->
          <div class="col-span-2 bg-blue-500/30 text-white p-3 rounded-lg mt-2 text-sm">
            <p><strong>Note:</strong> New firms require admin approval before they can be used. You will be notified by email when your firm is approved.</p>
          </div>
        </form>
      </div>

      <!-- Footer with gradient background and buttons -->
      <div class="bg-gradient-to-r from-purple-500/50 to-blue-500/50 p-4 rounded-b-lg flex justify-end space-x-3">
        <button type="button" @click="$emit('close')" :disabled="isLoading"
          class="px-4 py-2 bg-red-500/50 hover:bg-red-500/70 text-white rounded-xl transition-colors duration-300 disabled:opacity-50">
          Cancel
        </button>
        <button type="button" @click="createFirm" :disabled="isLoading"
          class="px-4 py-2 bg-green-500/50 hover:bg-green-500/70 text-white rounded-xl transition-colors duration-300 disabled:opacity-50 flex items-center">
          <span v-if="isLoading" class="mr-2">
            <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
          </span>
          {{ isLoading ? 'Creating...' : 'Create Firm' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, defineProps, defineEmits, ref } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  initialName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close', 'created']);
const isLoading = ref(false);
const errorMessage = ref('');

const newFirm = reactive({
  name: props.initialName,
  code: '',
  description: '',
  address: '',
  state: '',
  contactPerson: '',
  contactNo: '',
  email: '',
  gstNo: '',
  businessType: 'Other'
});

async function createFirm() {
  try {
    isLoading.value = true;
    errorMessage.value = '';

    // Validate required fields
    if (!newFirm.name) {
      errorMessage.value = 'Firm name is required';
      return;
    }

    if (!newFirm.address) {
      errorMessage.value = 'Address is required';
      return;
    }

    if (!newFirm.contactPerson) {
      errorMessage.value = 'Contact person is required';
      return;
    }

    if (!newFirm.contactNo) {
      errorMessage.value = 'Contact number is required';
      return;
    }

    if (!newFirm.email) {
      errorMessage.value = 'Email is required';
      return;
    }

    if (!newFirm.gstNo) {
      errorMessage.value = 'GST number is required';
      return;
    }

    if (!newFirm.businessType) {
      errorMessage.value = 'Business type is required';
      return;
    }

    // Generate a code from the name (first 3 characters + random 3 digits)
    newFirm.code = `${newFirm.name.substring(0, 3).toUpperCase()}${Math.floor(100 + Math.random() * 900)}`;


    // Use the special signup endpoint that doesn't require authentication
    // Use window.fetch directly to bypass any interceptors
    const response = await window.fetch('/api/firms/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(newFirm)
    });


    let data;
    try {
      data = await response.json();
    } catch (jsonError) {
      console.error('Error parsing response JSON:', jsonError);
      throw new Error('Failed to parse server response');
    }

    if (!response.ok) {
      console.error('Firm creation failed with status:', response.status);
      console.error('Response data:', data);
      throw new Error(data?.statusMessage || `Failed to create firm: ${response.status}`);
    }

    // Emit the created event with the new firm data
    if (data && data.firm) {
      emit('created', data.firm);

      // Close the modal
      emit('close');
    } else {
      console.error('Missing firm data in response');
      throw new Error('Server returned an invalid response');
    }
  } catch (error) {
    console.error('Error creating firm:', error);
    errorMessage.value = error.message || 'Failed to create firm';
  } finally {
    isLoading.value = false;
  }
}
</script>
