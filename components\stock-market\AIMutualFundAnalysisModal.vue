<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="$emit('close')"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
        <!-- Header -->
        <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-indigo-600">
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-xl font-semibold text-white">🤖 AI Mutual Fund Analysis</h3>
              <p class="text-purple-100 text-sm mt-1">{{ fund.schemeName }}</p>
            </div>
            <button
              @click="$emit('close')"
              class="text-white hover:text-gray-200 transition-colors"
            >
              <Icon name="heroicons:x-mark" class="w-6 h-6" />
            </button>
          </div>
        </div>

        <!-- Content -->
        <div class="px-6 py-4">
          <!-- Fund Info Summary -->
          <div class="bg-gray-50 rounded-lg p-4 mb-6">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span class="text-gray-500">Fund House:</span>
                <span class="font-medium ml-2">{{ fund.fundHouse }}</span>
              </div>
              <div>
                <span class="text-gray-500">Category:</span>
                <span class="font-medium ml-2">{{ fund.category }}</span>
              </div>
              <div>
                <span class="text-gray-500">Current NAV:</span>
                <span class="font-medium ml-2">₹{{ formatPrice(fund.currentNAV) }}</span>
              </div>
              <div>
                <span class="text-gray-500">Current Value:</span>
                <span class="font-medium ml-2" :class="fund.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'">
                  ₹{{ formatPrice(fund.currentValue) }}
                </span>
              </div>
            </div>
          </div>

          <!-- Tab Navigation -->
          <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8">
              <button
                @click="activeTab = 'technical'"
                :class="[
                  'py-2 px-1 border-b-2 font-medium text-sm',
                  activeTab === 'technical'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                ]"
              >
                📊 Technical Analysis
              </button>
              <button
                @click="activeTab = 'fundamental'"
                :class="[
                  'py-2 px-1 border-b-2 font-medium text-sm',
                  activeTab === 'fundamental'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                ]"
              >
                💰 Fundamental Analysis
              </button>
              <button
                @click="activeTab = 'portfolio'"
                :class="[
                  'py-2 px-1 border-b-2 font-medium text-sm',
                  activeTab === 'portfolio'
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                ]"
              >
                🏭 Portfolio Holdings
              </button>
            </nav>
          </div>

          <!-- Tab Content -->
          <div class="tab-content">
            <!-- AI Configuration Check -->
            <div v-if="!isConfigured" class="mb-6 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
              <div class="flex items-start">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">AI Configuration Required</h3>
                  <p class="mt-1 text-sm text-yellow-700">
                    Please configure your AI settings to use AI analysis features. Go to Settings > AI Settings to configure your preferred AI provider and API key.
                  </p>
                  <div class="mt-3">
                    <button
                      @click="openGlobalSettings"
                      class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors text-sm"
                    >
                      Configure AI Settings
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Technical Analysis Tab -->
            <div v-show="activeTab === 'technical'" class="tab-panel">
              <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-semibold text-gray-900">📊 Technical Analysis</h4>
                  <button
                    @click="fetchTechnicalAnalysis"
                    :disabled="technicalLoading || !isConfigured"
                    class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    <span v-if="technicalLoading">📈 Analyzing...</span>
                    <span v-else>📊 Analyze Fund</span>
                  </button>
                </div>

                <!-- Technical Analysis Loading State -->
                <div v-if="technicalLoading" class="mb-4">
                  <div class="bg-white rounded-lg p-4">
                    <div class="flex items-center space-x-3 mb-3">
                      <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
                      <span class="text-gray-700">{{ technicalStatusMessage || 'Analyzing mutual fund performance...' }}</span>
                    </div>
                    <div class="bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-green-500 h-2 rounded-full transition-all duration-500"
                        :style="{ width: technicalProgress + '%' }"
                      ></div>
                    </div>
                    <div class="flex justify-between text-xs text-gray-500 mt-2">
                      <span>{{ technicalStatusMessage }}</span>
                      <span>{{ technicalProgress }}%</span>
                    </div>
                  </div>
                </div>

                <!-- Technical Analysis Error State -->
                <div v-else-if="technicalError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-red-600 text-lg">❌</span>
                    <div>
                      <h5 class="font-medium text-red-800">Analysis Failed</h5>
                      <p class="text-red-700 text-sm">{{ technicalError }}</p>
                      <button
                        @click="fetchTechnicalAnalysis"
                        class="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Try Again
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Technical Analysis Results -->
                <div v-else-if="technicalAnalysis" class="space-y-4">
                  <!-- Performance Metrics -->
                  <div class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-900 mb-3">📈 Performance Metrics</h5>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div v-if="technicalAnalysis.performanceMetrics?.returns1Y !== undefined">
                        <span class="text-gray-600">1Y Returns:</span>
                        <span class="font-medium ml-1" :class="technicalAnalysis.performanceMetrics.returns1Y >= 0 ? 'text-green-600' : 'text-red-600'">
                          {{ technicalAnalysis.performanceMetrics.returns1Y.toFixed(2) }}%
                        </span>
                      </div>
                      <div v-if="technicalAnalysis.performanceMetrics?.returns3Y !== undefined">
                        <span class="text-gray-600">3Y Returns:</span>
                        <span class="font-medium ml-1" :class="technicalAnalysis.performanceMetrics.returns3Y >= 0 ? 'text-green-600' : 'text-red-600'">
                          {{ technicalAnalysis.performanceMetrics.returns3Y.toFixed(2) }}%
                        </span>
                      </div>
                      <div v-if="technicalAnalysis.performanceMetrics?.volatility !== undefined">
                        <span class="text-gray-600">Volatility:</span>
                        <span class="font-medium ml-1">{{ technicalAnalysis.performanceMetrics.volatility.toFixed(2) }}%</span>
                      </div>
                      <div v-if="technicalAnalysis.performanceMetrics?.sharpeRatio !== undefined">
                        <span class="text-gray-600">Sharpe Ratio:</span>
                        <span class="font-medium ml-1">{{ technicalAnalysis.performanceMetrics.sharpeRatio.toFixed(2) }}</span>
                      </div>
                      <div v-if="technicalAnalysis.performanceMetrics?.maxDrawdown !== undefined">
                        <span class="text-gray-600">Max Drawdown:</span>
                        <span class="font-medium ml-1 text-red-600">{{ technicalAnalysis.performanceMetrics.maxDrawdown.toFixed(2) }}%</span>
                      </div>
                      <div v-if="technicalAnalysis.performanceMetrics?.alpha !== undefined">
                        <span class="text-gray-600">Alpha:</span>
                        <span class="font-medium ml-1" :class="technicalAnalysis.performanceMetrics.alpha >= 0 ? 'text-green-600' : 'text-red-600'">
                          {{ technicalAnalysis.performanceMetrics.alpha.toFixed(2) }}%
                        </span>
                      </div>
                    </div>
                  </div>

                  <!-- AI Recommendation -->
                  <div class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-900 mb-2">🎯 AI Recommendation</h5>
                    <div v-if="technicalAnalysis.aiAnalysis?.technicalRecommendation" class="flex items-center space-x-3 mb-3">
                      <span class="px-3 py-1 rounded-full text-sm font-medium"
                            :class="getTechnicalRecommendationClass(technicalAnalysis.aiAnalysis.technicalRecommendation)">
                        {{ technicalAnalysis.aiAnalysis.technicalRecommendation }}
                      </span>
                      <span v-if="technicalAnalysis.aiAnalysis.confidence" class="text-gray-600 text-sm">
                        Confidence: {{ technicalAnalysis.aiAnalysis.confidence }}
                      </span>
                    </div>

                    <!-- AI Analysis Details -->
                    <div v-if="technicalAnalysis.aiAnalysis" class="space-y-3">
                      <div v-if="technicalAnalysis.aiAnalysis.performanceAnalysis" class="bg-gray-50 rounded p-3">
                        <h6 class="font-medium text-gray-900 mb-1">Performance Analysis</h6>
                        <p class="text-gray-700 text-sm">{{ technicalAnalysis.aiAnalysis.performanceAnalysis }}</p>
                      </div>

                      <div v-if="technicalAnalysis.aiAnalysis.riskAnalysis" class="bg-gray-50 rounded p-3">
                        <h6 class="font-medium text-gray-900 mb-1">Risk Analysis</h6>
                        <p class="text-gray-700 text-sm">{{ technicalAnalysis.aiAnalysis.riskAnalysis }}</p>
                      </div>

                      <div v-if="technicalAnalysis.aiAnalysis.trendAnalysis" class="bg-gray-50 rounded p-3">
                        <h6 class="font-medium text-gray-900 mb-1">Trend Analysis</h6>
                        <p class="text-gray-700 text-sm">{{ technicalAnalysis.aiAnalysis.trendAnalysis }}</p>
                      </div>

                      <div v-if="technicalAnalysis.aiAnalysis.investmentStrategy" class="bg-blue-50 rounded p-3">
                        <h6 class="font-medium text-blue-900 mb-1">Investment Strategy</h6>
                        <p class="text-blue-800 text-sm">{{ technicalAnalysis.aiAnalysis.investmentStrategy }}</p>
                      </div>

                      <div v-if="technicalAnalysis.aiAnalysis.outlook" class="bg-green-50 rounded p-3">
                        <h6 class="font-medium text-green-900 mb-1">Outlook</h6>
                        <p class="text-green-800 text-sm">{{ technicalAnalysis.aiAnalysis.outlook }}</p>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Initial State -->
                <div v-else class="bg-white rounded-lg p-4 text-center">
                  <p class="text-gray-500 mb-4">Click "Analyze Fund" to get AI-powered technical analysis of this mutual fund's performance.</p>
                </div>
              </div>
            </div>

            <!-- Fundamental Analysis Tab -->
            <div v-show="activeTab === 'fundamental'" class="tab-panel">
              <div class="bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-semibold text-gray-900">💰 AI Fundamental Analysis</h4>
                  <button
                    @click="fetchFundamentalAnalysis"
                    :disabled="fundamentalLoading || !isConfigured"
                    class="bg-gradient-to-r from-orange-600 to-amber-600 text-white px-4 py-2 rounded-lg hover:from-orange-700 hover:to-amber-700 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    <span v-if="fundamentalLoading">💰 Analyzing...</span>
                    <span v-else>💰 Analyze Fund</span>
                  </button>
                </div>

                <!-- Fundamental Analysis Loading State -->
                <div v-if="fundamentalLoading" class="mb-4">
                  <div class="bg-white rounded-lg p-4">
                    <div class="flex items-center space-x-3 mb-3">
                      <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-orange-500"></div>
                      <span class="text-gray-700">{{ fundamentalStatusMessage || 'Analyzing fund fundamentals...' }}</span>
                    </div>
                    <div class="bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-orange-500 h-2 rounded-full transition-all duration-500"
                        :style="{ width: fundamentalProgress + '%' }"
                      ></div>
                    </div>
                  </div>
                </div>

                <!-- Fundamental Analysis Error State -->
                <div v-else-if="fundamentalError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-red-600 text-lg">❌</span>
                    <div>
                      <h5 class="font-medium text-red-800">Analysis Failed</h5>
                      <p class="text-red-700 text-sm">{{ fundamentalError }}</p>
                      <button
                        @click="fetchFundamentalAnalysis"
                        class="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Try Again
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Fundamental Analysis Results -->
                <div v-else-if="fundamentalAnalysis" class="space-y-4">
                  <!-- Fund Metrics -->
                  <div class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-900 mb-3">📊 Key Fund Metrics</h5>
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div v-if="fundamentalAnalysis.metrics?.fundHouse">
                        <span class="text-gray-600">Fund House:</span>
                        <span class="font-medium ml-1">{{ fundamentalAnalysis.metrics.fundHouse }}</span>
                      </div>
                      <div v-if="fundamentalAnalysis.metrics?.currentNAV">
                        <span class="text-gray-600">Current NAV:</span>
                        <span class="font-medium ml-1">{{ fundamentalAnalysis.metrics.currentNAV }}</span>
                      </div>
                      <div v-if="fundamentalAnalysis.metrics?.category">
                        <span class="text-gray-600">Category:</span>
                        <span class="font-medium ml-1">{{ fundamentalAnalysis.metrics.category }}</span>
                      </div>
                      <div v-if="fundamentalAnalysis.metrics?.navDate">
                        <span class="text-gray-600">NAV Date:</span>
                        <span class="font-medium ml-1">{{ fundamentalAnalysis.metrics.navDate }}</span>
                      </div>
                    </div>
                  </div>



                  <!-- Investment Recommendation -->
                  <div class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-900 mb-3">🎯 Investment Recommendation</h5>
                    <div v-if="fundamentalAnalysis.recommendation" class="flex items-center space-x-3 mb-3">
                      <span class="px-3 py-1 rounded-full text-sm font-medium"
                            :class="getFundamentalRecommendationClass(fundamentalAnalysis.recommendation)">
                        {{ fundamentalAnalysis.recommendation }}
                      </span>
                      <span v-if="fundamentalAnalysis.confidence" class="text-gray-600 text-sm">
                        Confidence: {{ fundamentalAnalysis.confidence }}
                      </span>
                    </div>
                    <div v-if="fundamentalAnalysis.summary" class="prose prose-sm max-w-none">
                      <div v-html="fundamentalAnalysis.summary"></div>
                    </div>
                  </div>
                </div>

                <!-- Initial State -->
                <div v-else class="bg-white rounded-lg p-4 text-center">
                  <p class="text-gray-500 mb-4">Click "Analyze Fund" to get AI-powered fundamental analysis of this mutual fund.</p>
                </div>
              </div>
            </div>

            <!-- Portfolio Holdings Tab -->
            <div v-show="activeTab === 'portfolio'" class="tab-panel">
              <div class="bg-gradient-to-r from-blue-50 to-cyan-50 border border-blue-200 rounded-lg p-6">
                <div class="flex items-center justify-between mb-4">
                  <h4 class="text-lg font-semibold text-gray-900">🏭 Portfolio Holdings Analysis</h4>
                  <button
                    @click="fetchPortfolioAnalysis"
                    :disabled="portfolioLoading || !isConfigured"
                    class="bg-gradient-to-r from-blue-600 to-cyan-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-cyan-700 disabled:opacity-50 transition-all duration-200 shadow-sm"
                  >
                    <span v-if="portfolioLoading">🏭 Analyzing...</span>
                    <span v-else>🏭 Analyze Portfolio</span>
                  </button>
                </div>

                <!-- Portfolio Analysis Loading State -->
                <div v-if="portfolioLoading" class="mb-4">
                  <div class="bg-white rounded-lg p-4">
                    <div class="flex items-center space-x-3 mb-3">
                      <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-blue-500"></div>
                      <span class="text-gray-700">{{ portfolioStatusMessage || 'Analyzing portfolio holdings...' }}</span>
                    </div>
                    <div class="bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-blue-500 h-2 rounded-full transition-all duration-500"
                        :style="{ width: portfolioProgress + '%' }"
                      ></div>
                    </div>
                  </div>
                </div>

                <!-- Portfolio Analysis Error State -->
                <div v-else-if="portfolioError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                  <div class="flex items-center space-x-2">
                    <span class="text-red-600 text-lg">❌</span>
                    <div>
                      <p class="text-red-800 font-medium">Portfolio Analysis Failed</p>
                      <p class="text-red-600 text-sm">{{ portfolioError }}</p>
                      <button
                        @click="fetchPortfolioAnalysis"
                        class="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                      >
                        Try Again
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Portfolio Analysis Results -->
                <div v-else-if="portfolioAnalysis" class="space-y-4">
                  <!-- Sector Allocation Table -->
                  <div v-if="portfolioAnalysis.sectorAllocation && portfolioAnalysis.sectorAllocation.length > 0" class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-900 mb-3">🏭 Sector-wise Allocation</h5>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sector</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Allocation %</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="(sector, index) in portfolioAnalysis.sectorAllocation" :key="index">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ sector.sector }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <div class="flex items-center">
                                <div class="flex-1 bg-gray-200 rounded-full h-2 mr-2 max-w-20">
                                  <div class="bg-blue-600 h-2 rounded-full" :style="{ width: sector.percentage + '%' }"></div>
                                </div>
                                <span class="font-medium">{{ sector.percentage }}%</span>
                              </div>
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">{{ sector.description }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <!-- Top Holdings Table -->
                  <div v-if="portfolioAnalysis.topHoldings && portfolioAnalysis.topHoldings.length > 0" class="bg-white rounded-lg p-4">
                    <h5 class="font-semibold text-gray-900 mb-3">📈 Top Stock Holdings</h5>
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sector</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Weight %</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Market Cap</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="(holding, index) in portfolioAnalysis.topHoldings" :key="index">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ holding.company }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ holding.sector }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              <span class="font-medium">{{ holding.weight }}%</span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ holding.marketCap }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>

                <!-- Initial State -->
                <div v-else class="bg-white rounded-lg p-4 text-center">
                  <p class="text-gray-500 mb-4">Click "Analyze Portfolio" to get AI-powered analysis of this fund's sector and stock-wise holdings.</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- AI Disclaimer -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6 mx-6 mb-6">
          <div class="flex items-start space-x-2">
            <span class="text-yellow-600 text-lg">⚠️</span>
            <div class="text-sm text-yellow-800">
              <p class="font-medium mb-1">AI Analysis Disclaimer</p>
              <p>This analysis is generated by AI and should not be considered as financial advice. Always conduct your own research and consult with financial advisors before making investment decisions. Past performance does not guarantee future results.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- AI Provider Footer -->
      <div class="px-6 py-3 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center space-x-4">
            <span class="flex items-center">
              🤖 <span class="ml-1 font-medium">AI Provider:</span>
              <span class="ml-1 text-gray-700">{{ currentProvider?.name || aiConfig.provider }}</span>
            </span>
            <span class="flex items-center">
              🧠 <span class="ml-1 font-medium">Model:</span>
              <span class="ml-1 text-gray-700">{{ currentModel?.name || aiConfig.model }}</span>
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <span v-if="isConfigured" class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
              ✅ Configured
            </span>
            <span v-else class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
              ❌ Not Configured
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'
import { useAIConfig } from '~/composables/ai/useAIConfig'
import { useAIApi } from '~/composables/ai/useAIApi'

// Props
const props = defineProps({
  fund: {
    type: Object,
    required: true
  },
  show: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close'])

// AI Configuration
const {
  aiConfig,
  currentProvider,
  currentModel,
  isConfigured
} = useAIConfig()
const { makeAIRequest } = useAIApi()

// State
const activeTab = ref('technical')

// Technical Analysis State
const technicalLoading = ref(false)
const technicalError = ref('')
const technicalAnalysis = ref(null)
const technicalProgress = ref(0)
const technicalStatusMessage = ref('')

// Fundamental Analysis State
const fundamentalLoading = ref(false)
const fundamentalError = ref('')
const fundamentalAnalysis = ref(null)
const fundamentalProgress = ref(0)
const fundamentalStatusMessage = ref('')

// Portfolio Analysis State
const portfolioLoading = ref(false)
const portfolioError = ref('')
const portfolioAnalysis = ref(null)
const portfolioProgress = ref(0)
const portfolioStatusMessage = ref('')

// Methods
const formatPrice = (price) => {
  if (!price) return '0'
  return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2 }).format(price)
}

const getTechnicalRecommendationClass = (recommendation) => {
  if (!recommendation) return 'bg-gray-100 text-gray-800'
  const rec = recommendation.toLowerCase()
  if (rec.includes('buy') || rec.includes('strong buy')) return 'bg-green-100 text-green-800'
  if (rec.includes('sell') || rec.includes('strong sell')) return 'bg-red-100 text-red-800'
  if (rec.includes('hold')) return 'bg-yellow-100 text-yellow-800'
  return 'bg-gray-100 text-gray-800'
}

const getFundamentalRecommendationClass = (recommendation) => {
  if (!recommendation) return 'bg-gray-100 text-gray-800'
  const rec = recommendation.toLowerCase()
  if (rec.includes('buy') || rec.includes('invest') || rec.includes('strong buy')) return 'bg-green-100 text-green-800'
  if (rec.includes('sell') || rec.includes('avoid') || rec.includes('strong sell')) return 'bg-red-100 text-red-800'
  if (rec.includes('hold') || rec.includes('neutral')) return 'bg-yellow-100 text-yellow-800'
  return 'bg-gray-100 text-gray-800'
}

// Technical Analysis Function
const fetchTechnicalAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings first.')
    return
  }

  try {
    technicalLoading.value = true
    technicalError.value = ''
    technicalProgress.value = 0
    technicalStatusMessage.value = 'Initializing technical analysis...'

    const api = useApiWithAuth()

    // Queue the background technical analysis job for mutual fund using AI API
    const queueResponse = await makeAIRequest('/api/stock-market/mutual-fund-technical-analysis', {
      method: 'POST',
      body: {
        schemeCode: props.fund.schemeCode,
        schemeName: props.fund.schemeName,
        fundHouse: props.fund.fundHouse,
        category: props.fund.category,
        currentNAV: props.fund.currentNAV,
        currentValue: props.fund.currentValue,
        profitLoss: props.fund.profitLoss,
        profitLossPercentage: props.fund.profitLossPercentage
      }
    })

    if (!queueResponse.jobId) {
      throw new Error('Failed to queue technical analysis job')
    }

    // Poll for results
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await api.get(`/api/stock-market/ai-analysis-status/${queueResponse.jobId}`)
        
        technicalProgress.value = statusResponse.progress || 0
        technicalStatusMessage.value = statusResponse.statusMessage || 'Processing...'

        if (statusResponse.status === 'completed') {
          clearInterval(pollInterval)
          technicalAnalysis.value = statusResponse.analysis
          technicalLoading.value = false
          console.log('✅ Technical analysis completed:', statusResponse.analysis)
          console.log('📊 Performance Metrics:', statusResponse.analysis?.performanceMetrics)
          console.log('🤖 AI Analysis:', statusResponse.analysis?.aiAnalysis)
        } else if (statusResponse.status === 'failed') {
          clearInterval(pollInterval)
          throw new Error(statusResponse.error || 'Technical analysis failed')
        }
      } catch (pollError) {
        clearInterval(pollInterval)
        throw pollError
      }
    }, 2000)

    // Timeout after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval)
      if (technicalLoading.value) {
        technicalError.value = 'Analysis timed out. Please try again.'
        technicalLoading.value = false
      }
    }, 300000)

  } catch (error) {
    console.error('❌ Technical analysis error:', error)
    console.error('🔍 Error details:', {
      message: error.message,
      statusCode: error.statusCode,
      data: error.data,
      stack: error.stack
    })
    technicalError.value = error.data?.message || error.message || 'Failed to analyze mutual fund'
    technicalLoading.value = false
  }
}

// Helper function to format fundamental analysis summary
const formatFundamentalSummary = (aiAnalysis) => {
  if (!aiAnalysis) return ''

  let summary = ''

  // Fund House Analysis
  if (aiAnalysis.fundHouseAnalysis) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">🏢 Fund House Analysis</h6><p class="text-gray-700">${aiAnalysis.fundHouseAnalysis}</p></div>`
  }

  // Expense Analysis
  if (aiAnalysis.expenseAnalysis) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">💰 Expense Analysis</h6><p class="text-gray-700">${aiAnalysis.expenseAnalysis}</p></div>`
  }

  // Category Analysis
  if (aiAnalysis.categoryAnalysis) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">📊 Category Analysis</h6><p class="text-gray-700">${aiAnalysis.categoryAnalysis}</p></div>`
  }

  // Risk Assessment
  if (aiAnalysis.riskAssessment) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">⚠️ Risk Assessment</h6><p class="text-gray-700">${aiAnalysis.riskAssessment}</p></div>`
  }

  // Investment Horizon
  if (aiAnalysis.investmentHorizon) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">⏰ Investment Horizon</h6><p class="text-gray-700">${aiAnalysis.investmentHorizon}</p></div>`
  }

  // Portfolio Fit
  if (aiAnalysis.portfolioFit) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">🎯 Portfolio Fit</h6><p class="text-gray-700">${aiAnalysis.portfolioFit}</p></div>`
  }

  // Key Strengths
  if (aiAnalysis.keyStrengths) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-green-700 mb-2">✅ Key Strengths</h6><p class="text-gray-700">${aiAnalysis.keyStrengths}</p></div>`
  }

  // Key Risks
  if (aiAnalysis.keyRisks) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-red-700 mb-2">⚠️ Key Risks</h6><p class="text-gray-700">${aiAnalysis.keyRisks}</p></div>`
  }

  // Outlook
  if (aiAnalysis.outlook) {
    summary += `<div class="mb-4"><h6 class="font-semibold text-gray-900 mb-2">🔮 Outlook</h6><p class="text-gray-700">${aiAnalysis.outlook}</p></div>`
  }

  return summary
}

// Fundamental Analysis Function
const fetchFundamentalAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings first.')
    return
  }

  try {
    fundamentalLoading.value = true
    fundamentalError.value = ''
    fundamentalProgress.value = 0
    fundamentalStatusMessage.value = 'Initializing fundamental analysis...'

    const api = useApiWithAuth()

    // Queue the background fundamental analysis job for mutual fund using AI API
    const queueResponse = await makeAIRequest('/api/stock-market/mutual-fund-fundamental-analysis', {
      method: 'POST',
      body: {
        schemeCode: props.fund.schemeCode,
        schemeName: props.fund.schemeName,
        fundHouse: props.fund.fundHouse,
        category: props.fund.category,
        currentNAV: props.fund.currentNAV,
        currentValue: props.fund.currentValue,
        profitLoss: props.fund.profitLoss,
        profitLossPercentage: props.fund.profitLossPercentage
      }
    })

    if (!queueResponse.jobId) {
      throw new Error('Failed to queue fundamental analysis job')
    }

    // Poll for results
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await api.get(`/api/stock-market/ai-analysis-status/${queueResponse.jobId}`)
        
        fundamentalProgress.value = statusResponse.progress || 0
        fundamentalStatusMessage.value = statusResponse.statusMessage || 'Processing...'

        if (statusResponse.status === 'completed') {
          clearInterval(pollInterval)

          // Transform API response to match template structure
          const analysis = statusResponse.analysis
          if (analysis && analysis.aiAnalysis) {
            fundamentalAnalysis.value = {
              recommendation: analysis.aiAnalysis.fundamentalRecommendation,
              confidence: analysis.aiAnalysis.confidence,
              metrics: {
                expenseRatio: analysis.fundamentalMetrics?.expenseRatio,
                aum: analysis.fundamentalMetrics?.aum,
                fundAge: analysis.fundamentalMetrics?.fundAge,
                rating: analysis.fundamentalMetrics?.rating,
                fundHouse: analysis.fundamentalMetrics?.fundHouse,
                currentNAV: analysis.fundamentalMetrics?.currentNAV,
                navDate: analysis.fundamentalMetrics?.navDate,
                schemeCategory: analysis.fundamentalMetrics?.schemeCategory
              },
              summary: formatFundamentalSummary(analysis.aiAnalysis)
            }
          } else {
            fundamentalAnalysis.value = statusResponse.analysis
          }

          fundamentalLoading.value = false
          console.log('✅ Fundamental analysis completed:', fundamentalAnalysis.value)
        } else if (statusResponse.status === 'failed') {
          clearInterval(pollInterval)
          throw new Error(statusResponse.error || 'Fundamental analysis failed')
        }
      } catch (pollError) {
        clearInterval(pollInterval)
        throw pollError
      }
    }, 2000)

    // Timeout after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval)
      if (fundamentalLoading.value) {
        fundamentalError.value = 'Analysis timed out. Please try again.'
        fundamentalLoading.value = false
      }
    }, 300000)

  } catch (error) {
    console.error('❌ Fundamental analysis error:', error)
    fundamentalError.value = error.message || 'Failed to analyze mutual fund'
    fundamentalLoading.value = false
  }
}

// Portfolio Analysis Method
const fetchPortfolioAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings first.')
    return
  }

  try {
    portfolioLoading.value = true
    portfolioError.value = ''
    portfolioAnalysis.value = null
    portfolioProgress.value = 0
    portfolioStatusMessage.value = 'Starting portfolio analysis...'

    console.log('🏭 Starting portfolio analysis for:', props.fund.schemeName)

    const api = useApiWithAuth()

    // Start portfolio analysis using AI API
    console.log('🚀 Starting portfolio analysis API call...')
    const queueResponse = await makeAIRequest('/api/stock-market/mutual-fund-portfolio-analysis', {
      method: 'POST',
      body: {
        schemeName: props.fund.schemeName,
        schemeCode: props.fund.schemeCode,
        fundHouse: props.fund.fundHouse,
        category: props.fund.category,
        currentNAV: props.fund.currentNAV,
        currentValue: props.fund.currentValue,
        profitLoss: props.fund.profitLoss,
        profitLossPercentage: props.fund.profitLossPercentage
      }
    })

    if (!queueResponse || !queueResponse.jobId) {
      console.error('❌ Invalid response from portfolio analysis API:', queueResponse)
      throw new Error('Failed to queue portfolio analysis job - no jobId received')
    }

    const jobId = queueResponse.jobId
    console.log('📋 Portfolio analysis job started with jobId:', jobId)

    // Poll for results
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await api.get(`/api/stock-market/ai-analysis-status/${jobId}`)

        if (statusResponse.status === 'completed') {
          clearInterval(pollInterval)
          portfolioAnalysis.value = statusResponse.analysis
          portfolioLoading.value = false
          console.log('✅ Portfolio analysis completed:', portfolioAnalysis.value)
        } else if (statusResponse.status === 'failed') {
          clearInterval(pollInterval)
          throw new Error(statusResponse.error || 'Portfolio analysis failed')
        } else {
          // Update progress
          portfolioProgress.value = statusResponse.progress || 0
          portfolioStatusMessage.value = statusResponse.message || 'Analyzing portfolio...'
        }
      } catch (pollError) {
        clearInterval(pollInterval)
        throw pollError
      }
    }, 2000)

    // Timeout after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval)
      if (portfolioLoading.value) {
        portfolioError.value = 'Analysis timed out. Please try again.'
        portfolioLoading.value = false
      }
    }, 300000)

  } catch (error) {
    console.error('❌ Portfolio analysis error:', error)
    portfolioError.value = error.message || 'Failed to analyze portfolio'
    portfolioLoading.value = false
  }
}

// Open global settings
const openGlobalSettings = () => {
  // Emit event to open global settings
  window.dispatchEvent(new CustomEvent('open-global-settings', {
    detail: { activeTab: 'ai' }
  }))
}
</script>
