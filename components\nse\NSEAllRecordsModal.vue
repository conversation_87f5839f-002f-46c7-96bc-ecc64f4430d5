<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-11/12 max-w-[95vw] max-h-[90vh] overflow-hidden">
      <div class="p-4 border-b border-gray-200 bg-indigo-600 flex justify-between items-center">
        <div class="flex items-center">
          <h2 class="text-xl font-semibold text-white">All NSE Records</h2>
          <div class="ml-4 flex items-center text-indigo-200 text-xs">
            <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">ESC</span>
            <span class="mr-2">to close</span>
            <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">/</span>
            <span>to search</span>
          </div>
        </div>
        <button @click="$emit('close')" class="text-white hover:text-gray-200">
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <div class="p-4 overflow-auto max-h-[calc(90vh-8rem)]">
        <div v-if="loading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
        </div>

        <div v-else-if="error" class="text-red-600 text-center py-4">
          {{ error }}
        </div>

        <div v-else>
          <div v-if="records.length === 0" class="text-center py-8">
            <p class="text-gray-500">No records found. Please try again later.</p>
          </div>
          <div v-else>
            <!-- Search Bar and Controls -->
            <div class="mb-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
              <div class="flex items-center gap-2">
                <p class="text-sm text-gray-600">
                  <span class="font-medium">{{ filteredRecords.length }}</span> of <span class="font-medium">{{ records.length }}</span> records
                </p>
                <span v-if="searchQuery" class="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
                  Filtered
                </span>
              </div>
              <div class="relative w-full sm:w-80">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  v-model="searchQuery"
                  placeholder="Search in all columns... (Press '/' to focus)"
                  class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                  @keyup.esc="searchQuery = ''"
                  ref="searchInput"
                />
                <button
                  v-if="searchQuery"
                  @click="searchQuery = ''"
                  class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                  title="Clear search"
                >
                  <Icon name="heroicons:x-mark" class="w-5 h-5" />
                </button>
              </div>
            </div>

            <!-- Table -->
            <div class="overflow-x-auto border border-gray-200 rounded-lg shadow">
              <table class="min-w-full divide-y divide-gray-200 table-auto">
                <thead>
                  <tr>
                    <th v-for="header in tableHeaders" :key="header.key"
                        class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600 border-b border-indigo-700">
                      {{ header.label }}
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="record in filteredRecords" :key="record._id" class="hover:bg-gray-50">
                    <td v-for="header in tableHeaders" :key="header.key" class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span
                        :class="{
                          'text-green-600 font-medium': header.key === 'pl' && Number(record.pl) > 0,
                          'text-red-600 font-medium': header.key === 'pl' && Number(record.pl) < 0,
                          'font-medium': header.key === 'symbol',
                          'text-indigo-600': header.key === 'cn_no' || header.key === 'folio'
                        }"
                      >
                        {{ formatValue(record[header.key], header.key) }}
                      </span>
                    </td>
                  </tr>
                </tbody>
              </table>

              <!-- No Results Message -->
              <div v-if="filteredRecords.length === 0 && searchQuery" class="py-8 text-center bg-gray-50">
                <p class="text-gray-500">No records match your search criteria.</p>
                <button @click="searchQuery = ''" class="mt-2 text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                  Clear search
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'

const props = defineProps<{
  show: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

// Define interface for Folio records to ensure type safety
interface FolioRecord {
  _id?: string;
  cn_no: string;
  symbol: string;
  price: number;
  qnty: number;
  amt: number;
  brokerage: number;
  broker: string;
  pdate: Date | string;
  namt: number;
  folio: string;
  type: string;
  rid: string;
  sector: string;
  user: string;
  cprice?: number;
  cval?: number;
  age?: number;
  pl?: number;
  [key: string]: any; // Allow for additional properties
}

const records = ref<FolioRecord[]>([])
const loading = ref(false)
const error = ref<string | null>(null)
const searchQuery = ref('')

// Computed property for filtered records based on search query
const filteredRecords = computed(() => {
  if (!searchQuery.value.trim()) {
    return records.value
  }

  const query = searchQuery.value.toLowerCase()
  return records.value.filter(record => {
    // Search in all fields
    return Object.entries(record).some(([key, value]) => {
      // Skip internal fields and complex objects
      if (key.startsWith('_') || typeof value === 'object') {
        return false
      }

      // Convert value to string and check if it includes the search query
      const stringValue = String(value).toLowerCase()
      return stringValue.includes(query)
    })
  })
})

const tableHeaders = [
  { key: 'cn_no', label: 'CN No.' },
  { key: 'symbol', label: 'Symbol' },
  { key: 'price', label: 'Buy Price' },
  { key: 'qnty', label: 'Quantity' },
  { key: 'amt', label: 'Amount' },
  { key: 'brokerage', label: 'Brokerage' },
  { key: 'broker', label: 'Broker' },
  { key: 'pdate', label: 'Purchase Date' },
  { key: 'namt', label: 'Net Amount' },
  { key: 'folio', label: 'Folio' },
  { key: 'type', label: 'Type' },
  { key: 'sector', label: 'Sector' },
  { key: 'cprice', label: 'Current Price' },
  { key: 'cval', label: 'Current Value' },
  { key: 'age', label: 'Age (Days)' },
  { key: 'pl', label: 'Profit/Loss' }
]

const api = useApiWithAuth()

// Format values for display
const formatValue = (value: any, key: string) => {
  if (value === undefined || value === null) return '-'

  // Format numbers with commas and 2 decimal places
  if (typeof value === 'number') {
    // Format monetary values
    if (key === 'price' || key === 'cprice' || key === 'amt' || key === 'namt' || key === 'cval' || key === 'brokerage') {
      return `₹${value.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    }

    // Format profit/loss with sign and currency symbol
    if (key === 'pl') {
      if (value === 0) return `₹0.00`
      return value > 0
        ? `+₹${value.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
        : `-₹${Math.abs(value).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    }

    // Format quantities with commas but no decimals
    if (key === 'qnty') {
      return value.toLocaleString('en-IN', { maximumFractionDigits: 0 })
    }

    // Format age as a whole number
    if (key === 'age') {
      return Math.round(value).toLocaleString('en-IN')
    }

    // Default number formatting
    return value.toLocaleString('en-IN')
  }

  // Format dates
  if (key === 'pdate' && (typeof value === 'string' || value instanceof Date)) {
    try {
      const date = new Date(value)
      if (!isNaN(date.getTime())) {
        return date.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' })
      }
    } catch (e) {
      // If date parsing fails, return the original value
    }
  }

  // For symbol, ensure it doesn't have NSE: prefix
  if (key === 'symbol' && typeof value === 'string') {
    return value.replace('NSE:', '')
  }

  return value
}

const fetchRecords = async () => {
  loading.value = true
  error.value = null

  try {
    const api = useApiWithAuth()
    const response = await api.get('/api/nse/folio')

    // Process and validate the data
    if (Array.isArray(response)) {
      // Ensure all numeric fields are properly converted to numbers
      records.value = response.map(record => ({
        ...record,
        price: Number(record.price || 0),
        qnty: Number(record.qnty || 0),
        amt: Number(record.amt || 0),
        brokerage: Number(record.brokerage || 0),
        namt: Number(record.namt || 0),
        cprice: Number(record.cprice || 0),
        cval: Number(record.cval || 0),
        age: Number(record.age || 0),
        pl: Number(record.pl || 0)
      }))
    } else {
      console.error('Received invalid data format from API')
      records.value = []
      error.value = 'Invalid data format received from server'
    }
  } catch (err: any) {
    console.error('Error fetching all records:', err)
    error.value = err.message || 'Failed to fetch records'
  } finally {
    loading.value = false
  }
}

// Reference to search input element
const searchInput = ref<HTMLInputElement | null>(null)

// Watch for modal visibility changes
watch(() => props.show, (newVal) => {
  if (newVal) {
    fetchRecords()
    // Focus search input after a short delay to ensure the modal is fully rendered
    setTimeout(() => {
      searchInput.value?.focus()
    }, 100)
  } else {
    // Clear search when modal is closed
    searchQuery.value = ''
  }
})

// Global keyboard shortcut to focus search
onMounted(() => {
  if (process.client) {
    window.addEventListener('keydown', handleKeyDown)
  }
})

// Clean up event listener
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('keydown', handleKeyDown)
  }
})

// Handle keyboard shortcuts
const handleKeyDown = (event: KeyboardEvent) => {
  // Only handle if modal is open
  if (!props.show) return

  // '/' key to focus search (common in many web apps)
  if (event.key === '/' && document.activeElement !== searchInput.value) {
    event.preventDefault()
    searchInput.value?.focus()
  }

  // ESC key to close modal (unless focus is in search input)
  if (event.key === 'Escape' && document.activeElement !== searchInput.value) {
    event.preventDefault()
    emit('close')
  }
}
</script>