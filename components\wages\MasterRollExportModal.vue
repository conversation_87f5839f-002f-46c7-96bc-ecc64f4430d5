<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
    <div class="bg-white rounded-lg w-full max-w-4xl my-4 max-h-[90vh] flex flex-col overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-gradient-to-r from-purple-500 to-indigo-600 p-4 rounded-t-lg flex justify-between items-center sticky top-0">
        <h2 class="text-xl font-bold text-white">Export Master Roll Data</h2>
        <button @click="$emit('close')" class="text-white hover:text-red-200 transition-colors">
          <XMarkIcon class="h-6 w-6" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6 flex-1 overflow-y-auto">
        <!-- Export Format Selection -->
        <div class="mb-6">
          <label class="block text-sm font-medium text-gray-700 mb-3">Export Format</label>
          <div class="grid grid-cols-3 gap-4">
            <div class="relative">
              <input
                id="format-excel"
                v-model="exportOptions.format"
                type="radio"
                value="excel"
                class="sr-only"
              />
              <label
                for="format-excel"
                class="flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all"
                :class="exportOptions.format === 'excel' ? 'border-green-500 bg-green-50 text-green-700' : 'border-gray-300 hover:border-gray-400'"
              >
                <div class="text-center">
                  <div class="text-2xl mb-2">📊</div>
                  <div class="font-medium">Excel</div>
                  <div class="text-xs text-gray-500">Formatted spreadsheet</div>
                </div>
              </label>
            </div>
            <div class="relative">
              <input
                id="format-csv"
                v-model="exportOptions.format"
                type="radio"
                value="csv"
                class="sr-only"
              />
              <label
                for="format-csv"
                class="flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all"
                :class="exportOptions.format === 'csv' ? 'border-green-500 bg-green-50 text-green-700' : 'border-gray-300 hover:border-gray-400'"
              >
                <div class="text-center">
                  <div class="text-2xl mb-2">📄</div>
                  <div class="font-medium">CSV</div>
                  <div class="text-xs text-gray-500">Comma separated</div>
                </div>
              </label>
            </div>
            <div class="relative">
              <input
                id="format-pdf"
                v-model="exportOptions.format"
                type="radio"
                value="pdf"
                class="sr-only"
              />
              <label
                for="format-pdf"
                class="flex items-center justify-center p-4 border-2 rounded-lg cursor-pointer transition-all"
                :class="exportOptions.format === 'pdf' ? 'border-green-500 bg-green-50 text-green-700' : 'border-gray-300 hover:border-gray-400'"
              >
                <div class="text-center">
                  <div class="text-2xl mb-2">📋</div>
                  <div class="font-medium">PDF</div>
                  <div class="text-xs text-gray-500">Printable report</div>
                </div>
              </label>
            </div>
          </div>
        </div>

        <!-- Filter Options -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Filter Options</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Date Range Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Date of Joining Range</label>
              <div class="space-y-2">
                <input
                  v-model="exportOptions.filters.dateFrom"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="From date"
                />
                <input
                  v-model="exportOptions.filters.dateTo"
                  type="date"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder="To date"
                />
              </div>
            </div>

            <!-- Status Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Employee Status</label>
              <select
                v-model="exportOptions.filters.status"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">All Statuses</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="on Leave">On Leave</option>
                <option value="terminated">Terminated</option>
                <option value="left">Left Service</option>
              </select>
            </div>

            <!-- Category Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
              <select
                v-model="exportOptions.filters.category"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">All Categories</option>
                <option value="HELPER">HELPER</option>
                <option value="TECHNICIAN">TECHNICIAN</option>
                <option value="ELECTRICIAN">ELECTRICIAN</option>
                <option value="SEMI-SKILLED">SEMI-SKILLED</option>
                <option value="HIGHLY-SKILLED">HIGHLY-SKILLED</option>
                <option value="UNSKILLED">UNSKILLED</option>
              </select>
            </div>

            <!-- Project Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
              <select
                v-model="exportOptions.filters.project"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">All Projects</option>
                <option v-for="project in uniqueProjects" :key="project" :value="project">
                  {{ project }}
                </option>
              </select>
            </div>

            <!-- Site Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Site</label>
              <select
                v-model="exportOptions.filters.site"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">All Sites</option>
                <option v-for="site in uniqueSites" :key="site" :value="site">
                  {{ site }}
                </option>
              </select>
            </div>

            <!-- Phone Validation Filter -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number Status</label>
              <select
                v-model="exportOptions.filters.phoneValidation"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">All Phone Numbers</option>
                <option value="valid">Valid Phone Numbers</option>
                <option value="invalid">Invalid Phone Numbers</option>
              </select>
            </div>
          </div>
        </div>

        <!-- Column Selection -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Select Columns to Export</h3>
          <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
            <div v-for="column in availableColumns" :key="column.key" class="flex items-center">
              <input
                :id="`column-${column.key}`"
                v-model="exportOptions.selectedColumns"
                type="checkbox"
                :value="column.key"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <label :for="`column-${column.key}`" class="ml-2 block text-sm text-gray-700">
                {{ column.label }}
              </label>
            </div>
          </div>
          <div class="mt-3 flex space-x-2">
            <button
              @click="selectAllColumns"
              class="text-sm text-purple-600 hover:text-purple-800 font-medium"
            >
              Select All
            </button>
            <span class="text-gray-300">|</span>
            <button
              @click="deselectAllColumns"
              class="text-sm text-purple-600 hover:text-purple-800 font-medium"
            >
              Deselect All
            </button>
          </div>
        </div>

        <!-- Export Options -->
        <div class="mb-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Export Options</h3>
          <div class="space-y-3">
            <div class="flex items-center">
              <input
                id="respect-current-filters"
                v-model="exportOptions.respectCurrentFilters"
                type="checkbox"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
              />
              <label for="respect-current-filters" class="ml-2 block text-sm text-gray-700">
                Apply current table filters and search
              </label>
            </div>
            <div class="flex items-center">
              <input
                id="include-summary"
                v-model="exportOptions.includeSummary"
                type="checkbox"
                class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                :disabled="exportOptions.format === 'csv'"
              />
              <label for="include-summary" class="ml-2 block text-sm text-gray-700">
                Include summary statistics
                <span v-if="exportOptions.format === 'csv'" class="text-gray-400">(Excel/PDF only)</span>
              </label>
            </div>
          </div>
        </div>

        <!-- Preview -->
        <div class="mb-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-blue-900">Export Preview</h4>
              <p class="text-sm text-blue-700">
                {{ filteredRecordCount }} records will be exported
                <span v-if="exportOptions.selectedColumns.length > 0">
                  with {{ exportOptions.selectedColumns.length }} columns
                </span>
              </p>
            </div>
            <div class="text-2xl">
              {{ exportOptions.format === 'excel' ? '📊' : exportOptions.format === 'csv' ? '📄' : '📋' }}
            </div>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="bg-gray-50 px-6 py-4 rounded-b-lg flex justify-end space-x-3 flex-shrink-0">
        <button
          @click="$emit('close')"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500"
        >
          Cancel
        </button>
        <button
          @click="handleExport"
          :disabled="isExporting || exportOptions.selectedColumns.length === 0"
          class="px-4 py-2 text-sm font-medium text-white bg-purple-600 border border-transparent rounded-md hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div v-if="isExporting" class="flex items-center">
            <div class="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
            Exporting...
          </div>
          <div v-else class="flex items-center">
            <DocumentArrowDownIcon class="h-4 w-4 mr-2" />
            Export Data
          </div>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { XMarkIcon, DocumentArrowDownIcon } from '@heroicons/vue/24/outline'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  employees: {
    type: Array,
    default: () => []
  },
  currentFilters: {
    type: Object,
    default: () => ({})
  },
  searchTerm: {
    type: String,
    default: ''
  },
  uniqueProjects: {
    type: Array,
    default: () => []
  },
  uniqueSites: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'export-complete'])

const isExporting = ref(false)

const exportOptions = ref({
  format: 'excel',
  filters: {
    dateFrom: '',
    dateTo: '',
    status: '',
    category: '',
    project: '',
    site: '',
    phoneValidation: ''
  },
  selectedColumns: [
    'employeeName',
    'fatherHusbandName',
    'phoneNo',
    'dateOfJoining',
    'status',
    'category',
    'project',
    'site'
  ],
  respectCurrentFilters: true,
  includeSummary: true
})

const availableColumns = [
  { key: 'employeeName', label: 'Employee Name' },
  { key: 'fatherHusbandName', label: 'Father/Husband Name' },
  { key: 'dateOfBirth', label: 'Date of Birth' },
  { key: 'dateOfJoining', label: 'Date of Joining' },
  { key: 'aadhar', label: 'Aadhar' },
  { key: 'pan', label: 'PAN' },
  { key: 'phoneNo', label: 'Phone Number' },
  { key: 'address', label: 'Address' },
  { key: 'bank', label: 'Bank' },
  { key: 'branch', label: 'Branch' },
  { key: 'accountNo', label: 'Account Number' },
  { key: 'ifsc', label: 'IFSC' },
  { key: 'uan', label: 'UAN' },
  { key: 'esicNo', label: 'ESIC Number' },
  { key: 'sKalyanNo', label: 'S Kalyan Number' },
  { key: 'pDayWage', label: 'Per Day Wage' },
  { key: 'project', label: 'Project' },
  { key: 'site', label: 'Site' },
  { key: 'category', label: 'Category' },
  { key: 'status', label: 'Status' },
  { key: 'dateOfExit', label: 'Date of Exit' },
  { key: 'doeRem', label: 'Exit Remarks' }
]

// Phone number validation function (same as in master_roll.vue)
const isValidPhoneNumber = (phoneNo) => {
  if (!phoneNo) return false
  const cleanPhone = phoneNo.toString().replace(/\D/g, '')
  const invalidPatterns = [
    /^0+$/, /^1+$/, /^2+$/, /^3+$/, /^4+$/, /^5+$/, /^6+$/, /^7+$/, /^8+$/, /^9+$/,
    /^*********[0-9]$/, /^*********[0-9]$/
  ]
  for (const pattern of invalidPatterns) {
    if (pattern.test(cleanPhone)) return false
  }
  if (cleanPhone.length === 10) return /^[6-9]/.test(cleanPhone)
  if (cleanPhone.length === 11) return /^[0-9]/.test(cleanPhone)
  return false
}

const filteredRecordCount = computed(() => {
  let filtered = [...props.employees]
  
  // Apply current table filters if enabled
  if (exportOptions.value.respectCurrentFilters) {
    // Apply search term
    if (props.searchTerm) {
      const search = props.searchTerm.toLowerCase()
      filtered = filtered.filter(employee => {
        return (
          employee.employeeName?.toLowerCase().includes(search) ||
          employee.fatherHusbandName?.toLowerCase().includes(search) ||
          employee.phoneNo?.toLowerCase().includes(search) ||
          employee.category?.toLowerCase().includes(search) ||
          employee.status?.toLowerCase().includes(search) ||
          employee.project?.toLowerCase().includes(search) ||
          employee.site?.toLowerCase().includes(search)
        )
      })
    }
    
    // Apply column filters
    Object.keys(props.currentFilters).forEach(column => {
      if (props.currentFilters[column].length > 0) {
        filtered = filtered.filter(employee => {
          if (column === 'dateOfJoining') {
            const formattedDate = formatDate(employee[column])
            return props.currentFilters[column].includes(formattedDate)
          }
          return props.currentFilters[column].includes(employee[column])
        })
      }
    })
  }
  
  // Apply export-specific filters
  const filters = exportOptions.value.filters
  
  if (filters.dateFrom) {
    filtered = filtered.filter(emp => new Date(emp.dateOfJoining) >= new Date(filters.dateFrom))
  }
  if (filters.dateTo) {
    filtered = filtered.filter(emp => new Date(emp.dateOfJoining) <= new Date(filters.dateTo))
  }
  if (filters.status) {
    filtered = filtered.filter(emp => emp.status === filters.status)
  }
  if (filters.category) {
    filtered = filtered.filter(emp => emp.category === filters.category)
  }
  if (filters.project) {
    filtered = filtered.filter(emp => emp.project === filters.project)
  }
  if (filters.site) {
    filtered = filtered.filter(emp => emp.site === filters.site)
  }
  if (filters.phoneValidation) {
    if (filters.phoneValidation === 'valid') {
      filtered = filtered.filter(emp => isValidPhoneNumber(emp.phoneNo))
    } else if (filters.phoneValidation === 'invalid') {
      filtered = filtered.filter(emp => !isValidPhoneNumber(emp.phoneNo))
    }
  }
  
  return filtered.length
})

const formatDate = (date) => {
  if (!date) return ''
  const dateObj = new Date(date)
  const day = dateObj.getDate().toString().padStart(2, '0')
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
  const year = dateObj.getFullYear()
  return `${day}-${month}-${year}`
}

const selectAllColumns = () => {
  exportOptions.value.selectedColumns = availableColumns.map(col => col.key)
}

const deselectAllColumns = () => {
  exportOptions.value.selectedColumns = []
}

const handleExport = async () => {
  if (isExporting.value || exportOptions.value.selectedColumns.length === 0) return
  
  try {
    isExporting.value = true
    
    const api = useApiWithAuth()
    
    // Prepare export parameters
    const exportParams = {
      format: exportOptions.value.format,
      filters: exportOptions.value.filters,
      selectedColumns: exportOptions.value.selectedColumns,
      respectCurrentFilters: exportOptions.value.respectCurrentFilters,
      includeSummary: exportOptions.value.includeSummary,
      currentFilters: exportOptions.value.respectCurrentFilters ? props.currentFilters : {},
      searchTerm: exportOptions.value.respectCurrentFilters ? props.searchTerm : ''
    }
    
    // Make the export request
    const response = await api.fetchWithAuth('/api/master-roll/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(exportParams)
    })
    
    // Create blob and download
    const contentType = getContentType(exportOptions.value.format)
    const blob = new Blob([response], { type: contentType })
    const url = window.URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', getFileName(exportOptions.value.format))
    document.body.appendChild(link)
    link.click()
    
    // Cleanup
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)
    
    emit('export-complete', { success: true })
    emit('close')
    
  } catch (error) {
    console.error('Export error:', error)
    emit('export-complete', { success: false, error: error.message })
  } finally {
    isExporting.value = false
  }
}

const getContentType = (format) => {
  switch (format) {
    case 'excel':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    case 'csv':
      return 'text/csv'
    case 'pdf':
      return 'application/pdf'
    default:
      return 'application/octet-stream'
  }
}

const getFileName = (format) => {
  const date = new Date().toISOString().split('T')[0]
  const extension = format === 'excel' ? 'xlsx' : format
  return `master_roll_export_${date}.${extension}`
}

// Watch for format changes to disable summary for CSV
watch(() => exportOptions.value.format, (newFormat) => {
  if (newFormat === 'csv') {
    exportOptions.value.includeSummary = false
  }
})
</script>
