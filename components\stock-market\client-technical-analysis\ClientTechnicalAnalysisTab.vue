<template>
  <div class="tab-panel">
    <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6">
      <div class="flex items-center justify-between mb-4">
        <h4 class="text-lg font-semibold text-gray-900">📊 200-Day Technical Analysis (CLIENT)</h4>
        <button
          @click="fetchClientTechnicalAnalysis"
          :disabled="clientTechnicalLoading"
          class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-blue-700 hover:to-indigo-700 disabled:opacity-50 transition-all duration-200 shadow-sm"
        >
          <span v-if="clientTechnicalLoading">📈 Analyzing...</span>
          <span v-else>📊 Client Analysis</span>
        </button>
      </div>

      <!-- Enhanced Technical Analysis Loading State -->
      <div v-if="clientTechnicalLoading">
        <EnhancedProgressDisplay
          title="📊 AI Technical Analysis (CLIENT-SIDE)"
          :progress="enhancedProgress.overallProgress.value"
          :current-message="enhancedProgress.currentMessage.value"
          :elapsed-time="enhancedProgress.elapsedTime.value"
          :provider="aiConfig.provider || 'unknown'"
          :model="aiConfig.model || 'unknown'"
          analysis-type="technical"
          :current-stage="enhancedProgress.currentStage.value"
          :stages="enhancedProgress.stages.value"
          :estimated-remaining="enhancedProgress.estimatedRemaining.value"
          :show-advanced-options="true"
          :allow-cancel="true"
          @cancel="cancelAnalysis"
          @background-toggle="handleBackgroundToggle"
        />
      </div>

      <!-- Technical Analysis Error State -->
      <div v-else-if="clientTechnicalError" class="bg-red-50 border border-red-200 rounded-lg p-4">
        <div class="text-red-600 mb-2">⚠️ Client Technical Analysis Failed</div>
        <p class="text-red-700 mb-3">{{ clientTechnicalError }}</p>
        <button
          @click="fetchClientTechnicalAnalysis"
          class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
        >
          🔄 Retry Analysis
        </button>
      </div>

      <!-- Technical Analysis Results - 100% IDENTICAL TO SERVER VERSION -->
      <div v-else-if="clientTechnicalAnalysis" class="space-y-4">
        <!-- Technical Recommendation -->
        <div class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">📈 Technical Recommendation</h5>
          <div class="flex items-center space-x-3">
            <span v-if="clientTechnicalAnalysis.aiAnalysis?.technicalRecommendation" class="px-3 py-1 rounded-full text-sm font-medium"
                  :class="getTechnicalRecommendationClass(clientTechnicalAnalysis.aiAnalysis?.technicalRecommendation)">
              {{ clientTechnicalAnalysis.aiAnalysis?.technicalRecommendation }}
            </span>
            <span v-else class="text-gray-500 text-sm">No recommendation available</span>
            <span v-if="clientTechnicalAnalysis.aiAnalysis?.confidence" class="text-gray-600 text-sm">
              Confidence: {{ clientTechnicalAnalysis.aiAnalysis?.confidence }}
            </span>
          </div>
        </div>

        <!-- Technical Indicators - IDENTICAL TO SERVER VERSION -->
        <div class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-3">🔢 Key Technical Indicators</h5>
          <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
            <div>
              <span class="text-gray-600">200-day SMA:</span>
              <span class="font-medium ml-1">
                <span v-if="clientTechnicalAnalysis.technicalIndicators?.sma200">₹{{ clientTechnicalAnalysis.technicalIndicators.sma200.toFixed(2) }}</span>
                <span v-else class="text-gray-400">Not available</span>
              </span>
            </div>
            <div>
              <span class="text-gray-600">50-day SMA:</span>
              <span class="font-medium ml-1">
                <span v-if="clientTechnicalAnalysis.technicalIndicators?.sma50">₹{{ clientTechnicalAnalysis.technicalIndicators.sma50.toFixed(2) }}</span>
                <span v-else class="text-gray-400">Not available</span>
              </span>
            </div>
            <div>
              <span class="text-gray-600">RSI (14):</span>
              <span class="font-medium ml-1" :class="getRSIClass(clientTechnicalAnalysis.technicalIndicators?.rsi)">
                <span v-if="clientTechnicalAnalysis.technicalIndicators?.rsi">{{ clientTechnicalAnalysis.technicalIndicators.rsi.toFixed(2) }}</span>
                <span v-else class="text-gray-400">Not available</span>
              </span>
            </div>
            <div>
              <span class="text-gray-600">MACD:</span>
              <span class="font-medium ml-1">
                <span v-if="clientTechnicalAnalysis.technicalIndicators?.macd?.line">{{ clientTechnicalAnalysis.technicalIndicators.macd.line.toFixed(4) }}</span>
                <span v-else class="text-gray-400">Not available</span>
              </span>
            </div>
            <div>
              <span class="text-gray-600">ATR:</span>
              <span class="font-medium ml-1">
                <span v-if="clientTechnicalAnalysis.technicalIndicators?.atr">{{ clientTechnicalAnalysis.technicalIndicators.atr.toFixed(2) }}</span>
                <span v-else class="text-gray-400">Not available</span>
              </span>
            </div>
            <div>
              <span class="text-gray-600">Data Points:</span>
              <span class="font-medium ml-1">{{ clientTechnicalAnalysis.historicalDataPoints || 0 }}</span>
            </div>
          </div>
        </div>

        <!-- Trend Analysis - IDENTICAL TO SERVER VERSION -->
        <div v-if="clientTechnicalAnalysis.aiAnalysis?.trendAnalysis" class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">📈 200-Day Trend Analysis</h5>
          <div class="text-gray-700 text-sm" v-html="formatAnalysisText(clientTechnicalAnalysis.aiAnalysis.trendAnalysis)"></div>
        </div>

        <!-- Moving Average Analysis - IDENTICAL TO SERVER VERSION -->
        <div v-if="clientTechnicalAnalysis.aiAnalysis?.movingAverageAnalysis" class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">📊 Moving Average Analysis</h5>
          <div class="text-gray-700 text-sm" v-html="formatAnalysisText(clientTechnicalAnalysis.aiAnalysis.movingAverageAnalysis)"></div>
        </div>

        <!-- Support & Resistance - IDENTICAL TO SERVER VERSION -->
        <div v-if="clientTechnicalAnalysis.technicalIndicators?.supportResistance" class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">🎯 Support & Resistance Levels</h5>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="text-red-600 font-medium">Resistance:</span>
              <div class="mt-1">
                <span v-for="(level, index) in clientTechnicalAnalysis.technicalIndicators.supportResistance.resistance" :key="index"
                      class="inline-block bg-red-100 text-red-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                  ₹{{ level.toFixed(2) }}
                </span>
              </div>
            </div>
            <div>
              <span class="text-green-600 font-medium">Support:</span>
              <div class="mt-1">
                <span v-for="(level, index) in clientTechnicalAnalysis.technicalIndicators.supportResistance.support" :key="index"
                      class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                  ₹{{ level.toFixed(2) }}
                </span>
              </div>
            </div>
          </div>
        </div>

        <!-- Price Targets - IDENTICAL TO SERVER VERSION -->
        <div v-if="clientTechnicalAnalysis.aiAnalysis?.priceTargets" class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">🎯 Price Targets & Risk Management</h5>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div class="bg-blue-50 p-3 rounded">
              <span class="text-blue-600 font-medium">Short-term Target:</span>
              <div class="text-blue-800 font-semibold">{{ clientTechnicalAnalysis.aiAnalysis.priceTargets.shortTerm }}</div>
            </div>
            <div class="bg-green-50 p-3 rounded">
              <span class="text-green-600 font-medium">Medium-term Target:</span>
              <div class="text-green-800 font-semibold">{{ clientTechnicalAnalysis.aiAnalysis.priceTargets.mediumTerm }}</div>
            </div>
            <div class="bg-red-50 p-3 rounded">
              <span class="text-red-600 font-medium">Stop Loss:</span>
              <div class="text-red-800 font-semibold">{{ clientTechnicalAnalysis.aiAnalysis.priceTargets.stopLoss }}</div>
            </div>
          </div>
        </div>

        <!-- Trading Strategy - IDENTICAL TO SERVER VERSION -->
        <div v-if="clientTechnicalAnalysis.aiAnalysis?.tradingStrategy" class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-2">� Trading Strategy</h5>
          <div class="text-gray-700 text-sm" v-html="formatAnalysisText(clientTechnicalAnalysis.aiAnalysis.tradingStrategy)"></div>
        </div>

        <!-- Technical Charts - 100% IDENTICAL TO SERVER VERSION -->
        <div v-if="clientTechnicalAnalysis && clientTechnicalAnalysis.technicalIndicators" class="bg-white rounded-lg p-4">
          <h5 class="font-semibold text-gray-900 mb-4">📈 Technical Charts</h5>

          <!-- Chart Controls - IDENTICAL TO SERVER VERSION -->
          <div class="flex flex-wrap items-center justify-between gap-4 mb-4">
            <!-- Chart Type Selector -->
            <div class="flex flex-wrap gap-2">
              <button
                @click="activeChart = 'price'"
                :class="[
                  'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                  activeChart === 'price'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                � Price & SMA
              </button>
              <button
                @click="activeChart = 'candlestick'"
                :class="[
                  'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                  activeChart === 'candlestick'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                🕯️ Candlestick
              </button>
              <button
                @click="activeChart = 'rsi'"
                :class="[
                  'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                  activeChart === 'rsi'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                📈 RSI
              </button>
              <button
                @click="activeChart = 'macd'"
                :class="[
                  'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                  activeChart === 'macd'
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                ]"
              >
                📉 MACD
              </button>
            </div>

            <!-- Period Filter and Chart Controls - IDENTICAL TO SERVER VERSION -->
            <div class="flex items-center gap-2">
              <select
                v-model="chartPeriod"
                @change="updateChartPeriod"
                class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500"
              >
                <option value="30">30 Days</option>
                <option value="60">60 Days</option>
                <option value="90">90 Days</option>
                <option value="180">6 Months</option>
                <option value="365">1 Year</option>
                <option value="all">All Data</option>
              </select>
              <div class="flex items-center gap-1">
                <button
                  @click="zoomIn"
                  class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  title="Zoom In"
                >
                  🔍+
                </button>
                <button
                  @click="zoomOut"
                  class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                  title="Zoom Out"
                >
                  🔍-
                </button>
                <button
                  @click="resetChartZoom"
                  class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                  title="Reset Zoom"
                >
                  🔄 Reset
                </button>
              </div>
            </div>
          </div>

          <!-- Chart Container - IDENTICAL TO SERVER VERSION -->
          <div class="relative" style="height: 400px;">
            <canvas ref="clientTechnicalChartCanvas"></canvas>
          </div>

          <!-- Chart Info - IDENTICAL TO SERVER VERSION -->
          <div class="mt-3 text-xs text-gray-500 text-center">
            <span v-if="clientTechnicalAnalysis.historicalDataPoints">Data Points: {{ clientTechnicalAnalysis.historicalDataPoints }} | </span>
            <span v-if="clientTechnicalAnalysis.historicalDataPoints">Period: {{ getChartPeriodText() }} | </span>
            <span v-if="clientTechnicalAnalysis.analysisTimestamp">Last Updated: {{ formatTimestamp(clientTechnicalAnalysis.analysisTimestamp) }}</span>
            <span v-else class="text-gray-400">Chart data not available</span>
          </div>
        </div>
      </div>

      <!-- Initial State -->
      <div v-else class="bg-white rounded-lg p-4 text-center">
        <p class="text-gray-600 mb-3">Click the button above to get comprehensive 200-day technical analysis for {{ stock.symbol }} (CLIENT-SIDE)</p>
        <p class="text-gray-500 text-sm">Client-side AI will analyze moving averages, RSI, MACD, support/resistance levels, and provide trading recommendations</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick, onBeforeUnmount, watch } from 'vue'
import { useClientTechnicalAnalysis } from '~/composables/stock-market/client-technical-analysis/useClientTechnicalAnalysis'
import { useEnhancedProgress } from '~/composables/stock-market/shared/useEnhancedProgress'
import { useAIConfig } from '~/composables/ai/useAIConfig'
import EnhancedProgressDisplay from '~/components/stock-market/shared/EnhancedProgressDisplay.vue'
import {
  Chart,
  BarController,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineController,
  TimeScale,
  TimeSeriesScale
} from 'chart.js'
import { CandlestickController, OhlcController, CandlestickElement, OhlcElement } from 'chartjs-chart-financial'
import zoomPlugin from 'chartjs-plugin-zoom'
import 'chartjs-adapter-luxon'

// Register Chart.js components - IDENTICAL TO SERVER VERSION
Chart.register(
  BarController,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineController,
  TimeScale,
  TimeSeriesScale,
  CandlestickController,
  OhlcController,
  CandlestickElement,
  OhlcElement,
  zoomPlugin
)

// Props
interface Props {
  stock: any
}

const props = defineProps<Props>()

// Client Technical Analysis
const {
  loading: clientTechnicalLoading,
  error: clientTechnicalError,
  analysis: clientTechnicalAnalysis,
  progress: clientTechnicalProgress,
  statusMessage: clientTechnicalStatusMessage,
  performTechnicalAnalysis,
  reset
} = useClientTechnicalAnalysis()

// Enhanced Progress System
const enhancedProgress = useEnhancedProgress('technical')

// AI Configuration
const { aiConfig } = useAIConfig()

// Chart state - IDENTICAL TO SERVER VERSION
const activeChart = ref('price')
const clientTechnicalChartCanvas = ref<HTMLCanvasElement | null>(null)
const chartPeriod = ref('180') // Default to 6 months
let clientTechnicalChart: any = null

// Helper functions - IDENTICAL TO SERVER VERSION
const getTechnicalRecommendationClass = (recommendation: string) => {
  switch (recommendation?.toUpperCase()) {
    case 'BUY':
      return 'bg-green-100 text-green-800'
    case 'SELL':
      return 'bg-red-100 text-red-800'
    case 'HOLD':
      return 'bg-yellow-100 text-yellow-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getRSIClass = (rsi: number) => {
  if (!rsi) return ''
  if (rsi >= 70) return 'text-red-600' // Overbought
  if (rsi <= 30) return 'text-green-600' // Oversold
  return 'text-gray-700' // Neutral
}

const formatAnalysisText = (text: string) => {
  if (!text) return ''
  // Convert line breaks to HTML and preserve formatting
  return text.replace(/\n/g, '<br>').replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
}

const getChartPeriodText = () => {
  switch (chartPeriod.value) {
    case '30': return '30 Days'
    case '60': return '60 Days'
    case '90': return '90 Days'
    case '180': return '6 Months'
    case '365': return '1 Year'
    case 'all': return 'All Data'
    default: return '6 Months'
  }
}

const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

// Chart control functions - IDENTICAL TO SERVER VERSION
const updateChartPeriod = () => {
  if (clientTechnicalAnalysis.value) {
    nextTick(() => {
      initClientTechnicalChart()
    })
  }
}

const zoomIn = () => {
  if (clientTechnicalChart && clientTechnicalChart.zoom) {
    clientTechnicalChart.zoom(1.1)
  }
}

const zoomOut = () => {
  if (clientTechnicalChart && clientTechnicalChart.zoom) {
    clientTechnicalChart.zoom(0.9)
  }
}

const resetChartZoom = () => {
  if (clientTechnicalChart && clientTechnicalChart.resetZoom) {
    clientTechnicalChart.resetZoom()
  }
}

// Fetch client-side technical analysis
const fetchClientTechnicalAnalysis = async () => {
  console.log('📊 Starting client-side technical analysis for:', props.stock.symbol)

  // Start enhanced progress system
  enhancedProgress.start()
  enhancedProgress.autoUpdateEstimates(aiConfig.value?.provider || 'unknown', aiConfig.value?.model || 'unknown')

  try {
    await performTechnicalAnalysisWithProgress({
      symbol: props.stock.symbol,
      companyName: props.stock.meta?.companyName || props.stock.symbol,
      currentPrice: props.stock.lastPrice,
      change: props.stock.change,
      pChange: props.stock.pChange
    })
  } catch (err) {
    console.error('Client Technical Analysis Error:', err)
    enhancedProgress.reset()
  }
}

// Enhanced progress-aware technical analysis
const performTechnicalAnalysisWithProgress = async (stockData: any) => {
  // Stage 1: Initialize
  enhancedProgress.setStage('init', 0)
  await new Promise(resolve => setTimeout(resolve, 300))

  // Stage 2: Prepare data
  enhancedProgress.setStage('prepare', 0)
  enhancedProgress.setStageProgress(50)
  await new Promise(resolve => setTimeout(resolve, 200))
  enhancedProgress.setStageProgress(100)

  // Stage 3: Generate prompt
  enhancedProgress.setStage('prompt', 0)
  enhancedProgress.setStageProgress(100)

  // Stage 4: Send request
  enhancedProgress.setStage('send', 0)
  enhancedProgress.setStageProgress(100)

  // Stage 5: AI Processing (main work)
  enhancedProgress.setStage('process', 0)

  try {
    // Call the actual technical analysis
    await performTechnicalAnalysis(stockData)

    // Stage 6: Parse response
    enhancedProgress.setStage('parse', 0)
    enhancedProgress.setStageProgress(100)

    // Stage 7: Validate
    enhancedProgress.setStage('validate', 0)
    enhancedProgress.setStageProgress(100)

    // Stage 8: Finalize
    enhancedProgress.setStage('finalize', 0)
    enhancedProgress.setStageProgress(100)

    // Complete
    enhancedProgress.complete()

  } catch (error) {
    enhancedProgress.reset()
    throw error
  }
}

// Cancel analysis
const cancelAnalysis = () => {
  enhancedProgress.reset()
  console.log('Technical analysis cancellation requested')
}

// Handle background toggle
const handleBackgroundToggle = (enabled: boolean) => {
  console.log('Background processing:', enabled ? 'enabled' : 'disabled')
}

// Chart initialization - IDENTICAL TO SERVER VERSION
const initClientTechnicalChart = async () => {
  await nextTick()

  // Check if we have a canvas and sufficient historical data to render.
  if (!clientTechnicalChartCanvas.value || !clientTechnicalAnalysis.value?.historicalData || !Array.isArray(clientTechnicalAnalysis.value.historicalData) || clientTechnicalAnalysis.value.historicalData.length === 0) {
    return // Exit if no data or canvas. This is expected when data is not yet available.
  }

  // Destroy existing chart if it exists
  if (clientTechnicalChart) {
    clientTechnicalChart.destroy()
    clientTechnicalChart = null
  }

  // Filter data based on chart period - IDENTICAL TO SERVER VERSION
  let data = clientTechnicalAnalysis.value.historicalData
  if (chartPeriod.value !== 'all') {
    const periodDays = parseInt(chartPeriod.value)
    data = data.slice(-periodDays)
  }

  console.log(`📊 Preparing ${activeChart.value} chart with ${data.length} data points`)

  const ctx = clientTechnicalChartCanvas.value?.getContext('2d')
  if (!ctx) return

  // Prepare chart data based on active chart type
  let chartConfig: any = {}

  if (activeChart.value === 'price') {
    chartConfig = getPriceChartConfig(data)
  } else if (activeChart.value === 'candlestick') {
    console.log('📊 Initializing candlestick chart with', data.length, 'data points')
    chartConfig = getCandlestickChartConfig(data)
  } else if (activeChart.value === 'rsi') {
    chartConfig = getRSIChartConfig(data)
  } else if (activeChart.value === 'macd') {
    chartConfig = getMACDChartConfig(data)
  }

  try {
    console.log('📊 Chart config plugins:', chartConfig.options?.plugins)
    clientTechnicalChart = new Chart(ctx, chartConfig)
    console.log('📊 Client-side technical chart initialized:', activeChart.value, 'with', data.length, 'data points')
    console.log('📊 Chart zoom plugin available:', !!clientTechnicalChart.zoom)
  } catch (error) {
    console.error('❌ Error initializing client-side technical chart:', error)
    console.error('Chart config:', chartConfig)
    return
  }
}

// Price chart configuration
const getPriceChartConfig = (data: any[]) => {
  return {
    type: 'line',
    data: {
      labels: data.map(item => item.date),
      datasets: [
        {
          label: 'Price',
          data: data.map(item => item.close),
          borderColor: 'rgb(59, 130, 246)',
          backgroundColor: 'rgba(59, 130, 246, 0.1)',
          borderWidth: 2,
          fill: true,
          tension: 0.1
        },
        {
          label: 'SMA 20',
          data: data.map(item => item.sma20),
          borderColor: 'rgb(16, 185, 129)',
          borderWidth: 1.5,
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0
        },
        {
          label: 'SMA 50',
          data: data.map(item => item.sma50),
          borderColor: 'rgb(245, 158, 11)',
          borderWidth: 1.5,
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0
        },
        {
          label: 'SMA 200',
          data: data.map(item => item.sma200),
          borderColor: 'rgb(220, 38, 38)',
          borderWidth: 1.5,
          borderDash: [5, 5],
          fill: false,
          pointRadius: 0
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      interaction: {
        mode: 'index',
        intersect: false
      },
      plugins: {
        legend: {
          position: 'top',
          labels: {
            boxWidth: 12,
            usePointStyle: true
          }
        },
        tooltip: {
          enabled: true
        },
        zoom: {
          pan: {
            enabled: true,
            mode: 'x'
          },
          zoom: {
            wheel: {
              enabled: true
            },
            pinch: {
              enabled: true
            },
            mode: 'x'
          }
        }
      },
      scales: {
        x: {
          type: 'category',
          ticks: {
            maxTicksLimit: 10
          }
        },
        y: {
          title: {
            display: true,
            text: 'Price (₹)'
          }
        }
      }
    }
  }
}

// Candlestick chart configuration - IDENTICAL TO SERVER VERSION
const getCandlestickChartConfig = (data: any[]) => {
  console.log('📊 Configuring candlestick chart with data:', data.length, 'points')

  // Optimize data processing for better performance - IDENTICAL TO SERVER
  const candlestickData = data.map(d => {
    const candleData = {
      x: new Date(d.date).valueOf(),
      o: parseFloat(d.open) || 0,
      h: parseFloat(d.high) || 0,
      l: parseFloat(d.low) || 0,
      c: parseFloat(d.close) || 0
    }
    return candleData
  }).filter(d => d.o > 0 && d.h > 0 && d.l > 0 && d.c > 0) // Filter out invalid data

  const sma50Data = data.map(d => ({ x: new Date(d.date).valueOf(), y: d.sma50 })).filter(d => d.y !== null);
  const sma200Data = data.map(d => ({ x: new Date(d.date).valueOf(), y: d.sma200 })).filter(d => d.y !== null);

  console.log('📊 Candlestick data processed:', candlestickData.length, 'valid candles')

  const datasets: any[] = [{
    label: 'Candlestick',
    data: candlestickData,
    color: {
        up: '#10b981',
        down: '#ef4444',
        unchanged: '#6b7280',
    },
    borderColor: {
        up: '#10b981',
        down: '#ef4444',
        unchanged: '#6b7280',
    }
  }];

  if (sma50Data.length > 0) {
    datasets.push({
      label: '50-day SMA',
      data: sma50Data,
      type: 'line',
      borderColor: 'rgba(234, 179, 8, 1)',
      backgroundColor: 'rgba(234, 179, 8, 0.2)',
      borderWidth: 2,
      pointRadius: 0,
      pointHoverRadius: 0,
      tension: 0.4,
      spanGaps: true
    } as any);
  }

  if (sma200Data.length > 0) {
    datasets.push({
      label: '200-day SMA',
      data: sma200Data,
      type: 'line',
      borderColor: 'rgba(192, 38, 211, 1)',
      backgroundColor: 'rgba(192, 38, 211, 0.2)',
      borderWidth: 2,
      pointRadius: 0,
      pointHoverRadius: 0,
      tension: 0.4,
      spanGaps: true
    } as any);
  }

  console.log('📊 Final candlestick datasets:', datasets)

  return {
    type: 'candlestick',
    data: {
      datasets: datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      resizeDelay: 0,
      // Performance optimizations
      animation: {
        duration: 0 // Disable animations for better performance during interactions
      },
      interaction: {
        mode: 'nearest',
        intersect: false,
        includeInvisible: false
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day'
          },
          grid: {
            display: false
          }
        },
        y: {
          grid: {
            color: 'rgba(200, 200, 200, 0.1)'
          },
          ticks: {
            callback: function(value: any) {
              return '₹' + value.toFixed(2);
            }
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          enabled: true,
          mode: 'index',
          intersect: false,
          animation: false, // Disable tooltip animations for better performance
          callbacks: {
            title: function(context: any) {
              return new Date(context[0].parsed.x).toLocaleDateString();
            },
            label: function(context: any) {
              const data = context.raw;
              if (data.o !== undefined) {
                return [
                  `Open: ₹${data.o.toFixed(2)}`,
                  `High: ₹${data.h.toFixed(2)}`,
                  `Low: ₹${data.l.toFixed(2)}`,
                  `Close: ₹${data.c.toFixed(2)}`
                ];
              }
              return `${context.dataset.label}: ₹${context.parsed.y.toFixed(2)}`;
            }
          }
        },
        zoom: {
          pan: {
            enabled: true,
            mode: 'x'
          },
          zoom: {
            wheel: {
              enabled: true
            },
            pinch: {
              enabled: true
            },
            mode: 'x'
          }
        }
      }
    }
  };
}

// RSI chart configuration
const getRSIChartConfig = (data: any[]) => {
  return {
    type: 'line',
    data: {
      labels: data.map(item => item.date),
      datasets: [{
        label: 'RSI',
        data: data.map(item => item.rsi),
        borderColor: 'rgb(147, 51, 234)',
        backgroundColor: 'rgba(147, 51, 234, 0.1)',
        borderWidth: 2,
        fill: false
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        },
        zoom: {
          pan: {
            enabled: true,
            mode: 'x'
          },
          zoom: {
            wheel: {
              enabled: true
            },
            pinch: {
              enabled: true
            },
            mode: 'x'
          }
        }
      },
      scales: {
        x: {
          type: 'category'
        },
        y: {
          min: 0,
          max: 100,
          title: {
            display: true,
            text: 'RSI'
          }
        }
      }
    }
  }
}

// MACD chart configuration
const getMACDChartConfig = (data: any[]) => {
  return {
    type: 'line',
    data: {
      labels: data.map(item => item.date),
      datasets: [
        {
          label: 'MACD',
          data: data.map(item => item.macd),
          borderColor: 'rgb(59, 130, 246)',
          borderWidth: 2,
          fill: false
        },
        {
          label: 'Signal',
          data: data.map(item => item.macdSignal),
          borderColor: 'rgb(239, 68, 68)',
          borderWidth: 2,
          fill: false
        },
        {
          label: 'Histogram',
          data: data.map(item => item.macdHistogram),
          backgroundColor: data.map(item => item.macdHistogram >= 0 ? 'rgba(34, 197, 94, 0.5)' : 'rgba(239, 68, 68, 0.5)'),
          borderColor: data.map(item => item.macdHistogram >= 0 ? 'rgb(34, 197, 94)' : 'rgb(239, 68, 68)'),
          type: 'bar'
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top'
        },
        zoom: {
          pan: {
            enabled: true,
            mode: 'x'
          },
          zoom: {
            wheel: {
              enabled: true
            },
            pinch: {
              enabled: true
            },
            mode: 'x'
          }
        }
      },
      scales: {
        x: {
          type: 'category'
        },
        y: {
          title: {
            display: true,
            text: 'MACD'
          }
        }
      }
    }
  }
}

// Watch for analysis data changes
watch(() => clientTechnicalAnalysis.value, (newData) => {
  if (newData) {
    nextTick(() => {
      initClientTechnicalChart()
    })
  }
})

// Watch for chart type changes
watch(activeChart, () => {
  if (clientTechnicalAnalysis.value) {
    nextTick(() => {
      initClientTechnicalChart()
    })
  }
})



// Cleanup chart on unmount
onBeforeUnmount(() => {
  if (clientTechnicalChart) {
    clientTechnicalChart.destroy()
    clientTechnicalChart = null
  }
})
</script>

<style scoped>
.tab-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.chart-container {
  width: 100%;
}
</style>
