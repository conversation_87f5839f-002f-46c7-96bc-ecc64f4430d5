<template>
  <div class="max-w-10xl mx-auto py-4 sm:py-6 px-2 sm:px-6 lg:px-8">
    <div class="px-0 relative">
      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6 mb-6">
        <!-- Bill <PERSON> (8 columns on desktop, full width on mobile) -->
        <div class="bg-white p-3 md:p-4 rounded-lg shadow col-span-1 md:col-span-8">
          <div class="flex justify-between">
            <h2 class="text-xl font-semibold mb-4">Create New Invoice</h2>
            <!-- Settings Gear Icon at top right -->
            <button type="button" @click="showColumnSelectionModal = true"
              class="z-10 p-2 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                  d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z"
                  clip-rule="evenodd" />
              </svg>
            </button>
          </div>
          <form @submit.prevent="submitBillForm">
            <!-- Main Form Grid with optimized sections -->
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-4">

              <!-- Bill Information Section (25% width - 3 columns) -->
              <div class="lg:col-span-3 bg-gray-50 p-3 rounded-lg border">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 border-b border-gray-200 pb-1">Bill Information</h3>
                <div class="space-y-3">
                  <!-- Bill Type -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Bill Type</label>
                    <select v-model="billForm.type"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required>
                      <option value="">Select Bill Type</option>
                      <option value="SALES">SALES</option>
                      <option value="PURCHASE">PURCHASE</option>
                      <option value="DEBIT NOTE">DEBIT NOTE</option>
                      <option value="CREDIT NOTE">CREDIT NOTE</option>
                    </select>
                  </div>

                  <!-- Bill Number -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Bill Number</label>
                    <input v-model="billForm.bno" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required />
                  </div>

                  <!-- Bill Date -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Bill Date</label>
                    <input v-model="billForm.bdate" type="date"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required />
                  </div>
                </div>
              </div>

              <!-- Party Details Section (45% width - 5 columns) -->
              <div class="lg:col-span-5 bg-blue-50 p-3 rounded-lg border">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 border-b border-gray-200 pb-1">Party Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <!-- Party Name -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Party Name</label>
                    <input v-model="billForm.partyName" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required list="partyList" @change="handlePartySelection" @blur="openOrderDispatchModal" @keydown.alt.67.exact="showPartyModal = true"
                      @keydown.ctrl.enter="editSelectedParty" @keydown="handlePartyNameKeydown" />
                    <datalist id="partyList">
                      <option v-for="party in inventoryData?.parties || []" :key="party._id" :value="party.supply"></option>
                      <option value="Create New Party"></option>
                    </datalist>
                  </div>

                  <!-- Party Address -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Party Address</label>
                    <input v-model="billForm.partyAddress" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- Party GSTIN -->
                  <div class="md:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Party GSTIN</label>
                    <input v-model="billForm.partyGstin" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>
                </div>
              </div>

              <!-- Amount Details Section (30% width - 4 columns) -->
              <div class="lg:col-span-4 bg-green-50 p-3 rounded-lg border">
                <h3 class="text-sm font-semibold text-gray-700 mb-3 border-b border-gray-200 pb-1">Amount Details</h3>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                  <!-- Gross Total -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Gross Total</label>
                    <input v-model.number="billForm.gtot" type="number" step="0.01" min="0"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required />
                  </div>

                  <!-- Net Total -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Net Total</label>
                    <input v-model.number="billForm.ntot" type="number" step="0.01"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      readonly />
                  </div>
                </div>
              </div>
            </div>

            <!-- Additional Fields Section -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-4">

              <!-- Payment Mode -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Mode</label>
                <select v-model="billForm.paymentMode"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                  <option value="">Select Payment Mode</option>
                  <option value="CASH">CASH</option>
                  <option value="CHEQUE">CHEQUE</option>
                  <option value="ONLINE">ONLINE</option>
                  <option value="CREDIT">CREDIT</option>
                </select>
              </div>

              <!-- Payment Reference -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Payment Reference</label>
                <input v-model="billForm.paymentRef" type="text"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              </div>

              <!-- Discount -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Discount</label>
                <input v-model.number="billForm.disc" type="number" step="0.01" min="0"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              </div>

              <!-- Round Off -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Round Off</label>
                <input v-model.number="billForm.rof" type="number" step="0.01"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              </div>

              <!-- CGST -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">CGST</label>
                <input v-model.number="billForm.cgst" type="number" step="0.01" min="0"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              </div>

              <!-- SGST -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">SGST</label>
                <input v-model.number="billForm.sgst" type="number" step="0.01" min="0"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              </div>

              <!-- IGST -->
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">IGST</label>
                <input v-model.number="billForm.igst" type="number" step="0.01" min="0"
                  class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
              </div>
            </div>

            <!-- Stock Items Section (Full width) -->
            <div class="bg-white p-4 rounded-lg shadow mt-6">
              <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 sm:mb-2 gap-2 sm:gap-0">
                <h3 class="text-lg font-medium">Stock Items</h3>
                <div class="flex items-center gap-2 w-full sm:w-auto">
                  <button type="button" @click="addStockItem"
                    class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center gap-1 w-full sm:w-auto justify-center sm:justify-start">
                    <PlusCircleIcon class="h-5 w-5" /> Add New Item
                  </button>
                </div>
              </div>


              <!-- Stock Items Table -->
              <div class="overflow-x-auto -mx-3 md:mx-0">
                <table class="min-w-full divide-y divide-gray-200" style="table-layout: fixed; width: 100%;">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 180px; min-width: 150px;">Item</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 60px;">Rate</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 70px; min-width: 50px;">Qty</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 50px;">UOM</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 60px;">GST (%)
                      </th>
                      <th scope="col" v-if="columnVisibility.mrp"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">MRP
                      </th>
                      <th scope="col" v-if="columnVisibility.expiryDate"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 100px; min-width: 100px;">Expiry Date
                      </th>
                      <th scope="col" v-if="columnVisibility.cgst"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">CGST</th>
                      <th scope="col" v-if="columnVisibility.sgst"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">SGST</th>
                      <th scope="col" v-if="columnVisibility.igst"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">IGST</th>
                      <th scope="col" v-if="columnVisibility.disc"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">Disc (%)
                      </th>
                      <th scope="col" v-if="columnVisibility.project"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 100px; min-width: 100px;">Project
                      </th>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 100px; min-width: 100px;">Total</th>
                      <th scope极客时间="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-if="billForm.stockItems.length === 0">
                      <td colspan="14" class="px-3 py-3 text-center text-sm text-gray-500">No items added</td>
                    </tr>
                    <tr v-for="(item, index) in billForm.stockItems" :key="index" class="hover:bg-gray-50">
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.item" type="text" list="stockItemList"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          required @keydown.alt.67.exact="showStockItemModal = true" @keydown="handleStockItemKeydown"
                          @change="handleStockItemSelection(index)" />
                        <datalist id="stockItemList">
                          <option v-for="stock in inventoryData?.stocks || []" :key="stock._id" :value="stock.item">
                            {{ `${stock.item} | PN: ${stock.pno || 'N/A'} | Batch: ${stock.batch || 'N/A'} | HSN:
                            ${stock.hsn} | Qty: ${stock.qty} ${stock.uom} (${stock.qty <= 5 ? 'Low' : stock.qty <= 20
                ? 'Medium' : 'High'})` }} </option>
                        </datalist>
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.rate" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" required />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.qty" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" @blur="showNarrationModal(index)"
                          @keydown.enter.prevent="addStockItem" required />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.uom" type="text"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.grate" type="number" min="0" max="100" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" />
                      </td>
                      <td v-if="columnVisibility.mrp" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.mrp" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td v-if="columnVisibility.expiryDate" class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.expiryDate" type="date"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td v-if="columnVisibility.cgst" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.cgst" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)"
                          :disabled="Firm_state?.toLowerCase()?.trim() !== inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state?.toLowerCase()?.trim()" />
                      </td>
                      <td v-if="columnVisibility.sgst" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.sgst" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)"
                          :disabled="Firm_state?.toLowerCase()?.trim() !== inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state?.toLowerCase()?.trim()" />
                      </td>
                      <td v-if="columnVisibility.igst" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.igst" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)"
                          :disabled="Firm_state?.toLowerCase()?.trim() === inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state?.toLowerCase()?.trim()" />
                      </td>
                      <td v-if="columnVisibility.disc" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.disc" type="number" min="0" max="100" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" @keydown.enter.prevent="addStockItem" />
                      </td>
                      <td v-if="columnVisibility.project" class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.project" type="text"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                        ₹{{ item.total.toFixed(2) }}
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <button type="button" @click="removeStockItem(index)"
                          class="text-red-600 hover:text-red-900 focus:outline-none">
                          <TrashIcon class="h-4 w-4 sm:h-5 sm:w-5" />
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Form Buttons -->
            <div class="mt-6 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
              <!-- Show these buttons when invoice is not yet submitted -->
              <template v-if="!invoiceSubmitted">
                <button type="button" @click="resetBillForm"
                  class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                  <XCircleIcon class="h-5 w-5" />
                  Reset
                </button>
                <button type="submit"
                  class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                  <DocumentArrowDownIcon class="h-5 w-5" />
                  Save Invoice
                </button>
              </template>

              <!-- Show these buttons after successful submission -->
              <template v-else>
                <button type="button" @click="printInvoice"
                  class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                  <PrinterIcon class="h-5 w-5" />
                  Print Invoice
                </button>
                <button type="button" @click="createNewInvoice"
                  class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                  <DocumentPlusIcon class="h-5 w-5" />
                  Create New Invoice
                </button>
              </template>
            </div>
          </form>
          <OtherChargesModal v-model:show="showOtherChargesModal" :firm-state="Firm_state"
            :party-state="inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state || ''"
            @add-charge="addOtherCharge" :edit-charge="currentCharge"
            :existing-charges="getAllExistingCharges()" />
        </div>
        <!-- Charges Table (4 columns on desktop, full width on mobile) -->
        <div class="bg-white p-3 md:p-4 rounded-lg shadow col-span-1 md:col-span-4">
          <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 sm:mb-2 gap-2 sm:gap-0">
            <h3 class="text-lg font-medium">Other Charges</h3>
            <button type="button" @click="showOtherChargesModal = true"
              class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center gap-1 w-full sm:w-auto justify-center sm:justify-start">
              <PlusCircleIcon class="h-5 w-5" /> Add Other Charges
            </button>
          </div>

          <!-- Other Charges Table -->
          <div class="overflow-x-auto -mx-3 md:mx-0">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Description</th>
                  <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount
                  </th>
                  <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GST</th>
                  <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total
                  </th>
                  <th scope="col"
                    class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-if="billForm.oth_chg.length === 0">
                  <td colspan="5" class="px-3 py-3 text-center text-sm text-gray-500">No charges added</td>
                </tr>
                <tr v-for="(charge, index) in billForm.oth_chg" :key="index" class="hover:bg-gray-50">
                  <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">{{ charge.description }}</td>
                  <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">₹{{ charge.oth_amt.toFixed(2) }}
                  </td>
                  <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                    <span v-if="charge.oth_igst > 0">IGST: ₹{{ charge.oth_igst.toFixed(2) }}</span>
                    <span v-else>CGST: ₹{{ charge.oth_cgst.toFixed(2) }} + SGST: ₹{{ charge.oth_sgst.toFixed(2)
                      }}</span>
                  </td>
                  <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">₹{{ charge.oth_tot.toFixed(2) }}
                  </td>
                  <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                    <div class="flex space-x-2">
                      <button type="button" @click="editOtherCharge(index)"
                        class="text-blue-600 hover:text-blue-900 focus:outline-none">
                        <PencilSquareIcon class="h-4 w-4 sm:h-5 sm:w-5" />
                      </button>
                      <button type="button" @click="removeOtherCharge(index)"
                        class="text-red-600 hover:text-red-900 focus:outline-none">
                        <TrashIcon class="h-4 w-4 sm:h-5 sm:w-5" />
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  <PartyModal v-model:show="showPartyModal" :indian-states="indianStates" :party-edit="partyToEdit"
    @submit-party="submitPartyForm" />
  <StockItemModal v-model:show="showStockItemModal" :inventory-data="inventoryData" :edit-item="editStockItem"
    @submit-stock-item="submitStockItemForm" />
  <StockRegModal v-model:show="showStockRegModal" :stock-reg="inventoryData?.stockReg || []"
    :party-name="billForm.partyName" :item-name="selectedStockItem" @close="showStockRegModal = false" />

  <NarrationModal v-model:show="isNarrationModalVisible" @submit="updateItemNarration"
    @close="showNarrationModal = false" />
  <ColumnSelectionModal v-model:show="showColumnSelectionModal" v-model:columnVisibility="columnVisibility" />
  <OrderDispatchModal v-model:show="showOrderDispatchModal" :order-details="orderDetails"
    @submit-order-details="submitOrderDetails" />
  <!-- Toast notifications are handled globally -->
</template>



<script setup>
import { ref, onMounted, watch } from 'vue';
import { useRouter } from '#app';
import useToast from '~/composables/ui/useToast';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import OtherChargesModal from '~/components/inventory/OtherChargesModal.vue';
import PartyModal from '~/components/inventory/PartyModal.vue';
import StockItemModal from '~/components/inventory/StockItemModal.vue';
import StockRegModal from '~/components/inventory/StockRegModal.vue';
import ColumnSelectionModal from '~/components/inventory/ColumnSelectionModal.vue';
import OrderDispatchModal from '~/components/inventory/OrderDispatchModal.vue';
import useLocalStorage from '~/composables/utils/useLocalStorage';
import {
  PlusCircleIcon,
  DocumentArrowDownIcon,
  PencilSquareIcon,
  TrashIcon,
  XCircleIcon,
  PrinterIcon,
  DocumentPlusIcon
} from '@heroicons/vue/24/solid'
import NarrationModal from '~/components/inventory/NarrationModal.vue'

const router = useRouter();

const { toast, success, info } = useToast();

const inventoryData = ref(null);
const error = ref(null);
const showPartyModal = ref(false);
const showStockItemModal = ref(false);
const showStockRegModal = ref(false);
const isNarrationModalVisible = ref(false);
const showColumnSelectionModal = ref(false);
const showOrderDispatchModal = ref(false);
const invoiceSubmitted = ref(false);
const submittedInvoiceData = ref(null);
const orderDetails = ref({
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: ''
});
const columnVisibility = ref({
  project: true,
  disc: true,
  cgst: true,
  sgst: true,
  igst: true,
  mrp: true,
  expiryDate: true
});

// Initialize the localStorage composable
const localStorage = useLocalStorage();

// Function to save column visibility settings to IndexedDB
const saveColumnSettings = () => {
  if (process.client) {
    try {
      localStorage.setItem('inventoryColumnSettings', JSON.stringify(columnVisibility.value));
    } catch (error) {
      console.error('Error saving column settings:', error);
    }
  }
};

// Function to load column visibility settings from IndexedDB
const loadColumnSettings = () => {
  if (process.client) {
    try {
      const savedSettings = localStorage.getItem('inventoryColumnSettings');
      if (savedSettings) {
        const parsedSettings = JSON.parse(savedSettings);
        // Merge saved settings with default settings to ensure all properties exist
        columnVisibility.value = {
          ...columnVisibility.value,
          ...parsedSettings
        };
      }
    } catch (error) {
      console.error('Error loading column settings:', error);
    }
  }
};
const currentItemIndex = ref(-1);

const showNarrationModal = (index) => {
  currentItemIndex.value = index;
  isNarrationModalVisible.value = true;
};

const updateItemNarration = (narration) => {
  if (narration && currentItemIndex.value >= 0) {
    billForm.value.stockItems[currentItemIndex.value].item_narration = narration;
  }
};
const partyToEdit = ref(null);
const selectedStockItem = ref('');
const currentCharge = ref(null);
const chargeIndex = ref(-1);

// Variables to store userId and firm details
const userId = ref('');
const firmName = ref('');
const firmGst = ref('');
const Firm_state = ref('');
const Firm_name = ref('');
const Firm_address = ref('');
const Firm_gstin = ref('');

// Indian states data with codes
const indianStates = [
  { code: 1, name: 'Jammu & Kashmir' },
  { code: 2, name: 'Himachal Pradesh' },
  { code: 3, name: 'Punjab' },
  { code: 4, name: 'Chandigarh' },
  { code: 5, name: 'Uttarakhand' },
  { code: 6, name: 'Haryana' },
  { code: 7, name: 'Delhi' },
  { code: 8, name: 'Rajasthan' },
  { code: 9, name: 'Uttar Pradesh' },
  { code: 10, name: 'Bihar' },
  { code: 11, name: 'Sikkim' },
  { code: 12, name: 'Arunachal Pradesh' },
  { code: 13, name: 'Nagaland' },
  { code: 14, name: 'Manipur' },
  { code: 15, name: 'Mizoram' },
  { code: 16, name: 'Tripura' },
  { code: 17, name: 'Meghalaya' },
  { code: 18, name: 'Assam' },
  { code: 19, name: 'West Bengal' },
  { code: 20, name: 'Jharkhand' },
  { code: 21, name: 'Odisha' },
  { code: 22, name: 'Chhattisgarh' },
  { code: 23, name: 'Madhya Pradesh' },
  { code: 24, name: 'Gujarat' },
  { code: 25, name: 'Daman & Diu' },
  { code: 26, name: 'Dadra & Nagar Haveli' },
  { code: 27, name: 'Maharashtra' },
  { code: 28, name: 'Andra Pradesh (Old)' },
  { code: 29, name: 'Karnataka' },
  { code: 30, name: 'Goa' },
  { code: 31, name: 'Lakshadweep' },
  { code: 32, name: 'Kerala' },
  { code: 33, name: 'Tamil Nadu' },
  { code: 34, name: 'Puducherry' },
  { code: 35, name: 'Andaman & Nicobar Islands' },
  { code: 36, name: 'Telangana' },
  { code: 37, name: 'Andhra Pradesh' },
  { code: 38, name: 'Ladakh' }
];

// New variable to store party data from modal
const newParty = ref(null);

// Function to fetch inventory data
const fetchInventoryData = async () => {
  error.value = null;

  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/inventory');
    inventoryData.value = response;
    userId.value = response.userId;
    firmName.value = response.firmName;
    firmGst.value = response.gstNo;
    Firm_state.value = response.Firm_state;
    Firm_name.value = response.firmName;
    Firm_address.value = response.firmAddress || '';
    Firm_gstin.value = response.gstNo || '';
  } catch (err) {
    console.error('Error fetching inventory data:', err);
    error.value = 'Failed to load inventory data. Please try again.';
  }
};

// Fetch data when component is mounted
// Set page title and meta tags
usePageTitle('Inventory Management', 'Manage your inventory, bills, and stock items');

onMounted(async () => {
  // Load column visibility settings from localStorage
  loadColumnSettings();
  await fetchInventoryData();

  // Redirect to the dashboard page after a short delay
  setTimeout(() => {
    router.push('/inventory/dashboard');
  }, 100);
});

// Watch for changes in column visibility and save to localStorage
watch(columnVisibility, () => {
  saveColumnSettings();
}, { deep: true });


// Bill form data
const billForm = ref({
  type: '',
  bno: '',
  bdate: new Date().toISOString().slice(0, 10),
  partyName: '',
  partyAddress: '',
  partyGstin: '',
  partyState: '',
  partyPin: '',
  paymentMode: '',
  paymentRef: '',
  gtot: 0,
  disc: 0,
  cgst: 0,
  sgst: 0,
  igst: 0,
  rof: 0,
  ntot: 0,
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: '',
  vehicleNo: '',
  consigneeName: '',
  consigneeGstin: '',
  consigneeAddress: '',
  consigneeState: '',
  consigneePin: '',
  oth_chg: [],
  stockItems: []
});



// Reset bill form
const resetBillForm = () => {
  billForm.value = {
    type: '',
    bno: '',
    bdate: new Date().toISOString().slice(0, 10),
    partyName: '',
    partyAddress: '',
    partyGstin: '',
    partyState: '',
    partyPin: '',
    paymentMode: '',
    paymentRef: '',
    gtot: 0,
    disc: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    rof: 0,
    ntot: 0,
    orderNo: '',
    orderDate: '',
    dispatchThrough: '',
    docketNo: '',
    vehicleNo: '',
    oth_chg: [],
    stockItems: []
  };
  // Reset the submitted state
  invoiceSubmitted.value = false;
  submittedInvoiceData.value = null;
};

// Function to print the current invoice
const printInvoice = () => {

  try {

    // Get party state from selected party for GST calculation - do this once for the entire invoice
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
    const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
    const firmState = Firm_state.value?.toLowerCase()?.trim() || '';
    const sameState = firmState === partyState;

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      info('Please allow pop-ups to print the invoice');
      return;
    }

    // Basic invoice styling
    const styles = `
      <style>
        @page {
          size: auto;
          margin: 0;
          margin-left: 0.25in;
          margin-right: 0.25in;
        }
        body {
          font-family: Arial, sans-serif;
          margin: 0;
          padding: 5px;
          color: #333;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .invoice-container {
          max-width: 800px;
          margin: 0 auto;
          padding: 8px;
          border: 1px solid #ddd;
          box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        .invoice-header {
          text-align: center;
          margin-bottom: 5px;
          padding-bottom: 3px;
          border-bottom: 2px solid #4f46e5;
        }
        .invoice-title {
          background-color: #4f46e5 !important;
          color: white !important;
          padding: 5px;
          text-align: center;
          margin-bottom: 5px;
          font-weight: bold;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .row {
          display: flex;
          margin-bottom: 5px;
        }
        .col {
          flex: 1;
          padding: 0 3px;
        }
        .party-details {
          background-color: #f9fafb;
          padding: 5px;
          border-radius: 3px;
          margin-bottom: 5px;
        }
        .party-details-row {
          display: flex;
          width: 100%;
        }
        .party-details-col {
          flex: 1;
          padding: 0 3px;
        }
        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 5px;
          font-size: 10px;
        }
        th {
          background-color: #4f46e5 !important;
          color: white !important;
          padding: 3px 4px;
          text-align: left;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        td {
          padding: 2px 4px;
          border: 1px solid #ddd;
        }
        tr:nth-child(even) {
          background-color: #f9fafb;
        }
        .text-right {
          text-align: right;
        }
        .totals-table {
          width: 350px;
          margin-left: auto;
        }
        .totals-table td {
          padding: 2px 4px;
        }
        .totals-table .total-row {
          font-weight: bold;
          background-color: #4f46e5 !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .total-amount {
          font-weight: bold !important;
          font-size: 12px;
          background-color: #f9fafb !important;
          -webkit-print-color-adjust: exact !important;
          print-color-adjust: exact !important;
        }
        .signature-section {
          display: flex;
          justify-content: space-between;
          margin-top: 15px;
        }
        .signature-box {
          width: 45%;
        }
        .signature-line {
          margin-top: 30px;
          border-top: 1px solid #ddd;
          padding-top: 3px;
        }
        .small-text {
          font-size: 9px;
          color: #6b7280;
        }
        h3 {
          margin-top: 5px;
          margin-bottom: 3px;
          font-size: 12px;
        }
        p {
          margin: 2px 0;
          font-size: 10px;
        }
        @media print {
          body {
            padding: 0;
            background-color: white;
          }
          .invoice-container {
            box-shadow: none;
            border: none;
          }
          .no-print {
            display: none;
          }
          @page {
            margin: 0;
          }
          html, body {
            height: 99%;
          }
        }
      </style>
    `;

    // Generate invoice HTML content
    let invoiceHtml = '';

    // Create header section
    invoiceHtml += `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invoice ${billForm.value.bno}</title>
        ${styles}
      </head>
      <body>
        <div class="invoice-container">
          <div class="no-print" style="text-align: right; margin-bottom: 10px;">
            <button onclick="window.print();" style="padding: 5px 10px; background-color: #4f46e5; color: white; border: none; border-radius: 3px; cursor: pointer;">
              Print Invoice
            </button>
          </div>

          <div class="invoice-header">
            <h1 style="margin: 0; color: #4f46e5;">${Firm_name.value || 'Company Name'}</h1>
            <p>${Firm_address.value || 'Company Address'}</p>
            <p>GSTIN: ${Firm_gstin.value || 'N/A'} | State: ${Firm_state.value || 'N/A'} ${getStateCode(Firm_state.value)}</p>
          </div>

          <div class="invoice-title">
            ${billForm.value.type} INVOICE
          </div>
    `;

    // Add invoice details section
    invoiceHtml += `
          <div class="row">
            <div class="col">
              <table>
                <tr>
                  <td><strong>Invoice No:</strong></td>
                  <td>${billForm.value.bno}</td>
                </tr>
                <tr>
                  <td><strong>Date:</strong></td>
                  <td>${new Date(billForm.value.bdate).toLocaleDateString()}</td>
                </tr>
              </table>
            </div>
            <div class="col">
              <table>
                <tr>
                  <td><strong>Payment Mode:</strong></td>
                  <td>${billForm.value.paymentMode || 'N/A'}</td>
                </tr>
                <tr>
                  <td><strong>Reference:</strong></td>
                  <td>${billForm.value.paymentRef || 'N/A'}</td>
                </tr>
              </table>
            </div>
          </div>

          <div class="party-details">
            <div class="party-details-row">
              <div class="party-details-col">
                <h3 style="margin-top: 0; color: #4f46e5;">Party Details</h3>
                <table>
                  <tr>
                    <td width="80"><strong>Name:</strong></td>
                    <td>${billForm.value.partyName}</td>
                  </tr>
                  <tr>
                    <td><strong>Address:</strong></td>
                    <td>${billForm.value.partyAddress || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>GSTIN:</strong></td>
                    <td>${billForm.value.partyGstin || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>State:</strong></td>
                    <td>${billForm.value.partyState || selectedParty?.state || 'N/A'} ${getStateCode(billForm.value.partyState || selectedParty?.state)}</td>
                  </tr>
                </table>
              </div>

              <div class="party-details-col">
                <h3 style="margin-top: 0; color: #4f46e5;">Consignee Details</h3>
                <table>
                  <tr>
                    <td width="80"><strong>Name:</strong></td>
                    <td>${billForm.value.consigneeName || billForm.value.partyName}</td>
                  </tr>
                  <tr>
                    <td><strong>Address:</strong></td>
                    <td>${billForm.value.consigneeAddress || billForm.value.partyAddress || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>GSTIN:</strong></td>
                    <td>${billForm.value.consigneeGstin || billForm.value.partyGstin || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>State:</strong></td>
                    <td>${billForm.value.consigneeState || billForm.value.partyState || selectedParty?.state || 'N/A'} ${getStateCode(billForm.value.consigneeState || billForm.value.partyState || selectedParty?.state)}</td>
                  </tr>
                </table>
              </div>

              <div class="party-details-col">
                <h3 style="margin-top: 0; color: #4f46e5;">Order & Dispatch</h3>
                <table>
                  <tr>
                    <td width="80"><strong>Order No:</strong></td>
                    <td>${billForm.value.orderNo || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>Order Date:</strong></td>
                    <td>${billForm.value.orderDate ? formatDate(billForm.value.orderDate) : 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>Dispatch:</strong></td>
                    <td>${billForm.value.dispatchThrough || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>Docket No:</strong></td>
                    <td>${billForm.value.docketNo || 'N/A'}</td>
                  </tr>
                  <tr>
                    <td><strong>Vehicle No:</strong></td>
                    <td>${billForm.value.vehicleNo || 'N/A'}</td>
                  </tr>
                </table>
              </div>
            </div>
          </div>
    `;

    // Add items table
    invoiceHtml += `
          <h3 style="color: #4f46e5;">Items</h3>
          <table>
            <thead>
              <tr>
                <th style="width: 30px;">Sl</th>
                <th>Item</th>
                <th>HSN</th>
                <th class="text-right">Qty</th>
                <th class="text-right">Rate</th>
                <th class="text-right">Disc Amt</th>
                <th class="text-right">GST%</th>
                <th class="text-right">Amount</th>
              </tr>
            </thead>
            <tbody>
    `;

    // Add each item row
    billForm.value.stockItems.forEach((item, index) => {
      // Calculate discount amount
      const discountAmount = parseFloat(((item.rate * item.qty) * item.disc / 100).toFixed(2));
      const taxableAmount = parseFloat((item.rate * item.qty - discountAmount).toFixed(2));

      // Get party state from selected party for GST calculation
      const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
      const firmState = Firm_state.value?.toLowerCase()?.trim() || '';
      const sameState = firmState === partyState;

      // Determine which GST values to show based on state
      const cgstValue = sameState ? item.cgst : 0;
      const sgstValue = sameState ? item.sgst : 0;
      const igstValue = !sameState ? item.igst : 0;


      invoiceHtml += `
              <tr>
                <td style="text-align: center;">${index + 1}</td>
                <td>
                  ${item.item}
                  ${item.item_narration ? `<br><span style="font-size: 9px; color: #666;">${item.item_narration}</span>` : ''}
                </td>
                <td>${item.hsn && item.hsn.trim() !== '' ? item.hsn : 'No HSN'}</td>
                <td class="text-right">${item.qty} ${item.uom || ''}</td>
                <td class="text-right">₹${item.rate.toFixed(2)}</td>
                <td class="text-right">₹${discountAmount.toFixed(2)}</td>
                <td class="text-right">${item.grate}%</td>
                <td class="text-right">₹${taxableAmount.toFixed(2)}</td>
              </tr>
      `;
    });

    // Close the items table if there are no other charges
    if (billForm.value.oth_chg.length === 0) {
      invoiceHtml += `
              </tbody>
            </table>
      `;
    }

    // Add other charges directly to the items table
    if (billForm.value.oth_chg.length > 0) {
      // Reopen the items table
      invoiceHtml = invoiceHtml.replace(`</tbody></table>`, '');

      // Get party state from selected party for GST calculation
      const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
      const firmState = Firm_state.value?.toLowerCase()?.trim() || '';
      const sameState = firmState === partyState;

      // Add each charge as a row in the items table
      billForm.value.oth_chg.forEach((charge, index) => {

        // Determine which GST values to show based on state
        const cgstValue = sameState ? charge.oth_cgst : 0;
        const sgstValue = sameState ? charge.oth_sgst : 0;
        const igstValue = !sameState ? charge.oth_igst : 0;

        // Calculate effective GST rate
        let gstRate = 0;
        if (charge.oth_amt > 0) {
          if (sameState) {
            gstRate = (cgstValue + sgstValue) / charge.oth_amt * 100;
          } else {
            gstRate = igstValue / charge.oth_amt * 100;
          }
        }

        // Calculate the next sequential number after stock items
        const itemCount = billForm.value.stockItems.length;
        const chargeNumber = itemCount + index + 1;

        invoiceHtml += `
              <tr>
                <td style="text-align: center;">${chargeNumber}</td>
                <td>${charge.description}</td>
                <td>${charge.oth_hsn && charge.oth_hsn.trim() !== '' ? charge.oth_hsn : 'No HSN'}</td>
                <td class="text-right">1</td>
                <td class="text-right">₹${charge.oth_amt.toFixed(2)}</td>
                <td class="text-right">₹${(0).toFixed(2)}</td>
                <td class="text-right">${gstRate.toFixed(2)}%</td>
                <td class="text-right">₹${charge.oth_amt.toFixed(2)}</td>
              </tr>
        `;
      });

      // Close the items table
      invoiceHtml += `
              </tbody>
            </table>
      `;
    }

    // Add totals section
    invoiceHtml += `
          <table class="totals-table">
            <tr>
              <td><strong>Gross Total:</strong></td>
              <td class="text-right">₹${billForm.value.gtot.toFixed(2)}</td>
            </tr>
    `;

    // Remove discount from totals section

    // Use the same state variables defined at the beginning of the function

    if (sameState) {
      // Same state - show CGST and SGST
      invoiceHtml += `
            <tr>
              <td><strong>CGST:</strong></td>
              <td class="text-right">₹${parseFloat(billForm.value.cgst || 0).toFixed(2)}</td>
            </tr>
            <tr>
              <td><strong>SGST:</strong></td>
              <td class="text-right">₹${parseFloat(billForm.value.sgst || 0).toFixed(2)}</td>
            </tr>
      `;
    } else {
      // Different state - show IGST
      invoiceHtml += `
            <tr>
              <td><strong>IGST:</strong></td>
              <td class="text-right">₹${parseFloat(billForm.value.igst || 0).toFixed(2)}</td>
            </tr>
      `;
    }

    if (billForm.value.rof !== 0) {
      invoiceHtml += `
            <tr>
              <td><strong>Round Off:</strong></td>
              <td class="text-right">₹${billForm.value.rof.toFixed(2)}</td>
            </tr>
      `;
    }

    // Ensure ntot is a number and not undefined
    let netTotal = 0;
    if (typeof billForm.value.ntot === 'number' && !isNaN(billForm.value.ntot)) {
      netTotal = billForm.value.ntot;
    } else {
      // Calculate net total from components
      const gtot = parseFloat(billForm.value.gtot) || 0;
      const disc = parseFloat(billForm.value.disc) || 0;
      const cgst = parseFloat(billForm.value.cgst) || 0;
      const sgst = parseFloat(billForm.value.sgst) || 0;
      const igst = parseFloat(billForm.value.igst) || 0;
      const rof = parseFloat(billForm.value.rof) || 0;

      netTotal = gtot - disc + cgst + sgst + igst + rof;
    }

    invoiceHtml += `
            <tr class="total-row">
              <td><strong>Net Total:</strong></td>
              <td class="text-right total-amount">₹${netTotal.toFixed(2)}</td>
            </tr>
          </table>
    `;

    // Generate HSN-wise summary
    const hsnSummary = {};
    let totalTaxAmount = 0;

    // Process items for HSN summary
    billForm.value.stockItems.forEach(item => {
      // Use a default HSN code if not available
      const hsn = item.hsn && item.hsn.trim() !== '' ? item.hsn : 'No HSN';

      if (!hsnSummary[hsn]) {
        hsnSummary[hsn] = {
          taxableAmount: 0,
          cgst: 0,
          sgst: 0,
          igst: 0,
          total: 0
        };
      }

      // Ensure we're working with numbers
      const itemTotal = parseFloat(item.total) || 0;
      hsnSummary[hsn].taxableAmount += itemTotal;

      // Calculate tax based on the item's tax rate
      if (parseFloat(item.igst_rate) > 0) {
        const igst = (itemTotal * parseFloat(item.igst_rate)) / 100;
        hsnSummary[hsn].igst += isNaN(igst) ? 0 : igst;
        totalTaxAmount += isNaN(igst) ? 0 : igst;
      } else {
        const cgstRate = parseFloat(item.cgst_rate) || 0;
        const sgstRate = parseFloat(item.sgst_rate) || 0;

        const cgst = (itemTotal * cgstRate) / 100;
        const sgst = (itemTotal * sgstRate) / 100;

        hsnSummary[hsn].cgst += isNaN(cgst) ? 0 : cgst;
        hsnSummary[hsn].sgst += isNaN(sgst) ? 0 : sgst;

        totalTaxAmount += (isNaN(cgst) ? 0 : cgst) + (isNaN(sgst) ? 0 : sgst);
      }

      hsnSummary[hsn].total += itemTotal;
    });

    // Process other charges for HSN summary
    billForm.value.oth_chg.forEach(charge => {
      // Use a default HSN code if not available
      const hsn = charge.hsn && charge.hsn.trim() !== '' ? charge.hsn : 'No HSN';

      if (!hsnSummary[hsn]) {
        hsnSummary[hsn] = {
          taxableAmount: 0,
          cgst: 0,
          sgst: 0,
          igst: 0,
          total: 0
        };
      }

      // Ensure we're working with numbers
      const chargeAmount = parseFloat(charge.oth_amt) || 0;
      const chargeCgst = parseFloat(charge.oth_cgst) || 0;
      const chargeSgst = parseFloat(charge.oth_sgst) || 0;
      const chargeIgst = parseFloat(charge.oth_igst) || 0;

      hsnSummary[hsn].taxableAmount += chargeAmount;
      hsnSummary[hsn].cgst += chargeCgst;
      hsnSummary[hsn].sgst += chargeSgst;
      hsnSummary[hsn].igst += chargeIgst;
      hsnSummary[hsn].total += chargeAmount;

      totalTaxAmount += chargeCgst + chargeSgst + chargeIgst;
    });

    // Function to convert number to words (Indian currency format)
    function numberToWords(num) {
      const single = ['', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine'];
      const double = ['Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
      const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];
      const formatTenth = (digit, prev) => {
        return digit === 0 ? '' : ' ' + (digit === 1 ? double[prev] : tens[digit] + (prev !== 0 ? ' ' + single[prev] : ''));
      };
      const formatOther = (digit, next, label) => {
        return digit === 0 && next[0] === '0' ? '' : ' ' + single[digit] + ' ' + label;
      };

      let rupees = Math.floor(num);
      let paise = Math.round((num - rupees) * 100);
      let str = '';

      // Convert rupees to words
      const digits = rupees.toString().split('');
      const length = digits.length;

      for (let i = 0; i < length; i++) {
        const digit = parseInt(digits[i]);
        const next = i < length - 1 ? digits.slice(i + 1).join('') : '0';

        if (length - i === 9) {
          str += formatOther(digit, next, 'Crore');
        } else if (length - i === 8) {
          str += formatTenth(digit, parseInt(digits[i + 1]));
          i++;
        } else if (length - i === 7) {
          str += formatOther(digit, next, 'Crore');
        } else if (length - i === 6) {
          str += formatOther(digit, next, 'Lakh');
        } else if (length - i === 5) {
          str += formatTenth(digit, parseInt(digits[i + 1]));
          i++;
        } else if (length - i === 4) {
          str += formatOther(digit, next, 'Thousand');
        } else if (length - i === 3) {
          str += formatOther(digit, next, 'Hundred');
        } else if (length - i === 2) {
          str += formatTenth(digit, parseInt(digits[i + 1]));
          i++;
        } else {
          str += formatOther(digit, next, '');
        }
      }

      str = 'Rupees ' + str.trim();

      // Convert paise to words
      if (paise > 0) {
        const paiseDigits = paise.toString().split('');
        const paiseLength = paiseDigits.length;

        let paiseStr = '';

        for (let i = 0; i < paiseLength; i++) {
          const digit = parseInt(paiseDigits[i]);

          if (paiseLength - i === 2) {
            paiseStr += formatTenth(digit, parseInt(paiseDigits[i + 1]));
            i++;
          } else {
            paiseStr += formatOther(digit, '0', '');
          }
        }

        str += ' and ' + paiseStr.trim() + ' Paise';
      }

      return str.trim() + ' Only';
    }

    // Add HSN-wise summary section
    invoiceHtml += `
          <h3 style="color: #4f46e5; margin-top: 20px;">HSN-wise Summary</h3>
          <table>
            <thead>
              <tr>
                <th>HSN Code</th>
                <th class="text-right">Taxable Amount</th>
                <th class="text-right">CGST</th>
                <th class="text-right">SGST</th>
                <th class="text-right">IGST</th>
                <th class="text-right">Total</th>
              </tr>
            </thead>
            <tbody>
    `;

    // Add each HSN summary row
    Object.keys(hsnSummary).forEach(hsn => {
      const summary = hsnSummary[hsn];
      invoiceHtml += `
              <tr>
                <td>${hsn}</td>
                <td class="text-right">₹${summary.taxableAmount.toFixed(2)}</td>
                <td class="text-right">₹${summary.cgst.toFixed(2)}</td>
                <td class="text-right">₹${summary.sgst.toFixed(2)}</td>
                <td class="text-right">₹${summary.igst.toFixed(2)}</td>
                <td class="text-right">₹${(summary.taxableAmount + summary.cgst + summary.sgst + summary.igst).toFixed(2)}</td>
              </tr>
      `;
    });

    invoiceHtml += `
            </tbody>
          </table>
    `;

    // Add amount in words section
    const netTotalInWords = numberToWords(netTotal);
    const taxAmountInWords = numberToWords(totalTaxAmount);

    invoiceHtml += `
          <div style="margin-top: 20px; padding: 10px; background-color: #f9fafb; border-radius: 5px;">
            <p><strong>Amount in Words:</strong> ${netTotalInWords}</p>
            <p><strong>Tax Amount in Words:</strong> ${taxAmountInWords}</p>
          </div>
    `;

    // Add signature section
    invoiceHtml += `
          <div class="signature-section">
            <div class="signature-box">
              <div class="signature-line">Customer's Signature</div>
              <p class="small-text">Received the above goods in good condition.</p>
            </div>
            <div class="signature-box" style="text-align: right;">
              <div class="signature-line">For ${Firm_name.value || 'Company Name'}</div>
              <p class="small-text">Authorized Signatory</p>
            </div>
          </div>
        </div>
    `;

    // Add script section
    invoiceHtml += `
        <script>
          // Auto-print when loaded
          window.onload = function() {
            // Small delay to ensure everything is rendered
            setTimeout(function() {
              window.print();
            }, 500);
          };
        <\/script>
      </body>
      </html>
    `;

    // Write the content to the new window
    try {
      // Using a more modern approach to avoid document.write deprecation warning
      printWindow.document.open();
      printWindow.document.documentElement.innerHTML = invoiceHtml;
      printWindow.document.close();
    } catch (error) {
      console.error('Error writing to print window:', error);
      // Fallback to document.write if the modern approach fails
      try {
        printWindow.document.open();
        printWindow.document.write(invoiceHtml);
        printWindow.document.close();
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
      }
    }

    // Force the browser to wait for resources to load before printing
    printWindow.onload = function() {
      // This ensures the content is fully loaded before printing
      setTimeout(function() {
        printWindow.focus(); // Focus the window to ensure the print dialog appears
        printWindow.print(); // Trigger the print dialog
      }, 1000);
    };

    success('Invoice opened in a new tab for printing. The print dialog should appear automatically.');
  } catch (error) {
    console.error('Error printing invoice:', error);
    info('Failed to print invoice. Please try again.');
  }
};

// Function to format date in dd-MMM-yyyy format
function formatDate(dateString) {
  if (!dateString) return 'N/A';

  const date = new Date(dateString);
  if (isNaN(date.getTime())) return 'Invalid Date';

  const day = date.getDate().toString().padStart(2, '0');
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const month = monthNames[date.getMonth()];
  const year = date.getFullYear();

  return `${day}-${month}-${year}`;
}

// Function to get state code from state name using the indianStates array
function getStateCode(stateName) {
  if (!stateName) return '';

  // First try to find the state in the indianStates array
  const normalizedStateName = stateName.toLowerCase().trim();

  // Log the state name for debugging

  // Find the state in the indianStates array (case-insensitive comparison)
  const stateObj = indianStates.find(state =>
    state.name.toLowerCase().trim() === normalizedStateName ||
    state.name.toLowerCase().includes(normalizedStateName) ||
    normalizedStateName.includes(state.name.toLowerCase())
  );

  if (stateObj) {
    return `(${stateObj.code.toString().padStart(2, '0')})`;
  }

  // Fallback to manual mapping for any states not in the array
  const stateMap = {
    'andhra pradesh': '37',
    'arunachal pradesh': '12',
    'assam': '18',
    'bihar': '10',
    'chhattisgarh': '22',
    'goa': '30',
    'gujarat': '24',
    'haryana': '06',
    'himachal pradesh': '02',
    'jharkhand': '20',
    'karnataka': '29',
    'kerala': '32',
    'madhya pradesh': '23',
    'maharashtra': '27',
    'manipur': '14',
    'meghalaya': '17',
    'mizoram': '15',
    'nagaland': '13',
    'odisha': '21',
    'punjab': '03',
    'rajasthan': '08',
    'sikkim': '11',
    'tamil nadu': '33',
    'telangana': '36',
    'tripura': '16',
    'uttar pradesh': '09',
    'uttarakhand': '05',
    'west bengal': '19',
    'andaman and nicobar islands': '35',
    'chandigarh': '04',
    'dadra and nagar haveli and daman and diu': '26',
    'delhi': '07',
    'jammu and kashmir': '01',
    'ladakh': '38',
    'lakshadweep': '31',
    'puducherry': '34'
  };

  const stateCode = stateMap[normalizedStateName];
  if (stateCode) {
    return `(${stateCode})`;
  }

  return '';
}

// Function to get all existing charge descriptions from inventory data
const getAllExistingCharges = () => {
  const charges = [];

  // Add charges from current bill
  if (billForm.value.oth_chg && billForm.value.oth_chg.length > 0) {
    charges.push(...billForm.value.oth_chg);
  }

  // Add charges from all bills in inventory data
  if (inventoryData.value?.bills && Array.isArray(inventoryData.value.bills)) {
    inventoryData.value.bills.forEach(bill => {
      if (bill.oth_chg && Array.isArray(bill.oth_chg)) {
        charges.push(...bill.oth_chg);
      }
    });
  }

  return charges;
};

// Function to create a new invoice (reset the form)
const createNewInvoice = () => {
  resetBillForm();
  success('Ready to create a new invoice');
};

// Add stock item to the form
const addStockItem = () => {
  billForm.value.stockItems.push({
    item: '',
    hsn: '',
    batch: null, // ✅ Changed from '' to null
    qty: 0,
    oem: '',
    pno: null, // ✅ Changed from '' to null
    uom: '',
    rate: 0,
    grate: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    disc: 0,
    project: '',
    total: 0,
    item_narration: '',
    mrp: null,
    expiryDate: null
  });
};

// Edit other charge
const editOtherCharge = (index) => {
  currentCharge.value = { ...billForm.value.oth_chg[index] };
  chargeIndex.value = index;
  showOtherChargesModal.value = true;
};

// Remove other charge
const removeOtherCharge = (index) => {
  billForm.value.oth_chg.splice(index, 1);
  calculateBillTotal();
};


// Handle stock item selection and auto-fill fields
const handleStockItemSelection = (index) => {
  const selectedItem = billForm.value.stockItems[index].item;
  const stockItem = inventoryData.value?.stocks?.find(stock => stock.item === selectedItem);

  if (stockItem) {
    billForm.value.stockItems[index] = {
      ...billForm.value.stockItems[index],
      hsn: stockItem.hsn || '',
      pno: stockItem.pno || null, // ✅ Changed from '' to null
      oem: stockItem.oem || '',
      batch: stockItem.batch || null, // ✅ Changed from '' to null
      uom: stockItem.uom || '',
      rate: stockItem.rate || 0,
      grate: stockItem.grate || 0,
      mrp: stockItem.mrp || null,
      expiryDate: stockItem.expiryDate || null
    };
    calculateItemTotal(index);
  }
};

// Add these refs
const showOtherChargesModal = ref(false);
const otherChargeForm = ref({
  description: '',
  oth_amt: 0,
  oth_grate: 0,
  oth_cgst: 0,
  oth_sgst: 0,
  oth_igst: 0,
  oth_hsn: '',
  oth_tot: 0
});

// Update billForm data structure
billForm.value = {
  type: '',
  bno: '',
  bdate: new Date().toISOString().slice(0, 10),
  partyName: '',
  partyAddress: '',
  partyGstin: '',
  paymentMode: '',
  paymentRef: '',
  gtot: 0,
  disc: 0,
  cgst: 0,
  sgst: 0,
  igst: 0,
  rof: 0,
  ntot: 0,
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: '',
  vehicleNo: '',
  oth_chg: [],
  stockItems: []
};



const resetOtherChargeForm = () => {
  otherChargeForm.value = {
    description: '',
    oth_amt: 0,
    oth_grate: 0,
    oth_cgst: 0,
    oth_sgst: 0,
    oth_igst: 0,
    oth_hsn: '',
    oth_tot: 0
  };
};

const addOtherCharge = (chargeData) => {
  if (chargeIndex.value >= 0) {
    // Update existing charge at the specified index
    billForm.value.oth_chg[chargeIndex.value] = { ...chargeData };
    chargeIndex.value = -1;
    currentCharge.value = null;
  } else {
    // For new charges, check for duplicates
    const existingIndex = billForm.value.oth_chg.findIndex(charge =>
      charge.description === chargeData.description &&
      charge.oth_amt === chargeData.oth_amt
    );

    if (existingIndex === -1) {
      billForm.value.oth_chg.push({ ...chargeData });
    } else {
      billForm.value.oth_chg[existingIndex] = { ...chargeData };
    }
  }

  // Close modal and recalculate totals
  showOtherChargesModal.value = false;
  calculateBillTotal();
};

// Calculate bill totals from stock items
const calculateBillTotal = () => {
  // Reset totals
  billForm.value.gtot = 0;
  billForm.value.cgst = 0;
  billForm.value.sgst = 0;
  billForm.value.igst = 0;

  // Get party state from selected party
  const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
  const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
  const firmState = Firm_state.value?.toLowerCase()?.trim() || '';

  // Sum up stock items
  billForm.value.stockItems.forEach(item => {
    billForm.value.gtot += parseFloat((item.total || 0).toFixed(2));
    billForm.value.cgst += parseFloat((item.cgst || 0).toFixed(2));
    billForm.value.sgst += parseFloat((item.sgst || 0).toFixed(2));
    billForm.value.igst += parseFloat((item.igst || 0).toFixed(2));
  });

  // Format to 2 decimal places
  billForm.value.gtot = parseFloat(billForm.value.gtot.toFixed(2));
  billForm.value.cgst = parseFloat(billForm.value.cgst.toFixed(2));
  billForm.value.sgst = parseFloat(billForm.value.sgst.toFixed(2));
  billForm.value.igst = parseFloat(billForm.value.igst.toFixed(2));

  // Add other charges if present
  billForm.value.gtot += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_amt || 0), 0);
  billForm.value.cgst += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_cgst || 0), 0);
  billForm.value.sgst += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_sgst || 0), 0);
  billForm.value.igst += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_igst || 0), 0);

  // Calculate net total
  let netTotal;
  if (firmState === partyState) {
    // Same state - use CGST and SGST for net total
    netTotal = parseFloat((billForm.value.gtot + billForm.value.cgst + billForm.value.sgst).toFixed(2));
  } else {
    // Different states - use IGST for net total
    netTotal = parseFloat((billForm.value.gtot + billForm.value.igst).toFixed(2));
  }

  // Calculate round-off amount to nearest whole number
  const roundedTotal = Math.round(netTotal);
  billForm.value.rof = parseFloat((roundedTotal - netTotal).toFixed(2));

  // Set final net total with round-off
  billForm.value.ntot = parseFloat(roundedTotal.toFixed(2));
};

// Remove stock item from the form
const removeStockItem = (index) => {
  billForm.value.stockItems.splice(index, 1);
  calculateBillTotal();
};

// Calculate total for a stock item
const calculateItemTotal = (index) => {
  const item = billForm.value.stockItems[index];
  const discamt = parseFloat(((item.rate * item.qty) * item.disc / 100).toFixed(2));

  // Get party state from selected party
  const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
  const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
  const firmState = Firm_state.value?.toLowerCase()?.trim() || '';

  // Compare states to determine GST calculation
  if (firmState === partyState) {
    // Same state - apply CGST/SGST, set IGST to 0
    item.cgst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100 / 2).toFixed(2));
    item.sgst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100 / 2).toFixed(2));
    item.igst = 0;
  } else {
    // Different states - apply IGST, set CGST/SGST to 0
    item.cgst = 0;
    item.sgst = 0;
    item.igst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100).toFixed(2));
  }

  // Ensure total is properly formatted to 2 decimal places
  item.total = parseFloat((item.rate * item.qty - discamt).toFixed(2));
  calculateBillTotal();
};

// Submit the bill form
const submitBillForm = async () => {
  error.value = null;

  // Validate form before submission
  if (!billForm.value.type) {
    info('Please select a bill type');
    return;
  }

  if (!billForm.value.bno) {
    info('Please enter a bill number');
    return;
  }

  if (!billForm.value.partyName) {
    info('Please select or add a party');
    return;
  }

  // Remove any blank rows in stock items before submission
  billForm.value.stockItems = billForm.value.stockItems.filter(item => {
    return item.item && item.item.trim() !== '';
  });

  // ✅ ADDED: Sanitize stock item data before submission
  billForm.value.stockItems = billForm.value.stockItems.map(item => ({
    ...item,
    pno: (item.pno && item.pno.trim() !== '') ? item.pno : null,
    batch: (item.batch && item.batch.trim() !== '') ? item.batch : null
  }));

  if (billForm.value.stockItems.length === 0) {
    info('Please add at least one stock item');
    return;
  }

  // Validate each stock item
  for (let i = 0; i < billForm.value.stockItems.length; i++) {
    const item = billForm.value.stockItems[i];
    if (!item.item) {
      info(`Stock item #${i + 1}: Please select an item`);
      return;
    }
    if (!item.qty || item.qty <= 0) {
      info(`Stock item #${i + 1}: Please enter a valid quantity`);
      return;
    }
    if (!item.rate || item.rate <= 0) {
      info(`Stock item #${i + 1}: Please enter a valid rate`);
      return;
    }
  }

  try {
    const $api = useApiWithAuth();
    let endpoint = '/api/inventory';

    // Determine the endpoint based on bill type
    switch (billForm.value.type) {
      case 'SALES':
        endpoint = '/api/inventory/bills';
        break;
      case 'PURCHASE':
        endpoint = '/api/inventory/purchase';
        break;
      case 'DEBIT NOTE':
        endpoint = '/api/inventory/debit-note';
        break;
      case 'CREDIT NOTE':
        endpoint = '/api/inventory/credit-note';
        break;
    }

    // Create request payload including newParty if it exists
    const payload = {
      ...billForm.value,
      newParty: newParty.value
    };

    const response = await $api.post(endpoint, payload);
    // Store the response data and set the submitted flag
    submittedInvoiceData.value = response;
    invoiceSubmitted.value = true;

    success('Bill created successfully. You can now print the invoice or create a new one.');
  } catch (err) {
    console.error('Error creating bill:', err);
    info('Failed to create bill. Please try again.');
  }
};



const editSelectedParty = (e) => {
  e.preventDefault();

  if (!billForm.value.partyName) {
    return;
  }

  const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
  if (selectedParty) {
    partyToEdit.value = { ...selectedParty };
    showPartyModal.value = true;
  }
};

// Handle Alt+C keypress on party name field
const handlePartyNameKeydown = (event) => {
  // Check if Alt+C or Alt+c was pressed
  if (event.altKey && (event.key === 'c' || event.key === 'C')) {
    event.preventDefault();
    showPartyModal.value = true;
  }
};

// Handle keyboard shortcuts on stock item field
const handleStockItemKeydown = (event) => {
  // Check if Alt+C or Alt+c was pressed
  if (event.altKey && (event.key === 'c' || event.key === 'C')) {
    event.preventDefault();
    showStockItemModal.value = true;
  }

  // Check if Alt+V or Alt+v was pressed
  if (event.altKey && (event.key === 'v' || event.key === 'V')) {
    event.preventDefault();
    const index = event.target.closest('tr').rowIndex - 1; // Adjust for header row
    const currentItem = billForm.value.stockItems[index];

    if (currentItem && currentItem.item && billForm.value.partyName) {
      selectedStockItem.value = currentItem.item;
      showStockRegModal.value = true;
    } else {
      alert('Cannot show stock history: missing item name or party name');
    }
  }

  // Check if Ctrl+Enter was pressed
  if (event.ctrlKey && (event.key === 'Enter')) {
    event.preventDefault();
    const index = event.target.closest('tr').rowIndex - 1; // Adjust for header row
    const currentItem = billForm.value.stockItems[index];

    if (currentItem && currentItem.item) {
      // Set the current item from the bill form as the item to edit
      editStockItem.value = { ...currentItem };
      showStockItemModal.value = true;
    }
  }
};

// Submit the party form
const submitPartyForm = (partyData) => {
  // Check if this is a valid party submission (must have supply name)
  if (!partyData.supply) {
    return; // Don't process empty submissions
  }

  // Store the party data in the newParty ref
  newParty.value = {
    ...partyData,
    // Add a temporary ID for local use
    _id: 'temp_' + Date.now(),
  };
  // Add the new party to the local array and update the bill form
  handleAddParty(newParty.value);
  // Close modal if it's open
  if (showPartyModal.value) {
    showPartyModal.value = false;
  }
};

// Handle adding a new party from the modal
const handleAddParty = (newParty) => {
  // Ensure inventoryData.parties exists
  if (!inventoryData.value) {
    inventoryData.value = {};
  }

  if (!inventoryData.value.parties) {
    inventoryData.value.parties = [];
  }

  // Add the new party to the local array
  inventoryData.value.parties.push(newParty);

  // Set the party name in the form
  billForm.value.partyName = newParty.supply;

  // If the party has an address and GSTIN, set those too
  if (newParty.addr) {
    billForm.value.partyAddress = newParty.addr;
  }

  if (newParty.gstin) {
    billForm.value.partyGstin = newParty.gstin;
  }
};

// Show order and dispatch modal when party name field loses focus
const openOrderDispatchModal = () => {
  // Only show the modal if a party is selected and the party name is not empty
  if (billForm.value.partyName && billForm.value.partyName.trim() !== '') {
    // Find the selected party from inventoryData to get state and PIN
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);

    // Populate the orderDetails with current values from billForm
    orderDetails.value = {
      orderNo: billForm.value.orderNo || '',
      orderDate: billForm.value.orderDate || '',
      dispatchThrough: billForm.value.dispatchThrough || '',
      docketNo: billForm.value.docketNo || '',
      vehicleNo: billForm.value.vehicleNo || '',
      // Populate consignee details with party details by default
      consigneeName: billForm.value.partyName || '',
      consigneeGstin: billForm.value.partyGstin || '',
      consigneeAddress: billForm.value.partyAddress || '',
      consigneeState: selectedParty?.state || '',
      consigneePin: selectedParty?.pin || ''
    };
    // Show the modal
    showOrderDispatchModal.value = true;
  }
};

// Handle submission of order and dispatch details
const submitOrderDetails = (details) => {
  // Update the billForm with the submitted details
  billForm.value.orderNo = details.orderNo;
  billForm.value.orderDate = details.orderDate;
  billForm.value.dispatchThrough = details.dispatchThrough;
  billForm.value.docketNo = details.docketNo;
  billForm.value.vehicleNo = details.vehicleNo;
  // Update consignee details
  billForm.value.consigneeName = details.consigneeName;
  billForm.value.consigneeGstin = details.consigneeGstin;
  billForm.value.consigneeAddress = details.consigneeAddress;
  billForm.value.consigneeState = details.consigneeState;
  billForm.value.consigneePin = details.consigneePin;
};

// Add this function in the script section
const handlePartySelection = () => {
  if (billForm.value.partyName === 'Create New Party') {
    billForm.value.partyName = ''; // Clear the input
    showPartyModal.value = true; // Open the modal
  } else {
    // Find the selected party from inventoryData
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);

    // Update address and GSTIN if party found
    if (selectedParty) {
      billForm.value.partyAddress = selectedParty.addr || '';
      billForm.value.partyGstin = selectedParty.gstin || '';
    }
  }
};

// Reference to store the item being edited
const editStockItem = ref(null);

// Submit the stock item form
const submitStockItemForm = (stockItemData) => {
  // Check if this is a valid stock item submission (must have item name)
  if (!stockItemData.item) {
    return; // Don't process empty submissions
  }

  // Add user and firm information from the stored variables
  stockItemData.user = userId.value || 'unknown';
  stockItemData.firm = firmName.value || 'unknown';

  // Check if this is an edit operation
  if (stockItemData._isEdit && editStockItem.value) {
    // Find the index of the item being edited
    const editIndex = billForm.value.stockItems.findIndex(item =>
      item.item === editStockItem.value.item &&
      item.batch === editStockItem.value.batch);

    if (editIndex !== -1) {
      // Update the existing item with the new stock item data
      billForm.value.stockItems[editIndex] = {
        ...billForm.value.stockItems[editIndex],
        item: stockItemData.item,
        pno: stockItemData.pno,
        batch: stockItemData.batch,
        oem: stockItemData.oem,
        hsn: stockItemData.hsn,
        qty: stockItemData.qty,
        uom: stockItemData.uom,
        rate: stockItemData.rate,
        grate: stockItemData.grate,
        total: stockItemData.total
      };

      // Recalculate the bill total
      calculateItemTotal(editIndex);
    }
  } else if (billForm.value.stockItems.length > 0) {
    // Find the current active stock item (the one being edited)
    const currentIndex = billForm.value.stockItems.findIndex(item => item.item === '');

    if (currentIndex !== -1) {
      // Update the current item with the new stock item data
      billForm.value.stockItems[currentIndex] = {
        ...billForm.value.stockItems[currentIndex],
        item: stockItemData.item,
        pno: stockItemData.pno,
        batch: stockItemData.batch,
        oem: stockItemData.oem,
        hsn: stockItemData.hsn,
        qty: stockItemData.qty,
        uom: stockItemData.uom,
        rate: stockItemData.rate,
        grate: stockItemData.grate,
        total: stockItemData.total
      };

      // Recalculate the bill total
      calculateItemTotal(currentIndex);
    } else {
      // If no empty item found, add a new one
      billForm.value.stockItems.push({
        item: stockItemData.item,
        pno: stockItemData.pno,
        batch: stockItemData.batch,
        oem: stockItemData.oem,
        hsn: stockItemData.hsn,
        qty: stockItemData.qty,
        uom: stockItemData.uom,
        rate: stockItemData.rate,
        grate: stockItemData.grate,
        cgst: 0,
        sgst: 0,
        igst: 0,
        disc: 0,
        project: '',
        total: stockItemData.total
      });

      // Recalculate the bill total
      calculateBillTotal();
    }
  } else {
    // If no stock items exist, add this as the first one
    billForm.value.stockItems.push({
      item: stockItemData.item,
      pno: stockItemData.pno,
      batch: stockItemData.batch,
      oem: stockItemData.oem,
      hsn: stockItemData.hsn,
      qty: stockItemData.qty,
      uom: stockItemData.uom,
      rate: stockItemData.rate,
      grate: stockItemData.grate,
      total: stockItemData.total
    });

    // Recalculate the bill total
    calculateBillTotal();
  }

  // Close the modal and reset edit item
  showStockItemModal.value = false;
  editStockItem.value = null;
};
</script>

<style>
/* Custom styling for datalist options */
#stockItemList option {
  /* These styles will only be visible in some browsers that support styling datalist options */
  font-size: 14px;
  border-bottom: 1px solid #eaeaea;
}

/* Add color indicators based on stock quantity */
input[list="stockItemList"] {
  background-color: white;
  transition: background-color 0.3s ease;
}

/* Add colorful border to the datalist input when focused */
input[list="stockItemList"]:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Custom styling for the datalist dropdown (works in some browsers) */
input[list="stockItemList"]::-webkit-calendar-picker-indicator {
  color: #4f46e5;
}

/* Add a subtle background color change when hovering over the input */
input[list="stockItemList"]:hover {
  background-color: #f9fafb;
}
</style>
