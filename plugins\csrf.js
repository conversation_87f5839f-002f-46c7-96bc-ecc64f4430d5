/**
 * Plugin to initialize CSRF token on application startup
 * This ensures a CSRF token is available for all requests
 */
import useCsrf from '~/composables/auth/useCsrf';

export default defineNuxtPlugin(async () => {
  // Only run on client side
  if (process.server) {
    return;
  }
  
  // Initialize CSRF token
  const { getCsrfToken, fetchCsrfToken } = useCsrf();
  
  // Check if we already have a token
  const existingToken = getCsrfToken();
  
  // If no token exists, fetch a new one
  if (!existingToken) {
    await fetchCsrfToken();
  }
});
