<template>
  <div class="bg-white rounded-lg p-4 sm:p-6">
    <form @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
        <!-- Transaction Type Field -->
        <div>
          <label for="transactionType" class="block text-sm font-medium text-gray-700 mb-1">Transaction Type *</label>
          <div class="flex space-x-4">
            <label class="inline-flex items-center">
              <input
                type="radio"
                name="transactionType"
                value="PAYMENT"
                v-model="formData.transactionType"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                @change="updateCategory"
              />
              <span class="ml-2 text-gray-700">Payment (Expense)</span>
            </label>
            <label class="inline-flex items-center">
              <input
                type="radio"
                name="transactionType"
                value="RECEIPT"
                v-model="formData.transactionType"
                class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                @change="updateCategory"
              />
              <span class="ml-2 text-gray-700">Receipt (Income)</span>
            </label>
          </div>
        </div>

        <!-- Date Field -->
        <div>
          <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
          <input
            type="date"
            id="date"
            v-model="formData.date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Amount Field -->
        <div>
          <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">
            {{ formData.transactionType === 'RECEIPT' ? 'Amount Received *' : 'Amount Paid *' }}
          </label>
          <input
            type="number"
            id="amount"
            v-model="formData.amount"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Paid To Field with Custom Dropdown -->
        <div>
          <label for="paidTo" class="block text-sm font-medium text-gray-700 mb-1">
            {{ formData.transactionType === 'RECEIPT' ? 'Received From *' : 'Paid To *' }}
          </label>
          <div class="relative">
            <input
              type="text"
              id="paidTo"
              v-model="formData.paidTo"
              autocomplete="off"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 pr-10"
              @input="handlePaidToInput"
              @focus="showPaidToDropdown = true"
              @blur="handlePaidToBlur"
              @keydown="handlePaidToKeydown"
              placeholder="Type to search or select from list..."
              required
            />
            <!-- Dropdown Arrow Icon -->
            <div class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <ChevronDownIcon class="h-4 w-4 text-gray-400" />
            </div>

            <!-- Custom Dropdown -->
            <div
              v-if="showPaidToDropdown && filteredPaidToOptions.length > 0"
              class="absolute z-50 w-full mt-1 bg-white border border-gray-200 rounded-lg shadow-lg max-h-64 overflow-y-auto"
              style="min-width: 400px;"
            >
              <!-- Header -->
              <div class="sticky top-0 bg-gradient-to-r from-indigo-500 to-purple-600 px-4 py-3 border-b border-indigo-300">
                <div class="grid grid-cols-2 gap-4 text-xs font-semibold text-white uppercase tracking-wide">
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clip-rule="evenodd" />
                    </svg>
                    <span>Name</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M17.707 9.293a1 1 0 010 1.414l-7 7a1 1 0 01-1.414 0l-7-7A.997.997 0 012 10V5a3 3 0 013-3h5c.256 0 .512.098.707.293l7 7zM5 6a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                    <span>Type</span>
                  </div>
                </div>
              </div>

              <!-- Options -->
              <div class="max-h-48 overflow-y-auto">
                <div
                  v-for="(option, index) in filteredPaidToOptions"
                  :key="`${option.name}-${option.type}`"
                  @mousedown="selectPaidToOption(option)"
                  :class="[
                    'grid grid-cols-2 gap-4 px-4 py-3 cursor-pointer transition-all duration-200 ease-in-out',
                    index === selectedOptionIndex ? 'bg-indigo-50 border-l-4 border-indigo-500 shadow-sm' : 'hover:bg-gradient-to-r hover:from-yellow-50 hover:to-green-50 hover:shadow-sm',
                    'border-b border-gray-100 last:border-b-0'
                  ]"
                >
                  <!-- Name Column -->
                  <div class="flex items-center space-x-2">
                    <div
                      :class="[
                        'w-2 h-2 rounded-full flex-shrink-0',
                        getTypeColor(option.type)
                      ]"
                    ></div>
                    <span class="text-sm font-medium text-gray-900 truncate">{{ option.name }}</span>
                  </div>

                  <!-- Type Column -->
                  <div class="flex items-center">
                    <span
                      :class="[
                        'inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium',
                        getTypeBadgeColor(option.type)
                      ]"
                    >
                      {{ option.type }}
                    </span>
                  </div>
                </div>
              </div>

              <!-- No Results -->
              <div v-if="filteredPaidToOptions.length === 0" class="px-4 py-6 text-center text-gray-500">
                <div class="text-sm">No matches found</div>
                <div class="text-xs text-gray-400 mt-1">Try typing a different name</div>
              </div>
            </div>
          </div>
        </div>

        <!-- Paid To Group Field with Datalist -->
        <div>
          <label for="paidToGroup" class="block text-sm font-medium text-gray-700 mb-1">Paid To Group</label>
          <div class="relative">
            <input
              type="text"
              id="paidToGroup"
              v-model="formData.paidToGroup"
              list="paidToGroupList"
              autocomplete="off"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="paidToGroupList">
              <!-- Fixed Common Groups -->
              <option value="subs">subs</option>
              <option value="vendors">vendors</option>
              <option value="customers">customers</option>
              <option value="employees">employees</option>

              <!-- Values from existing expenses -->
              <option v-for="group in getUniquePaidToGroups" :key="group" :value="group">{{ group }}</option>
            </datalist>

            <!-- Debug output - hidden in production -->
            <div v-if="false" class="mt-1 text-xs text-gray-500 bg-gray-100 p-2 rounded">
              <div><strong>Unique Paid To Groups Count:</strong> {{ getUniquePaidToGroups.length }}</div>
              <div v-if="getUniquePaidToGroups.length > 0">
                <strong>First Group:</strong> {{ getUniquePaidToGroups[0] || 'None' }}
              </div>
              <div v-if="getUniquePaidToGroups.length > 0" class="mt-1">
                <strong>All Unique Paid To Groups:</strong>
                <ul class="list-disc pl-5">
                  <li v-for="group in getUniquePaidToGroups" :key="group">{{ group }}</li>
                </ul>
              </div>
              <div v-else class="text-red-500 font-bold">
                No paid-to groups found in expenses! Try adding some expenses first.
              </div>
            </div>
          </div>
        </div>

        <!-- Category Field with Datalist -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <div class="relative">
            <input
              type="text"
              id="category"
              v-model="formData.category"
              list="categoryList"
              autocomplete="off"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="categoryList">
              <option value="PAYMENT"></option>
              <option value="RECEIPT"></option>
              <option value="TRANSFER"></option>
              <option v-for="category in uniqueCategories" :key="category" :value="category"></option>
            </datalist>
          </div>
        </div>

        <!-- Project Field with Datalist -->
        <div>
          <label for="project" class="block text-sm font-medium text-gray-700 mb-1">Project</label>
          <div class="relative">
            <input
              type="text"
              id="project"
              v-model="formData.project"
              list="projectList"
              autocomplete="off"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="projectList">
              <option v-for="project in uniqueProjects" :key="project" :value="project"></option>
            </datalist>
          </div>
        </div>

        <!-- Payment Mode Field -->
        <div>
          <label for="paymentMode" class="block text-sm font-medium text-gray-700 mb-1">Payment Mode *</label>
          <div class="flex space-x-4">
            <div class="flex items-center">
              <input
                type="radio"
                id="cash"
                v-model="formData.paymentMode.type"
                value="cash"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                required
              />
              <label for="cash" class="ml-2 text-sm text-gray-700">Cash</label>
            </div>
            <div class="flex items-center">
              <input
                type="radio"
                id="bank"
                v-model="formData.paymentMode.type"
                value="bank"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                required
              />
              <label for="bank" class="ml-2 text-sm text-gray-700">Bank</label>
            </div>
          </div>
        </div>

        <!-- Bank Selection (if bank is selected) -->
        <div v-if="formData.paymentMode.type === 'bank'">
          <label for="bankId" class="block text-sm font-medium text-gray-700 mb-1">Bank Account *</label>
          <select
            id="bankId"
            v-model="formData.paymentMode.bankId"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          >
            <option value="" disabled>Select Bank Account</option>
            <option v-for="bank in bankLedgers" :key="bank.id" :value="bank.id">
              {{ bank.name }}
            </option>
          </select>
        </div>

        <!-- Instrument Number (if bank is selected) -->
        <div v-if="formData.paymentMode.type === 'bank'">
          <label for="instrumentNo" class="block text-sm font-medium text-gray-700 mb-1">Instrument No. *</label>
          <input
            type="text"
            id="instrumentNo"
            v-model="formData.paymentMode.instrumentNo"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Description Field -->
        <div class="md:col-span-2">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            id="description"
            v-model="formData.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          ></textarea>
        </div>
      </div>

      <!-- Dynamic Deduction Section -->
      <DeductionSection
        v-model:hasDeductions="formData.hasDeductions"
        v-model:deductions="formData.deductions"
        :grossAmount="Math.abs(parseFloat(formData.amount) || 0)"
        :transactionType="formData.transactionType"
        :disabled="isLoading"
      />

      <!-- Sub-Expenses Section has been removed -->
      <div v-if="false" class="mt-8">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Sub-Expenses</h3>

        <div v-for="(subExpense, index) in formData.subExpenses" :key="index" class="border border-gray-200 rounded-md p-4 mb-4">
          <div class="flex justify-between items-center mb-4">
            <h4 class="text-md font-medium text-gray-700">Sub-Expense #{{ index + 1 }}</h4>
            <button
              type="button"
              @click="removeSubExpense(index)"
              class="text-red-600 hover:text-red-800"
            >
              Remove
            </button>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Sub-Expense Date -->
            <div>
              <label :for="`subDate${index}`" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
              <input
                type="date"
                :id="`subDate${index}`"
                v-model="subExpense.date"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              />
            </div>

            <!-- Sub-Expense Amount -->
            <div>
              <label :for="`subAmount${index}`" class="block text-sm font-medium text-gray-700 mb-1">Amount *</label>
              <input
                type="number"
                :id="`subAmount${index}`"
                v-model="subExpense.amount"
                step="0.01"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              />
            </div>

            <!-- Sub-Expense Paid To -->
            <div>
              <label :for="`subPaidTo${index}`" class="block text-sm font-medium text-gray-700 mb-1">Paid To *</label>
              <input
                type="text"
                :id="`subPaidTo${index}`"
                v-model="subExpense.paidTo"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              />
            </div>

            <!-- Sub-Expense Category -->
            <div>
              <label :for="`subCategory${index}`" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
              <input
                type="text"
                :id="`subCategory${index}`"
                v-model="subExpense.category"
                list="categoryList"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <!-- Sub-Expense Project -->
            <div>
              <label :for="`subProject${index}`" class="block text-sm font-medium text-gray-700 mb-1">Project</label>
              <input
                type="text"
                :id="`subProject${index}`"
                v-model="subExpense.project"
                list="projectList"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <!-- Sub-Expense Description -->
            <div class="md:col-span-2">
              <label :for="`subDescription${index}`" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
              <textarea
                :id="`subDescription${index}`"
                v-model="subExpense.description"
                rows="2"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              ></textarea>
            </div>
          </div>
        </div>

        <button
          type="button"
          @click="addSubExpense"
          class="mt-2 px-4 py-2 border border-indigo-300 text-indigo-600 rounded-md hover:bg-indigo-50"
        >
          Add Sub-Expense
        </button>
      </div>

      <!-- Form Actions (only shown if showFooterButtons is true) -->
      <div v-if="showFooterButtons" class="mt-8 flex justify-end space-x-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>{{ expense ? 'Update' : 'Save' }}</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useLedgers } from '~/composables/expenses/useLedgers';
import { usePaidToGroups } from '~/composables/expenses/usePaidToGroups';
import { useSubs } from '~/composables/expenses/useSubs';
import { ChevronDownIcon } from '@heroicons/vue/24/outline';
import DeductionSection from './DeductionSection.vue';

export default {
  name: 'ExpenseForm',

  props: {
    expense: {
      type: Object,
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    showFooterButtons: {
      type: Boolean,
      default: true
    }
  },

  emits: ['submit', 'cancel'],

  components: {
    DeductionSection,
    ChevronDownIcon
  },

  setup(props, { emit }) {
    // Get composables
    const {
      getUniquePaidTo,
      getUniqueCategories,
      getUniqueProjects,
      getUniquePaidToGroups,
      fetchExpenses
    } = useExpenses();

    const {
      ledgers,
      bankLedgers,
      fetchLedgers
    } = useLedgers();

    const {
      paidToGroups,
      fetchPaidToGroups
    } = usePaidToGroups();

    const {
      subsModels,
      fetchSubsModels,
      getUniqueSubNames
    } = useSubs();

    // Initialize form data
    const formData = ref({
      date: new Date().toISOString().split('T')[0],
      paidTo: '',
      amount: '',
      transactionType: 'PAYMENT', // Default to payment
      category: 'PAYMENT',
      project: '',
      paymentMode: {
        type: 'cash',
        instrumentNo: '',
        bankId: ''
      },
      description: '',
      paidToGroup: '',
      // Dynamic deduction fields
      hasDeductions: false,
      deductions: []
    });

    // Dropdown state
    const showPaidToDropdown = ref(false);
    const selectedOptionIndex = ref(-1);
    const searchQuery = ref('');

    // Computed properties
    // Get party ledgers
    const partyLedgers = computed(() => {
      return ledgers.value.filter(ledger => ledger.type === 'party');
    });

    // Get party ledger names
    const partyLedgerNames = computed(() => {
      return partyLedgers.value.map(ledger => ledger.name);
    });

    const uniquePaidTo = computed(() => {
      // Combine unique paid to values from different sources with type information
      const paidToValues = getUniquePaidTo.value || [];
      const subNames = getUniqueSubNames.value || [];
      const partyNames = partyLedgerNames.value || [];

      // Create array of objects with name and type information
      const combinedOptions = [];

      // Add previous expense recipients
      paidToValues.forEach(name => {
        if (name && name.trim()) {
          combinedOptions.push({
            name: name.trim(),
            type: 'Previous Expense',
            displayValue: `${name.trim()} (Previous Expense)`
          });
        }
      });

      // Add sub-contractor names
      subNames.forEach(name => {
        if (name && name.trim()) {
          combinedOptions.push({
            name: name.trim(),
            type: 'Sub-contractor',
            displayValue: `${name.trim()} (Sub-contractor)`
          });
        }
      });

      // Add party ledger names
      partyNames.forEach(name => {
        if (name && name.trim()) {
          combinedOptions.push({
            name: name.trim(),
            type: 'Party Ledger',
            displayValue: `${name.trim()} (Party Ledger)`
          });
        }
      });

      // Remove duplicates based on name and type combination
      const uniqueOptions = combinedOptions.filter((option, index, self) =>
        index === self.findIndex(o => o.name === option.name && o.type === option.type)
      );

      // Sort by name first, then by type
      return uniqueOptions.sort((a, b) => {
        if (a.name === b.name) {
          return a.type.localeCompare(b.type);
        }
        return a.name.localeCompare(b.name);
      });
    });

    // Filtered options based on search query
    const filteredPaidToOptions = computed(() => {
      const query = (searchQuery.value || formData.value.paidTo || '').toLowerCase().trim();

      if (!query) {
        return uniquePaidTo.value.slice(0, 10); // Show first 10 options when no search
      }

      return uniquePaidTo.value.filter(option =>
        option.name.toLowerCase().includes(query) ||
        option.type.toLowerCase().includes(query)
      ).slice(0, 20); // Show up to 20 filtered results
    });

    const uniqueCategories = computed(() => getUniqueCategories.value);
    const uniqueProjects = computed(() => getUniqueProjects.value);

    // Utility methods for styling
    const getTypeColor = (type) => {
      switch (type) {
        case 'Sub-contractor':
          return 'bg-blue-500';
        case 'Party Ledger':
          return 'bg-green-500';
        case 'Previous Expense':
          return 'bg-purple-500';
        default:
          return 'bg-gray-500';
      }
    };

    const getTypeBadgeColor = (type) => {
      switch (type) {
        case 'Sub-contractor':
          return 'bg-blue-100 text-blue-800';
        case 'Party Ledger':
          return 'bg-green-100 text-green-800';
        case 'Previous Expense':
          return 'bg-purple-100 text-purple-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    // We don't need a grouped paid-to groups computed property anymore
    // since we're using the getUniquePaidToGroups from useExpenses

    // Methods
    // Method to update category based on transaction type
    const updateCategory = () => {
      // Sync category with transaction type
      formData.value.category = formData.value.transactionType;
    };

    const handleSubmit = () => {
      // Validate deductions if enabled
      if (formData.value.hasDeductions) {
        // Check if deductions are provided
        if (!formData.value.deductions || formData.value.deductions.length === 0) {
          alert('Please add at least one deduction or disable deductions.');
          return;
        }

        // Validate each deduction
        for (let i = 0; i < formData.value.deductions.length; i++) {
          const deduction = formData.value.deductions[i];
          if (!deduction.name?.trim()) {
            alert(`Deduction #${i + 1}: Name is required.`);
            return;
          }
          if (!deduction.amount || parseFloat(deduction.amount) <= 0) {
            alert(`Deduction #${i + 1}: Amount must be greater than 0.`);
            return;
          }
        }

        // Check if total deductions exceed gross amount
        const totalDeductions = formData.value.deductions.reduce((sum, d) => sum + (parseFloat(d.amount) || 0), 0);
        const grossAmount = Math.abs(parseFloat(formData.value.amount) || 0);

        if (totalDeductions > grossAmount) {
          alert('Total deductions cannot exceed the gross amount.');
          return;
        }
      }

      // Ensure amount has the correct sign based on transaction type
      if (formData.value.transactionType === 'RECEIPT') {
        // For receipts, amount should be positive
        formData.value.amount = Math.abs(Number(formData.value.amount));
      } else {
        // For payments, amount should be negative
        formData.value.amount = -Math.abs(Number(formData.value.amount));
      }

      // Sync category with transaction type
      formData.value.category = formData.value.transactionType;

      // We'll pass the form data as is - the parent component will check
      // if the Paid To Group matches a sub's name

      emit('submit', { ...formData.value });
    };

    // Sub-expense methods have been removed

    // Method to handle paid to input changes
    const handlePaidToInput = (event) => {
      formData.value.paidTo = event.target.value;
      searchQuery.value = event.target.value;
      selectedOptionIndex.value = -1;
      showPaidToDropdown.value = true;
    };

    // Method to handle dropdown blur
    const handlePaidToBlur = () => {
      // Delay hiding dropdown to allow for option selection
      setTimeout(() => {
        showPaidToDropdown.value = false;
        selectedOptionIndex.value = -1;
      }, 150);
    };

    // Method to handle keyboard navigation
    const handlePaidToKeydown = (event) => {
      if (!showPaidToDropdown.value || filteredPaidToOptions.value.length === 0) return;

      switch (event.key) {
        case 'ArrowDown':
          event.preventDefault();
          selectedOptionIndex.value = Math.min(
            selectedOptionIndex.value + 1,
            filteredPaidToOptions.value.length - 1
          );
          break;
        case 'ArrowUp':
          event.preventDefault();
          selectedOptionIndex.value = Math.max(selectedOptionIndex.value - 1, -1);
          break;
        case 'Enter':
          event.preventDefault();
          if (selectedOptionIndex.value >= 0) {
            selectPaidToOption(filteredPaidToOptions.value[selectedOptionIndex.value]);
          }
          break;
        case 'Escape':
          showPaidToDropdown.value = false;
          selectedOptionIndex.value = -1;
          break;
      }
    };

    // Method to select an option from dropdown
    const selectPaidToOption = (option) => {
      formData.value.paidTo = option.name;
      showPaidToDropdown.value = false;
      selectedOptionIndex.value = -1;
      searchQuery.value = '';
    };

    // Method to reset the form with default values
    const resetForm = (defaultData) => {
      // Create default form data
      const defaultFormData = {
        date: new Date().toISOString().split('T')[0],
        paidTo: '',
        amount: '',
        transactionType: 'PAYMENT',
        category: 'PAYMENT',
        project: '',
        paymentMode: {
          type: 'cash',
          instrumentNo: '',
          bankId: ''
        },
        description: '',
        paidToGroup: '',
        // Dynamic deduction fields
        hasDeductions: false,
        deductions: []
      };

      // If default data is provided, merge it with the default form data
      if (defaultData) {
        // Make sure to preserve the nested paymentMode object structure
        formData.value = {
          ...defaultFormData,
          ...defaultData,
          // Ensure paymentMode is properly set
          paymentMode: {
            ...defaultFormData.paymentMode,
            ...(defaultData.paymentMode || {})
          }
        };
      } else {
        // Use the default form data
        formData.value = { ...defaultFormData };
      }

      // Force update the transaction type and payment mode
      // This is needed because radio buttons don't always update with v-model
      setTimeout(() => {
        // Ensure the transaction type radio button is properly set
        const paymentRadio = document.querySelector(`input[name="transactionType"][value="${formData.value.transactionType}"]`);
        if (paymentRadio) paymentRadio.checked = true;

        // Ensure the payment mode radio button is properly set
        if (formData.value.paymentMode.type === 'cash') {
          const cashRadio = document.querySelector(`input#cash[value="cash"]`);
          if (cashRadio) cashRadio.checked = true;
        } else if (formData.value.paymentMode.type === 'bank') {
          const bankRadio = document.querySelector(`input#bank[value="bank"]`);
          if (bankRadio) bankRadio.checked = true;
        }
      }, 0);
    };

    // Initialize
    onMounted(async () => {
      try {
        // Fetch all data in parallel for efficiency
        await Promise.all([
          fetchExpenses(),
          fetchLedgers(),
          fetchSubsModels()
        ]);

        // Check if we have any paid-to groups
        // No console logging needed

        // Add event listener for form reset
        const form = document.querySelector('form');
        const resetFormHandler = (event) => {
          resetForm(event.detail);
        };

        if (form) {
          form.addEventListener('reset-form', resetFormHandler);
        }

        // Clean up event listener when component is unmounted
        onUnmounted(() => {
          if (form) {
            form.removeEventListener('reset-form', resetFormHandler);
          }
        });

        // If editing an existing expense, populate the form
        if (props.expense) {
          formData.value = {
            date: new Date(props.expense.date).toISOString().split('T')[0],
            paidTo: props.expense.paidTo,
            amount: props.expense.amount,
            transactionType: props.expense.category || 'PAYMENT',
            category: props.expense.category || 'PAYMENT',
            project: props.expense.project || '',
            paymentMode: {
              type: props.expense.paymentMode.type,
              instrumentNo: props.expense.paymentMode.instrumentNo || '',
              bankId: props.expense.paymentMode.bankId || ''
            },
            description: props.expense.description || '',
            paidToGroup: props.expense.paidToGroup || '',
            // Dynamic deduction fields
            hasDeductions: props.expense.hasDeductions || false,
            deductions: props.expense.deductions || [],
            subExpenses: props.expense.subExpenses ? props.expense.subExpenses.map(sub => ({
              date: new Date(sub.date).toISOString().split('T')[0],
              paidTo: sub.paidTo,
              amount: sub.amount,
              category: sub.category || '',
              project: sub.project || '',
              description: sub.description || ''
            })) : []
          };
        }
      } catch (error) {

      }
    });

    // Watch for paidToGroup has been removed

    return {
      formData,
      uniquePaidTo,
      filteredPaidToOptions,
      uniqueCategories,
      uniqueProjects,
      getUniquePaidToGroups, // Expose the unique paidToGroups from expenses
      bankLedgers,
      partyLedgers,
      partyLedgerNames,
      showPaidToDropdown,
      selectedOptionIndex,
      searchQuery,
      updateCategory,
      handleSubmit,
      handlePaidToInput,
      handlePaidToBlur,
      handlePaidToKeydown,
      selectPaidToOption,
      getTypeColor,
      getTypeBadgeColor,
      resetForm,
      ChevronDownIcon
    };
  }
};
</script>

<style scoped>
/* Custom dropdown styles */
.dropdown-enter-active, .dropdown-leave-active {
  transition: all 0.2s ease;
}

.dropdown-enter-from, .dropdown-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Custom scrollbar for dropdown */
.max-h-48::-webkit-scrollbar {
  width: 6px;
}

.max-h-48::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.max-h-48::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.max-h-48::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Smooth hover transitions */
.cursor-pointer {
  transition: all 0.2s ease-in-out;
}

.cursor-pointer:hover {
  transform: translateX(2px);
}

/* Focus ring for accessibility */
.cursor-pointer:focus {
  outline: 2px solid #6366f1;
  outline-offset: -2px;
}

/* Enhanced hover gradient effect */
.hover\:from-yellow-50:hover {
  background: linear-gradient(90deg, #fefce8 0%, #f0fdf4 100%);
}

/* Header gradient enhancement */
.bg-gradient-to-r.from-indigo-500.to-purple-600 {
  background: linear-gradient(90deg, #6366f1 0%, #8b5cf6 50%, #a855f7 100%);
}

/* Professional shadow for dropdown */
.shadow-lg {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}
</style>
