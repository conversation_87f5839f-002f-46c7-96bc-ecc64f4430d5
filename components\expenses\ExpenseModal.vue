<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-2 sm:p-4">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
      <!-- Modal Header with Gradient -->
      <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 flex justify-between items-center">
        <h3 class="text-lg font-medium text-white">{{ title }}</h3>
        <button
          @click="close"
          class="text-white hover:text-gray-200 focus:outline-none"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Body with Scrollable Content -->
      <div class="flex-1 overflow-y-auto p-4">
        <ExpenseForm
          :expense="expense"
          :is-loading="isLoading"
          @submit="handleSubmit"
          @cancel="close"
          :show-footer-buttons="false"
        />
      </div>

      <!-- Modal Footer with Gradient -->
      <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 flex justify-end space-x-4">
        <button
          type="button"
          @click="close"
          class="px-4 py-2 border border-white rounded-md shadow-sm text-sm font-medium text-white bg-transparent hover:bg-white hover:bg-opacity-10"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="button"
          @click="submitForm"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-indigo-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-700 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>{{ expense ? 'Update' : 'Save' }}</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useSubs } from '~/composables/expenses/useSubs';
import useToast from '~/composables/ui/useToast';

export default {
  name: 'ExpenseModal',

  props: {
    show: {
      type: Boolean,
      default: false
    },
    expense: {
      type: Object,
      default: null
    },
    title: {
      type: String,
      default: 'Add New Expense'
    }
  },

  emits: ['close', 'saved'],

  setup(props, { emit }) {
    // State
    const isLoading = ref(false);
    const error = ref(null);
    const formRef = ref(null);

    // Get composables
    const { createExpense, updateExpense } = useExpenses();
    const { createSub, fetchSubsModels, subsModels } = useSubs();
    const toast = useToast();

    // Methods
    const close = () => {
      emit('close');
    };

    // Method to trigger form submission from footer
    const submitForm = () => {
      // This will be called from the footer button
      // We'll simulate a submit event on the form
      document.querySelector('form').dispatchEvent(new Event('submit'));
    };

    const handleSubmit = async (formData) => {
      try {
        isLoading.value = true;
        error.value = null;


        // First, fetch the subs models to check if the Paid To/From matches a sub's name
        await fetchSubsModels();

        // Check if the Paid To/From value matches a sub's name
        const matchingSub = subsModels.value.find(sub => sub.name === formData.paidTo);

        if (matchingSub) {

          // Create a flag to indicate that we need to create a subs entry
          formData.createSubsEntry = true;
          formData.matchingSubId = matchingSub.id;
          formData.matchingSubName = matchingSub.name;
        }

        let response;

        // Create or update the expense
        if (props.expense) {
          response = await updateExpense(props.expense.id, formData);
        } else {
          response = await createExpense(formData);
        }

        // If we found a matching sub, create a subs entry as well
        if (matchingSub) {

          // Create a subs entry (RECEIPT)
          const subsData = {
            date: formData.date,
            paidTo: formData.paidTo,
            amount: Math.abs(Number(formData.amount)), // Make amount positive for RECEIPT (green)
            category: 'RECEIPT', // Always RECEIPT for subs entries from expenses
            project: formData.project || null,
            description: `Auto-created from expense: ${formData.description || 'No description'}`,
            subId: matchingSub.id,
            subName: matchingSub.name
          };

          await createSub(subsData);
        }

        // No need to show toast here as the parent component will handle it

        // Emit saved event
        emit('saved', response);

        // Reset the form but don't close the modal
        // Create default form data with proper structure
        const defaultFormData = {
          date: new Date().toISOString().split('T')[0],
          paidTo: '',
          amount: '',
          transactionType: 'PAYMENT',
          category: 'PAYMENT',
          project: '',
          paymentMode: {
            type: 'cash',
            instrumentNo: '',
            bankId: ''
          },
          description: '',
          paidToGroup: '',
          // Dynamic deduction fields
          hasDeductions: false,
          deductions: []
        };

        // Find the ExpenseForm component and reset its form data
        const expenseForm = document.querySelector('form');
        if (expenseForm) {
          // First reset the HTML form to clear all inputs
          expenseForm.reset();

          // Then dispatch a custom event to reset the Vue form data
          expenseForm.dispatchEvent(new CustomEvent('reset-form', { detail: defaultFormData }));
        }
      } catch (err) {
        error.value = err.message || 'Failed to save expense';
        // Let the parent component handle error toasts
      } finally {
        isLoading.value = false;
      }
    };

    return {
      isLoading,
      error,
      close,
      handleSubmit,
      submitForm,
      formRef
    };
  }
};
</script>
