<template>
  <div class="container mx-auto px-4 py-4 pb-12">
    <!-- Hero Section -->
    <div
      class="bg-gradient-to-br from-emerald-500 to-teal-600 py-12 px-6 rounded-2xl shadow-xl mb-12 relative overflow-hidden">
      <div class="absolute inset-0 bg-pattern opacity-10"></div>
      <div class="max-w-3xl mx-auto text-center relative z-10">
        <h1 class="text-4xl font-extrabold text-white sm:text-5xl tracking-tight">
          Document Management
        </h1>
        <p class="mt-4 text-xl text-teal-50 max-w-2xl mx-auto">
          Manage your important documents and track their validity
        </p>
      </div>
    </div>

    <!-- Document Management Interface -->
    <div class="bg-white rounded-xl shadow-md p-6 mb-8">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-2xl font-bold text-slate-800">Your Documents</h2>
        <div class="flex gap-4">
          <button @click="triggerNotifications"
            class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition-colors duration-300 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
            </svg>
            Send Notifications
          </button>
          <button @click="() => showAddDocumentModal = true"
            class="bg-emerald-500 text-white px-4 py-2 rounded-lg hover:bg-emerald-600 transition-colors duration-300 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            Add Document
          </button>
          <button @click="downloadDocuments"
            class="bg-orange-500 text-white px-4 py-2 rounded-lg hover:bg-orange-600 transition-colors duration-300 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M12 16v-8m0 8l-4-4m4 4l4-4M4 20h16" />
            </svg>
            Download
          </button>
        </div>
      </div>

      <!-- Warning Banner for Expiring Documents -->
      <div v-if="expiringDocuments.length > 0" class="bg-amber-50 border-l-4 border-amber-500 p-3 sm:p-4 mb-4 sm:mb-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-amber-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"
              fill="currentColor">
              <path fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-xs sm:text-sm text-amber-700">
              <span class="font-medium">Warning:</span> You have {{ expiringDocuments.length }} document(s) expiring
              soon.
            </p>
          </div>
        </div>
      </div>

      <!-- ✅ FIX: Search Controls - Always visible, moved outside conditional blocks -->
      <div class="flex flex-col md:flex-row justify-between items-center mb-4 space-y-3 md:space-y-0 gap-3">
        <div class="w-full md:w-64">
          <input v-model="searchQuery" type="text" placeholder="Search documents..."
            ref="searchInput"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500" />
        </div>
        <div class="flex space-x-2 w-full md:w-auto">
          <select v-model="sortBy"
            class="w-full md:w-auto px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500">
            <option value="name">Sort by Name</option>
            <option value="ref_no">Sort by Reference</option>
            <option value="expiryDate">Sort by Expiry Date</option>
            <option value="startDate">Sort by Start Date</option>
            <option value="value">Sort by Value</option>
          </select>
          <button @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'"
            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 bg-white">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500"
              :class="{ 'transform rotate-180': sortDirection === 'desc' }" fill="none" viewBox="0 0 24 24"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" />
            </svg>
          </button>
          <!-- ✅ FIX: Clear search button for better UX -->
          <button v-if="searchQuery" @click="clearSearch"
            class="px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-emerald-500 focus:border-emerald-500 bg-white text-gray-500 hover:text-gray-700"
            title="Clear search">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Document List -->
      <div v-if="isLoading" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-10 w-10 sm:h-12 sm:w-12 border-t-2 border-b-2 border-emerald-500"></div>
      </div>

      <!-- ✅ FIX: Enhanced empty state with search context -->
      <div v-else-if="documents.length === 0" class="text-center py-12 bg-gray-50 rounded-lg">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>

        <!-- ✅ FIX: Different messages for search vs empty state -->
        <div v-if="searchQuery">
          <h3 class="mt-2 text-sm font-medium text-gray-900">No documents found</h3>
          <p class="mt-1 text-sm text-gray-500">No documents match your search for "{{ searchQuery }}"</p>
          <div class="mt-6 space-x-3">
            <button @click="clearSearch"
              class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
              Clear Search
            </button>
            <button @click="showAddDocumentModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-emerald-500 hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Add Document
            </button>
          </div>
        </div>

        <!-- ✅ FIX: Original empty state when no search is active -->
        <div v-else>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No documents</h3>
          <p class="mt-1 text-sm text-gray-500">Get started by adding a new document.</p>
          <div class="mt-6">
            <button @click="showAddDocumentModal = true"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-emerald-500 hover:bg-emerald-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Add Document
            </button>
          </div>
        </div>
      </div>

      <div v-else>

        <!-- Documents Table -->
        <div class="overflow-x-auto -mx-4 sm:-mx-0">
          <div class="inline-block min-w-full py-2 px-4 sm:px-0 align-middle">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document Name
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                    Reference Number
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                    Description
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">
                    Start Date
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expiry Date
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">
                    Value
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="doc in documents" :key="doc._id" class="hover:bg-gray-50">
                  <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap">
                    <div class="text-xs sm:text-sm font-medium text-gray-900">{{ doc.name }}</div>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 hidden sm:table-cell">
                    <div class="text-xs sm:text-sm text-gray-500 break-words max-w-[200px]">{{ doc.ref_no }}</div>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 hidden md:table-cell">
                    <div class="text-xs sm:text-sm text-gray-500 break-words max-w-[300px]">{{ doc.description || 'N/A' }}</div>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap hidden sm:table-cell">
                    <div class="text-xs sm:text-sm text-gray-500">{{ formatDate(doc.startDate) }}</div>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap">
                    <div class="text-xs sm:text-sm text-gray-500">{{ formatDate(doc.expiryDate) }}</div>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap hidden md:table-cell">
                    <div class="text-xs sm:text-sm text-gray-500">{{ doc.value ? formatCurrency(doc.value) : 'N/A' }}</div>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap">
                    <span :class="{
                      'bg-red-100 text-red-800': isExpired(doc),
                      'bg-amber-100 text-amber-800': isExpiringSoon(doc),
                      'bg-green-100 text-green-800': !isExpiringSoon(doc) && !isExpired(doc)
                    }" class="px-2 py-1 text-xs rounded-full">
                      {{ getExpiryStatus(doc) }}
                    </span>
                  </td>
                  <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                    <div class="flex items-center justify-end space-x-2 sm:space-x-3">
                      <!-- View/Download File Button (only shown if file exists) -->
                      <a v-if="doc.file" :href="doc.file" target="_blank" class="text-green-600 hover:text-green-800 inline-flex items-center" title="View/Download File">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      </a>
                      <!-- Edit Button -->
                      <button @click="editDocument(doc)" class="text-blue-600 hover:text-blue-800 inline-flex items-center" title="Edit Document">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                      <!-- Delete Button -->
                      <button @click="confirmDeleteDocument(doc)" class="text-red-600 hover:text-red-800 inline-flex items-center" title="Delete Document">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 sm:h-5 sm:w-5" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>

        <!-- Pagination Controls -->
        <div class="flex flex-col sm:flex-row justify-between items-center mt-4 gap-3">
          <div class="text-xs sm:text-sm text-gray-700 order-2 sm:order-1">
            Showing <span class="font-medium">{{ paginationStart }}</span> to <span class="font-medium">{{ paginationEnd
            }}</span> of <span class="font-medium">{{ totalDocumentsCount }}</span> documents
          </div>
          <div class="flex space-x-2 order-1 sm:order-2">
            <button @click="goToPreviousPage()" :disabled="currentPage === 1"
              :class="{ 'opacity-50 cursor-not-allowed': currentPage === 1 }"
              class="px-3 py-1 border border-gray-300 rounded-md text-xs sm:text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Previous
            </button>
            <button @click="goToNextPage()" :disabled="currentPage >= totalPages"
              :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }"
              class="px-3 py-1 border border-gray-300 rounded-md text-xs sm:text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
              Next
            </button>
          </div>
        </div>
      </div>
    </div>

    <div v-if="showAddDocumentModal || showEditDocumentModal"
      class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Header -->
        <div
          class="px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-green-400 via-blue-500 to-green-400 text-white flex justify-between items-center">
          <h3 class="text-base sm:text-lg font-medium">
            {{ showEditDocumentModal ? 'Edit Document' : 'Add New Document' }}
          </h3>
          <button @click="cancelDocumentForm" class="text-white hover:text-gray-200 focus:outline-none">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 sm:h-6 sm:w-6" fill="none" viewBox="0 0 24 24"
              stroke="currentColor" stroke-width="2">
              <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="showEditDocumentModal ? updateDocument() : addDocument()">
          <div class="px-4 sm:px-6 py-4">
            <!-- Document Name -->
            <div class="mb-4">
              <label for="documentName" class="block text-sm font-medium text-gray-700 mb-1">
                Document Name*
              </label>
              <input id="documentName" v-model="documentForm.name" type="text" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
            </div>

            <!-- Reference Number -->
            <div class="mb-4">
              <label for="ref_no" class="block text-sm font-medium text-gray-700 mb-1">
                Reference Number*
              </label>
              <input id="ref_no" v-model="documentForm.ref_no" type="text" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
            </div>
            <!-- Description -->
            <div class="mb-4">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-1">
                Description*
              </label>
              <textarea id="description" v-model="documentForm.description" rows="3" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 items-start">
              <div>
                <!-- Start Date -->
                <div class="mb-4">
                  <label for="startDate" class="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input id="startDate" v-model="documentForm.startDate" type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <!-- Closed Date -->
                <div class="mb-4">
                  <label for="closedDate" class="block text-sm font-medium text-gray-700 mb-1">
                    Closed Date
                  </label>
                  <input id="closedDate" v-model="documentForm.closedDate" type="date"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                </div>
              </div>
              <div>
                <!-- Original Expiry Date -->
                <div class="mb-4">
                  <label for="oExpiryDate" class="block text-sm font-medium text-gray-700 mb-1">
                    Original Expiry Date*
                  </label>
                  <input id="oExpiryDate" v-model="documentForm.oExpiryDate" type="date" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                </div>
                <!-- Expiry Date -->
                <div class="mb-4">
                  <label for="expiryDate" class="block text-sm font-medium text-gray-700 mb-1">
                    Extended Expiry Date*
                  </label>
                  <input id="expiryDate" v-model="documentForm.expiryDate" type="date" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 items-start">
              <div>
                <!-- Value -->
                <div class="mb-4">
                  <label for="value" class="block text-sm font-medium text-gray-700 mb-1">
                    Value*
                  </label>
                  <input id="value" v-model="documentForm.value" type="number" step="0.01" min="0" required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                </div>
              </div>
              <div>
                <!-- Status -->
                <div class="mb-4">
                  <label for="status" class="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select id="status" v-model="documentForm.status"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="" disabled>Select status</option>
                    <option value="OPEN">OPEN</option>
                    <option value="RUNNING">RUNNING</option>
                    <option value="CLOSED">CLOSED</option>
                    <option value="TERMINATED">TERMINATED</option>
                  </select>
                </div>
              </div>
            </div>
            <!-- File Upload -->
            <div class="mb-4">
              <label for="fileUpload" class="block text-sm font-medium text-gray-700 mb-1">
                File Upload
              </label>
              <input id="fileUpload" type="file" ref="fileInput" @change="handleFileUpload"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />

              <!-- Current File Information -->
              <div v-if="documentForm.file" class="mt-3 p-3 border border-gray-200 rounded-md bg-gray-50">
                <div class="flex items-center justify-between">
                  <div>
                    <h4 class="text-sm font-medium text-gray-700">Current File</h4>
                    <p class="text-xs text-gray-500 mt-1 truncate max-w-xs">File ID: {{ documentForm.fileId || 'Not available' }}</p>
                  </div>
                  <div class="flex space-x-2">
                    <a :href="documentForm.file" target="_blank" class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                      </svg>
                      View
                    </a>
                  </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">Uploading a new file will replace the current one.</p>
              </div>

              <!-- Selected File Information -->
              <div v-if="selectedFile" class="mt-3 p-3 border border-green-200 rounded-md bg-green-50">
                <h4 class="text-sm font-medium text-gray-700">New File Selected</h4>
                <p class="text-xs text-gray-600 mt-1">{{ selectedFile.name }} ({{ formatFileSize(selectedFile.size) }})</p>
              </div>
            </div>
          </div>

          <!-- Footer -->
          <div class="px-4 sm:px-6 py-3 sm:py-4 bg-gradient-to-r from-green-400 via-blue-500 to-green-400 flex justify-end space-x-3">
            <button type="button" @click="cancelDocumentForm"
              class="px-3 sm:px-4 py-1.5 sm:py-2 border rounded-md shadow-sm text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-orange-400 to-red-500 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
              Cancel
            </button>
            <button type="submit"
              class="px-3 sm:px-4 py-1.5 sm:py-2 border rounded-md shadow-sm text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-indigo-500 to-purple-600 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 flex items-center justify-center min-w-[80px]"
              :disabled="isSubmitting">
              <svg v-if="isSubmitting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ showEditDocumentModal ? 'Update' : 'Add' }}
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal"
      class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50 p-4">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full">
        <div class="px-4 sm:px-6 py-3 sm:py-4 border-b border-gray-200">
          <h3 class="text-base sm:text-lg font-medium text-gray-900">Confirm Deletion</h3>
        </div>
        <div class="px-4 sm:px-6 py-3 sm:py-4">
          <p class="text-sm sm:text-base text-gray-700">Are you sure you want to delete this document? This action cannot be undone.</p>
          <div class="flex justify-end space-x-3 mt-4 sm:mt-6">
            <button @click="showDeleteModal = false"
              class="px-3 sm:px-4 py-1.5 sm:py-2 border border-gray-300 rounded-md shadow-sm text-xs sm:text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-emerald-500">
              Cancel
            </button>
            <button @click="deleteDocument"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              Delete
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Toast Notification -->
    <Toast :message="toastMessage" :type="toastType" :show="showToast" @close="showToast = false" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, onUnmounted, nextTick } from 'vue'
import { useRouter } from '#app'
import { useCookie } from '#app';
import downloadExcel from '~/composables/utils/downloadExcel';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import useAuthRefresh from '~/composables/auth/useAuthRefresh';
import useCsrf from '~/composables/auth/useCsrf';

definePageMeta({
  requiresAuth: true,
});

// Router
const router = useRouter()

// Auth refresh
const { isTokenExpired, refreshAccessToken, getToken, getRefreshToken } = useAuthRefresh();
const tokenCheckInterval = ref(null);

// Function to check token validity and refresh if needed
const checkTokenValidity = async () => {
  const token = getToken();
  const refreshToken = getRefreshToken();

  if (!token || !refreshToken) {
    console.warn('Missing authentication tokens');
    router.push('/login');
    return;
  }

  if (isTokenExpired(token)) {
    const newToken = await refreshAccessToken(refreshToken);

    if (!newToken) {
      console.warn('Token refresh failed, redirecting to login');
      router.push('/login');
    }
  }
};

// Set page title
usePageTitle('Documents', 'Manage your important documents and track their validity');

// State
const documents = ref([])
const isLoading = ref(true)
const isSubmitting = ref(false)
const totalDocumentsCount = ref(0)
const showAddDocumentModal = ref(false)
const showEditDocumentModal = ref(false)
const showDeleteModal = ref(false)
const documentToDelete = ref(null)
const documentToEdit = ref(null)
const searchQuery = ref('')
const sortBy = ref('expiryDate')
const sortDirection = ref('asc')
const currentPage = ref(1)
const itemsPerPage = ref(10)
const showToast = ref(false)
const toastMessage = ref('')
const toastType = ref('success')
const selectedFile = ref(null)
const fileInput = ref(null)
const searchInput = ref(null)

// Document form
const documentForm = ref({
  id: null,
  name: '',
  ref_no: '',
  description: 'No description provided',
  startDate: '',
  expiryDate: '',
  oExpiryDate: '',
  closedDate: '',
  value: 0, // Set default value to 0 instead of null
  status: '',
  file: '',
  fileId: '', // Google Drive file ID
})

// Computed properties
// No longer needed since we're using server-side filtering
// const filteredDocuments = computed(() => {
//   if (!searchQuery.value) return documents.value
//
//   const query = searchQuery.value.toLowerCase()
//   return documents.value.filter(doc =>
//     doc.name.toLowerCase().includes(query) ||
//     doc.ref_no.toLowerCase().includes(query) ||
//     (doc.description && doc.description.toLowerCase().includes(query))
//   )
// })

// No longer needed since we're using server-side sorting
// const sortedDocuments = computed(() => {
//   const docs = [...filteredDocuments.value]
//
//   return docs.sort((a, b) => {
//     let aValue = a[sortBy.value]
//     let bValue = b[sortBy.value]
//
//     // Handle special cases for sorting
//     if (sortBy.value === 'value') {
//       aValue = aValue || 0
//       bValue = bValue || 0
//     } else if (sortBy.value === 'startDate' || sortBy.value === 'expiryDate') {
//       aValue = aValue ? new Date(aValue).getTime() : 0
//       bValue = bValue ? new Date(bValue).getTime() : 0
//     } else {
//       aValue = aValue ? aValue.toString().toLowerCase() : ''
//       bValue = bValue ? bValue.toString().toLowerCase() : ''
//     }
//
//     if (sortDirection.value === 'asc') {
//       return aValue > bValue ? 1 : -1
//     } else {
//       return aValue < bValue ? 1 : -1
//     }
//   })
// })

// No longer needed since we're using server-side pagination
// const filteredAndSortedDocuments = computed(() => {
//   const startIndex = (currentPage.value - 1) * itemsPerPage.value
//   const endIndex = startIndex + itemsPerPage.value
//   return sortedDocuments.value.slice(startIndex, endIndex)
// })

const totalPages = computed(() => {
  // If we have data from the server, use that for total pages calculation
  if (totalDocumentsCount.value > 0) {
    // Ensure we always have at least 1 page, even if there are no documents
    return Math.max(1, Math.ceil(totalDocumentsCount.value / itemsPerPage.value))
  }
  // Fallback to client-side calculation (should rarely be used now)
  return Math.max(1, Math.ceil(documents.value.length / itemsPerPage.value))
})

const paginationStart = computed(() => {
  // If we have no documents, start from 0
  if (totalDocumentsCount.value === 0 || documents.value.length === 0) {
    return 0
  }
  // Otherwise, calculate the start index based on current page
  return (currentPage.value - 1) * itemsPerPage.value + 1
})

const paginationEnd = computed(() => {
  // If we have no documents, end at 0
  if (totalDocumentsCount.value === 0 || documents.value.length === 0) {
    return 0
  }
  // For server-side pagination, calculate based on current page and actual documents returned
  const calculatedEnd = (currentPage.value - 1) * itemsPerPage.value + documents.value.length
  // Return the minimum of calculated end and total documents
  return Math.min(calculatedEnd, totalDocumentsCount.value)
})

const expiringDocuments = computed(() => {
  const today = new Date()
  const thirtyDaysFromNow = new Date()
  thirtyDaysFromNow.setDate(today.getDate() + 30)

  return documents.value.filter(doc => {
    if (!doc.expiryDate) return false
    const expiryDate = new Date(doc.expiryDate)
    return expiryDate > today && expiryDate <= thirtyDaysFromNow
  })
})

// Debounce timer for search
const searchDebounceTimer = ref(null)

// Debounced search function
const debouncedSearch = () => {
  // Clear any existing timer
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
  }

  // Set a new timer
  searchDebounceTimer.value = setTimeout(() => {
    currentPage.value = 1
    fetchDocuments()
  }, 500) // 500ms delay
}

// ✅ FIX: Clear search function
const clearSearch = () => {
  searchQuery.value = ''
  currentPage.value = 1
  // fetchDocuments will be called automatically by the watch on searchQuery
}

// Watch for changes in search query to reset pagination and fetch new data
watch(searchQuery, () => {
  debouncedSearch()
})

// Watch for changes in sort options to fetch new data
watch([sortBy, sortDirection], () => {
  fetchDocuments()
})

// Pagination navigation functions
function goToPreviousPage() {
  if (currentPage.value > 1) {
    currentPage.value--
    fetchDocuments()
  }
}

function goToNextPage() {
  // Calculate the maximum safe page number
  const maxPage = Math.max(1, Math.ceil(totalDocumentsCount.value / itemsPerPage.value));

  // Only go to next page if we're not already at the last page
  if (currentPage.value < maxPage) {
    currentPage.value++
    fetchDocuments()
  } else {
    // If we're trying to go beyond the last page, show a message
    showToastNotification('You are already on the last page', 'info');
  }
}

// Watch for token changes and check validity
watch(() => getToken(), async (newToken) => {
  if (newToken) {
    // Check if the token is valid or needs to be refreshed
    if (isTokenExpired(newToken)) {
      await checkTokenValidity()
    }
  } else {
    // If token is removed, redirect to login
    router.push('/login')
  }
})

// Methods
async function triggerNotifications() {
  try {    // Use the API with Auth composable
    const { post } = useApiWithAuth();

    // Make the authenticated request
    const responseData = await post('/api/documents/sndmlnot');

    showToastNotification(responseData.message || 'Notifications sent successfully', 'success');
  } catch (error) {
    console.error('Error sending notifications:', error);
    showToastNotification(error.message || 'Failed to send notifications', 'error');
  }
}

async function fetchDocuments() {
  isLoading.value = true

  // Store the current focus state of the search input
  const wasSearchFocused = document.activeElement === searchInput.value

  try {
    // Use the API with Auth composable
    const { get } = useApiWithAuth();

    // Create query parameters for pagination, search, and sorting
    const queryParams = new URLSearchParams({
      page: currentPage.value,
      limit: itemsPerPage.value,
      search: searchQuery.value,
      sortBy: sortBy.value,
      sortDirection: sortDirection.value
    }).toString();

    // Make the authenticated request with automatic token refresh and pagination
    const data = await get(`/api/documents?${queryParams}`);

    // Map API response to match frontend field names
    if (data.documents) {
      documents.value = data.documents.map(doc => ({
        ...doc,
        ref_no: doc.ref_no,
      }));

      // Update pagination data if available
      if (data.pagination) {
        // Update total pages from server response
        const totalPagesFromServer = data.pagination.totalPages;
        if (totalPagesFromServer > 0) {
          // Only update if we have valid data
          itemsPerPage.value = data.pagination.limit;
          totalDocumentsCount.value = data.pagination.total;

          // If the server adjusted our page number (e.g., we requested page 3 but there are only 2 pages),
          // update our local page number to match
          if (data.pagination.requestedPage !== data.pagination.page) {
            currentPage.value = data.pagination.page;
          }

          // If we still got an empty result but we're not on the first page,
          // go back to the previous page and try again
          if (documents.value.length === 0 && currentPage.value > 1) {
            currentPage.value--;
            // Call fetchDocuments again with a slight delay to avoid infinite loops
            setTimeout(() => fetchDocuments(), 100);
            return;
          }
        }
      }
    } else {
      documents.value = [];
    }
  } catch (error) {
    showToastNotification('Failed to load documents', 'error');
  } finally {
    isLoading.value = false;

    // If the search input was focused before, restore focus after the table reloads
    if (wasSearchFocused && searchInput.value) {
      // Use nextTick to ensure the DOM has updated before trying to focus
      nextTick(() => {
        searchInput.value.focus()
      })
    }
  }
}

function handleFileUpload(event) {
  const file = event.target.files[0];

  // Check if file size exceeds 500KB (512000 bytes)
  if (file && file.size > 512000) {
    showToastNotification('File size exceeds maximum limit of 500KB. Please select a smaller file.', 'error');
    // Reset the file input
    event.target.value = '';
    return;
  }

  selectedFile.value = file;
}

function resetDocumentForm() {
  documentForm.value = {
    id: null,
    name: '',
    ref_no: '',
    description: 'No description provided',
    startDate: '',
    expiryDate: '',
    oExpiryDate: '',
    closedDate: '',
    value: 0, // Set default value to 0 instead of null
    status: '',
    file: '',
    fileId: '', // Google Drive file ID
  }
}

function cancelDocumentForm() {
  resetDocumentForm()
  showAddDocumentModal.value = false
  showEditDocumentModal.value = false
  // Reset the file input and selected file
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  selectedFile.value = null
}

function editDocument(doc) {
  documentToEdit.value = doc
  documentForm.value = {
    id: doc._id,
    name: doc.name,
    ref_no: doc.ref_no,
    description: doc.description || '',
    startDate: doc.startDate ? doc.startDate.slice(0, 10) : '',
    expiryDate: doc.expiryDate ? doc.expiryDate.slice(0, 10) : '',
    oExpiryDate: doc.oExpiryDate ? doc.oExpiryDate.slice(0, 10) : '',
    closedDate: doc.closedDate ? doc.closedDate.slice(0, 10) : '',
    value: doc.value || null,
    status: doc.status,
    file: doc.file || null,
    fileId: doc.fileId || '' // Include the Google Drive file ID
  }
  showEditDocumentModal.value = true
}

function confirmDeleteDocument(doc) {
  documentToDelete.value = doc
  showDeleteModal.value = true
}

async function addDocument() {
  isSubmitting.value = true;
  try {
    // Use the API with Auth composable
    const { post } = useApiWithAuth();

    // First upload the file to Google Drive if a file is selected
    let fileUrl = '';
    let fileId = ''; // Define fileId outside the if block

    if (selectedFile.value) {

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', selectedFile.value);

      // Use the API with Auth composable for file upload
      const { post } = useApiWithAuth();

      // Use the special uploadFile method that handles FormData correctly
      const uploadResponse = await post('/api/documents/uplddoc', formData, {
        // Set isFormData to true to handle FormData correctly
        isFormData: true
      });

      // Get the file URL and fileId from the response
      // The uplddoc endpoint returns the URL in the fileName field
      fileUrl = uploadResponse.fileName || uploadResponse.fileUrl;
      fileId = uploadResponse.fileId || '';
    }

    // Then create the document with the file URL and fileId
    const documentData = {
      ...documentForm.value,
      file: fileUrl || documentForm.value.file, // Use the uploaded file URL or keep existing one
      fileId: fileId || documentForm.value.fileId, // Store the Google Drive file ID
      value: documentForm.value.value || 0, // Ensure value is always a number
      description: documentForm.value.description || 'No description provided' // Ensure description is always set
    };

    // Make the authenticated POST request
    const newDocument = await post('/api/documents', documentData);

    // Add the new document to the array
    documents.value.push(newDocument);

    showToastNotification('Document added successfully', 'success');
    cancelDocumentForm();
    // Refresh documents list
    await fetchDocuments();
  } catch (error) {
    console.error('Error adding document:', error);
    // Provide more detailed error message
    let errorMessage = 'Failed to add document';
    if (error.message) {
      errorMessage = error.message;
    } else if (error.data && error.data.message) {
      errorMessage = error.data.message;
    }
    showToastNotification(errorMessage, 'error');
  } finally {
    isSubmitting.value = false;
  }
}

async function updateDocument() {
  isSubmitting.value = true;
  try {
    // Use the API with Auth composable
    const { put, post } = useApiWithAuth();

    // Keep track of existing file info
    let fileUrl = documentForm.value.file; // Keep existing file URL by default
    let fileId = documentForm.value.fileId; // Keep existing file ID by default

    // If a new file is selected, delete the old file first (if it exists)
    if (selectedFile.value && documentForm.value.fileId) {

      try {
        // Delete the old file from Google Drive
        await post('/api/documents/delete-file', {
          fileId: documentForm.value.fileId
        });
      } catch (deleteError) {
        console.error('Error deleting old file:', deleteError);
        // Continue with the update even if deletion fails
      }
    }
    if (selectedFile.value) {

      // Create a FormData object to send the file
      const formData = new FormData();
      formData.append('file', selectedFile.value);

      // Use the API with Auth composable for file upload
      const { post } = useApiWithAuth();

      // Use the special uploadFile method that handles FormData correctly
      const uploadResponse = await post('/api/documents/uplddoc', formData, {
        // Set isFormData to true to handle FormData correctly
        isFormData: true
      });

      // Get the file URL and fileId from the response
      // The uplddoc endpoint returns the URL in the fileName field
      fileUrl = uploadResponse.fileName || uploadResponse.fileUrl;
      fileId = uploadResponse.fileId || fileId; // Use the new fileId or keep the existing one
    }

    // Update document data with file URL and fileId
    const documentData = {
      ...documentForm.value,
      file: fileUrl, // Use the uploaded file URL or keep existing one
      fileId: fileId, // Store the Google Drive file ID
      value: documentForm.value.value || 0, // Ensure value is always a number
      description: documentForm.value.description || 'No description provided' // Ensure description is always set
    };

    // Make the authenticated PUT request
    const updatedDocument = await put(`/api/documents/${documentForm.value.id}`, documentData);

    // Update the document in the array
    const index = documents.value.findIndex(doc => doc._id === updatedDocument._id);
    if (index !== -1) {
      documents.value[index] = updatedDocument;
    }

    showToastNotification('Document updated successfully', 'success');
    cancelDocumentForm();
  } catch (error) {
    console.error('Error updating document:', error);
    // Provide more detailed error message
    let errorMessage = 'Failed to update document';
    if (error.message) {
      errorMessage = error.message;
    } else if (error.data && error.data.message) {
      errorMessage = error.data.message;
    }
    showToastNotification(errorMessage, 'error');
  } finally {
    isSubmitting.value = false;
  }
}



async function deleteDocument() {
  try {
    // Use the API with Auth composable
    const { delete: del, post } = useApiWithAuth();

    // If the document has a file in Google Drive, delete it first
    if (documentToDelete.value.fileId) {

      try {
        // Delete the file from Google Drive
        await post('/api/documents/delete-file', {
          fileId: documentToDelete.value.fileId
        });
      } catch (deleteError) {
        console.error('Error deleting file from Google Drive:', deleteError);
        // Continue with document deletion even if file deletion fails
      }
    }

    // Make the authenticated DELETE request to remove the document from the database
    await del(`/api/documents/${documentToDelete.value._id}`);

    // Remove the document from the array
    documents.value = documents.value.filter(doc => doc._id !== documentToDelete.value._id);

    showToastNotification('Document deleted successfully', 'success');
    showDeleteModal.value = false;
  } catch (error) {
    console.error('Error deleting document:', error);
    showToastNotification('Failed to delete document', 'error');
  }
}

function formatDate(dateString) {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

function formatCurrency(value) {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 2
  }).format(value)
}

// Format file size in bytes to a human-readable format
function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function isExpired(doc) {
  if (!doc.expiryDate) return false
  return new Date(doc.expiryDate) < new Date()
}

function isExpiringSoon(doc) {
  if (!doc.expiryDate) return false

  const today = new Date()
  const expiryDate = new Date(doc.expiryDate)
  const thirtyDaysFromNow = new Date()
  thirtyDaysFromNow.setDate(today.getDate() + 30)

  return expiryDate > today && expiryDate <= thirtyDaysFromNow
}

function getExpiryStatus(doc) {
  if (isExpired(doc)) return 'Expired'
  if (isExpiringSoon(doc)) return 'Expiring Soon'
  return 'Valid'
}

// Function removed as it's not being used

function showToastNotification(message, type = 'success') {
  toastMessage.value = message
  toastType.value = type
  showToast.value = true

  // Auto-hide toast after 5 seconds
  setTimeout(() => {
    showToast.value = false
  }, 5000)
}

const downloadDocuments = async () => {
  await downloadExcel();
};

onMounted(async () => {
  console.log('Documents: Component mounted - initializing');

  // Fetch documents
  await fetchDocuments()
  await triggerNotifications()

  // Initial token check
  await checkTokenValidity()

  // Set up interval to check token validity every minute
  console.log('Documents: Starting token validity check interval (1 minute)');
  tokenCheckInterval.value = setInterval(checkTokenValidity, 60000) // Check every minute
})

// Clean up interval and timers when component is unmounted
onUnmounted(() => {
  console.log('Documents: Component unmounting - cleaning up intervals');

  if (tokenCheckInterval.value) {
    clearInterval(tokenCheckInterval.value)
    tokenCheckInterval.value = null
  }

  // Clear search debounce timer if it exists
  if (searchDebounceTimer.value) {
    clearTimeout(searchDebounceTimer.value)
    searchDebounceTimer.value = null
  }
})
</script>

<style>
.bg-pattern {
  background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.2'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6z'%3E%3C/path%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
}
</style>