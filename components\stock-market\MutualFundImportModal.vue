<template>
  <div v-if="show" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-10 mx-auto p-5 border w-11/12 md:w-11/12 lg:w-11/12 xl:w-11/12 2xl:w-11/12 max-w-none shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop style="width: 90vw; max-width: 90vw;">
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Import Mutual Funds
        </h3>
        <button
          @click="$emit('close')"
          type="button"
          class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
        >
          <Icon name="heroicons:x-mark" class="w-5 h-5" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div class="space-y-6">
          <!-- Download and Upload Sections Side by Side -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Download Template Section -->
            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Download Template</h4>
              <button
                @click="downloadTemplate"
                :disabled="isDownloading"
                class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
              >
                <Icon
                  v-if="isDownloading"
                  name="heroicons:arrow-path"
                  class="w-4 h-4 mr-2 animate-spin"
                />
                <Icon
                  v-else
                  name="heroicons:arrow-down-tray"
                  class="w-4 h-4 mr-2"
                />
                {{ isDownloading ? 'Downloading...' : 'Download Template' }}
              </button>
            </div>

            <!-- Upload Section -->
            <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
              <h4 class="text-md font-medium text-gray-900 dark:text-white mb-3">Upload Excel File</h4>
              <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                <input
                  ref="fileInput"
                  type="file"
                  accept=".xlsx,.xls"
                  @change="handleFileUpload"
                  class="hidden"
                />
                <Icon name="heroicons:cloud-arrow-up" class="w-8 h-8 text-gray-400 mx-auto mb-2" />
                <button
                  @click="$refs.fileInput.click()"
                  class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                >
                  <Icon name="heroicons:document-arrow-up" class="w-4 h-4 mr-2" />
                  Choose Excel File
                </button>
                <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
                  {{ selectedFile ? selectedFile.name : 'Select .xlsx or .xls file' }}
                </p>
              </div>
            </div>
          </div>

          <!-- Excel Data Display -->
          <div v-if="excelData.length > 0" class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
            <div class="flex justify-between items-center mb-3">
              <h4 class="text-md font-medium text-gray-900 dark:text-white">Excel File Data</h4>
              <div class="flex flex-col items-end space-y-2">
                <div class="flex space-x-2">
                  <button
                    @click="saveToDatabase"
                    :disabled="isSavingToDatabase"
                    class="inline-flex items-center px-4 py-2 bg-indigo-600 hover:bg-indigo-700 disabled:bg-indigo-400 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors"
                  >
                    <Icon
                      v-if="isSavingToDatabase"
                      name="heroicons:arrow-path"
                      class="w-4 h-4 mr-2 animate-spin"
                    />
                    <Icon
                      v-else
                      name="heroicons:circle-stack"
                      class="w-4 h-4 mr-2"
                    />
                    {{ isSavingToDatabase ? 'Saving...' : 'Save to Database' }}
                  </button>
                  <button
                    @click="downloadExcel"
                    :disabled="isDownloadingExcel"
                    class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
                  >
                    <Icon
                      v-if="isDownloadingExcel"
                      name="heroicons:arrow-path"
                      class="w-4 h-4 mr-2 animate-spin"
                    />
                    <Icon
                      v-else
                      name="heroicons:arrow-down-tray"
                      class="w-4 h-4 mr-2"
                    />
                    {{ isDownloadingExcel ? 'Downloading...' : 'Download Excel' }}
                  </button>
                  <button
                    @click="calculateFinancials"
                    :disabled="isCalculating"
                    class="inline-flex items-center px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-colors"
                  >
                    <Icon
                      v-if="isCalculating"
                      name="heroicons:arrow-path"
                      class="w-4 h-4 mr-2 animate-spin"
                    />
                    <Icon
                      v-else
                      name="heroicons:calculator"
                      class="w-4 h-4 mr-2"
                    />
                    {{ isCalculating ? 'Calculating...' : 'Calculate' }}
                  </button>
                  <button
                    @click="getAMFIDetails"
                    :disabled="isLoadingAMFI"
                    class="inline-flex items-center px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-purple-400 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 transition-colors"
                  >
                    <Icon
                      v-if="isLoadingAMFI"
                      name="heroicons:arrow-path"
                      class="w-4 h-4 mr-2 animate-spin"
                    />
                    <Icon
                      v-else
                      name="heroicons:magnifying-glass"
                      class="w-4 h-4 mr-2"
                    />
                    {{ isLoadingAMFI ? 'Getting AMFI Details...' : 'Get AMFI Details' }}
                  </button>
                </div>

                <!-- Progress Bar -->
                <div v-if="isLoadingAMFI && amfiProgress.total > 0" class="w-64">
                  <div class="flex justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                    <span>{{ amfiProgress.current }} / {{ amfiProgress.total }}</span>
                    <span>{{ Math.round((amfiProgress.current / amfiProgress.total) * 100) }}%</span>
                  </div>
                  <div class="w-full bg-gray-200 rounded-full h-2">
                    <div
                      class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                      :style="{ width: `${(amfiProgress.current / amfiProgress.total) * 100}%` }"
                    ></div>
                  </div>
                  <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    {{ amfiProgress.currentScheme }}
                  </div>
                </div>
              </div>
            </div>
            <!-- Summary Cards -->
            <div v-if="excelData.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
              <!-- Scheme Names Summary -->
              <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <h5 class="text-sm font-medium text-blue-800 dark:text-blue-200 mb-3">Group by Scheme Name</h5>
                <div class="max-h-40 overflow-y-auto space-y-2">
                  <div
                    v-for="group in summaryData.schemeGroups"
                    :key="group.name"
                    class="border-b border-blue-200 dark:border-blue-700 pb-1"
                  >
                    <div class="text-xs text-blue-700 dark:text-blue-300 truncate">{{ group.name }}</div>
                    <div class="flex justify-between text-xs">
                      <span class="text-blue-600 dark:text-blue-400">Records: {{ group.count }}</span>
                      <span class="text-blue-600 dark:text-blue-400 font-medium">₹{{ group.value.toLocaleString() }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Fund Houses Summary -->
              <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <h5 class="text-sm font-medium text-green-800 dark:text-green-200 mb-3">Group by Fund House</h5>
                <div class="max-h-40 overflow-y-auto space-y-2">
                  <div
                    v-for="group in summaryData.fundHouseGroups"
                    :key="group.name"
                    class="border-b border-green-200 dark:border-green-700 pb-1"
                  >
                    <div class="text-xs text-green-700 dark:text-green-300 truncate">{{ group.name }}</div>
                    <div class="flex justify-between text-xs">
                      <span class="text-green-600 dark:text-green-400">Records: {{ group.count }}</span>
                      <span class="text-green-600 dark:text-green-400 font-medium">₹{{ group.value.toLocaleString() }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Folio Numbers Summary -->
              <div class="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
                <h5 class="text-sm font-medium text-purple-800 dark:text-purple-200 mb-3">Group by Folio Number</h5>
                <div class="max-h-40 overflow-y-auto space-y-2">
                  <div
                    v-for="group in summaryData.folioGroups"
                    :key="group.name"
                    class="border-b border-purple-200 dark:border-purple-700 pb-1"
                  >
                    <div class="text-xs text-purple-700 dark:text-purple-300 truncate">{{ group.name }}</div>
                    <div class="flex justify-between text-xs">
                      <span class="text-purple-600 dark:text-purple-400">Records: {{ group.count }}</span>
                      <span class="text-purple-600 dark:text-purple-400 font-medium">₹{{ group.value.toLocaleString() }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Brokers Summary -->
              <div class="bg-orange-50 dark:bg-orange-900/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                <h5 class="text-sm font-medium text-orange-800 dark:text-orange-200 mb-3">Group by Broker</h5>
                <div class="max-h-40 overflow-y-auto space-y-2">
                  <div
                    v-for="group in summaryData.brokerGroups"
                    :key="group.name"
                    class="border-b border-orange-200 dark:border-orange-700 pb-1"
                  >
                    <div class="text-xs text-orange-700 dark:text-orange-300 truncate">{{ group.name }}</div>
                    <div class="flex justify-between text-xs">
                      <span class="text-orange-600 dark:text-orange-400">Records: {{ group.count }}</span>
                      <span class="text-orange-600 dark:text-orange-400 font-medium">₹{{ group.value.toLocaleString() }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="border border-gray-300 dark:border-gray-600 rounded-lg overflow-hidden">
              <div class="overflow-x-auto overflow-y-auto max-h-[60vh]" style="scrollbar-width: thin; scrollbar-color: #6B7280 #F3F4F6;">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-600">
                <thead class="bg-gray-50 dark:bg-gray-700 sticky top-0">
                  <tr>
                    <th
                      v-for="(header, index) in excelHeaders"
                      :key="index"
                      class="px-3 py-2 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider border-r border-gray-200 dark:border-gray-600"
                    >
                      {{ header }}
                      <span v-if="isSchemeCodeColumn(index)" class="ml-2 text-green-600 dark:text-green-400 font-bold">
                        ({{ getSchemeCodeCompletionPercentage() }}%)
                      </span>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600">
                  <tr
                    v-for="(row, rowIndex) in excelData"
                    :key="rowIndex"
                    class="hover:bg-gray-50 dark:hover:bg-gray-700"
                  >
                    <td
                      v-for="(cell, cellIndex) in row"
                      :key="cellIndex"
                      class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 border-r border-gray-200 dark:border-gray-600"
                    >
                      <div class="flex items-center justify-between">
                        <span>{{ cell || '' }}</span>
                        <!-- Manual AMFI Update Button for Scheme Name column -->
                        <button
                          v-if="isSchemeNameColumn(cellIndex) && cell && !hasAMFIData(row, cellIndex)"
                          @click="openManualAMFIModal(cell, rowIndex)"
                          class="ml-2 p-1 text-xs bg-yellow-500 hover:bg-yellow-600 text-white rounded"
                          title="Manually fetch AMFI details"
                        >
                          <Icon name="heroicons:magnifying-glass" class="w-3 h-3" />
                        </button>
                      </div>
                    </td>
                  </tr>
                </tbody>
              </table>
              </div>
            </div>
            <p class="text-sm text-gray-500 dark:text-gray-400 mt-2">
              Showing {{ excelData.length }} rows
            </p>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-end p-4 border-t border-gray-200 dark:border-gray-600">
        <button
          @click="$emit('close')"
          type="button"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          Close
        </button>
      </div>
    </div>
  </div>

  <!-- Manual AMFI Search Modal -->
  <div v-if="showManualAMFIModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="closeManualAMFIModal">
    <div class="relative top-20 mx-auto p-5 border w-11/12 md:w-3/4 lg:w-1/2 xl:w-2/5 shadow-lg rounded-md bg-white dark:bg-gray-800" @click.stop>
      <!-- Modal Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-600">
        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
          Manual AMFI Search
        </h3>
        <button
          @click="closeManualAMFIModal"
          type="button"
          class="text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm p-1.5 ml-auto inline-flex items-center dark:hover:bg-gray-600 dark:hover:text-white"
        >
          <Icon name="heroicons:x-mark" class="w-5 h-5" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Current Scheme Name:
            </label>
            <div class="p-3 bg-gray-100 dark:bg-gray-700 rounded-md text-sm text-gray-900 dark:text-gray-100">
              {{ manualAMFI.originalSchemeName }}
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Search for AMFI Scheme:
            </label>
            <input
              v-model="manualAMFI.searchTerm"
              type="text"
              placeholder="Enter scheme name, ISIN, or scheme code..."
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-700 dark:text-white"
              @keyup.enter="searchManualAMFI"
            />
            <p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
              You can search by scheme name, ISIN (e.g., INF123456789), or scheme code
            </p>
          </div>

          <button
            @click="searchManualAMFI"
            :disabled="isSearchingManualAMFI || !manualAMFI.searchTerm.trim()"
            class="w-full inline-flex justify-center items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
          >
            <Icon
              v-if="isSearchingManualAMFI"
              name="heroicons:arrow-path"
              class="w-4 h-4 mr-2 animate-spin"
            />
            <Icon
              v-else
              name="heroicons:magnifying-glass"
              class="w-4 h-4 mr-2"
            />
            {{ isSearchingManualAMFI ? 'Searching...' : 'Search AMFI' }}
          </button>

          <!-- Search Results -->
          <div v-if="manualAMFI.searchResults.length > 0" class="space-y-2">
            <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300">Search Results:</h4>
            <div class="max-h-60 overflow-y-auto space-y-2">
              <div
                v-for="(result, index) in manualAMFI.searchResults"
                :key="index"
                class="p-3 border border-gray-200 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                @click="selectManualAMFIResult(result)"
              >
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ result.schemeName }}</div>
                <div class="text-xs text-gray-600 dark:text-gray-400">
                  {{ result.fundHouse }} | {{ result.category }} | NAV: ₹{{ result.currentNAV }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  ISIN: {{ result.isin || 'N/A' }} | Code: {{ result.schemeCode || 'N/A' }}
                </div>
              </div>
            </div>
          </div>

          <!-- No Results -->
          <div v-if="manualAMFI.searchAttempted && manualAMFI.searchResults.length === 0" class="text-center py-4">
            <p class="text-sm text-gray-500 dark:text-gray-400">No schemes found. Try a different search term.</p>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex items-center justify-end p-4 border-t border-gray-200 dark:border-gray-600 space-x-2">
        <button
          @click="closeManualAMFIModal"
          type="button"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-700"
        >
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close']);

const isDownloading = ref(false);
const isLoadingAMFI = ref(false);
const isCalculating = ref(false);
const isDownloadingExcel = ref(false);
const isSavingToDatabase = ref(false);
const selectedFile = ref(null);
const excelData = ref([]);
const excelHeaders = ref([]);
const amfiProgress = ref({
  current: 0,
  total: 0,
  currentScheme: ''
});

// Manual AMFI search state
const showManualAMFIModal = ref(false);
const isSearchingManualAMFI = ref(false);
const manualAMFI = ref({
  originalSchemeName: '',
  searchTerm: '',
  searchResults: [],
  searchAttempted: false,
  targetRowIndex: -1
});

// Computed property for summary data
const summaryData = computed(() => {
  if (!excelData.value.length || !excelHeaders.value.length) {
    return {
      schemeGroups: [],
      fundHouseGroups: [],
      folioGroups: [],
      brokerGroups: []
    };
  }

  // Find column indices
  const schemeNameIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme name'));
  const fundHouseIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('fund house'));
  const folioNumberIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('folio number'));
  const brokerIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('broker'));

  // Find value columns (investment amount, current value, etc.)
  const investmentAmountIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('investment amount'));
  const currentValueIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current value'));

  // Use investment amount as primary value, fallback to current value
  const valueIndex = investmentAmountIndex !== -1 ? investmentAmountIndex : currentValueIndex;

  // Group data
  const schemeGroups = new Map();
  const fundHouseGroups = new Map();
  const folioGroups = new Map();
  const brokerGroups = new Map();

  excelData.value.forEach(row => {
    const value = valueIndex !== -1 ? (parseFloat(row[valueIndex]) || 0) : 0;

    // Group by scheme name
    if (schemeNameIndex !== -1 && row[schemeNameIndex]) {
      const scheme = row[schemeNameIndex].toString().trim();
      if (!schemeGroups.has(scheme)) {
        schemeGroups.set(scheme, { count: 0, value: 0 });
      }
      const group = schemeGroups.get(scheme);
      group.count++;
      group.value += value;
    }

    // Group by fund house
    if (fundHouseIndex !== -1 && row[fundHouseIndex]) {
      const fundHouse = row[fundHouseIndex].toString().trim();
      if (!fundHouseGroups.has(fundHouse)) {
        fundHouseGroups.set(fundHouse, { count: 0, value: 0 });
      }
      const group = fundHouseGroups.get(fundHouse);
      group.count++;
      group.value += value;
    }

    // Group by folio number
    if (folioNumberIndex !== -1 && row[folioNumberIndex]) {
      const folio = row[folioNumberIndex].toString().trim();
      if (!folioGroups.has(folio)) {
        folioGroups.set(folio, { count: 0, value: 0 });
      }
      const group = folioGroups.get(folio);
      group.count++;
      group.value += value;
    }

    // Group by broker
    if (brokerIndex !== -1 && row[brokerIndex]) {
      const broker = row[brokerIndex].toString().trim();
      if (!brokerGroups.has(broker)) {
        brokerGroups.set(broker, { count: 0, value: 0 });
      }
      const group = brokerGroups.get(broker);
      group.count++;
      group.value += value;
    }
  });

  // Convert maps to arrays and sort by value descending
  const convertToArray = (groupMap) => {
    return Array.from(groupMap.entries())
      .map(([name, data]) => ({ name, count: data.count, value: data.value }))
      .sort((a, b) => b.value - a.value);
  };

  return {
    schemeGroups: convertToArray(schemeGroups),
    fundHouseGroups: convertToArray(fundHouseGroups),
    folioGroups: convertToArray(folioGroups),
    brokerGroups: convertToArray(brokerGroups)
  };
});

const downloadTemplate = async () => {
  try {
    isDownloading.value = true;

    const api = useApiWithAuth();
    const response = await api.fetchWithAuth('/api/stock-market/mutual-funds/template', {
      method: 'GET',
    });

    // Create a blob from the response
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);

    // Trigger file download
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'MutualFund_Import_Template.xlsx');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

  } catch (error) {
    console.error('Error downloading template:', error);
    alert('Failed to download template. Please try again.');
  } finally {
    isDownloading.value = false;
  }
};

const handleFileUpload = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  selectedFile.value = file;

  try {
    // Dynamically import ExcelJS to avoid SSR issues
    const ExcelJS = await import('exceljs');
    const workbook = new ExcelJS.Workbook();
    const arrayBuffer = await file.arrayBuffer();
    await workbook.xlsx.load(arrayBuffer);

    // Get the first worksheet
    const worksheet = workbook.worksheets[0];

    if (!worksheet) {
      throw new Error('No worksheet found in the Excel file');
    }

    const headers = [];
    const data = [];

    // Read all rows
    worksheet.eachRow((row, rowNumber) => {
      const rowValues = [];

      // Get all cell values in the row
      row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
        rowValues[colNumber - 1] = cell.value || '';
      });

      if (rowNumber === 1) {
        // First row as headers
        excelHeaders.value = rowValues;
      } else {
        // Check if row has any non-empty values
        const hasData = rowValues.some(cell =>
          cell !== null && cell !== undefined && cell !== ''
        );

        if (hasData) {
          data.push(rowValues);
        }
      }
    });

    excelData.value = data;

  } catch (error) {
    console.error('Error parsing Excel file:', error);
    alert('Error reading Excel file. Please make sure it\'s a valid Excel file.');
    excelHeaders.value = [];
    excelData.value = [];
    selectedFile.value = null;
  }
};

const getAMFIDetails = async () => {
  if (!excelData.value.length || !excelHeaders.value.length) return;

  try {
    isLoadingAMFI.value = true;

    // Find scheme name column index
    const schemeNameIndex = excelHeaders.value.findIndex(header =>
      header && header.toString().toLowerCase().includes('scheme name')
    );

    if (schemeNameIndex === -1) {
      alert('Scheme Name column not found in the Excel data');
      return;
    }

    // Find column indices
    const schemeCodeIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme code'));
    const fundHouseIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('fund house'));
    const categoryIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('category'));
    const currentNAVIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current nav'));
    const prevNAVIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('previous day nav'));
    const expenseIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('expense'));
    const dividendOptionIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('dividend option'));

    // Get unique scheme names
    const uniqueSchemes = [...new Set(
      excelData.value
        .map(row => row[schemeNameIndex])
        .filter(name => name && name.toString().trim())
    )];

    // Initialize progress
    amfiProgress.value = {
      current: 0,
      total: uniqueSchemes.length,
      currentScheme: ''
    };

    // Cache for AMFI details
    const amfiCache = new Map();

    // Search one by one for unique schemes
    for (let i = 0; i < uniqueSchemes.length; i++) {
      const schemeName = uniqueSchemes[i].toString().trim();

      // Update progress
      amfiProgress.value.current = i + 1;
      amfiProgress.value.currentScheme = schemeName;

      try {
        const amfiDetails = await fetchAMFIDetails(schemeName);
        amfiCache.set(schemeName, amfiDetails);
      } catch (error) {
        console.error(`Error fetching AMFI details for ${schemeName}:`, error);
        amfiCache.set(schemeName, null);
      }

      // Small delay to show progress
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    // Update all rows with cached data
    const updatedData = excelData.value.map(row => {
      const schemeName = row[schemeNameIndex];
      if (!schemeName) return row;

      const amfiDetails = amfiCache.get(schemeName.toString().trim());
      if (!amfiDetails) return row;

      const updatedRow = [...row];

      if (schemeCodeIndex !== -1) updatedRow[schemeCodeIndex] = amfiDetails.schemeCode || '';
      if (fundHouseIndex !== -1) updatedRow[fundHouseIndex] = amfiDetails.fundHouse || '';
      if (categoryIndex !== -1) updatedRow[categoryIndex] = amfiDetails.category || '';
      if (currentNAVIndex !== -1) updatedRow[currentNAVIndex] = amfiDetails.currentNAV || '';
      if (prevNAVIndex !== -1) updatedRow[prevNAVIndex] = amfiDetails.prevDayNAV || '';
      if (expenseIndex !== -1) updatedRow[expenseIndex] = amfiDetails.expense || '';
      if (dividendOptionIndex !== -1) updatedRow[dividendOptionIndex] = amfiDetails.dividendOption || '';

      return updatedRow;
    });

    excelData.value = updatedData;

  } catch (error) {
    console.error('Error getting AMFI details:', error);
    alert('Error fetching AMFI details. Please try again.');
  } finally {
    isLoadingAMFI.value = false;
    amfiProgress.value = { current: 0, total: 0, currentScheme: '' };
  }
};

const fetchAMFIDetails = async (schemeName) => {
  try {
    const api = useApiWithAuth();
    const response = await api.get(`/api/amfi/scheme-details?schemeName=${encodeURIComponent(schemeName)}`);
    return response;
  } catch (error) {
    console.error(`Error fetching AMFI details for ${schemeName}:`, error);
    throw error;
  }
};

// Helper functions for manual AMFI
const isSchemeNameColumn = (cellIndex) => {
  if (!excelHeaders.value.length) return false;
  const header = excelHeaders.value[cellIndex];
  return header && header.toString().toLowerCase().includes('scheme name');
};

const isSchemeCodeColumn = (cellIndex) => {
  if (!excelHeaders.value.length) return false;
  const header = excelHeaders.value[cellIndex];
  return header && header.toString().toLowerCase().includes('scheme code');
};

const getSchemeCodeCompletionPercentage = () => {
  if (!excelData.value.length || !excelHeaders.value.length) return 0;

  const schemeCodeIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme code'));
  if (schemeCodeIndex === -1) return 0;

  const totalRows = excelData.value.length;
  const completedRows = excelData.value.filter(row => row[schemeCodeIndex] && row[schemeCodeIndex].toString().trim()).length;

  return totalRows > 0 ? Math.round((completedRows / totalRows) * 100) : 0;
};

const hasAMFIData = (row, schemeNameIndex) => {
  // Check if the row already has AMFI data (scheme code, fund house, etc.)
  const schemeCodeIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme code'));
  const fundHouseIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('fund house'));

  return (schemeCodeIndex !== -1 && row[schemeCodeIndex]) ||
         (fundHouseIndex !== -1 && row[fundHouseIndex]);
};

const openManualAMFIModal = (schemeName, rowIndex) => {
  manualAMFI.value = {
    originalSchemeName: schemeName,
    searchTerm: schemeName,
    searchResults: [],
    searchAttempted: false,
    targetRowIndex: rowIndex
  };
  showManualAMFIModal.value = true;
};

const closeManualAMFIModal = () => {
  showManualAMFIModal.value = false;
  manualAMFI.value = {
    originalSchemeName: '',
    searchTerm: '',
    searchResults: [],
    searchAttempted: false,
    targetRowIndex: -1
  };
};

const searchManualAMFI = async () => {
  if (!manualAMFI.value.searchTerm.trim()) return;

  try {
    isSearchingManualAMFI.value = true;
    manualAMFI.value.searchAttempted = true;
    manualAMFI.value.searchResults = [];

    const api = useApiWithAuth();
    const response = await api.get(`/api/amfi/search-schemes?query=${encodeURIComponent(manualAMFI.value.searchTerm)}`);

    manualAMFI.value.searchResults = response || [];
  } catch (error) {
    console.error('Error searching AMFI schemes:', error);
    manualAMFI.value.searchResults = [];
  } finally {
    isSearchingManualAMFI.value = false;
  }
};

const selectManualAMFIResult = (selectedScheme) => {
  const rowIndex = manualAMFI.value.targetRowIndex;
  if (rowIndex === -1 || !excelData.value[rowIndex]) return;

  // Find column indices
  const schemeNameIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme name'));
  const schemeCodeIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme code'));
  const fundHouseIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('fund house'));
  const categoryIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('category'));
  const currentNAVIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current nav'));
  const prevNAVIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('previous day nav'));
  const expenseIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('expense'));
  const dividendOptionIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('dividend option'));

  // Get the scheme name from the target row
  const targetSchemeName = schemeNameIndex !== -1 ? excelData.value[rowIndex][schemeNameIndex] : null;

  if (!targetSchemeName) {
    // If no scheme name, just update the single row
    const updatedRow = [...excelData.value[rowIndex]];

    if (schemeCodeIndex !== -1) updatedRow[schemeCodeIndex] = selectedScheme.schemeCode || '';
    if (fundHouseIndex !== -1) updatedRow[fundHouseIndex] = selectedScheme.fundHouse || '';
    if (categoryIndex !== -1) updatedRow[categoryIndex] = selectedScheme.category || '';
    if (currentNAVIndex !== -1) updatedRow[currentNAVIndex] = selectedScheme.currentNAV || '';
    if (prevNAVIndex !== -1) updatedRow[prevNAVIndex] = selectedScheme.prevDayNAV || '';
    if (expenseIndex !== -1) updatedRow[expenseIndex] = selectedScheme.expense || '';
    if (dividendOptionIndex !== -1) updatedRow[dividendOptionIndex] = selectedScheme.dividendOption || '';

    excelData.value[rowIndex] = updatedRow;
  } else {
    // Update all rows with the same scheme name (100% identical)
    const targetSchemeNameTrimmed = targetSchemeName.toString().trim();
    let updatedCount = 0;

    const updatedData = excelData.value.map((row, index) => {
      const currentSchemeName = schemeNameIndex !== -1 ? row[schemeNameIndex] : null;

      // Check if scheme names are 100% identical
      if (currentSchemeName && currentSchemeName.toString().trim() === targetSchemeNameTrimmed) {
        updatedCount++;
        const updatedRow = [...row];

        if (schemeCodeIndex !== -1) updatedRow[schemeCodeIndex] = selectedScheme.schemeCode || '';
        if (fundHouseIndex !== -1) updatedRow[fundHouseIndex] = selectedScheme.fundHouse || '';
        if (categoryIndex !== -1) updatedRow[categoryIndex] = selectedScheme.category || '';
        if (currentNAVIndex !== -1) updatedRow[currentNAVIndex] = selectedScheme.currentNAV || '';
        if (prevNAVIndex !== -1) updatedRow[prevNAVIndex] = selectedScheme.prevDayNAV || '';
        if (expenseIndex !== -1) updatedRow[expenseIndex] = selectedScheme.expense || '';
        if (dividendOptionIndex !== -1) updatedRow[dividendOptionIndex] = selectedScheme.dividendOption || '';

        return updatedRow;
      }

      return row;
    });

    excelData.value = updatedData;

    // Show success message
    if (updatedCount > 1) {
      alert(`Updated ${updatedCount} rows with identical scheme name: "${targetSchemeNameTrimmed}"`);
    }
  }

  // Close modal
  closeManualAMFIModal();
};

const calculateFinancials = async () => {
  if (!excelData.value.length || !excelHeaders.value.length) return;

  try {
    isCalculating.value = true;

    // Find column indices
    const unitsIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('units'));
    const currentNAVIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current nav'));
    const purchaseNAVIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('purchase nav'));
    const investmentAmountIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('investment amount'));

    // Target columns for calculations
    const currentValueIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current value'));
    const profitLossIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('profit') && h.toString().toLowerCase().includes('loss') && !h.toString().toLowerCase().includes('%'));
    const profitLossPercentageIndex = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('profit') && h.toString().toLowerCase().includes('loss') && h.toString().toLowerCase().includes('%'));

    if (unitsIndex === -1 || currentNAVIndex === -1) {
      alert('Required columns (Units, Current NAV) not found for calculations');
      return;
    }

    let calculatedCount = 0;

    const updatedData = excelData.value.map(row => {
      const updatedRow = [...row];

      const units = parseFloat(row[unitsIndex]) || 0;
      const currentNAV = parseFloat(row[currentNAVIndex]) || 0;
      const purchaseNAV = parseFloat(row[purchaseNAVIndex]) || 0;
      const investmentAmount = parseFloat(row[investmentAmountIndex]) || 0;

      if (units > 0 && currentNAV > 0) {
        // Calculate Current Value = Units × Current NAV
        const currentValue = units * currentNAV;
        if (currentValueIndex !== -1) {
          updatedRow[currentValueIndex] = currentValue.toFixed(2);
        }

        // Calculate Profit/Loss
        let profitLoss = 0;
        if (investmentAmount > 0) {
          // Use investment amount if available
          profitLoss = currentValue - investmentAmount;
        } else if (purchaseNAV > 0) {
          // Fallback to purchase NAV calculation
          const purchaseValue = units * purchaseNAV;
          profitLoss = currentValue - purchaseValue;
        }

        if (profitLossIndex !== -1) {
          updatedRow[profitLossIndex] = profitLoss.toFixed(2);
        }

        // Calculate Profit/Loss Percentage
        if (profitLossPercentageIndex !== -1) {
          let profitLossPercentage = 0;
          if (investmentAmount > 0) {
            profitLossPercentage = (profitLoss / investmentAmount) * 100;
          } else if (purchaseNAV > 0) {
            const purchaseValue = units * purchaseNAV;
            if (purchaseValue > 0) {
              profitLossPercentage = (profitLoss / purchaseValue) * 100;
            }
          }
          updatedRow[profitLossPercentageIndex] = profitLossPercentage.toFixed(2);
        }

        calculatedCount++;
      }

      return updatedRow;
    });

    excelData.value = updatedData;

    alert(`Financial calculations completed for ${calculatedCount} rows`);

  } catch (error) {
    console.error('Error calculating financials:', error);
    alert('Error calculating financial metrics. Please try again.');
  } finally {
    isCalculating.value = false;
  }
};

const downloadExcel = async () => {
  if (!excelData.value.length || !excelHeaders.value.length) return;

  try {
    isDownloadingExcel.value = true;

    const api = useApiWithAuth();
    const response = await api.fetchWithAuth('/api/stock-market/mutual-funds/export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        headers: excelHeaders.value,
        data: excelData.value
      })
    });

    // Create a blob from the response
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });
    const url = window.URL.createObjectURL(blob);

    // Trigger file download
    const link = document.createElement('a');
    link.href = url;
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    link.setAttribute('download', `MutualFund_Data_${timestamp}.xlsx`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);

  } catch (error) {
    console.error('Error downloading Excel:', error);
    alert('Failed to download Excel file. Please try again.');
  } finally {
    isDownloadingExcel.value = false;
  }
};

const saveToDatabase = async () => {
  if (!excelData.value.length || !excelHeaders.value.length) return;

  try {
    isSavingToDatabase.value = true;

    // Transform HTML table data to match MutualFund model
    const transformedData = transformTableDataToMutualFunds();

    if (transformedData.length === 0) {
      alert('No valid data to save. Please ensure required fields are present.');
      return;
    }

    const api = useApiWithAuth();
    const response = await api.post('/api/stock-market/mutual-funds/bulk-import', {
      mutualFunds: transformedData
    });

    // Show detailed success/error information
    let message = `Import Summary:\n`;
    message += `✅ Successfully saved: ${response.savedCount} records\n`;

    if (response.summary && (response.summary.purchaseCount > 0 || response.summary.redemptionCount > 0)) {
      message += `   📈 Purchases: ${response.summary.purchaseCount || 0}\n`;
      message += `   📉 Redemptions: ${response.summary.redemptionCount || 0}\n`;
    }

    if (response.deletedExistingCount > 0) {
      message += `🗑️ Deleted existing records: ${response.deletedExistingCount}\n`;
    }

    if (response.failedCount > 0) {
      message += `❌ Failed records: ${response.failedCount}\n`;

      if (response.summary) {
        message += `   - Validation errors: ${response.summary.validationErrors}\n`;
        message += `   - Database errors: ${response.summary.insertionErrors}\n`;
      }

      // Show first few errors as examples
      if (response.errors && response.errors.length > 0) {
        message += `\nFirst few errors:\n`;
        response.errors.slice(0, 5).forEach((error, index) => {
          const transactionType = error.originalData?.transactionType || 'Unknown';
          message += `${index + 1}. Row ${error.row} [${transactionType}] (${error.schemeName}): ${error.error}\n`;
        });

        if (response.errors.length > 5) {
          message += `... and ${response.errors.length - 5} more errors\n`;
        }
      }
    }

    alert(message);

    // Optionally close the modal after successful save
    // emit('close');

  } catch (error) {
    console.error('Error saving to database:', error);
    alert('Failed to save data to database. Please try again.');
  } finally {
    isSavingToDatabase.value = false;
  }
};

const transformTableDataToMutualFunds = () => {
  // Find column indices for all fields
  const columnMap = {};

  // Required fields
  columnMap.schemeName = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme name'));
  columnMap.schemeCode = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('scheme code'));
  columnMap.fundHouse = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('fund house'));
  columnMap.category = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('category'));
  columnMap.purchaseNAV = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('purchase nav'));
  columnMap.units = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('units'));
  columnMap.investmentAmount = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('investment amount'));
  columnMap.purchaseDate = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('purchase date'));
  columnMap.folioNumber = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('folio number'));
  columnMap.broker = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('broker'));

  // Optional fields
  columnMap.currentNAV = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current nav'));
  columnMap.currentValue = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('current value'));
  columnMap.profitLoss = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('profit') && h.toString().toLowerCase().includes('loss') && !h.toString().toLowerCase().includes('%'));
  columnMap.profitLossPercentage = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('profit') && h.toString().toLowerCase().includes('loss') && h.toString().toLowerCase().includes('%'));
  columnMap.xirr = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('xirr'));
  columnMap.sipFlag = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('sip flag'));
  columnMap.sipAmount = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('sip amount'));
  columnMap.sipFrequency = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('sip frequency'));
  columnMap.sipDay = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('sip day'));
  columnMap.expense = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('expense'));
  columnMap.dividendOption = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('dividend option'));
  columnMap.prevDayNAV = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('previous day nav'));
  columnMap.dayPL = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('day p') && h.toString().toLowerCase().includes('l') && !h.toString().toLowerCase().includes('%'));
  columnMap.dayPLPercentage = excelHeaders.value.findIndex(h => h && h.toString().toLowerCase().includes('day p') && h.toString().toLowerCase().includes('l') && h.toString().toLowerCase().includes('%'));

  const transformedData = [];

  excelData.value.forEach((row, index) => {
    // Check if row has required fields
    const schemeName = columnMap.schemeName !== -1 ? row[columnMap.schemeName] : null;
    const schemeCode = columnMap.schemeCode !== -1 ? row[columnMap.schemeCode] : null;
    const fundHouse = columnMap.fundHouse !== -1 ? row[columnMap.fundHouse] : null;
    const category = columnMap.category !== -1 ? row[columnMap.category] : null;
    const purchaseNAV = columnMap.purchaseNAV !== -1 ? parseFloat(row[columnMap.purchaseNAV]) : null;
    const units = columnMap.units !== -1 ? parseFloat(row[columnMap.units]) : null;
    const investmentAmount = columnMap.investmentAmount !== -1 ? parseFloat(row[columnMap.investmentAmount]) : null;
    const purchaseDate = columnMap.purchaseDate !== -1 ? row[columnMap.purchaseDate] : null;
    const folioNumber = columnMap.folioNumber !== -1 ? row[columnMap.folioNumber] : null;
    const broker = columnMap.broker !== -1 ? row[columnMap.broker] : null;

    // Skip rows that don't have essential required fields
    if (!schemeName || !schemeCode || !fundHouse || !category ||
        !purchaseNAV || !units || !investmentAmount || !purchaseDate ||
        !folioNumber || !broker) {
      console.warn(`Skipping row ${index + 1}: Missing required fields`);
      return;
    }

    // Parse purchase date properly
    let parsedPurchaseDate = purchaseDate;
    if (typeof purchaseDate === 'string') {
      // Handle JavaScript Date string format like "Mon Jan 24 2022 03:30:00 GMT+0530 (India Standard Time)"
      if (purchaseDate.includes('GMT')) {
        // Extract the date part before GMT for better parsing
        const dateMatch = purchaseDate.match(/^(.+?)\s+\d{2}:\d{2}:\d{2}\s+GMT/);
        if (dateMatch) {
          parsedPurchaseDate = new Date(dateMatch[1]).toISOString();
        } else {
          parsedPurchaseDate = new Date(purchaseDate).toISOString();
        }
      } else {
        // Handle other date formats
        parsedPurchaseDate = new Date(purchaseDate).toISOString();
      }
    } else if (purchaseDate instanceof Date) {
      parsedPurchaseDate = purchaseDate.toISOString();
    }

    // Build the mutual fund object
    const mutualFund = {
      schemeName: schemeName.toString().trim(),
      schemeCode: schemeCode.toString().trim(),
      fundHouse: fundHouse.toString().trim(),
      category: category.toString().trim(),
      purchaseNAV: purchaseNAV,
      units: units,
      investmentAmount: investmentAmount,
      purchaseDate: parsedPurchaseDate,
      folioNumber: folioNumber.toString().trim(),
      broker: broker.toString().trim(),

      // Optional fields with defaults
      currentNAV: columnMap.currentNAV !== -1 ? (parseFloat(row[columnMap.currentNAV]) || purchaseNAV) : purchaseNAV,
      currentValue: columnMap.currentValue !== -1 ? (parseFloat(row[columnMap.currentValue]) || 0) : 0,
      profitLoss: columnMap.profitLoss !== -1 ? (parseFloat(row[columnMap.profitLoss]) || 0) : 0,
      profitLossPercentage: columnMap.profitLossPercentage !== -1 ? (parseFloat(row[columnMap.profitLossPercentage]) || 0) : 0,
      xirr: columnMap.xirr !== -1 ? (parseFloat(row[columnMap.xirr]) || null) : null,
      sipFlag: columnMap.sipFlag !== -1 ? (row[columnMap.sipFlag]?.toString().toLowerCase() === 'true') : false,
      sipAmount: columnMap.sipAmount !== -1 ? (parseFloat(row[columnMap.sipAmount]) || 0) : 0,
      sipFrequency: columnMap.sipFrequency !== -1 ? (row[columnMap.sipFrequency]?.toString().trim() || '') : '',
      sipDay: columnMap.sipDay !== -1 ? (parseInt(row[columnMap.sipDay]) || 0) : 0,
      expense: columnMap.expense !== -1 ? (parseFloat(row[columnMap.expense]) || 0) : 0,
      dividendOption: columnMap.dividendOption !== -1 ? (row[columnMap.dividendOption]?.toString().trim() || 'Growth') : 'Growth',
      prevDayNAV: columnMap.prevDayNAV !== -1 ? (parseFloat(row[columnMap.prevDayNAV]) || 0) : 0,
      dayPL: columnMap.dayPL !== -1 ? (parseFloat(row[columnMap.dayPL]) || 0) : 0,
      dayPLPercentage: columnMap.dayPLPercentage !== -1 ? (parseFloat(row[columnMap.dayPLPercentage]) || 0) : 0
    };

    transformedData.push(mutualFund);
  });

  return transformedData;
};
</script>

<style scoped>
/* Custom scrollbar styling */
.overflow-x-auto::-webkit-scrollbar,
.overflow-y-auto::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.overflow-x-auto::-webkit-scrollbar-track,
.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb,
.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #94a3b8;
  border-radius: 4px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover,
.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Dark mode scrollbar */
.dark .overflow-x-auto::-webkit-scrollbar-track,
.dark .overflow-y-auto::-webkit-scrollbar-track {
  background: #374151;
}

.dark .overflow-x-auto::-webkit-scrollbar-thumb,
.dark .overflow-y-auto::-webkit-scrollbar-thumb {
  background: #6b7280;
}

.dark .overflow-x-auto::-webkit-scrollbar-thumb:hover,
.dark .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}
</style>
