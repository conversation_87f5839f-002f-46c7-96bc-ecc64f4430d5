<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="closeModal"></div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <form @submit.prevent="submitForm">
          <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
              <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-orange-100 sm:mx-0 sm:h-10 sm:w-10">
                <Icon name="heroicons:minus" class="h-6 w-6 text-orange-600" />
              </div>
              <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                  Redeem Mutual Fund
                </h3>
                <div class="mt-4 space-y-4">
                  <!-- Scheme Selection -->
                  <div>
                    <label for="scheme" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Select Scheme to Redeem <span class="text-red-500">*</span>
                    </label>
                    <select
                      id="scheme"
                      v-model="selectedScheme"
                      @change="onSchemeChange"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-gray-100"
                      required
                    >
                      <option value="">Select a scheme</option>
                      <option v-for="scheme in availableSchemes" :key="scheme.key" :value="scheme.key">
                        {{ scheme.schemeName }} ({{ scheme.totalUnits }} units available)
                      </option>
                    </select>
                  </div>

                  <!-- Available Units Display -->
                  <div v-if="selectedSchemeData" class="bg-blue-50 dark:bg-blue-900 p-3 rounded-lg">
                    <div class="text-sm">
                      <div class="font-medium text-blue-900 dark:text-blue-100">Available Holdings:</div>
                      <div class="text-blue-700 dark:text-blue-300">Units: {{ formatNumber(selectedSchemeData.totalUnits) }}</div>
                      <div class="text-blue-700 dark:text-blue-300">Current NAV: ₹{{ formatNumber(selectedSchemeData.currentNAV || 0) }}</div>
                      <div class="text-blue-700 dark:text-blue-300">Current Value: ₹{{ formatNumber((selectedSchemeData.totalUnits * (selectedSchemeData.currentNAV || 0))) }}</div>
                    </div>
                  </div>

                  <!-- Redemption Method Selection -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Redemption Method <span class="text-red-500">*</span>
                    </label>
                    <div class="flex space-x-4">
                      <label class="flex items-center">
                        <input
                          v-model="redemptionMethod"
                          type="radio"
                          value="units"
                          class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">By Units</span>
                      </label>
                      <label class="flex items-center">
                        <input
                          v-model="redemptionMethod"
                          type="radio"
                          value="amount"
                          class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">By Amount</span>
                      </label>
                    </div>
                  </div>

                  <!-- Redemption Units (when method is units) -->
                  <div v-if="redemptionMethod === 'units'">
                    <label for="units" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Units to Redeem <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="units"
                      v-model.number="formData.units"
                      type="number"
                      step="0.001"
                      min="0.001"
                      :max="selectedSchemeData?.totalUnits || 0"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-gray-100"
                      placeholder="Enter units to redeem"
                      @input="calculateAmountFromUnits"
                      required
                    />
                    <p v-if="selectedSchemeData" class="mt-1 text-xs text-gray-500">
                      Maximum: {{ formatNumber(selectedSchemeData.totalUnits) }} units
                    </p>
                  </div>

                  <!-- Redemption Amount (when method is amount) -->
                  <div v-if="redemptionMethod === 'amount'">
                    <label for="amount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Amount to Redeem <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="amount"
                      v-model.number="formData.amount"
                      type="number"
                      step="0.01"
                      min="0.01"
                      :max="selectedSchemeData ? (selectedSchemeData.totalUnits * formData.nav) : 0"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-gray-100"
                      placeholder="Enter amount to redeem"
                      @input="calculateUnitsFromAmount"
                      required
                    />
                    <p v-if="selectedSchemeData && formData.nav" class="mt-1 text-xs text-gray-500">
                      Maximum: ₹{{ formatNumber(selectedSchemeData.totalUnits * formData.nav) }}
                    </p>
                  </div>

                  <!-- Redemption NAV -->
                  <div>
                    <label for="nav" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Redemption NAV <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="nav"
                      v-model.number="formData.nav"
                      type="number"
                      step="0.01"
                      min="0.01"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-gray-100"
                      placeholder="Enter redemption NAV"
                      required
                    />
                  </div>

                  <!-- Redemption Date -->
                  <div>
                    <label for="redemptionDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Redemption Date <span class="text-red-500">*</span>
                    </label>
                    <input
                      id="redemptionDate"
                      v-model="formData.redemptionDate"
                      type="date"
                      :max="today"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm dark:bg-gray-700 dark:text-gray-100"
                      required
                    />
                  </div>

                  <!-- Redemption Summary Display -->
                  <div v-if="(formData.units && formData.nav) || (formData.amount && formData.nav)" class="bg-orange-50 dark:bg-orange-900 p-3 rounded-lg">
                    <div class="text-sm">
                      <div class="font-medium text-orange-900 dark:text-orange-100">Redemption Summary:</div>
                      <div class="text-orange-700 dark:text-orange-300">Method: {{ redemptionMethod === 'units' ? 'By Units' : 'By Amount' }}</div>
                      <div class="text-orange-700 dark:text-orange-300">Units: {{ formatNumber(calculatedUnits) }}</div>
                      <div class="text-orange-700 dark:text-orange-300">NAV: ₹{{ formatNumber(formData.nav) }}</div>
                      <div class="text-orange-700 dark:text-orange-300 font-medium">Amount: ₹{{ formatNumber(calculatedAmount) }}</div>
                    </div>
                  </div>

                  <!-- Error message -->
                  <div v-if="error" class="text-red-600 text-sm">
                    {{ error }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="submit"
              :disabled="isLoading || !isFormValid"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-orange-600 text-base font-medium text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="isLoading" class="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></span>
              Redeem Fund
            </button>
            <button
              type="button"
              @click="closeModal"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            >
              Cancel
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';

// Props
interface Props {
  show: boolean;
  existingFunds: any[];
  isLoading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false
});

// Emits
const emit = defineEmits<{
  close: [];
  save: [data: any];
}>();

// Form data
const formData = ref({
  schemeCode: '',
  schemeName: '',
  units: 0,
  amount: 0,
  nav: 0,
  redemptionDate: new Date().toISOString().split('T')[0],
  fundHouse: '',
  category: ''
});

const selectedScheme = ref('');
const redemptionMethod = ref('units'); // 'units' or 'amount'
const error = ref('');

// Today's date for max date validation
const today = new Date().toISOString().split('T')[0];

// Group existing funds by scheme
const availableSchemes = computed(() => {
  const schemeMap = new Map();

  props.existingFunds.forEach(fund => {
    const key = `${fund.schemeCode}-${fund.schemeName}`;
    if (schemeMap.has(key)) {
      const existing = schemeMap.get(key);
      existing.totalUnits += fund.units;
    } else {
      schemeMap.set(key, {
        key,
        schemeCode: fund.schemeCode,
        schemeName: fund.schemeName,
        fundHouse: fund.fundHouse,
        category: fund.category,
        currentNAV: fund.currentNAV,
        totalUnits: fund.units
      });
    }
  });

  return Array.from(schemeMap.values()).filter(scheme => scheme.totalUnits > 0);
});

// Selected scheme data
const selectedSchemeData = computed(() => {
  if (!selectedScheme.value) return null;
  return availableSchemes.value.find(scheme => scheme.key === selectedScheme.value);
});

// Computed properties for calculated values
const calculatedUnits = computed(() => {
  if (redemptionMethod.value === 'units') {
    return formData.value.units;
  } else if (redemptionMethod.value === 'amount' && formData.value.nav > 0) {
    return formData.value.amount / formData.value.nav;
  }
  return 0;
});

const calculatedAmount = computed(() => {
  if (redemptionMethod.value === 'amount') {
    return formData.value.amount;
  } else if (redemptionMethod.value === 'units' && formData.value.nav > 0) {
    return formData.value.units * formData.value.nav;
  }
  return 0;
});

// Form validation
const isFormValid = computed(() => {
  const hasValidScheme = selectedScheme.value;
  const hasValidNav = formData.value.nav > 0;
  const hasValidDate = formData.value.redemptionDate;

  let hasValidRedemption = false;
  let withinLimits = true;

  if (redemptionMethod.value === 'units') {
    hasValidRedemption = formData.value.units > 0;
    withinLimits = !selectedSchemeData.value || formData.value.units <= selectedSchemeData.value.totalUnits;
  } else if (redemptionMethod.value === 'amount') {
    hasValidRedemption = formData.value.amount > 0;
    const maxAmount = selectedSchemeData.value ? (selectedSchemeData.value.totalUnits * formData.value.nav) : 0;
    withinLimits = !selectedSchemeData.value || formData.value.amount <= maxAmount;
  }

  return hasValidScheme && hasValidNav && hasValidDate && hasValidRedemption && withinLimits;
});

// Handle scheme selection
const onSchemeChange = () => {
  if (selectedSchemeData.value) {
    formData.value.schemeCode = selectedSchemeData.value.schemeCode;
    formData.value.schemeName = selectedSchemeData.value.schemeName;
    formData.value.fundHouse = selectedSchemeData.value.fundHouse;
    formData.value.category = selectedSchemeData.value.category;
    formData.value.nav = selectedSchemeData.value.currentNAV || 0;

    // Reset redemption values when scheme changes
    formData.value.units = 0;
    formData.value.amount = 0;
  }
  error.value = '';
};

// Calculate amount from units
const calculateAmountFromUnits = () => {
  if (formData.value.units > 0 && formData.value.nav > 0) {
    formData.value.amount = formData.value.units * formData.value.nav;
  }
};

// Calculate units from amount
const calculateUnitsFromAmount = () => {
  if (formData.value.amount > 0 && formData.value.nav > 0) {
    formData.value.units = formData.value.amount / formData.value.nav;
  }
};

// Format number helper
const formatNumber = (value: number) => {
  return new Intl.NumberFormat('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 3
  }).format(value);
};

// Submit form
const submitForm = () => {
  if (!isFormValid.value) {
    error.value = 'Please fill all required fields correctly';
    return;
  }

  const finalUnits = calculatedUnits.value;
  const finalAmount = calculatedAmount.value;

  if (selectedSchemeData.value && finalUnits > selectedSchemeData.value.totalUnits) {
    error.value = `Cannot redeem more than ${formatNumber(selectedSchemeData.value.totalUnits)} units`;
    return;
  }

  // Additional validation for amount method
  if (redemptionMethod.value === 'amount') {
    const maxAmount = selectedSchemeData.value ? (selectedSchemeData.value.totalUnits * formData.value.nav) : 0;
    if (finalAmount > maxAmount) {
      error.value = `Cannot redeem more than ₹${formatNumber(maxAmount)}`;
      return;
    }
  }

  // Create redemption data with negative values
  const redemptionData = {
    schemeCode: formData.value.schemeCode,
    schemeName: formData.value.schemeName,
    fundHouse: formData.value.fundHouse,
    category: formData.value.category,
    units: -Math.abs(finalUnits), // Negative units for redemption
    nav: formData.value.nav,
    investmentAmount: -Math.abs(finalAmount), // Negative amount for redemption
    purchaseDate: formData.value.redemptionDate,
    transactionType: 'redemption',
    redemptionMethod: redemptionMethod.value
  };

  emit('save', redemptionData);
};

// Close modal
const closeModal = () => {
  resetForm();
  emit('close');
};

// Reset form
const resetForm = () => {
  formData.value = {
    schemeCode: '',
    schemeName: '',
    units: 0,
    amount: 0,
    nav: 0,
    redemptionDate: new Date().toISOString().split('T')[0],
    fundHouse: '',
    category: ''
  };
  selectedScheme.value = '';
  redemptionMethod.value = 'units';
  error.value = '';
};

// Watch for modal close to reset form
watch(() => props.show, (newValue) => {
  if (!newValue) {
    resetForm();
  }
});
</script>
