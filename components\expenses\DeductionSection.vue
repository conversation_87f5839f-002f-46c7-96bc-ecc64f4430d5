<template>
  <div class="mt-6">
    <!-- Toggle Section -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-medium text-gray-900">Dynamic Deductions</h3>
      <div class="flex items-center space-x-3">
        <label class="inline-flex items-center">
          <input
            type="radio"
            name="hasDeductions"
            :value="false"
            :checked="!hasDeductions"
            @change="toggleDeductions(false)"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
            :disabled="disabled"
          />
          <span class="ml-2 text-sm text-gray-700">No Deductions</span>
        </label>
        <label class="inline-flex items-center">
          <input
            type="radio"
            name="hasDeductions"
            :value="true"
            :checked="hasDeductions"
            @change="toggleDeductions(true)"
            class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
            :disabled="disabled"
          />
          <span class="ml-2 text-sm text-gray-700">Add Deductions</span>
        </label>
      </div>
    </div>

    <!-- Deductions Section -->
    <div v-if="hasDeductions" class="space-y-4">
      <!-- Calculation Summary -->
      <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
        <div class="flex items-center justify-between text-sm">
          <div class="space-y-1">
            <div class="flex justify-between">
              <span class="text-gray-600">{{ transactionType === 'RECEIPT' ? 'Gross Amount Received:' : 'Gross Amount Paid:' }}</span>
              <span class="font-medium">{{ formatCurrency(grossAmount) }}</span>
            </div>
            <div class="flex justify-between">
              <span class="text-gray-600">Total Deductions:</span>
              <span class="font-medium text-red-600">{{ formatCurrency(totalDeductions) }}</span>
            </div>
            <hr class="border-blue-300">
            <div class="flex justify-between font-semibold text-base">
              <span class="text-gray-800">{{ transactionType === 'RECEIPT' ? 'Net Receipt to Bank/Cash:' : 'Net Payment from Bank/Cash:' }}</span>
              <span :class="netAmount >= 0 ? 'text-green-600' : 'text-red-600'">{{ formatCurrency(Math.abs(netAmount)) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Deduction Fields -->
      <div v-if="deductions.length > 0" class="space-y-4">
        <DeductionField
          v-for="(deduction, index) in deductions"
          :key="deduction.id"
          :deduction="deduction"
          :index="index"
          :disabled="disabled"
          :errors="getDeductionErrors(index)"
          @update="updateDeduction(index, $event)"
          @remove="removeDeduction(index)"
        />
      </div>

      <!-- Add Deduction Button -->
      <button
        type="button"
        @click="addDeduction"
        class="w-full flex items-center justify-center px-4 py-3 border-2 border-dashed border-gray-300 rounded-md text-gray-600 hover:border-indigo-300 hover:text-indigo-600 transition-colors duration-200"
        :disabled="disabled"
      >
        <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        Add Deduction
      </button>

      <!-- Validation Errors -->
      <div v-if="validationErrors.length > 0" class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
          <svg class="h-5 w-5 text-red-400 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <h4 class="text-sm font-medium text-red-800 mb-1">Please fix the following errors:</h4>
            <ul class="text-sm text-red-700 list-disc list-inside">
              <li v-for="error in validationErrors" :key="error">{{ error }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import DeductionField from './DeductionField.vue';

export default {
  name: 'DeductionSection',
  
  components: {
    DeductionField
  },

  props: {
    hasDeductions: {
      type: Boolean,
      default: false
    },
    deductions: {
      type: Array,
      default: () => []
    },
    grossAmount: {
      type: Number,
      default: 0
    },
    transactionType: {
      type: String,
      default: 'PAYMENT'
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },

  emits: ['update:hasDeductions', 'update:deductions'],

  computed: {
    totalDeductions() {
      return this.deductions.reduce((sum, deduction) => {
        return sum + (parseFloat(deduction.amount) || 0);
      }, 0);
    },

    netAmount() {
      // Calculate net amount (always positive for display purposes)
      // The grossAmount is always passed as positive (absolute value)
      const netValue = this.grossAmount - this.totalDeductions;

      // Return the net amount (positive for both receipts and payments in display)
      // The actual sign handling is done in the backend when saving
      return Math.max(0, netValue); // Ensure it doesn't go negative
    },

    validationErrors() {
      const errors = [];
      
      if (this.hasDeductions && this.deductions.length === 0) {
        errors.push('At least one deduction is required when deductions are enabled');
      }

      this.deductions.forEach((deduction, index) => {
        if (!deduction.name?.trim()) {
          errors.push(`Deduction #${index + 1}: Name is required`);
        }
        if (!deduction.amount || parseFloat(deduction.amount) <= 0) {
          errors.push(`Deduction #${index + 1}: Amount must be greater than 0`);
        }
      });

      if (this.totalDeductions > Math.abs(this.grossAmount)) {
        errors.push('Total deductions cannot exceed the gross amount');
      }

      return errors;
    }
  },

  methods: {
    toggleDeductions(enabled) {
      this.$emit('update:hasDeductions', enabled);
      if (!enabled) {
        this.$emit('update:deductions', []);
      } else if (this.deductions.length === 0) {
        this.addDeduction();
      }
    },

    addDeduction() {
      const newDeduction = {
        id: `deduction_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        name: '',
        amount: '',
        description: ''
      };
      
      const updatedDeductions = [...this.deductions, newDeduction];
      this.$emit('update:deductions', updatedDeductions);
    },

    updateDeduction(index, updatedDeduction) {
      const updatedDeductions = [...this.deductions];
      updatedDeductions[index] = updatedDeduction;
      this.$emit('update:deductions', updatedDeductions);
    },

    removeDeduction(index) {
      const updatedDeductions = this.deductions.filter((_, i) => i !== index);
      this.$emit('update:deductions', updatedDeductions);
    },

    getDeductionErrors(index) {
      const errors = {};
      const deduction = this.deductions[index];
      
      if (!deduction.name?.trim()) {
        errors.name = 'Name is required';
      }
      
      if (!deduction.amount || parseFloat(deduction.amount) <= 0) {
        errors.amount = 'Amount must be greater than 0';
      }
      
      return errors;
    },

    formatCurrency(amount) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount || 0);
    }
  }
};
</script>

<style scoped>
/* Component-specific styles */
button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.border-dashed:hover:not(:disabled) {
  background-color: #f8fafc;
}
</style>
