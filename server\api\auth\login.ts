// server/api/auth/login.ts
import { defineEventHand<PERSON>, readBody, createError } from 'h3';
import User from '../../models/User';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import { getRoleId, getRoleName } from '../../utils/roles';
import { emailService } from '../../utils/emailService';
import crypto from 'crypto';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { username, password } = body;

  if (!username || !password) {
    throw createError({ statusCode: 400, statusMessage: 'Missing username or password' });
  }

  // Find the user by username
  const user = await User.findOne({ username });
  if (!user) {
    throw createError({ statusCode: 401, statusMessage: 'Invalid credentials' });
  }

  // Check if account is locked
  if (user.isAccountLocked()) {
    const lockoutTime = user.accountLockedUntil;
    const remainingTime = Math.ceil((lockoutTime!.getTime() - Date.now()) / (1000 * 60));

    throw createError({
      statusCode: 423,
      statusMessage: `Account is temporarily locked due to multiple failed login attempts. Please try again in ${remainingTime} minutes.`
    });
  }

  // Validate the password
  const isMatch = await bcrypt.compare(password, user.password);
  if (!isMatch) {
    // Increment failed attempts
    await user.incrementFailedAttempts();

    // Check if account is now locked after this attempt
    const updatedUser = await User.findById(user._id);
    if (updatedUser?.isAccountLocked()) {
      // Send lockout notification email
      try {
        const lockoutTime = updatedUser.accountLockedUntil!;
        await emailService.sendAccountLockoutEmail(updatedUser.email, {
          fullname: updatedUser.fullname,
          lockoutDuration: '30 minutes',
          unlockTime: lockoutTime.toLocaleString()
        });
      } catch (emailError) {
        console.error('Failed to send lockout notification:', emailError);
      }

      throw createError({
        statusCode: 423,
        statusMessage: 'Account has been temporarily locked due to multiple failed login attempts. Please check your email for more information.'
      });
    }

    throw createError({ statusCode: 401, statusMessage: 'Invalid credentials' });
  }

  // Reset failed attempts on successful login
  if (user.failedLoginAttempts && user.failedLoginAttempts > 0) {
    await user.resetFailedAttempts();
  }

  // Get or create roleId if it doesn't exist
  if (!user.roleId) {
    // Get the roleId for the user's role
    user.roleId = await getRoleId(user.role);
    await user.save();
  }

  // Get the role name for the status check
  const roleName = await getRoleName(user.roleId);

  // Check user status - only allow approved users and managers to login
  // Admins are exempt from this restriction
  if (roleName !== 'admin') {
    // For users and managers, check if they are approved (status = 1)
    if (user.status !== 1) {
      if (user.status === 0) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Your account is pending approval. Please contact your manager.'
        });
      } else if (user.status === -1) {
        throw createError({
          statusCode: 403,
          statusMessage: 'Your account has been rejected. Please contact your manager for assistance.'
        });
      } else {
        throw createError({
          statusCode: 403,
          statusMessage: 'Your account is not active. Please contact your manager.'
        });
      }
    }
  }

  // Generate session ID for session management
  const sessionId = crypto.randomUUID();

  // Update last login time and add session
  user.lastLogin = new Date();

  // Manage active sessions (limit to 5 concurrent sessions)
  if (!user.activeSessions) {
    user.activeSessions = [];
  }

  // Add new session
  user.activeSessions.unshift(sessionId);

  // Keep only last 5 sessions
  if (user.activeSessions.length > 5) {
    user.activeSessions = user.activeSessions.slice(0, 5);
  }

  await user.save();

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  // We already got the role name earlier, reuse it here

  // Token payload
  const tokenPayload = {
    id: user._id,
    username: user.username,
    email: user.email,
    fullname: user.fullname,
    role: roleName, // Use the role name for backward compatibility
    roleId: user.roleId, // Include the role UUID
    status: user.status, // Add user status to the payload
    sessionId: sessionId // Add session ID for session management
  };

  // Create tokens:
  // - Access token (short-lived)
  // - Refresh token (long-lived)
  const accessToken = jwt.sign(tokenPayload, secret, { expiresIn: '15m' });
  const refreshToken = jwt.sign(tokenPayload, secret, { expiresIn: '7d' });

  // Return the user object with role name for the UI
  return {
    accessToken,
    refreshToken,
    user: {
      ...tokenPayload,
      role: roleName // Ensure the UI gets the role name
    }
  };
});