<template>
  <div v-if="isOpen" class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <div class="flex justify-between items-center">
                <h3 class="text-lg leading-6 font-medium text-gray-900">
                  Contract Notes
                </h3>
                <div class="flex items-center">
                  <div class="relative mr-4">
                    <input type="text" v-model="searchQuery" placeholder="Search by CN number..."
                      class="focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <div v-if="isLoading" class="flex justify-center items-center h-40">
                  <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
                </div>

                <div v-else-if="filteredNotes.length === 0" class="text-center py-8">
                  <p class="text-gray-500">No contract notes found.</p>
                </div>

                <div v-else class="space-y-4">
                  <div v-for="note in filteredNotes" :key="note._id" class="bg-gray-50 p-4 rounded-lg">
                    <div class="flex justify-between items-center mb-2">
                      <div>
                        <h4 class="text-md font-medium text-gray-900">CN #{{ note.cn_no }}</h4>
                        <p class="text-sm text-gray-500">{{ formatDate(note.cn_date) }} | {{ note.trade_type }}</p>
                      </div>
                      <div class="flex space-x-2">
                        <button @click="editNote(note)" class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 shadow-sm text-xs font-medium rounded text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                          <svg class="-ml-0.5 mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                          </svg>
                          Edit
                        </button>
                      </div>
                    </div>

                    <div class="overflow-x-auto mt-2">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-100">
                          <tr>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Price</th>
                            <th scope="col" class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P/L</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="record in note.Folio_rec" :key="record._id">
                            <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ record.symbol }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ record.qnty }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">₹{{ record.price.toLocaleString() }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">₹{{ record.cprice.toLocaleString() }}</td>
                            <td class="px-4 py-2 whitespace-nowrap text-sm" :class="record.pl >= 0 ? 'text-green-600' : 'text-red-600'">
                              {{ record.pl >= 0 ? '+' : '' }}₹{{ record.pl.toLocaleString() }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button type="button" @click="$emit('close')" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  cnNotes: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'edit']);

const searchQuery = ref('');

// Filter notes based on search query
const filteredNotes = computed(() => {
  if (!searchQuery.value) return props.cnNotes;

  const query = searchQuery.value.toLowerCase();
  return props.cnNotes.filter(note =>
    (note.cn_no || '').toLowerCase().includes(query)
  );
});

// Format date for display
function formatDate(dateString) {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
}

// Edit a note
function editNote(note) {
  emit('edit', note);
  emit('close');
}
</script>
