import { testSupabaseConnection } from '~/utils/supabase.js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { supabaseUrl, supabaseAnonKey } = body

    // Validate required fields
    if (!supabaseUrl || !supabaseAnonKey) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Supabase URL and Anon Key are required for testing'
      })
    }

    // Validate Supabase URL format
    if (!supabaseUrl.match(/^https:\/\/[a-zA-Z0-9-]+\.supabase\.co$/)) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid Supabase URL format'
      })
    }

    // Test the connection
    try {
      const { createClient } = await import('@supabase/supabase-js')
      const testClient = createClient(supabaseUrl, supabaseAnonKey)
      
      // Simple connection test - try to get auth session
      const { error } = await testClient.auth.getSession()
      
      // Even if auth returns an error, if we get a response, connection is working
      return {
        success: true,
        message: 'Connection test successful',
        data: {
          status: 'connected',
          timestamp: new Date().toISOString()
        }
      }
    } catch (connectionError) {
      return {
        success: false,
        message: connectionError.message || 'Connection failed',
        data: {
          status: 'failed',
          timestamp: new Date().toISOString()
        }
      }
    }
  } catch (error) {
    console.error('Error testing Supabase connection:', error)
    
    return {
      success: false,
      message: error.message || 'Connection test failed',
      data: {
        status: 'error',
        timestamp: new Date().toISOString()
      }
    }
  }
})