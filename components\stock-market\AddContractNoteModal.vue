<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[85vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-gradient-to-r from-red-700 to-red-900 p-4 text-white flex justify-between items-center">
        <h2 class="text-xl font-bold">Create Contract Note</h2>
        <button @click="close" class="text-white hover:text-gray-200">
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[calc(85vh-8rem)]">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">CN Number</label>
              <input
                v-model="formData.cn_no"
                type="text"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
                :class="{ 'border-red-500': cnNoError }"
                @blur="validateCnNo"
              />
              <div v-if="cnNoError" class="text-red-500 text-xs mt-1">{{ cnNoError }}</div>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">CN Date</label>
              <input
                v-model="formData.cn_date"
                type="date"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
              />
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Broker</label>
              <input
                v-model="formData.broker"
                type="text"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
                list="broker-options"
              />
              <datalist id="broker-options">
                <option v-for="broker in brokerOptions" :key="broker" :value="broker">{{ broker }}</option>
              </datalist>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Type</label>
              <select
                v-model="formData.type"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Select Type</option>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Folio</label>
              <input
                v-model="formData.folio"
                type="text"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
                list="folio-options"
              />
              <datalist id="folio-options">
                <option v-for="folio in folioOptions" :key="folio" :value="folio">{{ folio }}</option>
              </datalist>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Other Charges</label>
              <input
                v-model.number="formData.oth_chg"
                type="number"
                step="0.01"
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
              />
            </div>
          </div>

          <!-- Records Table -->
          <div class="mt-6">
            <h3 class="text-lg font-semibold mb-4">Folio Records</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Symbol</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Brokerage</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Net Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(record, index) in recordsData" :key="index">
                    <td class="px-6 py-4">
                      <StockSymbolDropdown
                        v-model="record.symbol"
                        :options="symbolOptions"
                        @update:modelValue="updatePriceFromSymbol($event, record)"
                        @price-updated="(price) => { record.price = price; calculateAmount(record); }"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.price"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        @input="calculateAmount(record)"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.qnty"
                        type="number"
                        class="w-full p-1 border rounded"
                        @input="calculateAmount(record)"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.amt"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        readonly
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.brokeragePerUnit"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        @input="calculateNetAmount(record)"
                        placeholder="Per unit"
                        title="Enter brokerage per unit"
                      />
                      <div class="text-xs text-gray-500 mt-1">
                        Total: {{ record.brokerage || 0 }}
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.namt"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        readonly
                      />
                    </td>
                    <td class="px-6 py-4">
                      <button
                        type="button"
                        @click="removeRecord(index)"
                        class="text-red-600 hover:text-red-800"
                      >
                        <Icon name="heroicons:trash" class="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <button
              type="button"
              @click="addRecord"
              class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Add Record
            </button>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end mt-6">
            <button
              type="submit"
              class="bg-red-600 text-white px-6 py-2 rounded hover:bg-red-700"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? 'Saving...' : 'Save' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted } from 'vue'
import StockSymbolDropdown from './StockSymbolDropdown.vue'

const props = defineProps<{
  show: boolean
}>();

const emit = defineEmits(['close', 'submit', 'success']);

// Define options for broker and folio datalists
const brokerOptions = ref<string[]>([]);
const folioOptions = ref<string[]>([]);

// Define interface for form data to ensure type safety
interface CNNoteFormData {
  cn_no: string;
  cn_date: string;
  broker: string;
  type: string;
  folio: string;
  oth_chg: number;
  famt: number;
}

// Define interface for record data
interface RecordData {
  symbol: string;
  price: number;
  qnty: number;
  amt: number;
  brokerage: number; // This will store the total brokerage (per unit * quantity)
  brokeragePerUnit?: number; // This is for UI display only
  namt: number;
  sector: string;
  rid: string;
  pdate: string;
  [key: string]: any; // Allow for additional properties
}

const formData = ref<CNNoteFormData>({
  cn_no: '',
  cn_date: new Date().toISOString().split('T')[0], // Default to today
  broker: '',
  type: '',
  folio: '',
  oth_chg: 0,
  famt: 0
});

const recordsData = ref<RecordData[]>([]);
const isSubmitting = ref(false);
const cnNoError = ref<string>('');

// For datalist
const symbolOptions = ref<string[]>([]);

// Validate CN Number
function validateCnNo() {
  cnNoError.value = '';

  if (!formData.value.cn_no) {
    cnNoError.value = 'CN Number is required';
    return false;
  }

  formData.value.cn_no = formData.value.cn_no.trim();
  if (formData.value.cn_no === '') {
    cnNoError.value = 'CN Number cannot be empty';
    return false;
  }

  return true;
}

// Function to fetch broker and folio options
const fetchOptions = async () => {
  try {
    // Fetch existing contract notes to extract broker and folio options
    const response = await $fetch('/api/stock-market/contract-notes');
    
    if (response && response.contractNotes) {
      // Extract unique brokers
      const brokers = new Set<string>();
      // Extract unique folios
      const folios = new Set<string>();
      
      response.contractNotes.forEach((note: any) => {
        if (note.broker) brokers.add(note.broker);
        if (note.folio) folios.add(note.folio);
      });
      
      brokerOptions.value = Array.from(brokers).sort();
      folioOptions.value = Array.from(folios).sort();
    }
  } catch (error) {
    console.error('Error fetching options:', error);

    // Sample data removed - show empty options on error
    brokerOptions.value = [];
    folioOptions.value = [];
  }
};

// Function to fetch symbol options
const fetchSymbolOptions = async () => {
  try {
    // Fetch stock symbols from Yahoo Finance API
    const response = await $fetch('/api/stock-market/prices');
    
    if (response && response.priceUpdates) {
      const symbols = Object.keys(response.priceUpdates);
      symbolOptions.value = symbols.sort();
      console.log(`Loaded ${symbolOptions.value.length} unique symbols for dropdown`);
    } else {
      // Fallback to fetching from investments API
      try {
        const investmentsResponse = await $fetch('/api/stock-market/investments');
        
        if (investmentsResponse && investmentsResponse.investments) {
          const symbols = new Set<string>();
          investmentsResponse.investments.forEach((investment: any) => {
            if (investment.symbol) symbols.add(investment.symbol);
          });
          
          symbolOptions.value = Array.from(symbols).sort();
          console.log(`Loaded ${symbolOptions.value.length} unique symbols from investments`);
        }
      } catch (investmentsError) {
        console.error('Error fetching symbols from investments:', investmentsError);
      }
    }
  } catch (error) {
    console.error('Error fetching symbol options:', error);
  }
};

// Fetch data when component is mounted
onMounted(async () => {
  try {
    // Fetch data in parallel for better performance
    await Promise.all([
      fetchSymbolOptions(),
      fetchOptions()
    ]);
    console.log('AddContractNoteModal: All data loaded successfully');
  } catch (error) {
    console.error('Error loading initial data:', error);
  }
});

function resetForm() {
  formData.value = {
    cn_no: '',
    cn_date: new Date().toISOString().split('T')[0], // Reset to today
    broker: '',
    type: '',
    folio: '',
    oth_chg: 0,
    famt: 0
  };
  recordsData.value = [];
  cnNoError.value = '';
}

function calculateAmount(record: RecordData) {
  if (record.price && record.qnty) {
    // Ensure proper number conversion and precision
    record.amt = Number(parseFloat((record.price * record.qnty).toString()).toFixed(2));

    // Recalculate brokerage based on quantity
    if (record.brokeragePerUnit !== undefined) {
      calculateNetAmount(record);
    } else {
      calculateNetAmount(record);
    }
  }
}

function calculateNetAmount(record: RecordData) {
  if (record.amt !== undefined) {
    // Get the brokerage per unit from the UI input
    const brokeragePerUnit = Number(record.brokeragePerUnit || 0);

    // Calculate total brokerage (brokerage per unit * quantity)
    const totalBrokerage = brokeragePerUnit * (record.qnty || 0);

    // Store the total brokerage in the brokerage field (this is what gets sent to the backend)
    record.brokerage = Number(parseFloat(totalBrokerage.toString()).toFixed(2));

    // Calculate net amount (amount + total brokerage)
    record.namt = Number(parseFloat((record.amt + record.brokerage).toString()).toFixed(2));

    updateTotalAmount();
  }
}

function updateTotalAmount() {
  // Calculate total with proper number handling
  const total = recordsData.value.reduce(
    (sum, record) => sum + (Number(record.namt) || 0),
    0
  );
  formData.value.famt = Number(parseFloat(total.toString()).toFixed(2));
}

function addRecord() {
  const newRecord = {
    symbol: '',
    price: 0,
    qnty: 0,
    amt: 0,
    brokeragePerUnit: 0, // Per unit brokerage for UI
    brokerage: 0, // Total brokerage (per unit * quantity) for backend
    namt: 0,
    sector: 'EQUITY', // Default sector
    rid: generateRid(), // Generate a unique record ID
    pdate: formData.value.cn_date // Use the CN date
  };
  recordsData.value.push(newRecord);
}

// Generate a unique record ID
function generateRid() {
  return 'RID_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
}

function removeRecord(index: number) {
  recordsData.value.splice(index, 1);
  updateTotalAmount();
}

// Function to update price when a symbol is selected
async function updatePriceFromSymbol(symbol: string, record: RecordData) {
  if (!symbol) return;

  try {
    // Fetch price from Yahoo Finance API
    const response = await $fetch(`/api/stock-market/prices`);
    
    if (response && response.priceUpdates && response.priceUpdates[symbol]) {
      const priceData = response.priceUpdates[symbol];
      if (priceData.currentPrice !== undefined) {
        record.price = Number(priceData.currentPrice);
        calculateAmount(record);
      }
    }
  } catch (error) {
    console.error('Error fetching price for symbol:', error);
  }
}

async function handleSubmit() {
  try {
    isSubmitting.value = true;

    // Validate form data before submission
    if (!validateCnNo() || !formData.value.cn_date || !formData.value.broker || !formData.value.type || !formData.value.folio) {
      alert('Please fill in all required fields');
      isSubmitting.value = false;
      return;
    }

    // Validate records data
    if (recordsData.value.length === 0) {
      alert('Please add at least one record');
      isSubmitting.value = false;
      return;
    }

    // Ensure all records have required fields
    for (const record of recordsData.value) {
      if (!record.symbol || !record.qnty) {
        alert('Please fill in all required fields for each record');
        isSubmitting.value = false;
        return;
      }
    }

    // Create a clean payload with proper type conversions
    const payload = {
      formData: {
        ...formData.value,
        oth_chg: Number(formData.value.oth_chg),
        famt: Number(formData.value.famt)
      },
      recordsData: recordsData.value.map(record => {
        // Make sure all calculations are up to date
        calculateNetAmount(record);

        return {
          ...record,
          // Basic fields with proper type conversion
          price: Number(record.price),
          qnty: Number(record.qnty),
          amt: Number(record.amt),
          brokerage: Number(record.brokerage), // This is already the total brokerage
          namt: Number(record.namt),

          // Required fields from the Folio model
          cn_no: formData.value.cn_no,
          broker: formData.value.broker,
          type: formData.value.type,
          folio: formData.value.folio,
          pdate: formData.value.cn_date,
          sector: record.sector || 'EQUITY',
          rid: record.rid || generateRid()
        };
      })
    };

    // Submit the form data to the API
    const response = await $fetch('/api/stock-market/contract-notes/add', {
      method: 'POST',
      body: payload
    });

    if (response && response.success) {
      // Emit success event
      emit('success', response.cnNote);
      
      // Reset form
      resetForm();
      
      // Close modal
      emit('close');
    } else {
      alert(response?.message || 'Failed to add contract note');
    }
  } catch (error: any) {
    console.error('Error submitting form:', error);
    
    // Show appropriate error message
    if (error.response && error.response.status === 409) {
      alert('CN Number already exists. Please use a different CN Number.');
    } else {
      alert(error.message || 'Failed to add contract note');
    }
  } finally {
    isSubmitting.value = false;
  }
}

function close() {
  emit('close');
  resetForm();
}
</script>
