import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    console.log('Received request to save attendance')
    const body = await readBody(event)
    const { attendanceData, firmId, fromDate, toDate } = body
    console.log('Request body:', body)

    if (!attendanceData || !firmId || !fromDate || !toDate) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields'
      })
    }

    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )
    
    const recordsToUpsert = []
    for (const row of attendanceData) {
        for (const date in row.attendance) {
            recordsToUpsert.push({
                labor_id: row.profile.id,
                attendance_date: date,
                days_worked: row.attendance[date],
                daily_rate: row.profile.daily_rate,
                period_start: fromDate,
                period_end: toDate,
                firm_id: firmId,
            })
        }
    }

    if (recordsToUpsert.length > 0) {
        console.log('Upserting records:', recordsToUpsert)
        const { error } = await supabase
            .from('attendance_records')
            .upsert(recordsToUpsert, { onConflict: 'labor_id, attendance_date' })

        if (error) {
            console.error('Supabase error:', error)
            throw createError({
                statusCode: 500,
                statusMessage: `Database error: ${error.message}`
            })
        }
        console.log('Upsert successful')
    }

    return {
      success: true,
      message: 'Attendance saved successfully'
    }
  } catch (error) {
    console.error('Error saving attendance:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to save attendance'
    })
  }
})