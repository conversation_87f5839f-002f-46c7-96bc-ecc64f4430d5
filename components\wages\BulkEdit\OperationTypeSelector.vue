<template>
  <div class="space-y-6">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-blue-800 mb-4">Choose Edit Type</h3>
      
      <div class="space-y-4">
        <!-- Personal Details Edit Option -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
          <label class="flex items-start cursor-pointer">
            <input 
              type="radio" 
              v-model="selectedEditType" 
              value="individual"
              class="mt-1 rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-500 focus:ring-blue-500"
            />
            <div class="ml-3 flex-1">
              <div class="text-base font-medium text-gray-900">Personal Details Edit</div>
              <div class="text-sm text-gray-600 mt-1">
                Edit individual details in table format
              </div>
              <div class="text-xs text-gray-500 mt-2">
                <strong>Includes:</strong> Personal info, Banking details, Government IDs
                <br />
                <strong>Note:</strong> Each employee keeps their unique values
              </div>
            </div>
          </label>
        </div>

        <!-- Employment Bulk Update Option -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-green-300 transition-colors">
          <label class="flex items-start cursor-pointer">
            <input 
              type="radio" 
              v-model="selectedEditType" 
              value="bulk"
              class="mt-1 rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <div class="ml-3 flex-1">
              <div class="text-base font-medium text-gray-900">Employment Bulk Update</div>
              <div class="text-sm text-gray-600 mt-1">
                Apply same employment details to all selected employees
              </div>
              <div class="text-xs text-gray-500 mt-2">
                <strong>Includes:</strong> Status, Category, Project, Site, Daily Wage
                <br />
                <strong>Note:</strong> Safe for bulk operations only
              </div>
            </div>
          </label>
        </div>

        <!-- Mixed Edit Option -->
        <div class="border border-gray-200 rounded-lg p-4 hover:border-purple-300 transition-colors">
          <label class="flex items-start cursor-pointer">
            <input 
              type="radio" 
              v-model="selectedEditType" 
              value="mixed"
              class="mt-1 rounded border-gray-300 text-purple-600 shadow-sm focus:border-purple-500 focus:ring-purple-500"
            />
            <div class="ml-3 flex-1">
              <div class="text-base font-medium text-gray-900">Mixed Edit (Advanced)</div>
              <div class="text-sm text-gray-600 mt-1">
                Combine both personal and employment editing
              </div>
              <div class="text-xs text-gray-500 mt-2">
                <strong>Best of both worlds:</strong> Individual table + Bulk updates
              </div>
            </div>
          </label>
        </div>
      </div>

      <!-- Selection Summary -->
      <div class="mt-6 p-3 bg-orange-50 border border-orange-200 rounded-lg">
        <div class="text-sm font-medium text-orange-800">
          Selected: {{ selectedEmployeesCount }} employees
        </div>
        <div v-if="selectedEditType" class="text-xs text-orange-600 mt-1">
          Edit Type: {{ getEditTypeLabel(selectedEditType) }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  selectedEmployeesCount: {
    type: Number,
    required: true
  }
})

const emit = defineEmits(['update:editType'])

const selectedEditType = ref('')

const getEditTypeLabel = (type) => {
  const labels = {
    individual: 'Personal Details Edit',
    bulk: 'Employment Bulk Update', 
    mixed: 'Mixed Edit (Advanced)'
  }
  return labels[type] || ''
}

// Watch for changes and emit to parent
watch(selectedEditType, (newType) => {
  emit('update:editType', newType)
})
</script>
