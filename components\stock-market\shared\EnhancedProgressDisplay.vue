<template>
  <div class="enhanced-progress-container">
    <!-- Main Progress Header -->
    <div class="progress-header">
      <div class="flex items-center justify-between mb-3">
        <h4 class="text-lg font-semibold text-gray-900 flex items-center">
          <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 mr-3"
            :class="getSpinnerColor()"></div>
          {{ title }}
        </h4>
        <div class="text-sm text-gray-600">
          {{ Math.round(progress) }}%
        </div>
      </div>
    </div>

    <!-- Reasoning Model Special Notice -->
    <div v-if="isReasoningModel" class="reasoning-notice mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
      <div class="flex items-start space-x-3">
        <div class="flex-shrink-0">
          <div class="reasoning-brain-icon">🧠</div>
        </div>
        <div>
          <h5 class="font-semibold text-blue-900 mb-1">Reasoning Model Active</h5>
          <p class="text-sm text-blue-800 mb-2">
            This AI model thinks through the analysis step-by-step, providing higher quality results.
            Expected time: {{ getEstimatedTimeRange() }}
          </p>
          <!-- Reasoning Stages -->
          <div class="reasoning-stages flex space-x-4 text-xs">
            <div class="reasoning-stage" :class="getReasoningStageClass('thinking')">
              💭 Thinking
            </div>
            <div class="reasoning-stage" :class="getReasoningStageClass('analyzing')">
              📊 Analyzing
            </div>
            <div class="reasoning-stage" :class="getReasoningStageClass('concluding')">
              📝 Concluding
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Progress Bar -->
    <div class="main-progress-wrapper mb-4">
      <div class="progress-bar-container">
        <div class="progress-bar-background">
          <div class="progress-bar-fill" 
            :style="{ width: progress + '%' }"
            :class="getProgressBarClass()">
          </div>
        </div>
      </div>
    </div>

    <!-- Stage Indicators -->
    <div class="stage-indicators mb-4">
      <div class="stages-container">
        <div v-for="(stage, index) in stages" :key="stage.id" 
          class="stage-item"
          :class="getStageItemClass(stage, index)">
          <div class="stage-icon">{{ stage.icon }}</div>
          <div class="stage-label">{{ stage.name }}</div>
        </div>
      </div>
    </div>

    <!-- Current Status -->
    <div class="current-status bg-white rounded-lg p-4 border border-gray-200">
      <div class="status-main">
        <div class="current-message text-gray-700 font-medium mb-2">
          {{ currentMessage }}
        </div>
        <div class="status-details flex items-center justify-between text-sm text-gray-500">
          <div class="timing-info flex items-center space-x-4">
            <span class="elapsed-time">
              ⏱️ Elapsed: {{ formatTime(elapsedTime) }}
            </span>
            <span v-if="estimatedRemaining && estimatedRemaining > 0" class="eta">
              🕐 ETA: {{ formatTime(estimatedRemaining) }}
            </span>
          </div>
          <div class="provider-info">
            <span class="provider-badge px-2 py-1 bg-gray-100 rounded text-xs">
              {{ providerDisplayName }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Advanced Options (if enabled) -->
    <div v-if="showAdvancedOptions" class="advanced-options mt-4 p-3 bg-gray-50 rounded-lg">
      <div class="flex items-center justify-between">
        <label class="flex items-center text-sm text-gray-600">
          <input type="checkbox" v-model="allowBackground" class="mr-2">
          Continue in background (switch tabs freely)
        </label>
        <button v-if="allowCancel" @click="$emit('cancel')" 
          class="cancel-btn px-3 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors">
          ❌ Cancel
        </button>
      </div>
    </div>

    <!-- Debug Info (development only) -->
    <div v-if="showDebugInfo && isDevelopment" class="debug-info mt-4 p-2 bg-yellow-50 border border-yellow-200 rounded text-xs">
      <div>Current Stage: {{ currentStage?.id }}</div>
      <div>Progress: {{ progress.toFixed(1) }}%</div>
      <div>Stage Progress: {{ stageProgress.toFixed(1) }}%</div>
      <div>Provider: {{ provider }} | Model: {{ model }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'

// Props
const props = defineProps<{
  title: string
  progress: number
  currentMessage: string
  elapsedTime: number
  provider: string
  model: string
  analysisType: 'ai-analysis' | 'technical' | 'fundamental' | 'news'
  currentStage?: any
  stages?: any[]
  estimatedRemaining?: number
  showAdvancedOptions?: boolean
  allowCancel?: boolean
  showDebugInfo?: boolean
}>()

// Emits
const emit = defineEmits<{
  cancel: []
  backgroundToggle: [enabled: boolean]
}>()

// Local state
const allowBackground = ref(false)
const isDevelopment = computed(() => process.env.NODE_ENV === 'development')

// Watch background toggle
watch(allowBackground, (newValue) => {
  emit('backgroundToggle', newValue)
})

// Computed properties
const isReasoningModel = computed(() => {
  const model = props.model?.toLowerCase() || ''
  return model.includes('r1') || 
         model.includes('reasoning') || 
         model.includes('think') ||
         model.includes('deepseek-r1')
})

const providerDisplayName = computed(() => {
  const providerNames: Record<string, string> = {
    'openai': 'OpenAI',
    'google': 'Google AI',
    'anthropic': 'Anthropic',
    'openrouter': 'OpenRouter',
    'custom': 'Custom'
  }
  return providerNames[props.provider] || props.provider
})

const stageProgress = computed(() => {
  if (!props.currentStage || !props.stages) return 0
  const currentIndex = props.stages.findIndex(s => s.id === props.currentStage.id)
  if (currentIndex === -1) return 0
  return ((currentIndex + 1) / props.stages.length) * 100
})

// Methods
const getSpinnerColor = () => {
  const colors: Record<string, string> = {
    'ai-analysis': 'border-blue-500',
    'technical': 'border-green-500',
    'fundamental': 'border-orange-500',
    'news': 'border-purple-500'
  }
  return colors[props.analysisType] || 'border-blue-500'
}

const getProgressBarClass = () => {
  const classes: Record<string, string> = {
    'ai-analysis': 'bg-gradient-to-r from-blue-500 to-blue-600',
    'technical': 'bg-gradient-to-r from-green-500 to-green-600',
    'fundamental': 'bg-gradient-to-r from-orange-500 to-orange-600',
    'news': 'bg-gradient-to-r from-purple-500 to-purple-600'
  }
  return classes[props.analysisType] || 'bg-gradient-to-r from-blue-500 to-blue-600'
}

const getStageItemClass = (stage: any, index: number) => {
  if (!props.currentStage) return 'stage-pending'
  
  const currentIndex = props.stages?.findIndex(s => s.id === props.currentStage.id) || 0
  
  if (index < currentIndex) return 'stage-completed'
  if (index === currentIndex) return 'stage-active'
  return 'stage-pending'
}

const getReasoningStageClass = (stageName: string) => {
  if (!isReasoningModel.value) return 'inactive'
  
  // Simple heuristic based on progress
  if (props.progress < 30) return stageName === 'thinking' ? 'active' : 'inactive'
  if (props.progress < 80) return stageName === 'analyzing' ? 'active' : stageName === 'thinking' ? 'completed' : 'inactive'
  return stageName === 'concluding' ? 'active' : 'completed'
}

const getEstimatedTimeRange = () => {
  if (!isReasoningModel.value) return '30-60 seconds'
  return '2-3 minutes'
}

const formatTime = (seconds: number) => {
  if (seconds < 60) return `${seconds}s`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  return `${minutes}m ${remainingSeconds}s`
}
</script>

<style scoped>
.enhanced-progress-container {
  @apply bg-white rounded-lg p-6 border border-gray-200;
}

.reasoning-brain-icon {
  @apply text-2xl animate-pulse;
}

.progress-bar-container {
  @apply w-full;
}

.progress-bar-background {
  @apply w-full bg-gray-200 rounded-full h-3 overflow-hidden;
}

.progress-bar-fill {
  @apply h-full transition-all duration-500 ease-out;
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { opacity: 1; }
  50% { opacity: 0.8; }
  100% { opacity: 1; }
}

.stages-container {
  @apply flex justify-between items-center;
}

.stage-item {
  @apply flex flex-col items-center space-y-1 flex-1;
}

.stage-icon {
  @apply text-lg;
}

.stage-label {
  @apply text-xs font-medium text-center;
}

.stage-completed {
  @apply text-green-600;
}

.stage-completed .stage-icon {
  @apply bg-green-100 rounded-full p-1;
}

.stage-active {
  @apply text-blue-600;
}

.stage-active .stage-icon {
  @apply bg-blue-100 rounded-full p-1 animate-pulse;
}

.stage-pending {
  @apply text-gray-400;
}

.reasoning-stages {
  @apply flex space-x-4;
}

.reasoning-stage {
  @apply px-2 py-1 rounded-full transition-all duration-300;
}

.reasoning-stage.active {
  @apply bg-blue-200 text-blue-800 font-medium;
}

.reasoning-stage.completed {
  @apply bg-green-200 text-green-800;
}

.reasoning-stage.inactive {
  @apply bg-gray-100 text-gray-500;
}

.cancel-btn:hover {
  @apply shadow-sm;
}
</style>
