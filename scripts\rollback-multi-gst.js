#!/usr/bin/env node

/**
 * Rollback script for multi-GST migration
 * This script removes the multi-GST fields from existing collections
 */

import mongoose from 'mongoose';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/your-database';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function rollbackFirms() {
  console.log('🔄 Rolling back Firm collection...');
  
  const result = await mongoose.connection.db.collection('firms').updateMany(
    {},
    { 
      $unset: { 
        additionalGSTs: '',
        hasMultipleGSTs: ''
      } 
    }
  );
  
  console.log(`✅ Rolled back ${result.modifiedCount} firm records`);
  return result.modifiedCount;
}

async function rollbackParties() {
  console.log('🔄 Rolling back Party collection...');
  
  const result = await mongoose.connection.db.collection('parties').updateMany(
    {},
    { 
      $unset: { 
        additionalGSTs: '',
        hasMultipleGSTs: ''
      } 
    }
  );
  
  console.log(`✅ Rolled back ${result.modifiedCount} party records`);
  return result.modifiedCount;
}

async function rollbackBills() {
  console.log('🔄 Rolling back Bills collection...');
  
  const result = await mongoose.connection.db.collection('bills').updateMany(
    {},
    { 
      $unset: { 
        gstSelection: ''
      } 
    }
  );
  
  console.log(`✅ Rolled back ${result.modifiedCount} bill records`);
  return result.modifiedCount;
}

async function validateRollback() {
  console.log('🔍 Validating rollback...');
  
  // Check firms
  const firmsWithFields = await mongoose.connection.db.collection('firms').countDocuments({
    $or: [
      { additionalGSTs: { $exists: true } },
      { hasMultipleGSTs: { $exists: true } }
    ]
  });
  
  // Check parties
  const partiesWithFields = await mongoose.connection.db.collection('parties').countDocuments({
    $or: [
      { additionalGSTs: { $exists: true } },
      { hasMultipleGSTs: { $exists: true } }
    ]
  });
  
  // Check bills
  const billsWithFields = await mongoose.connection.db.collection('bills').countDocuments({
    gstSelection: { $exists: true }
  });
  
  if (firmsWithFields === 0 && partiesWithFields === 0 && billsWithFields === 0) {
    console.log('✅ Rollback validation passed');
    return true;
  } else {
    console.log(`❌ Rollback validation failed:`);
    console.log(`  - Firms with fields: ${firmsWithFields}`);
    console.log(`  - Parties with fields: ${partiesWithFields}`);
    console.log(`  - Bills with fields: ${billsWithFields}`);
    return false;
  }
}

async function runRollback() {
  console.log('🔄 Starting Multi-GST Rollback');
  console.log('==============================');
  
  try {
    await connectToDatabase();
    
    // Run rollbacks
    const firmCount = await rollbackFirms();
    const partyCount = await rollbackParties();
    const billCount = await rollbackBills();
    
    // Validate rollback
    const isValid = await validateRollback();
    
    console.log('\n📊 Rollback Summary:');
    console.log('====================');
    console.log(`Firms rolled back: ${firmCount}`);
    console.log(`Parties rolled back: ${partyCount}`);
    console.log(`Bills rolled back: ${billCount}`);
    console.log(`Validation: ${isValid ? 'PASSED' : 'FAILED'}`);
    
    if (isValid) {
      console.log('\n✅ Multi-GST Rollback completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Multi-GST Rollback failed validation!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
  }
}

// Run rollback if this script is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runRollback();
}

export { runRollback };
