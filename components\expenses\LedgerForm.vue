<template>
  <div class="bg-white rounded-lg shadow p-6">
    <form @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Name Field -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Ledger Name *</label>
          <input
            type="text"
            id="name"
            v-model="formData.name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Type Field -->
        <div>
          <label for="type" class="block text-sm font-medium text-gray-700 mb-1">Ledger Type *</label>
          <div class="flex space-x-4">
            <div class="flex items-center">
              <input
                type="radio"
                id="cash"
                v-model="formData.type"
                value="cash"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                required
                :disabled="ledger"
              />
              <label for="cash" class="ml-2 text-sm text-gray-700">Cash</label>
            </div>
            <div class="flex items-center">
              <input
                type="radio"
                id="bank"
                v-model="formData.type"
                value="bank"
                class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                required
                :disabled="ledger"
              />
              <label for="bank" class="ml-2 text-sm text-gray-700">Bank</label>
            </div>
          </div>
          <p v-if="ledger" class="mt-1 text-xs text-gray-500">Ledger type cannot be changed after creation.</p>
        </div>

        <!-- Opening Balance Field -->
        <div>
          <label for="openingBalance" class="block text-sm font-medium text-gray-700 mb-1">Opening Balance *</label>
          <input
            type="number"
            id="openingBalance"
            v-model="formData.openingBalance"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Current Balance Field (Read-only for editing) -->
        <div v-if="ledger">
          <label for="currentBalance" class="block text-sm font-medium text-gray-700 mb-1">Current Balance</label>
          <input
            type="text"
            id="currentBalance"
            :value="formatCurrency(ledger.currentBalance)"
            class="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50"
            readonly
          />
        </div>
      </div>

      <!-- Bank Details Section (if type is bank) -->
      <div v-if="formData.type === 'bank'" class="mt-6">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Bank Details</h3>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Bank Name Field -->
          <div>
            <label for="bankName" class="block text-sm font-medium text-gray-700 mb-1">Bank Name *</label>
            <input
              type="text"
              id="bankName"
              v-model="formData.bankDetails.bankName"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <!-- Account Number Field -->
          <div>
            <label for="accountNumber" class="block text-sm font-medium text-gray-700 mb-1">Account Number *</label>
            <input
              type="text"
              id="accountNumber"
              v-model="formData.bankDetails.accountNumber"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            />
          </div>

          <!-- IFSC Code Field -->
          <div>
            <label for="ifscCode" class="block text-sm font-medium text-gray-700 mb-1">IFSC Code</label>
            <input
              type="text"
              id="ifscCode"
              v-model="formData.bankDetails.ifscCode"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>

          <!-- Branch Field -->
          <div>
            <label for="branch" class="block text-sm font-medium text-gray-700 mb-1">Branch</label>
            <input
              type="text"
              id="branch"
              v-model="formData.bankDetails.branch"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="mt-8 flex justify-end space-x-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>{{ ledger ? 'Update' : 'Save' }}</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue';

export default {
  name: 'LedgerForm',

  props: {
    ledger: {
      type: Object,
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['submit', 'cancel'],

  setup(props, { emit }) {
    // Initialize form data
    const formData = ref({
      name: '',
      type: 'cash',
      openingBalance: 0,
      bankDetails: {
        bankName: '',
        accountNumber: '',
        ifscCode: '',
        branch: ''
      }
    });

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount);
    };

    const handleSubmit = () => {


      // Prepare data for submission
      const submitData = {
        name: formData.value.name,
        type: formData.value.type,
        openingBalance: parseFloat(formData.value.openingBalance)
      };

      // Add bank details if type is bank
      if (formData.value.type === 'bank') {
        submitData.bankDetails = {
          bankName: formData.value.bankDetails.bankName,
          accountNumber: formData.value.bankDetails.accountNumber,
          ifscCode: formData.value.bankDetails.ifscCode || null,
          branch: formData.value.bankDetails.branch || null
        };
      }

      emit('submit', submitData);
    };

    // Initialize
    onMounted(() => {
      // If editing an existing ledger, populate the form
      if (props.ledger) {
        formData.value = {
          name: props.ledger.name,
          type: props.ledger.type,
          openingBalance: props.ledger.openingBalance,
          bankDetails: {
            bankName: props.ledger.bankDetails?.bankName || '',
            accountNumber: props.ledger.bankDetails?.accountNumber || '',
            ifscCode: props.ledger.bankDetails?.ifscCode || '',
            branch: props.ledger.bankDetails?.branch || ''
          }
        };
      }
    });

    // Watch for changes to ledger prop
    watch(() => props.ledger, (newLedger) => {
      if (newLedger) {
        formData.value = {
          name: newLedger.name,
          type: newLedger.type,
          openingBalance: newLedger.openingBalance,
          bankDetails: {
            bankName: newLedger.bankDetails?.bankName || '',
            accountNumber: newLedger.bankDetails?.accountNumber || '',
            ifscCode: newLedger.bankDetails?.ifscCode || '',
            branch: newLedger.bankDetails?.branch || ''
          }
        };
      }
    });

    return {
      formData,
      formatCurrency,
      handleSubmit
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
