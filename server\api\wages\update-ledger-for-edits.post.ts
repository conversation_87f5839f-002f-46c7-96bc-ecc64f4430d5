import { defineEvent<PERSON><PERSON><PERSON>, readBody, createError } from 'h3'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'

export default defineEventHandler(async (event) => {
  const userId = event.context.userId
  const firmId = event.context.user?.firmId

  if (!userId || !firmId) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized'
    })
  }

  try {
    const { ledgerId, oldWages, newWages } = await readBody(event)

    if (!ledgerId) {
      throw createError({
        statusCode: 400,
        message: 'ledgerId is required'
      })
    }

    if (!oldWages || !Array.isArray(oldWages) || !newWages || !Array.isArray(newWages)) {
      throw createError({
        statusCode: 400,
        message: 'oldWages and newWages must be arrays'
      })
    }

    // Calculate total amounts
    const oldTotal = oldWages.reduce((sum, wage) => sum + (wage.net_salary || 0), 0)
    const newTotal = newWages.reduce((sum, wage) => sum + (wage.net_salary || 0), 0)
    
    // Calculate the adjustment amount
    // If newTotal > oldTotal, we need to deduct more from the ledger (negative adjustment)
    // If newTotal < oldTotal, we need to add back to the ledger (positive adjustment)
    const adjustmentAmount = oldTotal - newTotal
    
    console.log(`Updating ledger ${ledgerId} for wage edits:`)
    console.log(`Old total: ${oldTotal}, New total: ${newTotal}, Adjustment: ${adjustmentAmount}`)
    
    // If there's no change, no need to update the ledger
    if (adjustmentAmount === 0) {
      return {
        success: true,
        message: 'No adjustment needed, wage totals unchanged',
        ledgerId,
        adjustmentAmount: 0
      }
    }
    
    const db = getFirestore()
    
    // Use a transaction to ensure data consistency
    await db.runTransaction(async (transaction) => {
      // FIRST: Perform all reads before any writes
      
      // Get the current ledger to update its balance
      const ledgerRef = db.collection('ledgers').doc(ledgerId)
      console.log(`Getting ledger document with ID: ${ledgerId}`)
      const ledgerDoc = await transaction.get(ledgerRef)
      
      if (!ledgerDoc.exists) {
        throw new Error(`Ledger with ID ${ledgerId} not found`)
      }
      
      const ledgerData = ledgerDoc.data()
      console.log(`Ledger found. Current balance: ${ledgerData.currentBalance}`)
      
      // Calculate new balance by adding the adjustment amount
      // If adjustmentAmount is positive (oldTotal > newTotal), we're adding money back
      // If adjustmentAmount is negative (oldTotal < newTotal), we're taking more money out
      const newBalance = ledgerData.currentBalance + adjustmentAmount
      
      // NOW: Perform all writes after all reads are complete
      
      // Update ledger balance
      transaction.update(ledgerRef, {
        currentBalance: newBalance,
        updatedAt: Timestamp.now()
      })
      
      // Add a ledger transaction record for this adjustment
      const ledgerTransactionRef = db.collection('ledgerTransactions').doc()
      transaction.set(ledgerTransactionRef, {
        amount: Math.abs(adjustmentAmount),
        balance: newBalance,
        createdAt: Timestamp.now(),
        date: new Date(),
        description: adjustmentAmount > 0 
          ? `Balance adjustment (credit): Wage edit correction` 
          : `Balance adjustment (debit): Wage edit correction`,
        expenseId: `wage_edit_adjustment_${Date.now()}`,
        firmId: firmId.toString(),
        ledgerId: ledgerId,
        type: adjustmentAmount > 0 ? "credit" : "debit",
        userId: userId.toString()
      })
    })
    
    return {
      success: true,
      message: `Successfully updated ledger balance with adjustment amount: ${adjustmentAmount}`,
      ledgerId,
      adjustmentAmount
    }
  } catch (error) {
    console.error('Error updating Firestore ledger balance for wage edits:', error)
    throw createError({
      statusCode: 500,
      message: error?.message || 'Error updating Firestore ledger balance for wage edits'
    })
  }
})
