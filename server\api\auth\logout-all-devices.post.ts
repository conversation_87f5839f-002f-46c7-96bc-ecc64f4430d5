// server/api/auth/logout-all-devices.post.ts
import { defineEvent<PERSON>and<PERSON>, createError } from 'h3';
import { verifyToken } from '../../utils/auth';
import User from '../../models/User';

export default defineEventHandler(async (event) => {
  try {
    // Verify the user is authenticated
    const currentUser = await verifyToken(event);
    
    // Find the user in the database
    const user = await User.findById(currentUser.id);
    if (!user) {
      throw createError({
        statusCode: 404,
        statusMessage: 'User not found'
      });
    }

    // Clear all active sessions
    user.activeSessions = [];
    await user.save();

    console.log(`All sessions cleared for user: ${user.email}`);

    return {
      success: true,
      message: 'Successfully logged out from all devices'
    };

  } catch (error: any) {
    console.error('Logout all devices error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to logout from all devices'
    });
  }
});
