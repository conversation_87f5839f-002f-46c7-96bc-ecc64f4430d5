import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'
import { LaborPaymentService } from '~/utils/laborPaymentService.js'

/**
 * Test endpoint to create and validate payment scenarios
 * This endpoint helps test the new payment logic with predefined scenarios
 */
export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { firmId, scenario, action } = body

    if (!firmId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'firmId is required'
      })
    }

    // Get Supabase configuration
    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found for this firm'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    const paymentService = new LaborPaymentService(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Test group data
    const testGroupData = {
      name: 'Test Labor Group',
      color: '#3B82F6',
      firm_id: firmId
    }

    if (action === 'setup') {
      // Create test group and labor profiles
      const { data: group, error: groupError } = await supabase
        .from('labor_groups')
        .insert([testGroupData])
        .select()
        .single()

      if (groupError) {
        throw new Error(`Failed to create test group: ${groupError.message}`)
      }

      // Create test labor profiles
      const laborProfiles = [
        { name: 'Test Worker A', daily_rate: 500, group_id: group.id, firm_id: firmId },
        { name: 'Test Worker B', daily_rate: 600, group_id: group.id, firm_id: firmId },
        { name: 'Test Worker C', daily_rate: 550, group_id: group.id, firm_id: firmId }
      ]

      const { data: profiles, error: profilesError } = await supabase
        .from('labor_profiles')
        .insert(laborProfiles)
        .select()

      if (profilesError) {
        throw new Error(`Failed to create test profiles: ${profilesError.message}`)
      }

      return {
        success: true,
        message: 'Test data setup completed',
        data: {
          group,
          profiles
        }
      }
    }

    if (action === 'create-scenario' && scenario) {
      // Get test group
      const { data: groups } = await supabase
        .from('labor_groups')
        .select('*')
        .eq('name', 'Test Labor Group')
        .eq('firm_id', firmId)

      if (!groups || groups.length === 0) {
        throw new Error('Test group not found. Run setup first.')
      }

      const testGroup = groups[0]

      // Get test profiles
      const { data: profiles } = await supabase
        .from('labor_profiles')
        .select('*')
        .eq('group_id', testGroup.id)

      if (scenario === 'scenario1') {
        // Create Scenario 1: Settled period with final payment
        const attendanceRecords = []
        const paymentRecords = []

        // Create attendance records for period 04-04-2025 to 26-04-2025
        const startDate = new Date('2025-04-04')
        const endDate = new Date('2025-04-26')

        for (const profile of profiles) {
          // Add attendance records
          for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
            const daysWorked = Math.random() > 0.1 ? 1 : 0 // 90% attendance
            if (daysWorked > 0) {
              attendanceRecords.push({
                labor_id: profile.id,
                attendance_date: d.toISOString().split('T')[0],
                days_worked: daysWorked,
                daily_rate: profile.daily_rate,
                period_start: '2025-04-04',
                period_end: '2025-04-26',
                site_expenses: Math.random() > 0.8 ? Math.floor(Math.random() * 200) : 0,
                firm_id: firmId
              })
            }
          }
        }

        // Insert attendance records
        const { error: attendanceError } = await supabase
          .from('attendance_records')
          .insert(attendanceRecords)

        if (attendanceError) {
          throw new Error(`Failed to create attendance records: ${attendanceError.message}`)
        }

        // Create payment records
        const payments = [
          { group_id: testGroup.id, payment_date: '2025-04-06', amount: 2000, payment_type: 'Advance', payment_method: 'cash', firm_id: firmId },
          { group_id: testGroup.id, payment_date: '2025-04-09', amount: 1500, payment_type: 'Advance', payment_method: 'cash', firm_id: firmId },
          { group_id: testGroup.id, payment_date: '2025-04-14', amount: 2000, payment_type: 'Site Expense', payment_method: 'cash', firm_id: firmId },
          { group_id: testGroup.id, payment_date: '2025-04-20', amount: 3000, payment_type: 'Advance', payment_method: 'cash', firm_id: firmId },
          { group_id: testGroup.id, payment_date: '2025-04-28', amount: 29750, payment_type: 'Final Payment', payment_method: 'cash', firm_id: firmId }
        ]

        const { error: paymentsError } = await supabase
          .from('payment_records')
          .insert(payments)

        if (paymentsError) {
          throw new Error(`Failed to create payment records: ${paymentsError.message}`)
        }

        return {
          success: true,
          message: 'Scenario 1 data created successfully',
          data: {
            attendanceRecords: attendanceRecords.length,
            paymentRecords: payments.length,
            groupId: testGroup.id
          }
        }
      }

      // Add more scenarios as needed...
    }

    if (action === 'validate') {
      // Get test group
      const { data: groups } = await supabase
        .from('labor_groups')
        .select('*')
        .eq('name', 'Test Labor Group')
        .eq('firm_id', firmId)

      if (!groups || groups.length === 0) {
        throw new Error('Test group not found. Run setup first.')
      }

      const testGroup = groups[0]

      // Calculate unpaid amounts using new logic
      const unpaidAmounts = await paymentService.calculateUnpaidAmounts(testGroup.id)
      const validation = await paymentService.validatePaymentData(testGroup.id)

      return {
        success: true,
        message: 'Validation completed',
        data: {
          unpaidAmounts,
          validation,
          groupId: testGroup.id
        }
      }
    }

    if (action === 'cleanup') {
      // Clean up test data
      const { data: groups } = await supabase
        .from('labor_groups')
        .select('id')
        .eq('name', 'Test Labor Group')
        .eq('firm_id', firmId)

      if (groups && groups.length > 0) {
        const groupId = groups[0].id

        // Delete in correct order due to foreign key constraints
        await supabase.from('attendance_records').delete().eq('firm_id', firmId)
        await supabase.from('payment_records').delete().eq('group_id', groupId)
        await supabase.from('labor_profiles').delete().eq('group_id', groupId)
        await supabase.from('labor_groups').delete().eq('id', groupId)
      }

      return {
        success: true,
        message: 'Test data cleaned up successfully'
      }
    }

    throw createError({
      statusCode: 400,
      statusMessage: 'Invalid action. Use: setup, create-scenario, validate, or cleanup'
    })

  } catch (error) {
    console.error('Error in test scenarios:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to execute test scenario'
    })
  }
})
