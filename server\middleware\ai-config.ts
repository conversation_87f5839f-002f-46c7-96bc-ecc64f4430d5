import type { AIConfiguration } from '~/types/ai'

export default defineEventHandler(async (event) => {
  // Only process AI-related routes
  if (!event.node.req.url?.includes('/api/ai/') && 
      !event.node.req.url?.includes('/api/stock-market/') && 
      !event.node.req.url?.includes('ai-analysis') &&
      !event.node.req.url?.includes('fundamental-analysis') &&
      !event.node.req.url?.includes('stock-news-search') &&
      !event.node.req.url?.includes('mutual-fund-technical-analysis')) {
    return
  }

  // Skip validation endpoint
  if (event.node.req.url?.includes('/api/ai/validate-key')) {
    return
  }

  try {
    // Check for AI configuration in headers
    const aiConfigHeader = getHeader(event, 'x-ai-config')
    
    if (aiConfigHeader) {
      const aiConfig: AIConfiguration = JSON.parse(aiConfigHeader)
      
      // Basic validation
      if (!aiConfig.provider || !aiConfig.apiKey || !aiConfig.model) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid AI configuration. Provider, API key, and model are required.'
        })
      }

      // Store validated config in event context for use by handlers
      event.context.aiConfig = aiConfig
    }
  } catch (error: any) {
    // If parsing fails or validation fails, let the handler deal with fallback
    console.warn('AI config validation warning:', error.message)
  }
})
