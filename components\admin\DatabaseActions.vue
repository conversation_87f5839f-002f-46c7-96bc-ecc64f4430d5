<template>
  <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
    <h3 class="text-lg font-semibold mb-4 flex items-center">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
      </svg>
      Database Actions
    </h3>

    <!-- Action cards in a row on larger screens -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Backup Section Card -->
      <div class="border border-gray-200 rounded-lg p-3 shadow-sm h-full">
        <h4 class="font-medium text-gray-700 mb-2 pb-1 border-b">Backup Database</h4>
        <div class="flex flex-wrap gap-2">
          <button
            @click="backupDatabase('mongodb')"
            class="px-3 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center"
            :disabled="loading.mongoBackup"
          >
            <svg v-if="loading.mongoBackup" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
            </svg>
            Backup MongoDB
          </button>

          <button
            @click="backupDatabase('firestore')"
            class="px-3 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors flex items-center"
            :disabled="loading.firestoreBackup"
          >
            <svg v-if="loading.firestoreBackup" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
            </svg>
            Backup Firestore
          </button>
        </div>
      </div>

      <!-- Restore Section Card -->
      <div class="border border-gray-200 rounded-lg p-3 shadow-sm h-full overflow-auto">
        <h4 class="font-medium text-gray-700 mb-2 pb-1 border-b">Restore Database</h4>

        <!-- MongoDB Restore -->
        <div class="mb-3">
          <div class="flex flex-col space-y-2 mb-2">
            <div class="flex items-center">
              <span class="text-blue-600 font-medium w-24">MongoDB:</span>
              <select
                v-model="restoreOptions.mongodb.model"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="" disabled>Select a model</option>
                <option v-for="model in models" :key="model.name" :value="model.name">
                  {{ model.name }}
                </option>
              </select>
            </div>

            <div class="flex items-center">
              <span class="text-blue-600 font-medium w-24">Mode:</span>
              <select
                v-model="restoreOptions.mongodb.mode"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="merge">Merge</option>
                <option value="replace">Replace All</option>
              </select>
            </div>

            <div class="flex items-center space-x-2">
              <label class="flex-1 cursor-pointer bg-blue-500 text-white rounded-md px-3 py-1 hover:bg-blue-600 transition-colors flex items-center justify-center text-sm">
                <input
                  type="file"
                  ref="mongoFileInput"
                  class="hidden"
                  accept=".json"
                  @change="handleFileChange('mongodb')"
                />
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                {{ mongoFileSelected ? 'File Selected' : 'Choose File' }}
              </label>

              <button
                @click="restoreDatabase('mongodb')"
                class="flex-1 px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center justify-center text-sm"
                :disabled="loading.mongoRestore || !mongoFileSelected || !restoreOptions.mongodb.model"
              >
                <svg v-if="loading.mongoRestore" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                </svg>
                Restore
              </button>
            </div>
          </div>
        </div>

        <!-- Firestore Restore -->
        <div>
          <div class="flex flex-col space-y-2">
            <div class="flex items-center">
              <span class="text-orange-600 font-medium w-24">Firestore:</span>
              <select
                v-model="restoreOptions.firestore.collection"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="" disabled>Select a collection</option>
                <option v-for="collection in firestoreCollections" :key="collection.name" :value="collection.name">
                  {{ collection.name }}
                </option>
              </select>
            </div>

            <div class="flex items-center">
              <span class="text-orange-600 font-medium w-24">Mode:</span>
              <select
                v-model="restoreOptions.firestore.mode"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="merge">Merge</option>
                <option value="replace">Replace All</option>
              </select>
            </div>

            <div class="flex items-center space-x-2">
              <label class="flex-1 cursor-pointer bg-orange-500 text-white rounded-md px-3 py-1 hover:bg-orange-600 transition-colors flex items-center justify-center text-sm">
                <input
                  type="file"
                  ref="firestoreFileInput"
                  class="hidden"
                  accept=".json"
                  @change="handleFileChange('firestore')"
                />
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                </svg>
                {{ firestoreFileSelected ? 'File Selected' : 'Choose File' }}
              </label>

              <button
                @click="restoreDatabase('firestore')"
                class="flex-1 px-3 py-1 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors flex items-center justify-center text-sm"
                :disabled="loading.firestoreRestore || !firestoreFileSelected || !restoreOptions.firestore.collection"
              >
                <svg v-if="loading.firestoreRestore" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-8l-4-4m0 0l-4 4m4-4v12" />
                </svg>
                Restore
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- Export Section Card -->
      <div class="border border-gray-200 rounded-lg p-3 shadow-sm h-full">
        <h4 class="font-medium text-gray-700 mb-2 pb-1 border-b">Export Data</h4>

        <!-- MongoDB Export -->
        <div class="mb-4">
          <div class="flex flex-col space-y-2">
            <div class="flex items-center">
              <span class="text-blue-600 font-medium w-24">MongoDB:</span>
              <select
                v-model="exportOptions.mongodb.model"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="" disabled>Select a model</option>
                <option v-for="model in models" :key="model.name" :value="model.name">
                  {{ model.name }}
                </option>
              </select>
            </div>

            <div class="flex items-center">
              <span class="text-blue-600 font-medium w-24">Format:</span>
              <select
                v-model="exportOptions.mongodb.format"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="json">JSON</option>
                <option value="excel">Excel</option>
              </select>
            </div>

            <div class="flex justify-end">
              <button
                @click="exportData('mongodb')"
                class="px-4 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center text-sm"
                :disabled="loading.mongoExport || !exportOptions.mongodb.model"
              >
                <svg v-if="loading.mongoExport" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export
              </button>
            </div>
          </div>
        </div>

        <!-- Firestore Export -->
        <div>
          <div class="flex flex-col space-y-2">
            <div class="flex items-center">
              <span class="text-orange-600 font-medium w-24">Firestore:</span>
              <select
                v-model="exportOptions.firestore.collection"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="" disabled>Select a collection</option>
                <option v-for="collection in firestoreCollections" :key="collection.name" :value="collection.name">
                  {{ collection.name }}
                </option>
              </select>
            </div>

            <div class="flex items-center">
              <span class="text-orange-600 font-medium w-24">Format:</span>
              <select
                v-model="exportOptions.firestore.format"
                class="flex-1 px-2 py-1 border border-gray-300 rounded-md text-sm"
              >
                <option value="json">JSON</option>
                <option value="excel">Excel</option>
              </select>
            </div>

            <div class="flex justify-end">
              <button
                @click="exportData('firestore')"
                class="px-4 py-1 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors flex items-center text-sm"
                :disabled="loading.firestoreExport || !exportOptions.firestore.collection"
              >
                <svg v-if="loading.firestoreExport" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                </svg>
                Export
              </button>
            </div>
          </div>
        </div>
      </div>
    </div> <!-- End of grid -->

    <!-- Results Section -->
    <div v-if="lastAction" class="mt-4 p-3 bg-gray-50 rounded-md border border-gray-200">
      <div class="flex justify-between items-start">
        <h5 class="font-medium text-gray-700">Last Action Result:</h5>
        <button @click="lastAction = null" class="text-gray-400 hover:text-gray-600">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <div class="mt-2">
        <div class="flex items-center text-sm">
          <span class="font-medium mr-2">Type:</span>
          <span :class="{
            'text-blue-600': lastAction.type === 'backup',
            'text-green-600': lastAction.type === 'export',
            'text-purple-600': lastAction.type === 'restore'
          }">
            {{ lastAction.type === 'backup' ? 'Backup' : lastAction.type === 'export' ? 'Export' : 'Restore' }}
          </span>
        </div>

        <div class="flex items-center text-sm mt-1">
          <span class="font-medium mr-2">Database:</span>
          <span :class="lastAction.database === 'mongodb' ? 'text-blue-600' : 'text-orange-600'">
            {{ lastAction.database === 'mongodb' ? 'MongoDB' : 'Firestore' }}
          </span>
        </div>

        <div class="flex items-center text-sm mt-1">
          <span class="font-medium mr-2">Status:</span>
          <span :class="lastAction.success ? 'text-green-600' : 'text-red-600'">
            {{ lastAction.success ? 'Success' : 'Failed' }}
          </span>
        </div>

        <div v-if="lastAction.message" class="text-sm mt-1">
          <span class="font-medium">Message:</span>
          <p class="text-gray-700 mt-1">{{ lastAction.message }}</p>
        </div>

        <!-- No download link needed as files are downloaded directly -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import useToast from '~/composables/ui/useToast';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { useRealTimeStatus } from '~/composables/utils/useRealTimeStatus';

const props = defineProps({
  models: {
    type: Array,
    default: () => []
  },
  firestoreCollections: {
    type: Array,
    default: () => []
  }
});

const { success, error } = useToast();

// Loading states
const loading = ref({
  mongoBackup: false,
  firestoreBackup: false,
  mongoExport: false,
  firestoreExport: false,
  mongoRestore: false,
  firestoreRestore: false
});

// File input references
const mongoFileInput = ref(null);
const firestoreFileInput = ref(null);
const mongoFileSelected = ref(false);
const firestoreFileSelected = ref(false);

// Export options
const exportOptions = ref({
  mongodb: {
    model: '',
    format: 'excel' // Default to Excel instead of JSON
  },
  firestore: {
    collection: '',
    format: 'excel' // Default to Excel instead of JSON
  }
});

// Restore options
const restoreOptions = ref({
  mongodb: {
    model: '',
    mode: 'merge' // merge or replace
  },
  firestore: {
    collection: '',
    mode: 'merge' // merge or replace
  }
});

// Last action result
const lastAction = ref(null);

// Backup database with REAL-TIME STATUS
async function backupDatabase(type) {
  const loadingKey = type === 'mongodb' ? 'mongoBackup' : 'firestoreBackup';
  loading.value[loadingKey] = true;

  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `backup_${type}_${Date.now()}`;
  const dbName = type === 'mongodb' ? 'MongoDB' : 'Firestore';

  // Start real-time monitoring
  startOperation(operationId, `${dbName} Backup`, 'Initializing database backup...');

  try {
    updateProgress(operationId, 1, 'Preparing backup request...', 10);

    // Create a timestamp for the filename
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
    const filename = `${type}_backup_${timestamp}.zip`;

    updateProgress(operationId, 2, 'Connecting to database...', 20);

    // Use the API with auth composable which handles CSRF tokens
    const api = useApiWithAuth();

    updateProgress(operationId, 3, 'Sending backup request...', 30);

    // Make the request with responseType: 'blob' to get binary data
    const response = await api.post(`/api/database/backup/${type}`, {}, {
      responseType: 'blob'
    });

    updateProgress(operationId, 4, 'Processing backup data...', 70);

    // Create a download link for the blob
    const url = window.URL.createObjectURL(response);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);

    updateProgress(operationId, 5, 'Starting download...', 90);

    // Trigger the download
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    updateProgress(operationId, 6, 'Backup completed successfully', 100);

    // Show success message
    success(`${dbName} backup download started`);

    lastAction.value = {
      type: 'backup',
      database: type,
      success: true,
      timestamp: new Date().toISOString(),
      message: `Backup downloaded as ${filename}`
    };

    completeOperation(operationId, true, `${dbName} backup completed successfully`);

  } catch (err) {
    console.error(`${type} backup error:`, err);
    error(`Failed to backup ${dbName} database`);

    lastAction.value = {
      type: 'backup',
      database: type,
      success: false,
      timestamp: new Date().toISOString(),
      message: err.message || 'Unknown error occurred'
    };

    completeOperation(operationId, false, `${dbName} backup failed: ${err.message}`);
  } finally {
    loading.value[loadingKey] = false;
  }
}

// Export data
async function exportData(type) {
  const loadingKey = type === 'mongodb' ? 'mongoExport' : 'firestoreExport';
  loading.value[loadingKey] = true;

  try {
    // Prepare the payload
    const payload = type === 'mongodb'
      ? {
          modelName: exportOptions.value.mongodb.model,
          format: exportOptions.value.mongodb.format
        }
      : {
          collectionName: exportOptions.value.firestore.collection,
          format: exportOptions.value.firestore.format
        };

    // Create a timestamp for the filename
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
    const format = type === 'mongodb' ? exportOptions.value.mongodb.format : exportOptions.value.firestore.format;
    const modelOrCollection = type === 'mongodb' ? exportOptions.value.mongodb.model : exportOptions.value.firestore.collection;
    const filename = `${modelOrCollection}_export_${timestamp}.${format === 'excel' ? 'xlsx' : 'json'}`;

    // Use the API with auth composable which handles CSRF tokens
    const api = useApiWithAuth();

    // Make the request with responseType: 'blob' to get binary data
    const response = await api.post(`/api/database/export/${type}`, payload, {
      responseType: 'blob'
    });

    // Create a download link for the blob
    const url = window.URL.createObjectURL(response);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);

    // Trigger the download
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    // Show success message
    success(`${type === 'mongodb' ? 'MongoDB' : 'Firestore'} export download started`);

    lastAction.value = {
      type: 'export',
      database: type,
      success: true,
      timestamp: new Date().toISOString(),
      message: `Export downloaded as ${filename}`
    };
  } catch (err) {
    console.error(`${type} export error:`, err);
    error(`Failed to export ${type === 'mongodb' ? 'MongoDB' : 'Firestore'} data`);

    lastAction.value = {
      type: 'export',
      database: type,
      success: false,
      timestamp: new Date().toISOString(),
      message: err.message || 'Unknown error occurred'
    };
  } finally {
    loading.value[loadingKey] = false;
  }
}

// Handle file selection for restore
function handleFileChange(type) {
  if (type === 'mongodb') {
    mongoFileSelected.value = mongoFileInput.value.files.length > 0;
  } else {
    firestoreFileSelected.value = firestoreFileInput.value.files.length > 0;
  }
}

// Restore database
async function restoreDatabase(type) {
  const loadingKey = type === 'mongodb' ? 'mongoRestore' : 'firestoreRestore';
  loading.value[loadingKey] = true;

  try {
    // Get the file input element
    const fileInput = type === 'mongodb' ? mongoFileInput.value : firestoreFileInput.value;

    if (!fileInput.files || fileInput.files.length === 0) {
      throw new Error('No file selected');
    }

    // Create form data
    const formData = new FormData();
    formData.append('file', fileInput.files[0]);

    // Add model/collection name and mode
    if (type === 'mongodb') {
      formData.append('modelName', restoreOptions.value.mongodb.model);
      formData.append('mode', restoreOptions.value.mongodb.mode);
    } else {
      formData.append('collectionName', restoreOptions.value.firestore.collection);
      formData.append('mode', restoreOptions.value.firestore.mode);
    }

    // Use the API with auth composable which handles CSRF tokens
    const api = useApiWithAuth();

    // Make the request
    const response = await api.post(`/api/database/restore/${type}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });

    // Show success message
    success(`${type === 'mongodb' ? 'MongoDB' : 'Firestore'} restore completed successfully`);

    // Reset file input
    fileInput.value = '';
    if (type === 'mongodb') {
      mongoFileSelected.value = false;
    } else {
      firestoreFileSelected.value = false;
    }

    lastAction.value = {
      type: 'restore',
      database: type,
      success: true,
      timestamp: new Date().toISOString(),
      message: response.message || `Successfully restored data to ${type === 'mongodb' ? restoreOptions.value.mongodb.model : restoreOptions.value.firestore.collection}`
    };
  } catch (err) {
    console.error(`${type} restore error:`, err);
    error(`Failed to restore ${type === 'mongodb' ? 'MongoDB' : 'Firestore'} data`);

    lastAction.value = {
      type: 'restore',
      database: type,
      success: false,
      timestamp: new Date().toISOString(),
      message: err.message || 'Unknown error occurred'
    };
  } finally {
    loading.value[loadingKey] = false;
  }
}
</script>
