import { getFirestore } from 'firebase-admin/firestore';

// Track admin login status
let adminLoggedIn = false;
let lastAdminActivity = 0;
let isBackupInProgress = false;

// Function to check if admin is currently logged in
function isAdminActive() {
  const currentTime = Date.now();
  // Consider admin active if there was activity in the last 10 minutes
  return adminLoggedIn && (currentTime - lastAdminActivity) < (10 * 60 * 1000);
}

// Function to trigger immediate Excel backup and cleanup
async function triggerImmediateExcelBackup() {
  try {
    // Prevent multiple simultaneous backups
    if (isBackupInProgress) {
      console.log('[API Logs Auto Backup] ⏳ Backup already in progress, skipping...');
      return;
    }

    isBackupInProgress = true;
    (global as any).backupInProgress = true; // Global flag for API status
    console.log('[API Logs Auto Backup] 🚀 TRIGGERING IMMEDIATE EXCEL BACKUP...');

    const db = getFirestore();
    const collectionName = 'api_logs';
    const collectionRef = db.collection(collectionName);

    // Get all documents
    const snapshot = await collectionRef.get();

    if (snapshot.empty) {
      console.log('[API Logs Auto Backup] Collection is empty, no backup needed');
      return;
    }

    // Process documents
    const documents: any[] = [];
    snapshot.forEach(doc => {
      const data = doc.data();

      // Convert Firestore Timestamps to ISO strings
      const processedData: any = {};
      Object.keys(data).forEach(key => {
        const value = data[key];
        if (value && typeof value === 'object' && value.constructor && value.constructor.name === 'Timestamp') {
          processedData[key] = value.toDate().toISOString();
        } else {
          processedData[key] = value;
        }
      });

      documents.push({
        id: doc.id,
        ...processedData
      });
    });

    // Create Excel workbook
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('API Logs');

    // Define columns
    worksheet.columns = [
      { header: 'ID', key: 'id', width: 20 },
      { header: 'Timestamp', key: 'timestamp', width: 25 },
      { header: 'Method', key: 'method', width: 10 },
      { header: 'Path', key: 'path', width: 40 },
      { header: 'Status Code', key: 'statusCode', width: 12 },
      { header: 'Response Time (ms)', key: 'responseTime', width: 18 },
      { header: 'User ID', key: 'userId', width: 25 },
      { header: 'IP Address', key: 'ip', width: 15 },
      { header: 'User Agent', key: 'userAgent', width: 50 }
    ];

    // Add header styling
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFE0E0E0' }
    };

    // Add data rows
    documents.forEach(doc => {
      worksheet.addRow({
        id: doc.id,
        timestamp: doc.timestamp || '',
        method: doc.method || '',
        path: doc.path || '',
        statusCode: doc.statusCode || '',
        responseTime: doc.responseTime || '',
        userId: doc.userId || '',
        ip: doc.ip || '',
        userAgent: doc.userAgent || ''
      });
    });

    // Create timestamp for filename
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
    const filename = `api_logs_auto_backup_${timestamp}.xlsx`;

    // Generate Excel buffer
    const stream = new PassThrough();
    await workbook.xlsx.write(stream);

    // Convert stream to buffer
    const chunks = [];
    for await (const chunk of stream) {
      chunks.push(chunk);
    }
    const excelBuffer = Buffer.concat(chunks);

    // Store the Excel data globally for admin to download
    (global as any).autoBackupReady = {
      buffer: excelBuffer,
      filename,
      count: documents.length,
      timestamp: new Date().toISOString()
    };

    console.log(`[API Logs Auto Backup] ✅ Excel backup ready for download: ${filename} (${documents.length} records)`);

    // Delete all documents from the collection after successful Excel generation
    const batch = db.batch();
    const batchSize = 400; // Firestore limit is 500 operations per batch
    let operationCount = 0;
    let batchCount = 0;

    for (const doc of snapshot.docs) {
      batch.delete(doc.ref);
      operationCount++;

      // Commit when batch size is reached
      if (operationCount >= batchSize) {
        await batch.commit();
        batchCount++;
        operationCount = 0;
      }
    }

    // Commit any remaining operations
    if (operationCount > 0) {
      await batch.commit();
      batchCount++;
    }

    console.log(`[API Logs Auto Backup] 🧹 Collection cleaned up in ${batchCount} batches`);
    console.log(`[API Logs Auto Backup] ✅ COMPLETED: Auto backup ready for download and collection cleaned up!`);

  } catch (error) {
    console.error('[API Logs Auto Backup] Error:', error);
  } finally {
    isBackupInProgress = false;
    (global as any).backupInProgress = false; // Clear global flag
  }
}

// Function to check and trigger immediate backup if needed
async function checkAndTriggerBackup() {
  try {
    // Only run if admin is logged in and no backup in progress
    if (!isAdminActive() || isBackupInProgress) {
      return;
    }

    const db = getFirestore();
    const collectionName = 'api_logs';
    const collectionRef = db.collection(collectionName);

    // Count documents in the collection
    const countSnapshot = await collectionRef.count().get();
    const count = countSnapshot.data().count;

    // If count >= 1000, trigger IMMEDIATE backup and STOP checking
    if (count >= 1000) {
      console.log(`[API Logs Auto Backup] 🚨 THRESHOLD REACHED: ${count} logs - IMMEDIATE backup!`);

      // Trigger immediate Excel backup (this will set isBackupInProgress = true)
      triggerImmediateExcelBackup().catch((err: any) => {
        console.error('[API Logs Auto Backup] Backup failed:', err);
        isBackupInProgress = false; // Reset on error
      });
    }
  } catch (error) {
    console.error('[API Logs Auto Backup] Check error:', error);
  }
}

export default defineEventHandler(async (event) => {
  // 🛑 AUTO BACKUP DISABLED - MANUAL ONLY
  // Just track admin activity, no auto backup
  const user = event.context.user;
  if (user && user.role === 'admin') {
    adminLoggedIn = true;
    lastAdminActivity = Date.now();
  }

  // NO AUTO BACKUP - MANUAL ONLY
});
