<template>
  <!-- Bottom Console Panel - Always visible -->
  <div class="fixed bottom-0 left-0 right-0 z-40 bg-white border-t border-gray-200 shadow-lg">
    <!-- Console Header - Always visible and clickable -->
    <div
      class="bg-gray-50 px-4 py-3 flex items-center justify-between cursor-pointer hover:bg-gray-100 transition-colors"
      :class="{ 'border-b border-gray-200': !isMinimized }"
      @click="isMinimized = !isMinimized"
    >
      <div class="flex items-center space-x-4">
        <h3 class="text-sm font-semibold text-gray-800">Admin Console</h3>
        <div class="flex items-center space-x-2">
          <span class="text-xs text-gray-500">Operations:</span>
          <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full font-medium">
            {{ globalStatus.operations.length }}
          </span>
        </div>
        <!-- Status indicator when minimized -->
        <div v-if="isMinimized && globalStatus.operations.length > 0" class="flex items-center space-x-2">
          <svg class="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span class="text-xs text-blue-600 font-medium">{{ globalStatus.operations.length }} running</span>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <button @click.stop="clearAll" class="text-gray-400 hover:text-gray-600 p-1" title="Clear All">
          <svg class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
          </svg>
        </button>
        <div class="text-gray-400 p-1" :title="isMinimized ? 'Click to expand' : 'Click to minimize'">
          <svg v-if="isMinimized" class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>
          </svg>
          <svg v-else class="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </div>
      </div>
    </div>

    <!-- Console Content -->
    <div v-if="!isMinimized" class="h-64 flex">
      <!-- Left Column: Operations Console -->
      <div class="w-1/2 border-r border-gray-200 flex flex-col">
        <!-- Operations Header -->
        <div class="px-3 py-2 bg-gray-100 border-b border-gray-200">
          <h4 class="text-xs font-semibold text-gray-700 uppercase tracking-wide">Operations</h4>
        </div>
        
        <!-- Operations Content -->
        <div class="flex-1 overflow-y-auto p-3">
          <!-- No Operations Message -->
          <div v-if="globalStatus.operations.length === 0" class="text-center text-gray-500 py-8">
            <svg class="mx-auto h-8 w-8 text-gray-400 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="text-sm">No active operations</p>
            <p class="text-xs text-gray-400 mt-1">Operations will appear here when running</p>
          </div>
          
          <!-- Operations List -->
          <div v-for="operation in globalStatus.operations" :key="operation.id" class="mb-3 p-3 bg-gray-50 rounded border">
            <!-- Operation Header -->
            <div class="flex items-center justify-between mb-2">
              <h4 class="text-sm font-medium text-gray-900">{{ operation.title }}</h4>
              <div class="flex items-center space-x-2">
                <!-- Status Icon -->
                <svg v-if="operation.status === 'running'" class="animate-spin h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else-if="operation.status === 'completed'" class="h-4 w-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                <svg v-else-if="operation.status === 'failed'" class="h-4 w-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
                
                <!-- Duration -->
                <span class="text-xs text-gray-500">
                  {{ formatDuration(operation) }}
                </span>
              </div>
            </div>

            <!-- Progress Bar -->
            <div v-if="operation.status === 'running'" class="mb-2">
              <div class="bg-gray-200 rounded-full h-2">
                <div 
                  class="bg-blue-500 h-2 rounded-full transition-all duration-300"
                  :style="{ width: `${operation.progress}%` }"
                ></div>
              </div>
              <div class="text-xs text-gray-500 mt-1">{{ operation.progress }}%</div>
            </div>

            <!-- Current Description -->
            <p class="text-sm text-gray-600 mb-2">{{ operation.description }}</p>

            <!-- Steps History -->
            <div v-if="operation.steps.length > 0" class="space-y-1">
              <div v-for="(step, index) in operation.steps.slice(-2)" :key="index" class="flex items-center text-xs text-gray-500">
                <span class="w-4 text-center">{{ step.step }}</span>
                <span class="ml-2">{{ step.message }}</span>
                <span class="ml-auto">{{ formatTime(step.timestamp) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Right Column: Real-time Code -->
      <div class="w-1/2 flex flex-col">
        <!-- Code Header -->
        <div class="px-3 py-2 bg-gray-100 border-b border-gray-200">
          <h4 class="text-xs font-semibold text-gray-700 uppercase tracking-wide">Operation Logs</h4>
        </div>
        
        <!-- Operation Logs Content -->
        <div class="flex-1 overflow-y-auto p-3 bg-gray-900 text-green-400 font-mono text-xs">
          <div v-if="operationLogs.length === 0" class="text-center text-gray-500 py-8">
            <svg class="mx-auto h-8 w-8 text-gray-600 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            <p class="text-sm">No operation logs</p>
            <p class="text-xs text-gray-600 mt-1">Real operation logs will appear here</p>
          </div>

          <!-- Operation Log Lines -->
          <div v-for="(log, index) in operationLogs.slice(-20)" :key="index" class="mb-1">
            <span class="text-gray-600 mr-2">{{ log.timestamp }}:</span>
            <span :class="log.type === 'error' ? 'text-red-400' : log.type === 'success' ? 'text-green-400' : 'text-blue-400'">
              {{ log.message }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRealTimeStatus } from '~/composables/utils/useRealTimeStatus';

const { globalStatus, clearAll: clearOperations } = useRealTimeStatus();

// Console panel state
const isMinimized = ref(false);
const operationLogs = ref([]);

// Clear all operations and logs
const clearAll = () => {
  clearOperations();
  operationLogs.value = [];
};

// Real operation logging function
const addOperationLog = (message, type = 'info') => {
  const timestamp = new Date().toLocaleTimeString('en-US', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  operationLogs.value.push({
    timestamp,
    message,
    type
  });

  // Keep only last 50 logs
  if (operationLogs.value.length > 50) {
    operationLogs.value = operationLogs.value.slice(-50);
  }
};

// Watch for operation changes and log them
watch(() => globalStatus.operations, (newOperations, oldOperations) => {
  // Log when new operations start
  if (newOperations.length > (oldOperations?.length || 0)) {
    const newOperation = newOperations[newOperations.length - 1];
    addOperationLog(`Started: ${newOperation.title}`, 'info');
  }

  // Log operation progress updates
  newOperations.forEach(operation => {
    const oldOperation = oldOperations?.find(op => op.id === operation.id);
    if (oldOperation && operation.currentStep !== oldOperation.currentStep) {
      addOperationLog(`${operation.title}: ${operation.currentMessage}`, 'info');
    }
  });

  // Log when operations complete
  if (oldOperations) {
    oldOperations.forEach(oldOp => {
      if (!newOperations.find(op => op.id === oldOp.id)) {
        const status = oldOp.status === 'success' ? 'success' : 'error';
        addOperationLog(`Completed: ${oldOp.title} - ${oldOp.finalMessage || oldOp.status}`, status);
      }
    });
  }
}, { deep: true });

onMounted(() => {
  addOperationLog('Admin console initialized', 'info');
});

onUnmounted(() => {
  addOperationLog('Admin console closed', 'info');
});

// Format duration
function formatDuration(operation) {
  if (operation.status === 'running') {
    const duration = Date.now() - operation.startTime;
    return `${Math.floor(duration / 1000)}s`;
  } else if (operation.duration) {
    return `${Math.floor(operation.duration / 1000)}s`;
  }
  return '';
}

// Format time
function formatTime(timestamp) {
  return new Date(timestamp).toLocaleTimeString('en-US', { 
    hour12: false, 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  });
}
</script>
