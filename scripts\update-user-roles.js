// scripts/update-user-roles.js
import dotenv from 'dotenv';
import mongoose from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

// Initialize dotenv
dotenv.config();

// MongoDB connection
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('MongoDB connected successfully');
  } catch (error) {
    console.error('MongoDB connection error:', error);
    process.exit(1);
  }
};

// Define Role schema
const RoleSchema = new mongoose.Schema(
  {
    id: { type: String, required: true, unique: true },
    name: { type: String, required: true, unique: true },
    description: { type: String, required: true },
  },
  {
    timestamps: true,
  }
);

// Define User schema (simplified for this script)
const UserSchema = new mongoose.Schema(
  {
    username: { type: String, required: true, unique: true },
    email: { type: String, required: true, unique: true },
    fullname: { type: String, required: true },
    password: { type: String, required: true },
    role: { type: String, enum: ['user', 'manager', 'admin'], default: 'user' },
    roleId: { type: String }, // New field for UUID role
    firmId: { type: mongoose.Schema.Types.ObjectId, ref: 'Firm', required: true },
    status: { type: Number, enum: [-1, 0, 1], default: 0 },
    lastmailsent: { type: Date },
    lastLogin: { type: Date },
  },
  {
    timestamps: true,
  }
);

// Create models
const Role = mongoose.model('Role', RoleSchema);
const User = mongoose.model('User', UserSchema);

// Default role UUIDs
const DEFAULT_ROLES = {
  USER: {
    id: '8a9b5cde-f123-45g6-h789-0i1j2k3l4m5n',
    name: 'user',
    description: 'Regular user with limited permissions'
  },
  MANAGER: {
    id: '9b0c6def-g234-56h7-i890-1j2k3l4m5n6o',
    name: 'manager',
    description: 'Manager with elevated permissions to manage users and resources'
  },
  ADMIN: {
    id: '0c1d7efg-h345-67i8-j901-2k3l4m5n6o7p',
    name: 'admin',
    description: 'Administrator with full system access'
  }
};

// Initialize roles
const initializeRoles = async () => {
  try {
    // Check if roles already exist
    const existingRoles = await Role.find();

    if (existingRoles.length === 0) {
      // Create default roles if none exist
      await Role.create([
        DEFAULT_ROLES.USER,
        DEFAULT_ROLES.MANAGER,
        DEFAULT_ROLES.ADMIN
      ]);
      console.log('Default roles created successfully');
    } else {
      console.log('Roles already exist in the database:');
      existingRoles.forEach(role => {
        console.log(`- ${role.name}: ${role.id}`);
      });
    }

    return existingRoles.length > 0 ? existingRoles : await Role.find();
  } catch (error) {
    console.error('Error initializing roles:', error);
    throw error;
  }
};

// Update user roles
const updateUserRoles = async (roles) => {
  try {
    // Create a mapping of role names to IDs
    const roleMap = {};
    roles.forEach(role => {
      roleMap[role.name] = role.id;
    });

    // Get all users
    const users = await User.find();
    console.log(`Found ${users.length} users to update`);

    // Update each user with the appropriate role ID
    let updatedCount = 0;
    for (const user of users) {
      const roleId = roleMap[user.role];
      if (roleId) {
        // Add roleId field while keeping the original role field
        user.roleId = roleId;
        await user.save();
        updatedCount++;
      } else {
        console.warn(`No matching role found for user ${user.username} with role ${user.role}`);
      }
    }

    console.log(`Updated ${updatedCount} users with role IDs`);
  } catch (error) {
    console.error('Error updating user roles:', error);
    throw error;
  }
};

// Main function
const main = async () => {
  try {
    await connectDB();

    // Initialize roles
    const roles = await initializeRoles();

    // Update user roles
    await updateUserRoles(roles);

    console.log('Role update completed successfully');
    process.exit(0);
  } catch (error) {
    console.error('Error in main function:', error);
    process.exit(1);
  }
};

// Run the script
main();
