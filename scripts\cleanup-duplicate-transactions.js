import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { initializeApp, cert } from 'firebase-admin/app';

/**
 * Cleanup script to remove duplicate transactions from subsModels collection
 * 
 * This script removes the old transactions array from subsModels documents
 * since transactions are now stored in the separate 'subs' collection.
 * 
 * This will fix the duplicate transaction issue where:
 * - One transaction is stored in the 'subs' collection (new method)
 * - Another transaction is stored in the 'transactions' array within subsModels (old method)
 */

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID,
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL
};

// Initialize Firebase
if (!initializeApp.length) {
  initializeApp({
    credential: cert(serviceAccount)
  });
}

const db = getFirestore();

async function cleanupDuplicateTransactions() {
  console.log('🧹 Starting cleanup of duplicate transactions...');
  
  try {
    // Get all subsModels documents
    const subsModelsCollection = db.collection('subsModels');
    const snapshot = await subsModelsCollection.get();
    
    if (snapshot.empty) {
      console.log('No subsModels found.');
      return;
    }
    
    let processedCount = 0;
    let updatedCount = 0;
    
    console.log(`Found ${snapshot.size} subsModels to process...`);
    
    // Process each subsModel document
    for (const doc of snapshot.docs) {
      const data = doc.data();
      processedCount++;
      
      console.log(`\n📋 Processing subsModel ${processedCount}/${snapshot.size}: ${data.name || 'Unnamed'} (ID: ${doc.id})`);
      
      // Check if this document has a transactions array
      if (data.transactions && Array.isArray(data.transactions) && data.transactions.length > 0) {
        console.log(`  ⚠️  Found ${data.transactions.length} transactions in old format`);
        console.log(`  🗑️  Removing transactions array from subsModel...`);
        
        // Remove the transactions array but keep all other data
        const updateData = {
          updatedAt: Timestamp.now(),
          // Remove the transactions field
        };
        
        // Use FieldValue.delete() to remove the transactions field
        const { FieldValue } = require('firebase-admin/firestore');
        updateData.transactions = FieldValue.delete();
        
        await doc.ref.update(updateData);
        updatedCount++;
        
        console.log(`  ✅ Successfully removed transactions array from ${data.name || 'Unnamed'}`);
      } else {
        console.log(`  ✨ No transactions array found - already clean`);
      }
    }
    
    console.log('\n🎉 Cleanup completed!');
    console.log(`📊 Summary:`);
    console.log(`   - Total subsModels processed: ${processedCount}`);
    console.log(`   - SubsModels updated: ${updatedCount}`);
    console.log(`   - SubsModels already clean: ${processedCount - updatedCount}`);
    
    if (updatedCount > 0) {
      console.log('\n✅ Duplicate transactions have been removed!');
      console.log('   Transactions are now only stored in the "subs" collection.');
      console.log('   The application will no longer show duplicate transactions.');
    } else {
      console.log('\n✨ No cleanup needed - all subsModels were already clean!');
    }
    
  } catch (error) {
    console.error('❌ Error during cleanup:', error);
    throw error;
  }
}

// Run the cleanup
cleanupDuplicateTransactions()
  .then(() => {
    console.log('\n🏁 Script completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Script failed:', error);
    process.exit(1);
  });
