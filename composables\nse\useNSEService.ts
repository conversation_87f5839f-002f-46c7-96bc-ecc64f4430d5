import { ref, reactive } from 'vue';

export function useNSEService() {
  const isLoading = ref(true);
  const error = ref(null);

  // Function to get NSE records
  async function getNSERecords() {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await $fetch('/api/nse/get_nse');
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch NSE records';
      isLoading.value = false;
      throw err;
    }
  }

  // Function to get all folio records
  async function getAllFolioRecords() {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await $fetch('/api/nse/folio');
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch folio records';
      isLoading.value = false;
      throw err;
    }
  }

  // Function to get all CN Notes
  async function getAllCnNote() {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await $fetch('/api/nse/cn_note');
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch CN Notes';
      isLoading.value = false;
      throw err;
    }
  }

  // Function to get Google Sheets data
  async function gs_sheet() {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await $fetch('/api/nse/gs_record');
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || 'Failed to fetch Google Sheets data';
      isLoading.value = false;
      throw err;
    }
  }

  // Function to submit new data
  async function submitData(formData: any, recordsData: any[]) {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await $fetch('/api/nse/addnse_data', {
        method: 'POST',
        body: { formData, recordsData }
      });
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || 'Failed to submit data';
      isLoading.value = false;
      throw err;
    }
  }

  // Function to update existing data
  async function updateData(formData: any, recordsData: any[]) {
    isLoading.value = true;
    error.value = null;
    try {
      const response = await $fetch('/api/nse/updnse_data', {
        method: 'POST',
        body: { formData, recordsData }
      });
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || 'Failed to update data';
      isLoading.value = false;
      throw err;
    }
  }

  return {
    isLoading,
    error,
    getNSERecords,
    getAllFolioRecords,
    getAllCnNote,
    gs_sheet,
    submitData,
    updateData
  };
}
