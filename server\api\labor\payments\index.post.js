import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'
import { LaborPaymentService } from '~/utils/laborPaymentService.js'

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event)
    const { groupId, paymentDate, amount, project, paymentMethod, paymentType, firmId, description } = body

    // Enhanced validation
    if (!groupId || !paymentDate || !amount || !firmId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: groupId, paymentDate, amount, and firmId are required'
      })
    }

    // Validate payment type
    const validPaymentTypes = ['Advance', 'Misc Payment', 'Final Payment', 'Site Expense']
    if (paymentType && !validPaymentTypes.includes(paymentType)) {
      throw createError({
        statusCode: 400,
        statusMessage: `Invalid payment type. Must be one of: ${validPaymentTypes.join(', ')}`
      })
    }

    // Validate amount
    const numericAmount = parseFloat(amount)
    if (isNaN(numericAmount) || numericAmount <= 0) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Amount must be a positive number'
      })
    }

    // Validate payment date
    const paymentDateObj = new Date(paymentDate)
    if (isNaN(paymentDateObj.getTime())) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid payment date format'
      })
    }

    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Initialize payment service for validation
    const paymentService = new LaborPaymentService(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Additional validation for Final Payment
    if (paymentType === 'Final Payment') {
      // Check if there's already a final payment for recent periods
      const existingPayments = await paymentService.getGroupPayments(groupId)
      const recentFinalPayments = existingPayments.filter(p =>
        p.payment_type === 'Final Payment' &&
        new Date(p.payment_date) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // Last 30 days
      )

      if (recentFinalPayments.length > 0) {
        console.warn(`Warning: Final payment being made when recent final payment exists (${recentFinalPayments[0].payment_date})`)
      }
    }

    const paymentData = {
      group_id: groupId,
      payment_date: paymentDate,
      amount: numericAmount,
      project: project || null,
      payment_method: paymentMethod === 'cash' ? 'cash' : 'bank',
      payment_type: paymentType || 'Advance',
      bank_details: paymentMethod === 'cash' ? {} : { bankId: paymentMethod },
      description: description || null,
      firm_id: firmId,
    }

    const { data, error } = await supabase
      .from('payment_records')
      .insert([paymentData])
      .select()
      .single()

    if (error) {
      throw createError({
        statusCode: 500,
        statusMessage: `Database error: ${error.message}`
      })
    }

    // Validate payment data consistency after insertion
    try {
      const validation = await paymentService.validatePaymentData(groupId)
      if (!validation.isValid) {
        console.warn('Payment data validation warnings:', validation.warnings)
      }
    } catch (validationError) {
      console.error('Payment validation error:', validationError)
      // Don't fail the request for validation errors, just log them
    }

    // Here you would add the logic to sync with Firestore
    // For now, we'll just return success

    return {
      success: true,
      data,
      message: 'Payment saved successfully',
      paymentType: paymentType || 'Advance',
      metadata: {
        validationPerformed: true,
        paymentDate: paymentDate,
        amount: numericAmount
      }
    }
  } catch (error) {
    console.error('Error saving payment:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to save payment'
    })
  }
})