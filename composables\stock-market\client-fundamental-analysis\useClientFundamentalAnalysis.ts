import { ref, computed, readonly } from 'vue'
import { useUniversalAIClient } from '~/composables/ai/useUniversalAIClient'

export interface ClientFundamentalAnalysisResult {
  metrics: {
    pe: string | null
    pb: string | null
    roe: string | null
    debtToEquity: string | null
    dividendYield: string | null
    marketCap: string | null
    eps: string | null
    revenue: string | null
  }
  summary: string
  recommendation: string
  confidence: string
  pros: string[]
  cons: string[]
  financialHealth: string
  profitability: string
  analysisTimestamp: string
}

export const useClientFundamentalAnalysis = () => {
  // State
  const loading = ref(false)
  const error = ref('')
  const analysis = ref<ClientFundamentalAnalysisResult | null>(null)
  const progress = ref(0)
  const statusMessage = ref('')
  
  // Enhanced progress tracking
  const dataPointsProcessed = ref(0)
  const currentOperation = ref('')
  const operationStartTime = ref<Date | null>(null)

  // Universal AI Client - NO MORE HARDCODED PROVIDERS!
  const { callAIForJSON, isConfigured, getAIInfo } = useUniversalAIClient()

  // Generate AI analysis prompt - 100% IDENTICAL TO SERVER VERSION
  const generateAIAnalysisPrompt = (stockData: any) => {
    return `You are an expert financial analyst specializing in fundamental analysis. Provide a comprehensive fundamental analysis of the following Indian stock:

STOCK DETAILS:
- Symbol: ${stockData.symbol}
- Company: ${stockData.companyName || stockData.symbol}
- Current Price: ₹${stockData.currentPrice}
- Price Change: ₹${stockData.change} (${stockData.pChange}%)
- Volume: ${stockData.volume}
- Day High: ₹${stockData.dayHigh}
- Day Low: ₹${stockData.dayLow}

Provide a detailed fundamental analysis in JSON format with the following structure:
{
  "metrics": {
    "pe": "P/E ratio or null if not available",
    "pb": "P/B ratio or null if not available",
    "roe": "Return on Equity % or null if not available",
    "debtToEquity": "Debt to Equity ratio or null if not available",
    "dividendYield": "Dividend yield % or null if not available",
    "marketCap": "Market capitalization or null if not available",
    "eps": "Earnings per share or null if not available",
    "revenue": "Annual revenue or null if not available"
  },
  "summary": "Comprehensive investment summary and outlook",
  "recommendation": "BUY/SELL/HOLD",
  "confidence": "High/Medium/Low",
  "pros": ["List of company strengths and positive factors"],
  "cons": ["List of company weaknesses and risk factors"],
  "financialHealth": "Detailed analysis of financial stability and debt management",
  "profitability": "Analysis of profitability trends, margins, and growth prospects"
}

CRITICAL REQUIREMENTS:
- ONLY provide REAL financial data - NO mock, sample, or fake data
- If specific metrics are not available, set them to null (not fabricated values)
- Focus on actual company fundamentals, business model, competitive position
- Provide genuine analysis based on the company's actual financial performance
- Include sector-specific analysis and industry comparisons
- Consider current market conditions and economic factors
- Be honest about data limitations - use null for unavailable metrics
- Provide actionable insights for investors

Focus on Indian market context and provide actionable fundamental analysis based on real company data.`
  }

  // Universal AI Provider Call - NO MORE HARDCODED LOGIC!
  const callAIProviderDirectly = async (prompt: string): Promise<any> => {
    const aiInfo = getAIInfo()
    console.log(`🤖 Making universal AI call to ${aiInfo.provider} with model ${aiInfo.model}`)

    try {
      // Use universal AI client - works with ANY provider dynamically
      const systemPrompt = 'You are a professional fundamental analyst. Provide accurate, data-driven fundamental analysis based only on real company data. Return only valid JSON format matching the exact structure requested.'
      return await callAIForJSON(prompt, systemPrompt)
    } catch (error: any) {
      console.error(`❌ Universal AI call failed:`, error)
      throw new Error(`AI analysis failed: ${error.message}`)
    }
  }

  // ALL HARDCODED PROVIDER FUNCTIONS REMOVED!
  // Now using Universal AI Client for complete dynamic support

  // Google AI Direct API call
  const callGoogleAIDirect = async (prompt: string, config: any): Promise<string> => {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${config.model}:generateContent?key=${config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `You are a professional fundamental analyst. Provide accurate, data-driven fundamental analysis based only on real company data. Return only valid JSON format matching the exact structure requested.\n\n${prompt}`
          }]
        }],
        generationConfig: {
          temperature: config.temperature || 0.7,
          maxOutputTokens: config.maxTokens || 8192
        }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Google AI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.candidates?.[0]?.content?.parts?.[0]?.text || ''
  }

  // Anthropic Direct API call
  const callAnthropicDirect = async (prompt: string, config: any): Promise<string> => {
    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'x-api-key': config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: config.model,
        max_tokens: config.maxTokens || 8192,
        temperature: config.temperature || 0.7,
        messages: [
          {
            role: 'user',
            content: `You are a professional fundamental analyst. Provide accurate, data-driven fundamental analysis based only on real company data. Return only valid JSON format matching the exact structure requested.\n\n${prompt}`
          }
        ]
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.content?.[0]?.text || ''
  }

  // OpenRouter Direct API call
  const callOpenRouterDirect = async (prompt: string, config: any): Promise<string> => {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'Stock Market Analysis'
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional fundamental analyst. Provide accurate, data-driven fundamental analysis based only on real company data. Return only valid JSON format matching the exact structure requested.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 8192
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.choices?.[0]?.message?.content || ''
  }

  // Custom AI Provider Direct API call
  const callCustomAIDirect = async (prompt: string, config: any): Promise<string> => {
    const headers: any = {
      'Content-Type': 'application/json'
    }

    // Dynamic headers based on config
    if (config.headers) {
      Object.assign(headers, config.headers)
    }

    // Dynamic API key handling
    if (config.apiKey) {
      if (config.authHeader) {
        headers[config.authHeader] = config.apiKey
      } else {
        headers['Authorization'] = `Bearer ${config.apiKey}`
      }
    }

    const body: any = {
      model: config.model,
      messages: [
        {
          role: 'system',
          content: 'You are a professional fundamental analyst. Provide accurate, data-driven fundamental analysis based only on real company data. Return only valid JSON format matching the exact structure requested.'
        },
        {
          role: 'user',
          content: prompt
        }
      ],
      temperature: config.temperature || 0.7,
      max_tokens: config.maxTokens || 8192
    }

    // Allow custom body parameters
    if (config.bodyParams) {
      Object.assign(body, config.bodyParams)
    }

    const response = await fetch(`${config.baseUrl}/chat/completions`, {
      method: 'POST',
      headers,
      body: JSON.stringify(body)
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Custom AI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()

    // Dynamic response parsing based on config
    if (config.responsePath) {
      return config.responsePath.split('.').reduce((obj: any, key: string) => obj?.[key], data) || ''
    }

    // Default OpenAI-compatible response format
    return data.choices?.[0]?.message?.content || data.content || ''
  }

  // Perform client-side fundamental analysis with REAL-TIME PROGRESS
  const performFundamentalAnalysis = async (stockData: any) => {
    if (!isConfigured.value) {
      throw new Error('AI configuration is not complete. Please configure your AI settings.')
    }

    loading.value = true
    error.value = ''
    analysis.value = null
    progress.value = 0
    dataPointsProcessed.value = 0
    currentOperation.value = 'Initializing'
    operationStartTime.value = new Date()
    statusMessage.value = `Starting fundamental analysis for ${stockData.symbol}...`

    try {
      // Step 1: Prepare stock data for analysis
      progress.value = 10
      currentOperation.value = 'Data Preparation'
      statusMessage.value = `Preparing fundamental data for ${stockData.symbol}...`

      const stockInfo = {
        symbol: stockData.symbol,
        companyName: stockData.companyName || stockData.symbol,
        currentPrice: stockData.currentPrice,
        change: stockData.change,
        pChange: stockData.pChange,
        volume: stockData.volume,
        dayHigh: stockData.dayHigh,
        dayLow: stockData.dayLow
      }

      progress.value = 20
      currentOperation.value = 'AI Prompt Generation'
      statusMessage.value = 'Preparing AI analysis prompt with stock fundamentals...'

      const aiPrompt = generateAIAnalysisPrompt(stockInfo)

      progress.value = 30
      currentOperation.value = 'AI Analysis Request'
      const aiInfo = getAIInfo()
      statusMessage.value = `Sending fundamental analysis request to ${aiInfo.provider} (${aiInfo.model})...`

      // Make UNIVERSAL AI provider call - COMPLETELY DYNAMIC!
      const fundamentalAnalysis = await callAIProviderDirectly(aiPrompt)

      // Step 2: Validate AI response with detailed progress
      progress.value = 70
      currentOperation.value = 'Response Processing'
      statusMessage.value = `Received AI response, validating structure...`

      if (!fundamentalAnalysis || typeof fundamentalAnalysis !== 'object') {
        throw new Error('Invalid response from AI provider - expected JSON object')
      }

      progress.value = 85
      statusMessage.value = 'Validating fundamental analysis fields...'

      console.log('✅ Received parsed fundamental analysis result:', fundamentalAnalysis)

        // Validate required fields for server compatibility
        const requiredFields = ['summary', 'recommendation', 'pros', 'cons', 'financialHealth', 'profitability']
        const missingFields = requiredFields.filter(field => !fundamentalAnalysis[field])

        if (missingFields.length > 0) {
          throw new Error(`AI response missing required fields: ${missingFields.join(', ')}`)
        }

        // Validate that arrays are actually arrays
        if (!Array.isArray(fundamentalAnalysis.pros) || !Array.isArray(fundamentalAnalysis.cons)) {
          throw new Error('Invalid analysis structure - pros and cons must be arrays')
        }

        // Validate that we have meaningful content (not empty strings)
        if (!fundamentalAnalysis.summary.trim() || !fundamentalAnalysis.recommendation.trim()) {
          throw new Error('Analysis contains empty required fields')
        }

        progress.value = 90
        statusMessage.value = 'Fundamental analysis validation complete'

        console.log('✅ Client-side fundamental analysis parsed successfully:', fundamentalAnalysis)

      // Step 3: Prepare final response with detailed completion
      progress.value = 95
      currentOperation.value = 'Final Processing'
      statusMessage.value = 'Preparing final fundamental analysis result...'

      const finalAnalysis: ClientFundamentalAnalysisResult = {
        metrics: fundamentalAnalysis.metrics || {},
        summary: fundamentalAnalysis.summary,
        recommendation: fundamentalAnalysis.recommendation,
        confidence: fundamentalAnalysis.confidence,
        pros: fundamentalAnalysis.pros,
        cons: fundamentalAnalysis.cons,
        financialHealth: fundamentalAnalysis.financialHealth,
        profitability: fundamentalAnalysis.profitability,
        analysisTimestamp: new Date().toISOString()
      }

      progress.value = 100
      currentOperation.value = 'Complete'
      statusMessage.value = `Fundamental analysis complete! Analyzed ${stockData.symbol} with comprehensive metrics`

      analysis.value = finalAnalysis
      loading.value = false

      console.log('✅ Client-side fundamental analysis completed:', finalAnalysis)
      return finalAnalysis

    } catch (err: any) {
      console.error('❌ Client-side fundamental analysis error:', err)
      error.value = err.message
      loading.value = false
      progress.value = 0
      statusMessage.value = `Error: ${err.message}`
      throw err
    }
  }

  // Reset state
  const reset = () => {
    loading.value = false
    error.value = ''
    analysis.value = null
    progress.value = 0
    statusMessage.value = ''
    dataPointsProcessed.value = 0
    currentOperation.value = ''
    operationStartTime.value = null
  }

  return {
    // State
    loading: readonly(loading),
    error: readonly(error),
    analysis: readonly(analysis),
    progress: readonly(progress),
    statusMessage: readonly(statusMessage),

    // Enhanced progress tracking
    dataPointsProcessed: readonly(dataPointsProcessed),
    currentOperation: readonly(currentOperation),
    operationStartTime: readonly(operationStartTime),

    // Computed
    isConfigured,

    // Methods
    performFundamentalAnalysis,
    generateAIAnalysisPrompt,
    callAIProviderDirectly,
    reset
  }
}
