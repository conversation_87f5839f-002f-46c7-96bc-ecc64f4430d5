<template>
  <div class="bg-white shadow rounded-lg p-6 mb-6">
    <h2 class="text-lg font-medium text-gray-900 mb-4">Portfolio Summary</h2>
    
    <div v-if="isLoading" class="flex justify-center items-center h-40">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-4 gap-4">
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-500">Total Investment</h3>
        <p class="text-2xl font-semibold text-gray-900">₹{{ metrics.totalInvestment.toLocaleString() }}</p>
      </div>
      
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-500">Current Value</h3>
        <p class="text-2xl font-semibold text-gray-900">₹{{ metrics.currentValue.toLocaleString() }}</p>
      </div>
      
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-500">Profit/Loss</h3>
        <p :class="['text-2xl font-semibold', metrics.profitLoss >= 0 ? 'text-green-600' : 'text-red-600']">
          {{ metrics.profitLoss >= 0 ? '+' : '' }}₹{{ metrics.profitLoss.toLocaleString() }}
        </p>
      </div>
      
      <div class="bg-gray-50 p-4 rounded-lg">
        <h3 class="text-sm font-medium text-gray-500">P/L Percentage</h3>
        <p :class="['text-2xl font-semibold', metrics.profitLossPercentage >= 0 ? 'text-green-600' : 'text-red-600']">
          {{ metrics.profitLossPercentage >= 0 ? '+' : '' }}{{ metrics.profitLossPercentage.toFixed(2) }}%
        </p>
      </div>
    </div>
    
    <div class="mt-6">
      <h3 class="text-lg font-medium text-gray-900 mb-4">Holdings</h3>
      
      <div v-if="isLoading" class="flex justify-center items-center h-40">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>
      
      <div v-else-if="folioRecords.length === 0" class="text-center py-8">
        <p class="text-gray-500">No holdings found. Add transactions to see your portfolio.</p>
      </div>
      
      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Price</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Price</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Current Value</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">P/L</th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Age (Days)</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in folioRecords" :key="record._id">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ record.symbol }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.qnty }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₹{{ record.price.toLocaleString() }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₹{{ record.cprice.toLocaleString() }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₹{{ (record.cprice * record.qnty).toLocaleString() }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm" :class="record.pl >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ record.pl >= 0 ? '+' : '' }}₹{{ record.pl.toLocaleString() }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ record.age || 'N/A' }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { calculatePortfolioMetrics } from '~/utils/chartUtils';

const props = defineProps({
  folioRecords: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

const metrics = computed(() => {
  return calculatePortfolioMetrics(props.folioRecords);
});
</script>
