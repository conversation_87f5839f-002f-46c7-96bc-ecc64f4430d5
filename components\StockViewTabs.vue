<template>
  <div class="mb-6">
    <!-- Tabs header -->
    <div class="flex border-b border-gray-200">
      <div class="flex flex-grow overflow-x-auto">
        <!-- Default Nifty 50 tab -->
        <button
          @click="setActiveTab('nifty50')"
          class="px-4 py-2 text-sm font-medium whitespace-nowrap"
          :class="activeTab === 'nifty50' ? 'border-b-2 border-indigo-500 text-indigo-600' : 'text-gray-500 hover:text-gray-700'"
        >
          Nifty 50
        </button>

        <!-- User views tabs -->
        <button
          v-for="view in views"
          :key="view.id"
          @click="setActiveTab(view.id)"
          class="px-4 py-2 text-sm font-medium whitespace-nowrap flex items-center"
          :class="activeTab === view.id ? 'border-b-2 border-indigo-500 text-indigo-600' : 'text-gray-500 hover:text-gray-700'"
        >
          {{ view.name }}
          <span
            @click.stop="confirmDeleteView(view.id)"
            class="ml-2 text-gray-400 hover:text-red-500"
            title="Remove view"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </span>
        </button>
      </div>

      <!-- Add new view button (only if less than 5 views) -->
      <button
        v-if="views.length < 5"
        @click="showCreateViewModal = true"
        class="px-3 py-2 text-sm font-medium text-indigo-600 hover:text-indigo-800 flex items-center"
        title="Create new view"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
      </button>
    </div>

    <!-- Create view modal -->
    <div v-if="showCreateViewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Create New View</h3>
        <div class="mb-4">
          <label for="viewName" class="block text-sm font-medium text-gray-700 mb-1">View Name</label>
          <input
            id="viewName"
            v-model="newViewName"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Enter view name"
          />
        </div>
        <div class="flex justify-end space-x-3">
          <button
            @click="showCreateViewModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            @click="createNewView"
            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
            :disabled="!newViewName.trim()"
          >
            Create
          </button>
        </div>
      </div>
    </div>

    <!-- Delete confirmation modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
        <h3 class="text-lg font-medium text-gray-900 mb-4">Delete View</h3>
        <p class="mb-4 text-gray-600">Are you sure you want to delete this view? This action cannot be undone.</p>
        <div class="flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
          >
            Cancel
          </button>
          <button
            @click="deleteView"
            class="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import { useStockViews } from '~/composables/stock-market/useStockViews';

const props = defineProps({
  initialActiveTab: {
    type: String,
    default: 'nifty50'
  }
});

const emit = defineEmits(['tab-change']);

const {
  views,
  isLoading,
  error,
  fetchViews,
  createView,
  deleteView: deleteViewAction,
  setActiveView
} = useStockViews();

const activeTab = ref(props.initialActiveTab);
const showCreateViewModal = ref(false);
const showDeleteModal = ref(false);
const newViewName = ref('');
const viewToDelete = ref<string | null>(null);

// Fetch views on component mount
onMounted(async () => {
  console.log('StockViewTabs: Fetching views...');
  await fetchViews();
  console.log('StockViewTabs: Views fetched:', views.value.length);
});

// Set active tab
const setActiveTab = (tabId: string) => {
  activeTab.value = tabId;
  if (tabId !== 'nifty50') {
    setActiveView(tabId);
  }
  emit('tab-change', tabId);
};

// Create a new view
const createNewView = async () => {
  if (!newViewName.value.trim()) return;

  const view = await createView(newViewName.value.trim());
  if (view) {
    setActiveTab(view.id);
    showCreateViewModal.value = false;
    newViewName.value = '';
  }
};

// Confirm delete view
const confirmDeleteView = (viewId: string) => {
  viewToDelete.value = viewId;
  showDeleteModal.value = true;
};

// Delete view
const deleteView = async () => {
  if (!viewToDelete.value) return;

  const success = await deleteViewAction(viewToDelete.value);
  if (success) {
    // If the deleted view was active, switch to Nifty 50
    if (activeTab.value === viewToDelete.value) {
      setActiveTab('nifty50');
    }
    showDeleteModal.value = false;
    viewToDelete.value = null;
  }
};
</script>
