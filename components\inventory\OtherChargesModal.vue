<template>
  <!-- Toast notifications are handled globally -->
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <!-- Gradient Header -->
        <div class="bg-gradient-to-r from-green-500 to-teal-600 px-4 py-3 rounded-t-lg shadow-md">
          <h3 class="text-lg leading-6 font-medium text-white">Add Other Charges</h3>
        </div>

        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">

              <form @submit.prevent="addCharge">
                <div class="grid grid-cols-1 gap-4">
                  <!-- Description with custom dropdown -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                    <div class="relative">
                      <input ref="firstInput" v-model="form.description" type="text"
                        @focus="handleFocus"
                        @blur="handleDescriptionBlur"
                        @input="filterDropdownItems"
                        @keydown.esc="handleEscapeKey"
                        @keydown.down.prevent="handleKeyNavigation('down')"
                        @keydown.up.prevent="handleKeyNavigation('up')"
                        @keydown.enter.prevent="handleKeyNavigation('enter')"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <button type="button" @click="toggleDescriptionDropdown" class="absolute right-2 top-1/2 transform -translate-y-1/2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                      </button>

                      <!-- Custom dropdown -->
                      <div v-if="showDescriptionDropdown" class="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-y-auto">
                        <!-- Previous descriptions section -->
                        <div class="p-2 bg-gradient-to-r from-blue-50 to-blue-100 border-b border-gray-300">
                          <div class="text-xs font-semibold text-blue-700 mb-1">Previous Descriptions</div>
                          <div v-for="(item, index) in filteredUniqueDescriptions" :key="item.description"
                               @mousedown="selectDescriptionWithData(item)"
                               :class="[
                                 'px-2 py-1 text-sm cursor-pointer rounded transition-colors flex justify-between',
                                 selectedIndex === index ? 'bg-blue-200' : 'hover:bg-blue-200'
                               ]">
                            <span>{{ item.description }}</span>
                            <span class="text-xs text-gray-500">{{ item.hsn ? `HSN: ${item.hsn}` : '' }} {{ item.gstRate ? `GST: ${item.gstRate}%` : '' }}</span>
                          </div>
                          <div v-if="filteredUniqueDescriptions.length === 0" class="px-2 py-1 text-sm text-gray-500 italic">
                            {{ existingChargesData.length === 0 ? 'No previous descriptions' : 'No matching descriptions' }}
                          </div>
                        </div>

                        <!-- Common descriptions section -->
                        <div class="p-2 bg-gradient-to-r from-green-50 to-green-100 border-b border-gray-300">
                          <div class="text-xs font-semibold text-green-700 mb-1">Common Descriptions</div>
                          <div v-for="(item, index) in filteredCommonDescriptions" :key="item.description"
                               @mousedown="selectDescriptionWithData(item)"
                               :class="[
                                 'px-2 py-1 text-sm cursor-pointer rounded transition-colors flex justify-between',
                                 selectedIndex === (filteredUniqueDescriptions.length + index) ? 'bg-green-200' : 'hover:bg-green-200'
                               ]">
                            <span>{{ item.description }}</span>
                            <span class="text-xs text-gray-500">HSN: {{ item.hsn }} | GST: {{ item.gstRate }}%</span>
                          </div>
                          <div v-if="filteredCommonDescriptions.length === 0" class="px-2 py-1 text-sm text-gray-500 italic">
                            No matching descriptions
                          </div>
                        </div>

                        <!-- HSN Codes section -->
                        <div class="p-2 bg-gradient-to-r from-purple-50 to-purple-100 border-b border-gray-300">
                          <div class="text-xs font-semibold text-purple-700 mb-1">HSN Codes</div>
                          <div v-for="(item, index) in filteredHsnCodes" :key="item.code"
                               @mousedown="selectHSN(item)"
                               :class="[
                                 'px-2 py-1 text-sm cursor-pointer rounded transition-colors flex justify-between',
                                 selectedIndex === (filteredUniqueDescriptions.length + filteredCommonDescriptions.length + index) ? 'bg-purple-200' : 'hover:bg-purple-200'
                               ]">
                            <span>{{ item.code }}</span>
                            <span class="text-xs text-gray-500">{{ item.description }} | GST: {{ item.gstRate }}%</span>
                          </div>
                          <div v-if="filteredHsnCodes.length === 0" class="px-2 py-1 text-sm text-gray-500 italic">
                            No matching HSN codes
                          </div>
                        </div>

                        <!-- GST Rates section -->
                        <div class="p-2 bg-gradient-to-r from-yellow-50 to-yellow-100">
                          <div class="text-xs font-semibold text-yellow-700 mb-1">GST Rates</div>
                          <div v-for="(rate, index) in filteredGstRates" :key="rate.value"
                               @mousedown="selectGSTRate(rate.value)"
                               :class="[
                                 'px-2 py-1 text-sm cursor-pointer rounded transition-colors flex justify-between',
                                 selectedIndex === (filteredUniqueDescriptions.length + filteredCommonDescriptions.length + filteredHsnCodes.length + index) ? 'bg-yellow-200' : 'hover:bg-yellow-200'
                               ]">
                            <span>{{ rate.value }}%</span>
                            <span class="text-xs text-gray-500">{{ rate.description }}</span>
                          </div>
                          <div v-if="filteredGstRates.length === 0" class="px-2 py-1 text-sm text-gray-500 italic">
                            No matching GST rates
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Amount -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Amount</label>
                    <input v-model.number="form.oth_amt" type="number" step="0.01" min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required />
                  </div>

                  <!-- GST Rate -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">GST Rate (%)</label>
                    <input v-model.number="form.oth_grate" type="number" step="0.01" min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- CGST -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">CGST</label>
                    <input v-model.number="form.oth_cgst" type="number" step="0.01" min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- SGST -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">SGST</label>
                    <input v-model.number="form.oth_sgst" type="number" step="0.01" min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- IGST -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">IGST</label>
                    <input v-model.number="form.oth_igst" type="number" step="0.01" min="0"
                      :disabled="statesMatch"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed" />
                  </div>

                  <!-- HSN Code -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">HSN Code</label>
                    <input v-model="form.oth_hsn" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- Total -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Total</label>
                    <input v-model.number="form.oth_tot" type="number" step="0.01" min="0"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required />
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                  <button type="submit"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:col-start-2 sm:text-sm">
                    Add Charge
                  </button>
                  <button type="button" @click="closeModal"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Gradient Footer -->
        <div class="bg-gradient-to-r from-teal-600 to-green-500 px-4 py-3 rounded-b-lg shadow-md"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, computed, nextTick } from 'vue';
import Toast from '~/components/Toast.vue';
import useToast from '~/composables/ui/useToast';

const { toast, success, error } = useToast()

// State for custom dropdown
const showDescriptionDropdown = ref(false);
const dropdownSearchTerm = ref('');
const selectedIndex = ref(-1);
const allDropdownItems = ref([]);
const currentSection = ref('');

// Common descriptions with HSN codes and GST rates
const commonDescriptions = [
  { description: 'Transportation Charges', hsn: '9965', gstRate: 5 },
  { description: 'Packaging Charges', hsn: '9985', gstRate: 18 },
  { description: 'Installation Charges', hsn: '9987', gstRate: 18 },
  { description: 'Loading/Unloading Charges', hsn: '9997', gstRate: 18 },
  { description: 'Delivery Charges', hsn: '9965', gstRate: 5 },
  { description: 'Handling Charges', hsn: '9985', gstRate: 18 },
  { description: 'Insurance Charges', hsn: '9971', gstRate: 18 },
  { description: 'Discount', hsn: '', gstRate: 0 },
  { description: 'Round Off', hsn: '', gstRate: 0 }
];

// HSN codes with descriptions and GST rates
const hsnCodes = [
  { code: '9965', description: 'Transport Services', gstRate: 5 },
  { code: '9985', description: 'Support Services', gstRate: 18 },
  { code: '9987', description: 'Maintenance & Repair', gstRate: 18 },
  { code: '9971', description: 'Financial Services', gstRate: 18 },
  { code: '9973', description: 'Leasing Services', gstRate: 18 },
  { code: '9997', description: 'Other Services', gstRate: 18 },
  { code: '8431', description: 'Parts for Machinery', gstRate: 18 },
  { code: '8708', description: 'Parts for Vehicles', gstRate: 28 },
  { code: '8409', description: 'Parts for Engines', gstRate: 28 }
];

// GST rates with descriptions
const gstRates = [
  { value: 0, description: 'Exempt' },
  { value: 3, description: 'Special Rate' },
  { value: 5, description: 'Lower Rate' },
  { value: 12, description: 'Standard Rate' },
  { value: 18, description: 'Standard Rate' },
  { value: 28, description: 'Higher Rate' }
];

// Handle focus on the description input
const handleFocus = () => {
  showDescriptionDropdown.value = true;
  // Reset selection and update dropdown items
  selectedIndex.value = -1;
  updateAllDropdownItems();
};

// Toggle dropdown visibility
const toggleDescriptionDropdown = () => {
  showDescriptionDropdown.value = !showDescriptionDropdown.value;
  if (showDescriptionDropdown.value) {
    // Reset selection and update dropdown items
    selectedIndex.value = -1;
    updateAllDropdownItems();
  }
};

// Handle blur event with delay to allow click events to fire first
const handleDescriptionBlur = () => {
  setTimeout(() => {
    showDescriptionDropdown.value = false;
    // Reset selection
    selectedIndex.value = -1;
  }, 200);
};

// Handle Escape key press
const handleEscapeKey = () => {
  // If dropdown is open, close it first
  if (showDescriptionDropdown.value) {
    showDescriptionDropdown.value = false;
    selectedIndex.value = -1;
    return;
  }

  // Otherwise close the modal and return focus to the Add Other Charges button
  closeModal();

  // Return focus to the Add Other Charges button
  nextTick(() => {
    const addOtherChargesButton = document.querySelector('#addOtherChargesButton');
    if (addOtherChargesButton) {
      addOtherChargesButton.focus();
    }
  });
};

// Select a description from the dropdown
const selectDescription = (description) => {
  // Find the full item with HSN and GST rate
  const item = existingChargesData.value.find(item => item.description === description);
  if (item) {
    selectDescriptionWithData(item);
  } else {
    form.value.description = description;
    showDescriptionDropdown.value = false;
  }
};

// Select a description with HSN and GST rate
const selectDescriptionWithData = (item) => {
  form.value.description = item.description;
  form.value.oth_hsn = item.hsn;
  form.value.oth_grate = item.gstRate;
  showDescriptionDropdown.value = false;
  // Recalculate GST after setting the rate
  calculateGST();
};

// Select an HSN code
const selectHSN = (item) => {
  form.value.oth_hsn = item.code;
  form.value.oth_grate = item.gstRate;
  showDescriptionDropdown.value = false;
  // Recalculate GST after setting the rate
  calculateGST();
};

// Select a GST rate
const selectGSTRate = (rate) => {
  form.value.oth_grate = rate;
  showDescriptionDropdown.value = false;
  // Recalculate GST after setting the rate
  calculateGST();
};

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  firmState: {
    type: String,
    default: ''
  },
  partyState: {
    type: String,
    default: ''
  },
  editCharge: {
    type: Object,
    default: null
  },
  existingCharges: {
    type: Array,
    default: () => []
  }
});

watch(() => props.show, (newVal) => {
  if (newVal) {
    // Focus the first input when modal opens
    nextTick(() => {
      if (firstInput.value) {
        firstInput.value.focus();
      }
    });
    if (props.editCharge) {
      // Initialize form with edit charge data
      form.value = { ...props.editCharge };
    } else {
      // Reset form for new charge
      form.value = {
        description: '',
        oth_amt: 0,
        oth_grate: 0,
        oth_cgst: 0,
        oth_sgst: 0,
        oth_igst: 0,
        oth_hsn: '',
        oth_tot: 0
      };
    }
  }
});

const emit = defineEmits(['update:show', 'add-charge']);

const firstInput = ref(null);

// New variable to store charge data
const newCharge = ref(null);

const { showToast } = useToast();

const form = ref({
  description: '',
  oth_amt: 0,
  oth_grate: 0,
  oth_cgst: 0,
  oth_sgst: 0,
  oth_igst: 0,
  oth_hsn: '',
  oth_tot: 0
});

// Auto calculation function
const calculateGST = () => {
  if (form.value.oth_amt && form.value.oth_grate) {
    const gstAmount = (form.value.oth_amt * form.value.oth_grate) / 100;

    console.log('💰 OtherCharges GST Calculation:', {
      amount: form.value.oth_amt,
      gstRate: form.value.oth_grate,
      gstAmount,
      statesMatch: statesMatch.value,
      firmState: props.firmState,
      partyState: props.partyState
    });

    // Use the statesMatch computed property for consistency
    if (statesMatch.value) {
      // Same state - apply CGST/SGST
      form.value.oth_cgst = parseFloat((gstAmount / 2).toFixed(2));
      form.value.oth_sgst = parseFloat((gstAmount / 2).toFixed(2));
      form.value.oth_igst = 0;
      console.log('✅ Applied CGST/SGST:', { cgst: form.value.oth_cgst, sgst: form.value.oth_sgst });
    } else {
      // Different states - apply IGST
      form.value.oth_igst = parseFloat(gstAmount.toFixed(2));
      form.value.oth_cgst = 0;
      form.value.oth_sgst = 0;
      console.log('✅ Applied IGST:', { igst: form.value.oth_igst });
    }

    // Calculate total
    form.value.oth_tot = parseFloat((form.value.oth_amt + gstAmount).toFixed(2));
  } else {
    // If amount or GST rate is not set, ensure total is just the amount
    form.value.oth_tot = parseFloat((form.value.oth_amt || 0).toFixed(2));
  }
};

// Compute if states match for disabling fields
const statesMatch = computed(() => {
  const normalizedFirmState = props.firmState?.toLowerCase()?.trim() ?? '';
  const normalizedPartyState = props.partyState?.toLowerCase()?.trim() ?? '';

  console.log('🔍 OtherChargesModal GST State Check:', {
    firmState: props.firmState,
    partyState: props.partyState,
    normalizedFirmState,
    normalizedPartyState,
    statesMatch: normalizedFirmState === normalizedPartyState
  });

  // If either state is empty, default to same state (CGST/SGST) for safety
  return !normalizedFirmState || !normalizedPartyState || normalizedFirmState === normalizedPartyState;
});

// Extract unique descriptions and their associated HSN codes and GST rates from existing charges
const existingChargesData = computed(() => {
  if (!props.existingCharges || !Array.isArray(props.existingCharges)) return [];

  // Create a map to store unique descriptions with their HSN and GST rate
  const uniqueMap = new Map();

  props.existingCharges.forEach(charge => {
    if (charge.description && charge.description.trim() !== '') {
      const description = charge.description.trim();
      // Only add if not already in the map or update with non-empty values
      if (!uniqueMap.has(description)) {
        uniqueMap.set(description, {
          description,
          hsn: charge.oth_hsn || '',
          gstRate: charge.oth_grate || 0
        });
      }
    }
  });

  // Convert map to array
  return Array.from(uniqueMap.values());
});

// Extract just the descriptions for backward compatibility
const uniqueDescriptions = computed(() => {
  return existingChargesData.value.map(item => item.description);
});

// Filtered descriptions based on search term
const filteredUniqueDescriptions = computed(() => {
  if (!form.value.description) return existingChargesData.value;

  const searchTerm = form.value.description.toLowerCase();
  return existingChargesData.value.filter(item =>
    item.description.toLowerCase().includes(searchTerm)
  );
});

// Filtered common descriptions based on search term
const filteredCommonDescriptions = computed(() => {
  if (!form.value.description) return commonDescriptions;

  const searchTerm = form.value.description.toLowerCase();
  return commonDescriptions.filter(item =>
    item.description.toLowerCase().includes(searchTerm)
  );
});

// Filtered HSN codes based on search term
const filteredHsnCodes = computed(() => {
  if (!form.value.description) return hsnCodes;

  const searchTerm = form.value.description.toLowerCase();
  return hsnCodes.filter(item =>
    item.code.toLowerCase().includes(searchTerm) ||
    item.description.toLowerCase().includes(searchTerm)
  );
});

// Filtered GST rates based on search term
const filteredGstRates = computed(() => {
  if (!form.value.description) return gstRates;

  const searchTerm = form.value.description.toLowerCase();
  return gstRates.filter(item =>
    item.value.toString().includes(searchTerm) ||
    item.description.toLowerCase().includes(searchTerm)
  );
});

// Function to filter dropdown items based on input
const filterDropdownItems = () => {
  // Reset selection when filtering
  selectedIndex.value = -1;

  // Update all dropdown items for keyboard navigation
  updateAllDropdownItems();
};

// Update the combined list of all dropdown items for keyboard navigation
const updateAllDropdownItems = () => {
  allDropdownItems.value = [
    ...filteredUniqueDescriptions.value.map(item => ({ type: 'existing', data: item })),
    ...filteredCommonDescriptions.value.map(item => ({ type: 'common', data: item })),
    ...filteredHsnCodes.value.map(item => ({ type: 'hsn', data: item })),
    ...filteredGstRates.value.map(item => ({ type: 'gst', data: item }))
  ];
};

// Handle keyboard navigation
const handleKeyNavigation = (direction) => {
  if (!showDescriptionDropdown.value) {
    showDescriptionDropdown.value = true;
    updateAllDropdownItems();
    return;
  }

  if (direction === 'down') {
    selectedIndex.value = Math.min(selectedIndex.value + 1, allDropdownItems.value.length - 1);
  } else if (direction === 'up') {
    selectedIndex.value = Math.max(selectedIndex.value - 1, -1);
  } else if (direction === 'enter' && selectedIndex.value >= 0) {
    const selected = allDropdownItems.value[selectedIndex.value];
    if (selected) {
      if (selected.type === 'existing') {
        selectDescriptionWithData(selected.data);
      } else if (selected.type === 'common') {
        selectDescriptionWithData(selected.data);
      } else if (selected.type === 'hsn') {
        selectHSN(selected.data);
      } else if (selected.type === 'gst') {
        selectGSTRate(selected.data.value);
      }
    }
  }
};

// Watch for changes in amount or GST rate to trigger auto calculation
watch([() => form.value.oth_amt, () => form.value.oth_grate], () => {
  calculateGST();
});

// Watch for changes in IGST to adjust CGST and SGST accordingly
watch(() => form.value.oth_igst, (newVal) => {
  if (newVal > 0) {
    form.value.oth_cgst = 0;
    form.value.oth_sgst = 0;
    calculateGST();
  }
});

// Watch for changes in CGST or SGST to adjust IGST accordingly
watch([() => form.value.oth_cgst, () => form.value.oth_sgst], ([newCGST, newSGST]) => {
  if (newCGST > 0 || newSGST > 0) {
    form.value.oth_igst = 0;
    // Recalculate total with CGST and SGST
    const totalGST = parseFloat(newCGST) + parseFloat(newSGST);
    form.value.oth_tot = parseFloat((form.value.oth_amt + totalGST).toFixed(2));
  }
});

const closeModal = () => {
  // Just close the modal without emitting any data
  emit('update:show', false);

  // Return focus to the Add Other Charges button
  nextTick(() => {
    const addOtherChargesButton = document.querySelector('#addOtherChargesButton');
    if (addOtherChargesButton) {
      addOtherChargesButton.focus();
    }
  });
};

const close = async () => {
  // Ensure GST calculations are up to date before closing
  if (form.value.oth_igst > 0) {
    calculateGST();
  } else if (form.value.oth_cgst > 0 || form.value.oth_sgst > 0) {
    const totalGST = parseFloat(form.value.oth_cgst) + parseFloat(form.value.oth_sgst);
    form.value.oth_tot = parseFloat((form.value.oth_amt + totalGST).toFixed(2));
  }

  // Store form values in newCharge before closing
  newCharge.value = { ...form.value };
  // Log form values when modal is closed

  // Show toast notification
  success('Other Charges modal closed');

  // Small delay before closing
  await new Promise(resolve => setTimeout(resolve, 50));

  // When closing, emit the current form values to ensure parent component has latest data
  emit('add-charge', newCharge.value);
  emit('update:show', false);
};

const addCharge = () => {
  // Only emit if we have valid data (description and amount)
  if (form.value.description && form.value.oth_amt > 0) {
    // Store in newCharge and emit
    newCharge.value = { ...form.value };
    // Log form values when charge is added
    emit('add-charge', newCharge.value);

    // Reset form after successful submission
    if (!props.editCharge) {
      form.value = {
        description: '',
        oth_amt: 0,
        oth_grate: 0,
        oth_cgst: 0,
        oth_sgst: 0,
        oth_igst: 0,
        oth_hsn: '',
        oth_tot: 0
      };
    }

    // Close the modal
    emit('update:show', false);
  }
};
</script>