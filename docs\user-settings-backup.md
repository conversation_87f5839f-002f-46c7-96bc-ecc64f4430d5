# User Settings Backup & Restore Feature

## Overview

The application now includes comprehensive backup and restore functionality for all user settings and data. This feature is available in two places:

1. **AI Settings Tab**: Export/Import AI-specific configuration
2. **Tools Tab**: Export/Import ALL user data and settings

## Features Added

### 1. Enhanced Logout Functionality

**Location**: `composables/auth/useLogout.ts`

- **Complete Data Cleanup**: When users log out, ALL localStorage data is cleared
- **Privacy Protection**: Ensures the next user gets a completely clean experience
- **Comprehensive Clearing**: Removes authentication tokens, AI settings, app preferences, API keys, user data, and more
- **Pattern-based Cleanup**: Automatically detects and clears user-specific data patterns

**Benefits**:
- Prevents data sharing between different users on the same device
- Ensures privacy and security
- Provides a fresh start for each new user

### 2. AI Settings Export/Import

**Location**: AI Settings tab in GlobalSettingsPopup.vue

**Features**:
- Export AI configuration with options to include/exclude sensitive data
- Import AI settings with merge or replace options
- Handles multiple providers, models, and API keys
- Includes usage statistics and custom providers

**Export Options**:
- ✅ Include API Keys (⚠️ Security sensitive)
- ✅ Include Usage Statistics
- ✅ Include Custom Providers

**Import Options**:
- ✅ Merge with existing settings
- ✅ Import API Keys
- ✅ Import Usage Statistics  
- ✅ Import Custom Providers

### 3. Comprehensive User Data Backup

**Location**: Tools tab in GlobalSettingsPopup.vue
**Composable**: `composables/utils/useUserDataManager.ts`

**Features**:
- Export ALL user data from localStorage
- Categorized data organization
- Selective import/export options
- Data summary and preview
- Complete backup and restore functionality

**Data Categories**:
- 🔐 Authentication (tokens, user data)
- 🤖 AI Settings (configuration, API keys, usage stats)
- ⚙️ App Settings (preferences, themes, tool visibility)
- 🔑 API Keys (various service credentials)
- 👤 User Data (weather cities, notes, inventory data, timer data)
- 🗂️ Temporary Data (cache, auto-save data)
- 📁 Other Data (miscellaneous user-specific data)

**Export Options**:
- ✅ Include Authentication Tokens (⚠️ Security Risk!)
- ✅ Include API Keys & Sensitive Data (⚠️ Keep Secure!)
- ✅ Include Temporary & Cache Data

**Import Options**:
- ✅ Merge with existing settings (instead of overwriting)
- ✅ Import Authentication Tokens (if included)
- ✅ Import API Keys & Sensitive Data
- ✅ Import Temporary & Cache Data

## Security Considerations

### High Security Items
- **Authentication Tokens**: Can be used to access user accounts
- **API Keys**: Can incur costs or access external services
- **Sensitive Data**: Personal information and credentials

### Recommendations
1. **Never share backup files** containing authentication tokens or API keys
2. **Store backup files securely** if they contain sensitive data
3. **Use separate backups** for different purposes (settings vs. complete data)
4. **Regularly clean up** old backup files

## Usage Examples

### Basic Settings Backup
1. Go to Tools tab in Settings
2. Click "Export All Settings" 
3. Keep all sensitive options unchecked
4. Download the backup file

### Complete Data Migration
1. Export all data with all options enabled (on old device)
2. Securely transfer the backup file
3. Import with merge disabled (on new device)
4. Delete the backup file securely

### AI Settings Only
1. Go to AI Settings tab
2. Use the AI-specific export/import feature
3. Share AI configurations safely (without API keys)

## File Formats

### AI Settings Export
```json
{
  "version": "2.0",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "basicConfig": { ... },
  "extendedConfig": { ... },
  "usageStats": [ ... ]
}
```

### Complete User Data Export
```json
{
  "version": "1.0",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "userAgent": "...",
  "data": {
    "authentication": { ... },
    "aiSettings": { ... },
    "appSettings": { ... },
    "apiKeys": { ... },
    "userData": { ... },
    "temporaryData": { ... },
    "other": { ... }
  },
  "metadata": {
    "totalKeys": 25,
    "exportedKeys": 20,
    "categories": ["aiSettings", "appSettings", "userData"],
    "options": { ... }
  }
}
```

## Implementation Details

### Key Components
- `useLogout.ts`: Centralized logout with complete data cleanup
- `useUserDataManager.ts`: Comprehensive data management
- `useAIConfig.ts`: Enhanced AI settings export/import
- `GlobalSettingsPopup.vue`: UI for all backup/restore features

### Data Flow
1. **Export**: localStorage → Categorization → JSON → File Download
2. **Import**: File Upload → JSON Parse → Validation → localStorage
3. **Logout**: Pattern Detection → Complete Cleanup → Redirect

## Privacy & Security Features

### Privacy First Design
- Complete data cleanup on logout
- No data persistence between users
- Secure handling of sensitive information
- User control over what data to include/exclude

### Security Measures
- Clear warnings for sensitive data
- Optional inclusion of security-critical items
- Secure file handling
- No automatic cloud storage or sharing

## Future Enhancements

### Planned Features
- Encrypted backup files
- Cloud storage integration (optional)
- Scheduled automatic backups
- Data compression for large backups
- Backup verification and integrity checks

### Potential Improvements
- Selective category import/export
- Backup file comparison tools
- Migration wizards for major updates
- Cross-device synchronization options
