<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-[95vw] max-h-[90vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-gradient-to-r from-indigo-600 to-indigo-900 p-4 text-white flex justify-between items-center">
        <div class="flex items-center">
          <h2 class="text-xl font-bold">CN Notes</h2>
          <div class="ml-4 flex items-center text-indigo-200 text-xs flex-wrap gap-2">
            <div>
              <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">ESC</span>
              <span class="mr-2">close</span>
            </div>
            <div>
              <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">/</span>
              <span class="mr-2">search</span>
            </div>
            <div>
              <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">↑↓</span>
              <span class="mr-2">navigate</span>
            </div>
            <div>
              <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">Alt+↑↓</span>
              <span class="mr-2">navigate in search</span>
            </div>
            <div>
              <span class="bg-indigo-700 rounded px-1.5 py-0.5 mr-1">Enter</span>
              <span>expand</span>
            </div>
          </div>
        </div>
        <button @click="close" class="text-white hover:text-gray-200">
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
        <!-- Search Bar and Controls -->
        <div class="mb-2 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
          <div class="flex items-center gap-2">
            <h3 class="text-lg font-semibold">All CN Notes</h3>
            <span v-if="searchQuery" class="text-xs bg-indigo-100 text-indigo-800 px-2 py-1 rounded-full">
              Filtered
            </span>
          </div>
          <div class="flex items-center gap-2">
            <div class="relative w-full sm:w-80" :class="{'ring-2 ring-yellow-400': isSearchFocused && selectedRowId}">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Search in all columns... (Alt+↑↓ to navigate while typing)"
                class="w-full pl-10 pr-10 py-2 border border-gray-300 rounded-md focus:outline-none"
                :class="{'focus:ring-2 focus:ring-indigo-500': !selectedRowId, 'focus:ring-2 focus:ring-yellow-400': selectedRowId}"
                @keyup.esc="searchQuery = ''"
                @focus="isSearchFocused = true"
                @blur="isSearchFocused = false"
                ref="searchInput"
                title="Press Alt+↑↓ to navigate rows and Alt+Enter to expand/collapse while typing"
              />
              <!-- Clear search button -->
              <button
                v-if="searchQuery"
                @click="searchQuery = ''"
                class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                title="Clear search"
              >
                <Icon name="heroicons:x-mark" class="w-5 h-5" />
              </button>

              <!-- Navigation active indicator -->
              <div
                v-if="isSearchFocused && selectedRowId"
                class="absolute right-10 top-1/2 transform -translate-y-1/2 flex items-center"
                title="Keyboard navigation active - use Alt+↑↓ to navigate"
              >
                <span class="text-yellow-500 animate-pulse">
                  <Icon name="heroicons:arrow-up-down" class="w-4 h-4" />
                </span>
              </div>
            </div>
            <button
              @click="showFormModal = true"
              class="bg-indigo-600 text-white px-4 py-2 rounded hover:bg-indigo-700 flex items-center"
            >
              <Icon name="heroicons:plus" class="w-5 h-5 mr-1" />
              Add New
            </button>
          </div>
        </div>

        <!-- Keyboard Shortcuts Help -->
        <div class="mb-4 bg-indigo-50 p-2 rounded-md text-xs text-indigo-800 flex flex-wrap gap-x-4 gap-y-1">
          <div class="flex items-center">
            <span class="font-medium mr-1">Keyboard Navigation:</span>
          </div>
          <div class="flex items-center">
            <kbd class="px-1.5 py-0.5 bg-white border border-indigo-300 rounded shadow-sm mr-1">↑↓</kbd>
            <span>Navigate rows</span>
          </div>
          <div class="flex items-center">
            <kbd class="px-1.5 py-0.5 bg-white border border-indigo-300 rounded shadow-sm mr-1">Enter</kbd>
            <span>Expand/collapse</span>
          </div>
          <div class="flex items-center">
            <kbd class="px-1.5 py-0.5 bg-white border border-indigo-300 rounded shadow-sm mr-1">E</kbd>
            <span>Edit row</span>
          </div>
          <div class="flex items-center">
            <kbd class="px-1.5 py-0.5 bg-white border border-indigo-300 rounded shadow-sm mr-1">Delete</kbd>
            <span>Delete row</span>
          </div>
          <div class="flex items-center font-medium text-indigo-600 ml-2">
            <span>When search is focused:</span>
          </div>
          <div class="flex items-center">
            <kbd class="px-1.5 py-0.5 bg-white border border-indigo-300 rounded shadow-sm mr-1">Alt+↑↓</kbd>
            <span>Navigate rows</span>
          </div>
          <div class="flex items-center">
            <kbd class="px-1.5 py-0.5 bg-white border border-indigo-300 rounded shadow-sm mr-1">Alt+Enter</kbd>
            <span>Expand/collapse</span>
          </div>
        </div>

        <!-- All CN Notes Table -->
        <div class="mb-8">
          <div class="overflow-x-auto border border-gray-200 rounded-lg shadow">
            <table class="min-w-full divide-y divide-gray-200" :class="{'search-focused': isSearchFocused}">
              <thead>
                <tr>
                  <th class="w-10 px-4 py-3 bg-indigo-600 text-white"></th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">CN Number</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">CN Date</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">Broker</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">Type</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">Folio</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">Other Charges</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">Final Amount</th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider bg-indigo-600">Actions</th>
                </tr>
                <!-- Selection indicator row -->
                <tr class="bg-indigo-500">
                  <th colspan="9" class="px-2 py-0.5 text-xs text-white">
                    <div class="flex justify-between items-center">
                      <div class="flex items-center">
                        <span v-if="selectedRowId">
                          Selected: {{ getSelectedNoteCnNo() }}
                        </span>
                        <span v-else>Click on a row to select it or use arrow keys to navigate</span>

                        <!-- Status Message -->
                        <span v-if="statusMessage" class="ml-4 bg-indigo-600 px-2 py-0.5 rounded animate-pulse">
                          {{ statusMessage }}
                        </span>
                      </div>
                      <span>{{ filteredNotes.length }} records</span>
                    </div>
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <template v-for="(note, index) in filteredNotes" :key="note._id">
                  <!-- Main Row -->
                  <tr
                    :class="{
                      'bg-gray-50': index % 2 === 0 && selectedRowId !== note._id,
                      'hover:bg-indigo-50': selectedRowId !== note._id,
                      'bg-yellow-green': selectedRowId === note._id
                    }"
                    @click="selectRow(note._id)"
                    :id="`row-${note._id}`"
                    tabindex="0"
                    @keydown="handleRowKeyDown($event, note._id, index)"
                  >
                    <td class="px-4 py-4 whitespace-nowrap text-center">
                      <button
                        @click.stop="toggleExpandRow(note._id)"
                        class="text-indigo-600 hover:text-indigo-800 focus:outline-none"
                        :title="expandedRows.includes(note._id) ? 'Collapse' : 'Expand'"
                      >
                        <Icon
                          :name="expandedRows.includes(note._id) ? 'heroicons:chevron-down' : 'heroicons:chevron-right'"
                          class="w-5 h-5"
                        />
                      </button>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap font-medium text-indigo-600">{{ note.cn_no }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(note.cn_date) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ note.broker }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ note.type }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ note.folio }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">{{ formatCurrency(note.oth_chg) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap font-medium">{{ formatCurrency(note.famt) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap">
                      <div class="flex items-center space-x-2">
                        <button @click.stop="editNote(note)" class="text-blue-600 hover:text-blue-800" title="Edit">
                          <Icon name="heroicons:pencil" class="w-5 h-5" />
                        </button>
                        <button @click.stop="deleteNote(note._id)" class="text-red-600 hover:text-red-800" title="Delete">
                          <Icon name="heroicons:trash" class="w-5 h-5" />
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Expanded Row with Folio Details -->
                  <tr v-if="expandedRows.includes(note._id)">
                    <td colspan="9" class="px-0 py-0 border-b border-gray-200">
                      <div class="bg-indigo-50 p-4">
                        <h4 class="text-sm font-medium text-indigo-800 mb-2">Folio Records</h4>

                        <!-- No Folio Records Message -->
                        <div v-if="!note.Folio_rec || note.Folio_rec.length === 0" class="text-center py-4 text-gray-500">
                          No folio records found for this CN Note.
                        </div>

                        <!-- Folio Records Table -->
                        <div v-else class="overflow-x-auto border border-indigo-200 rounded-lg">
                          <table class="min-w-full divide-y divide-indigo-200">
                            <thead class="bg-indigo-100">
                              <tr>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Symbol</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Quantity</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Buy Price</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Amount</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Brokerage</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Net Amount</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Current Price</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Current Value</th>
                                <th class="px-4 py-2 text-left text-xs font-medium text-indigo-700">Profit/Loss</th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-indigo-100">
                              <tr v-for="folio in note.Folio_rec" :key="folio._id" class="hover:bg-indigo-50">
                                <td class="px-4 py-2 whitespace-nowrap font-medium">{{ folio.symbol }}</td>
                                <td class="px-4 py-2 whitespace-nowrap">{{ formatNumber(folio.qnty) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap">{{ formatCurrency(folio.price) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap">{{ formatCurrency(folio.amt) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap">{{ formatCurrency(folio.brokerage) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap font-medium">{{ formatCurrency(folio.namt) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap">{{ formatCurrency(folio.cprice) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap">{{ formatCurrency(folio.cval) }}</td>
                                <td class="px-4 py-2 whitespace-nowrap" :class="getProfitLossClass(folio.pl)">
                                  {{ formatProfitLoss(folio.pl) }}
                                </td>
                              </tr>
                            </tbody>
                            <!-- Summary Row -->
                            <tfoot class="bg-indigo-50">
                              <tr>
                                <td class="px-4 py-2 font-medium text-indigo-700">Total</td>
                                <td class="px-4 py-2">{{ calculateTotalQuantity(note.Folio_rec) }}</td>
                                <td class="px-4 py-2">-</td>
                                <td class="px-4 py-2 font-medium">{{ formatCurrency(calculateTotalAmount(note.Folio_rec, 'amt')) }}</td>
                                <td class="px-4 py-2">{{ formatCurrency(calculateTotalAmount(note.Folio_rec, 'brokerage')) }}</td>
                                <td class="px-4 py-2 font-medium">{{ formatCurrency(calculateTotalAmount(note.Folio_rec, 'namt')) }}</td>
                                <td class="px-4 py-2">-</td>
                                <td class="px-4 py-2 font-medium">{{ formatCurrency(calculateTotalAmount(note.Folio_rec, 'cval')) }}</td>
                                <td class="px-4 py-2 font-medium" :class="getProfitLossClass(calculateTotalProfitLoss(note.Folio_rec))">
                                  {{ formatProfitLoss(calculateTotalProfitLoss(note.Folio_rec)) }}
                                </td>
                              </tr>
                            </tfoot>
                          </table>
                        </div>
                      </div>
                    </td>
                  </tr>
                </template>

                <!-- No Results Message -->
                <tr v-if="filteredNotes.length === 0">
                  <td colspan="9" class="px-6 py-8 text-center text-gray-500">
                    <p v-if="searchQuery">No CN Notes match your search criteria.</p>
                    <p v-else>No CN Notes found. Add your first CN Note to get started.</p>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Nested Form Modal -->
  <CNNoteFormModal
    :show="showFormModal"
    :is-edit="!!selectedNote"
    :initial-data="selectedNote"
    @close="closeFormModal"
    @submit="handleFormSubmit"
  />
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import CNNoteFormModal from './CNNoteFormModal.vue'
const props = defineProps<{
  show: boolean
  isEdit: boolean
  initialData?: any
  cnNotes?: any[]
}>()

const emit = defineEmits(['close', 'submit', 'edit', 'delete'])

// State variables
const showFormModal = ref(false)
const selectedNote = ref(null)
const searchQuery = ref('')
const expandedRows = ref<string[]>([])
const searchInput = ref<HTMLInputElement | null>(null)
const selectedRowId = ref<string | null>(null)
const isSearchFocused = ref(false)

// Computed property for filtered notes based on search query
const filteredNotes = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.cnNotes || []
  }

  const query = searchQuery.value.toLowerCase()
  return (props.cnNotes || []).filter(note => {
    // Search in main CN Note fields
    if (
      (note.cn_no && note.cn_no.toLowerCase().includes(query)) ||
      (note.broker && note.broker.toLowerCase().includes(query)) ||
      (note.type && note.type.toLowerCase().includes(query)) ||
      (note.folio && note.folio.toLowerCase().includes(query))
    ) {
      return true
    }

    // Search in Folio records if they exist
    if (note.Folio_rec && note.Folio_rec.length > 0) {
      return note.Folio_rec.some((folio: any) =>
        (folio.symbol && folio.symbol.toLowerCase().includes(query)) ||
        (folio.sector && folio.sector.toLowerCase().includes(query))
      )
    }

    return false
  })
})

// Toggle row expansion
function toggleExpandRow(noteId: string) {
  const index = expandedRows.value.indexOf(noteId)
  if (index === -1) {
    expandedRows.value.push(noteId)
  } else {
    expandedRows.value.splice(index, 1)
  }
}

// Select a row
function selectRow(noteId: string, maintainSearchFocus = false) {
  selectedRowId.value = noteId

  // If we need to maintain search focus (when navigating from search box)
  if (maintainSearchFocus && isSearchFocused.value) {
    // Just update the selection without changing focus
    // Scroll the selected row into view if needed
    setTimeout(() => {
      const rowElement = document.getElementById(`row-${noteId}`)
      if (rowElement) {
        // Scroll into view without stealing focus
        rowElement.scrollIntoView({ behavior: 'smooth', block: 'nearest' })
      }
    }, 10)
  } else {
    // Standard behavior - focus the row for keyboard navigation
    setTimeout(() => {
      const rowElement = document.getElementById(`row-${noteId}`)
      if (rowElement) {
        rowElement.focus()
      }
    }, 10)
  }
}

// Handle keyboard navigation in rows
function handleRowKeyDown(event: KeyboardEvent, noteId: string, index: number) {
  const notes = filteredNotes.value

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      // Move to next row if available
      if (index < notes.length - 1) {
        selectRow(notes[index + 1]._id)
        // Show a status message
        showStatusMessage(`Selected: ${notes[index + 1].cn_no}`)
      }
      break

    case 'ArrowUp':
      event.preventDefault()
      // Move to previous row if available
      if (index > 0) {
        selectRow(notes[index - 1]._id)
        // Show a status message
        showStatusMessage(`Selected: ${notes[index - 1].cn_no}`)
      }
      break

    case 'Enter':
    case ' ': // Space key
      event.preventDefault()
      // Toggle expansion of the selected row
      toggleExpandRow(noteId)
      // Show a status message
      const isExpanded = expandedRows.value.includes(noteId)
      showStatusMessage(isExpanded ? `Expanded: ${getSelectedNoteCnNo()}` : `Collapsed: ${getSelectedNoteCnNo()}`)
      break

    case 'e':
    case 'E':
      event.preventDefault()
      // Edit the selected note
      editNote(notes[index])
      showStatusMessage(`Editing: ${notes[index].cn_no}`)
      break

    case 'Delete':
      event.preventDefault()
      // Delete the selected note (with confirmation)
      if (confirm('Are you sure you want to delete this CN Note?')) {
        const cnNo = notes[index].cn_no
        deleteNote(noteId)
        showStatusMessage(`Deleted: ${cnNo}`)
      }
      break

    case 'Tab':
      // Allow normal tab navigation but ensure the row stays selected
      if (!event.shiftKey && index < notes.length - 1) {
        // If tabbing forward and not on the last row, select the next row
        setTimeout(() => {
          selectRow(notes[index + 1]._id)
        }, 10)
      } else if (event.shiftKey && index > 0) {
        // If tabbing backward and not on the first row, select the previous row
        setTimeout(() => {
          selectRow(notes[index - 1]._id)
        }, 10)
      }
      break
  }
}

// Show a temporary status message
const statusMessage = ref('')
const statusTimeout = ref<number | null>(null)

function showStatusMessage(message: string) {
  statusMessage.value = message

  // Clear any existing timeout
  if (statusTimeout.value) {
    clearTimeout(statusTimeout.value)
  }

  // Set a new timeout to clear the message after 2 seconds
  statusTimeout.value = window.setTimeout(() => {
    statusMessage.value = ''
    statusTimeout.value = null
  }, 2000) as unknown as number
}

function editNote(note: any) {

  // Format the date for the form input (YYYY-MM-DD format)
  const formattedNote = {
    ...note,
    cn_date: formatDateForInput(note.cn_date)
  }
  selectedNote.value = formattedNote
  showFormModal.value = true
}

// Format date for the date input (YYYY-MM-DD format)
function formatDateForInput(dateString: string) {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return ''
  return date.toISOString().split('T')[0]
}

function deleteNote(id: string) {
  emit('delete', id)
}

function handleFormSubmit(payload: any) {
  emit('submit', payload)
  showFormModal.value = false
}

function closeFormModal() {
  showFormModal.value = false
  selectedNote.value = null
}

function close() {
  emit('close')
}

// Format date for display
function formatDate(dateString: string) {
  if (!dateString) return ''
  const date = new Date(dateString)
  if (isNaN(date.getTime())) return dateString
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Format currency values
function formatCurrency(value: number | string | undefined | null): string {
  if (value === undefined || value === null) return '-'
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return '-'
  return `₹${numValue.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// Format numbers with commas
function formatNumber(value: number | string | undefined | null): string {
  if (value === undefined || value === null) return '-'
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return '-'
  return numValue.toLocaleString('en-IN', { maximumFractionDigits: 0 })
}

// Format profit/loss values with sign
function formatProfitLoss(value: number | string | undefined | null): string {
  if (value === undefined || value === null) return '-'
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return '-'
  if (numValue === 0) return `₹0.00`

  return numValue > 0
    ? `+₹${numValue.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
    : `-₹${Math.abs(numValue).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`
}

// Get CSS class for profit/loss values
function getProfitLossClass(value: number | string | undefined | null): string {
  if (value === undefined || value === null) return ''
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (isNaN(numValue)) return ''
  if (numValue > 0) return 'text-green-600 font-medium'
  if (numValue < 0) return 'text-red-600 font-medium'
  return ''
}

// Calculate total quantity for a set of folio records
function calculateTotalQuantity(folioRecords: any[]): string {
  if (!folioRecords || !folioRecords.length) return '0'
  const total = folioRecords.reduce((sum, record) => {
    const qty = typeof record.qnty === 'string' ? parseFloat(record.qnty) : record.qnty
    return sum + (isNaN(qty) ? 0 : qty)
  }, 0)
  return total.toLocaleString('en-IN', { maximumFractionDigits: 0 })
}

// Calculate total amount for a specific field in folio records
function calculateTotalAmount(folioRecords: any[], field: string): number {
  if (!folioRecords || !folioRecords.length) return 0
  return folioRecords.reduce((sum, record) => {
    const value = typeof record[field] === 'string' ? parseFloat(record[field]) : record[field]
    return sum + (isNaN(value) ? 0 : value)
  }, 0)
}

// Calculate total profit/loss for a set of folio records
function calculateTotalProfitLoss(folioRecords: any[]): number {
  if (!folioRecords || !folioRecords.length) return 0
  return folioRecords.reduce((sum, record) => {
    const pl = typeof record.pl === 'string' ? parseFloat(record.pl) : record.pl
    return sum + (isNaN(pl) ? 0 : pl)
  }, 0)
}

// Get the CN Number of the currently selected note
function getSelectedNoteCnNo(): string {
  if (!selectedRowId.value) return ''

  const selectedNote = filteredNotes.value.find(note => note._id === selectedRowId.value)
  if (!selectedNote) return ''

  return selectedNote.cn_no || ''
}

// Keyboard event handlers for the entire modal
function handleKeyDown(event: KeyboardEvent) {
  // Only handle if modal is open
  if (!props.show) return

  // '/' key to focus search
  if (event.key === '/' && document.activeElement !== searchInput.value) {
    event.preventDefault()
    searchInput.value?.focus()
  }

  // ESC key to close modal (unless focus is in search input)
  if (event.key === 'Escape' && document.activeElement !== searchInput.value) {
    event.preventDefault()
    close()
  }

  // Handle navigation keys even when search is focused
  if (isSearchFocused.value && filteredNotes.value.length > 0) {
    const currentIndex = selectedRowId.value ?
      filteredNotes.value.findIndex(note => note._id === selectedRowId.value) : -1

    // Handle arrow keys for navigation
    if (event.key === 'ArrowDown' && event.altKey) {
      event.preventDefault()
      // Move to next row or first row if none selected
      if (currentIndex === -1 || currentIndex >= filteredNotes.value.length - 1) {
        selectRow(filteredNotes.value[0]._id, true) // Maintain search focus
        showStatusMessage(`Selected: ${filteredNotes.value[0].cn_no}`)
      } else {
        selectRow(filteredNotes.value[currentIndex + 1]._id, true) // Maintain search focus
        showStatusMessage(`Selected: ${filteredNotes.value[currentIndex + 1].cn_no}`)
      }
    } else if (event.key === 'ArrowUp' && event.altKey) {
      event.preventDefault()
      // Move to previous row or last row if none selected
      if (currentIndex <= 0) {
        const lastIndex = filteredNotes.value.length - 1
        selectRow(filteredNotes.value[lastIndex]._id, true) // Maintain search focus
        showStatusMessage(`Selected: ${filteredNotes.value[lastIndex].cn_no}`)
      } else {
        selectRow(filteredNotes.value[currentIndex - 1]._id, true) // Maintain search focus
        showStatusMessage(`Selected: ${filteredNotes.value[currentIndex - 1].cn_no}`)
      }
    } else if (event.key === 'Enter' && event.altKey && selectedRowId.value) {
      event.preventDefault()
      // Toggle expansion of the selected row
      toggleExpandRow(selectedRowId.value)
      const isExpanded = expandedRows.value.includes(selectedRowId.value)
      showStatusMessage(isExpanded ? `Expanded: ${getSelectedNoteCnNo()}` : `Collapsed: ${getSelectedNoteCnNo()}`)
    }
  }
}

// Lifecycle hooks
onMounted(() => {
  if (process.client) {
    window.addEventListener('keydown', handleKeyDown)
  }
})

onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('keydown', handleKeyDown)
  }
})

// Watch for modal visibility changes
watch(() => props.show, (newVal) => {
  if (newVal) {
    // Focus search input after a short delay to ensure the modal is fully rendered
    setTimeout(() => {
      searchInput.value?.focus()
    }, 100)

    // Select the first row if available
    if (filteredNotes.value.length > 0) {
      selectedRowId.value = filteredNotes.value[0]._id
    }
  } else {
    // Clear search, expanded rows, and selection when modal is closed
    searchQuery.value = ''
    expandedRows.value = []
    selectedRowId.value = null
  }
})

// Watch for filtered notes changes to update selection
watch(filteredNotes, (newNotes) => {
  // If there are notes but no selection, select the first one
  if (newNotes.length > 0 && !selectedRowId.value) {
    selectedRowId.value = newNotes[0]._id
  }
  // If the selected row is no longer in the filtered list, select the first one
  else if (newNotes.length > 0 && selectedRowId.value && !newNotes.some(note => note._id === selectedRowId.value)) {
    selectedRowId.value = newNotes[0]._id
  }
  // If there are no notes, clear the selection
  else if (newNotes.length === 0) {
    selectedRowId.value = null
  }
})
</script>

<style scoped>
.bg-yellow-green {
  background-color: #adff2f !important;
}

tr:focus {
  outline: 2px solid #4f46e5;
  outline-offset: -2px;
  position: relative;
}

tr:focus::after {
  content: '⟸';
  position: absolute;
  left: -20px;
  color: #4f46e5;
  font-weight: bold;
  font-size: 1.2rem;
}

/* Add smooth transition for row selection */
tr {
  transition: background-color 0.2s ease;
}

/* Add a left border to indicate selected row even when not focused */
tr.bg-yellow-green {
  border-left: 4px solid #4f46e5;
  position: relative;
}

/* Add a pulsing animation to the selected row when search is focused */
@keyframes pulse-border {
  0% { border-left-color: #4f46e5; }
  50% { border-left-color: #adff2f; }
  100% { border-left-color: #4f46e5; }
}

.search-focused tr.bg-yellow-green {
  animation: pulse-border 2s infinite;
}
</style>