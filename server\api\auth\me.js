/**
 * API endpoint to get the current authenticated user
 */
import { defineE<PERSON><PERSON><PERSON><PERSON>, createError } from 'h3';
import { getUserFromEvent } from '../../utils/auth';

export default defineEventHandler(async (event) => {
  try {
    // Get the authenticated user
    const user = await getUserFromEvent(event);
    if (!user) {
      throw createError({
        statusCode: 401,
        message: 'Unauthorized'
      });
    }

    // Return user data (excluding sensitive information)
    return {
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        fullname: user.fullname,
        role: user.role,
        firmId: user.firmId,
        status: user.status
      }
    };
  } catch (error) {
    console.error('Error in auth/me API:', error);

    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Internal server error'
    });
  }
});
