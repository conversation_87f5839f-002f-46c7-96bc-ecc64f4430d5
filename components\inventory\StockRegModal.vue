<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <!-- Gradient Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-3 rounded-t-lg shadow-md flex justify-between items-center">
          <h3 class="text-lg leading-6 font-medium text-white">Stock History for {{ partyName }} - {{ itemName }}</h3>
          <button @click="close" class="text-white hover:text-gray-200 focus:outline-none">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="w-full">
            <!-- Table for stock registration data -->
            <div v-if="filteredStockReg.length > 0" class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill No</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GST %</th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(item, index) in filteredStockReg" :key="index" class="hover:bg-gray-50">
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ item.type }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ item.bno }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ formatDate(item.bdate) }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ item.batch || '-' }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ item.qty }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ item.uom }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(item.rate) }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ item.grate || 0 }}%</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(item.total) }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
            <div v-else class="py-4 text-center text-gray-500">
              No stock history found for this item and party.
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button @click="close" type="button"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, defineProps, defineEmits } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  stockReg: {
    type: Array,
    default: () => []
  },
  partyName: {
    type: String,
    default: ''
  },
  itemName: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['close']);

// Filter stock registration data by party name and item name
const filteredStockReg = computed(() => {
  if (!props.stockReg || !props.partyName || !props.itemName) return [];
  
  return props.stockReg.filter(item => 
    item.supply === props.partyName && 
    item.item === props.itemName
  );
});

// Format date for display
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Format currency for display
const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(value);
};

// Close the modal
const close = () => {
  emit('close');
};
</script>