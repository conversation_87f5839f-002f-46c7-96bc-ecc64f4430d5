// server/utils/register-models.ts
import mongoose from 'mongoose';

// Import all models to ensure they are registered with Mongoose
import '../models/User';
import '../models/Firm';
import '../models/Role';
import '../models/Document';
import '../models/AIHistory';
import { NSE } from '../models/NSE';
import { Folio } from '../models/Folio';
import { CNNote } from '../models/CNNote';
import NSEDocumentModel from '../models/NSEDocument';

// Import additional MongoDB models
import '../models/AdvanceRecovery';
import { ChatMessage } from '../models/ChatMessage';
import '../models/EmployeeAdvance';
import '../models/ManagerCode';
import '../models/MasterRoll';
import { MutualFund } from '../models/MutualFund';
import '../models/Wage';

// Import inventory models
import '../models/inventory/Bills';
import '../models/inventory/StockReg';
import '../models/inventory/Stocks';
import '../models/inventory/Party';

/**
 * Register all models with Mongoose
 * This function doesn't need to do anything as the imports above
 * will register the models with Mongoose
 */
export function registerAllModels() {
  console.log('Registering all Mongoose models...');

  // Log all registered models
  const modelNames = Object.keys(mongoose.models);
  console.log(`Registered models: ${modelNames.join(', ')}`);

  return modelNames;
}

// Export default for use as a plugin
export default registerAllModels;
