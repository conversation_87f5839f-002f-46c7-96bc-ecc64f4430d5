<template>
  <div class="bg-white rounded-lg shadow p-6 subs-form">
    <form @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Name Field -->
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Sub Name *</label>
          <input
            type="text"
            id="name"
            v-model="formData.name"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Opening Balance Field -->
        <div>
          <label for="balance" class="block text-sm font-medium text-gray-700 mb-1">Opening Balance</label>
          <input
            type="number"
            id="balance"
            v-model="formData.balance"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>

        <!-- Contact Info Section -->
        <div class="md:col-span-2">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Phone Field -->
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">Phone</label>
              <input
                type="tel"
                id="phone"
                v-model="formData.contactInfo.phone"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <!-- Email Field -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input
                type="email"
                id="email"
                v-model="formData.contactInfo.email"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              />
            </div>

            <!-- Address Field -->
            <div class="md:col-span-2">
              <label for="address" class="block text-sm font-medium text-gray-700 mb-1">Address</label>
              <textarea
                id="address"
                v-model="formData.contactInfo.address"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              ></textarea>
            </div>
          </div>
        </div>

        <!-- Active Status -->
        <div class="md:col-span-2">
          <div class="flex items-center">
            <input
              type="checkbox"
              id="isActive"
              v-model="formData.isActive"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="isActive" class="ml-2 block text-sm text-gray-900">
              Active
            </label>
          </div>
        </div>
      </div>

      <!-- Form Actions (only shown if showFooterButtons is true) -->
      <div v-if="showFooterButtons" class="mt-8 flex justify-end space-x-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>{{ subsModel ? 'Update' : 'Save' }}</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, defineProps, defineEmits, defineExpose } from 'vue';

// Props
const props = defineProps({
    subsModel: {
      type: Object,
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    showFooterButtons: {
      type: Boolean,
      default: true
    }
  });

// Emits
const emit = defineEmits(['submit', 'cancel']);
// Initialize form data
const formData = ref({
  name: '',
  balance: 0,
  contactInfo: {
    phone: '',
    email: '',
    address: ''
  },
  isActive: true
});

// Methods
const handleSubmit = () => {

  emit('submit', { ...formData.value });
};

// Initialize
onMounted(() => {
  // If editing an existing subs model, populate the form
  if (props.subsModel) {
    formData.value = {
      name: props.subsModel.name,
      balance: props.subsModel.balance || 0,
      contactInfo: {
        phone: props.subsModel.contactInfo?.phone || '',
        email: props.subsModel.contactInfo?.email || '',
        address: props.subsModel.contactInfo?.address || ''
      },
      isActive: props.subsModel.isActive !== false
    };
  }
});

// Watch for changes to subsModel prop
watch(() => props.subsModel, (newSubsModel) => {
  if (newSubsModel) {
    formData.value = {
      name: newSubsModel.name,
      balance: newSubsModel.balance || 0,
      contactInfo: {
        phone: newSubsModel.contactInfo?.phone || '',
        email: newSubsModel.contactInfo?.email || '',
        address: newSubsModel.contactInfo?.address || ''
      },
      isActive: newSubsModel.isActive !== false
    };
  }
});

// Expose methods to parent component
defineExpose({
  handleSubmit
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
