import { defineNitroPlugin } from 'nitropack/dist/runtime/plugin';
import { initFirebase } from '../utils/firebase';
import { useRuntimeConfig } from '#imports';
import { setCookie } from 'h3';

// Declare global Firebase auth storage
declare global {
  var __firebaseAuth: {
    idToken: string;
    refreshToken: string;
    expiresIn: string;
  } | undefined;
}

export default defineNitroPlugin(async (nitroApp) => {
  initFirebase();

  try {
    const config = useRuntimeConfig();
    const apiKey = config.firebaseApiKey;
    const email = process.env.FB_USER;
    const password = process.env.FB_PASS;

    if (!email || !password) {
      console.error('Firebase authentication credentials not found in environment variables');
      return;
    }

    // Make the request to Firebase Auth API
    const response = await fetch(
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password,
          returnSecureToken: true,
        }),
      }
    );

    const data = await response.json();


    // Check if authentication was successful
    if (data.idToken) {

      // Store tokens in global variable for server-side use
      // This is a simple approach - for production, consider using a more robust solution
      global.__firebaseAuth = {
        idToken: data.idToken,
        refreshToken: data.refreshToken,
        expiresIn: data.expiresIn
      };

      // Set cookies for client-side use
      // Note: This will only work for event handlers that have access to the event context
      nitroApp.hooks.hook('request', (event) => {
        setCookie(event, 'firebase_id_token', data.idToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          maxAge: parseInt(data.expiresIn) || 3600,
          path: '/'
        });

        setCookie(event, 'firebase_refresh_token', data.refreshToken, {
          httpOnly: true,
          secure: process.env.NODE_ENV === 'production',
          maxAge: 30 * 24 * 60 * 60, // 30 days
          path: '/'
        });
      });
    } else {
      console.error('Firebase Authentication failed:', data.error);
    }
  } catch (error) {
    console.error('Error during Firebase Authentication:', error);
  }
});