# Supabase Database Setup Guide

## Overview
This guide will help you set up the required database tables in your Supabase project for the Labor Management System.

## Prerequisites
1. A Supabase project created at [supabase.com](https://supabase.com)
2. Access to the Supabase SQL Editor in your project dashboard

## Database Schema Setup

### Step 1: Create Labor Groups Table

```sql
-- Labor Groups Table
CREATE TABLE IF NOT EXISTS labor_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(7) DEFAULT '#3B82F6',
  phone VARCHAR(20),
  address TEXT,
  aadhar VARCHAR(12),
  bank_details JSONB DEFAULT '{}',
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(name, firm_id)
);
```

### Step 2: Create Labor Profiles Table

```sql
-- Labor Profiles Table
CREATE TABLE IF NOT EXISTS labor_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  daily_rate DECIMAL(10,2) NOT NULL,
  group_id UUID REFERENCES labor_groups(id) ON DELETE SET NULL,
  is_active BOOLEAN DEFAULT true,
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(name, firm_id)
);
```

### Step 3: Create Attendance Records Table

```sql
-- Attendance Records Table
CREATE TABLE IF NOT EXISTS attendance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  labor_id UUID REFERENCES labor_profiles(id) ON DELETE CASCADE,
  attendance_date DATE NOT NULL,
  days_worked DECIMAL(3,1) DEFAULT 0 CHECK (days_worked IN (0, 0.5, 1, 1.5, 2)),
  daily_rate DECIMAL(10,2) NOT NULL,
  amount DECIMAL(10,2) GENERATED ALWAYS AS (days_worked * daily_rate) STORED,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  site_expenses DECIMAL(10,2) DEFAULT 0,
  site_materials DECIMAL(10,2) DEFAULT 0,
  medical_expenses DECIMAL(10,2) DEFAULT 0,
  other_expenses DECIMAL(10,2) DEFAULT 0,
  notes TEXT,
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(labor_id, attendance_date)
);
```

### Step 4: Create Payment Records Table

```sql
-- Payment Records Table
CREATE TABLE IF NOT EXISTS payment_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES labor_groups(id) ON DELETE SET NULL,
  payment_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  project VARCHAR(255),
  payment_type VARCHAR(50) NOT NULL,
  payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('bank', 'cash')),
  bank_details JSONB DEFAULT '{}',
  advance_amount DECIMAL(10,2) DEFAULT 0,
  description TEXT,
  firestore_sync_status VARCHAR(20) DEFAULT 'pending' CHECK (firestore_sync_status IN ('pending', 'synced', 'failed')),
  firestore_doc_id VARCHAR(255),
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### Step 5: Create Performance Indexes

```sql
-- Performance Indexes
CREATE INDEX IF NOT EXISTS idx_labor_profiles_group_id ON labor_profiles(group_id);
CREATE INDEX IF NOT EXISTS idx_labor_profiles_firm_id ON labor_profiles(firm_id);
CREATE INDEX IF NOT EXISTS idx_labor_profiles_name ON labor_profiles(name);

CREATE INDEX IF NOT EXISTS idx_labor_groups_firm_id ON labor_groups(firm_id);
CREATE INDEX IF NOT EXISTS idx_labor_groups_name ON labor_groups(name);

CREATE INDEX IF NOT EXISTS idx_attendance_records_labor_id ON attendance_records(labor_id);
CREATE INDEX IF NOT EXISTS idx_attendance_records_date ON attendance_records(attendance_date);
CREATE INDEX IF NOT EXISTS idx_attendance_records_period ON attendance_records(period_start, period_end);
CREATE INDEX IF NOT EXISTS idx_attendance_records_firm_id ON attendance_records(firm_id);

CREATE INDEX IF NOT EXISTS idx_payment_records_group_id ON payment_records(group_id);
CREATE INDEX IF NOT EXISTS idx_payment_records_date ON payment_records(payment_date);
CREATE INDEX IF NOT EXISTS idx_payment_records_firm_id ON payment_records(firm_id);
```

## Row Level Security (RLS) Setup

### Step 6: Enable RLS on All Tables

```sql
-- Enable Row Level Security
ALTER TABLE labor_groups ENABLE ROW LEVEL SECURITY;
ALTER TABLE labor_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE attendance_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_records ENABLE ROW LEVEL SECURITY;
```

### Step 7: Create RLS Policies

```sql
-- RLS Policies for Labor Groups
CREATE POLICY "Users can view their firm's labor groups" ON labor_groups
  FOR SELECT USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can insert labor groups for their firm" ON labor_groups
  FOR INSERT WITH CHECK (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can update their firm's labor groups" ON labor_groups
  FOR UPDATE USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can delete their firm's labor groups" ON labor_groups
  FOR DELETE USING (true); -- Adjust based on your auth requirements

-- RLS Policies for Labor Profiles
CREATE POLICY "Users can view their firm's labor profiles" ON labor_profiles
  FOR SELECT USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can insert labor profiles for their firm" ON labor_profiles
  FOR INSERT WITH CHECK (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can update their firm's labor profiles" ON labor_profiles
  FOR UPDATE USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can delete their firm's labor profiles" ON labor_profiles
  FOR DELETE USING (true); -- Adjust based on your auth requirements

-- RLS Policies for Attendance Records
CREATE POLICY "Users can view their firm's attendance records" ON attendance_records
  FOR SELECT USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can insert attendance records for their firm" ON attendance_records
  FOR INSERT WITH CHECK (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can update their firm's attendance records" ON attendance_records
  FOR UPDATE USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can delete their firm's attendance records" ON attendance_records
  FOR DELETE USING (true); -- Adjust based on your auth requirements

-- RLS Policies for Payment Records
CREATE POLICY "Users can view their firm's payment records" ON payment_records
  FOR SELECT USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can insert payment records for their firm" ON payment_records
  FOR INSERT WITH CHECK (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can update their firm's payment records" ON payment_records
  FOR UPDATE USING (true); -- Adjust based on your auth requirements

CREATE POLICY "Users can delete their firm's payment records" ON payment_records
  FOR DELETE USING (true); -- Adjust based on your auth requirements
```

## Setup Instructions

### Method 1: Using Supabase SQL Editor (Recommended)

1. **Open your Supabase project dashboard**
2. **Navigate to the SQL Editor** (in the left sidebar)
3. **Create a new query**
4. **Copy and paste each SQL block above** (one at a time)
5. **Run each query** by clicking the "Run" button
6. **Verify tables are created** by checking the Table Editor

### Method 2: Using Database URL

If you prefer using a database client:

1. **Get your database URL** from Project Settings > Database
2. **Connect using your preferred PostgreSQL client**
3. **Execute the SQL statements above**

## Verification

After running all the SQL statements, you should see the following tables in your Supabase project:

- ✅ `labor_groups`
- ✅ `labor_profiles` 
- ✅ `attendance_records`
- ✅ `payment_records`

## Security Notes

⚠️ **Important**: The RLS policies above use `true` for simplicity. In production, you should:

1. **Implement proper authentication** in your Nuxt app
2. **Update RLS policies** to use actual user/firm context
3. **Test security policies** thoroughly before going live

Example of a more secure policy:
```sql
CREATE POLICY "Users can view their firm's data" ON labor_groups
  FOR SELECT USING (firm_id = auth.jwt() ->> 'firm_id');
```

## Troubleshooting

### Common Issues:

1. **Permission Denied**: Make sure you're using the service role key for table creation
2. **Table Already Exists**: Use `IF NOT EXISTS` in your CREATE statements
3. **Foreign Key Errors**: Create tables in the correct order (groups before profiles)

### Getting Help:

- Check the [Supabase Documentation](https://supabase.com/docs)
- Visit the [Supabase Community](https://github.com/supabase/supabase/discussions)
- Review your project logs in the Supabase dashboard

## Next Steps

Once your database is set up:

1. **Configure your Supabase connection** in the Labor Management System
2. **Test the connection** using the configuration modal
3. **Start creating labor groups and profiles**

Your database is now ready for the Labor Management System! 🎉