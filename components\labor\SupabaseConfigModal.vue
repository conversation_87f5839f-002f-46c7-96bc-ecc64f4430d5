<template>
  <div v-if="isOpen" class="modal-overlay" @click="closeModal">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? 'Update' : 'Configure' }} Supabase Connection
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="saveConfiguration" class="space-y-6">
          <!-- Configuration Name -->
          <div>
            <label for="configName" class="block text-sm font-medium text-gray-700 mb-2">
              Configuration Name
            </label>
            <input
              id="configName"
              v-model="form.configName"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="e.g., Production Labor DB"
            />
          </div>

          <!-- Supabase URL -->
          <div>
            <label for="supabaseUrl" class="block text-sm font-medium text-gray-700 mb-2">
              Supabase URL
            </label>
            <input
              id="supabaseUrl"
              v-model="form.supabaseUrl"
              type="url"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="https://your-project.supabase.co"
            />
            <p class="mt-1 text-sm text-gray-500">
              Your Supabase project URL from the project settings
            </p>
          </div>

          <!-- Supabase Anon Key -->
          <div>
            <label for="supabaseAnonKey" class="block text-sm font-medium text-gray-700 mb-2">
              Supabase Anon Key
            </label>
            <textarea
              id="supabaseAnonKey"
              v-model="form.supabaseAnonKey"
              required
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            ></textarea>
            <p class="mt-1 text-sm text-gray-500">
              Public anon key from your Supabase project API settings
            </p>
          </div>

          <!-- Supabase Service Key -->
          <div>
            <label for="supabaseServiceKey" class="block text-sm font-medium text-gray-700 mb-2">
              Supabase Service Role Key
            </label>
            <textarea
              id="supabaseServiceKey"
              v-model="form.supabaseServiceKey"
              required
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
            ></textarea>
            <p class="mt-1 text-sm text-gray-500">
              Service role key with full database access (keep this secure!)
            </p>
          </div>

          <!-- Connection Test -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex items-center justify-between mb-3">
              <h4 class="text-sm font-medium text-gray-900">Connection Test</h4>
              <button
                type="button"
                @click="testConnection"
                :disabled="testing || !form.supabaseUrl || !form.supabaseAnonKey"
                class="btn btn-secondary text-sm"
              >
                <Icon v-if="testing" name="heroicons:arrow-path" class="w-4 h-4 animate-spin mr-2" />
                <Icon v-else name="heroicons:wifi" class="w-4 h-4 mr-2" />
                {{ testing ? 'Testing...' : 'Test Connection' }}
              </button>
            </div>

            <!-- Test Results -->
            <div v-if="testResult" class="mt-3">
              <div
                v-if="testResult.success"
                class="flex items-center text-green-700 bg-green-50 p-3 rounded-md"
              >
                <Icon name="heroicons:check-circle" class="w-5 h-5 mr-2" />
                <span class="text-sm">{{ testResult.message }}</span>
              </div>
              <div
                v-else
                class="flex items-center text-red-700 bg-red-50 p-3 rounded-md"
              >
                <Icon name="heroicons:x-circle" class="w-5 h-5 mr-2" />
                <span class="text-sm">{{ testResult.message }}</span>
              </div>
            </div>
          </div>

          <!-- Current Configuration Status -->
          <div v-if="currentConfig" class="bg-blue-50 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-blue-900 mb-2">Current Configuration</h4>
            <div class="text-sm text-blue-700">
              <p><strong>Name:</strong> {{ currentConfig.configName }}</p>
              <p><strong>URL:</strong> {{ currentConfig.supabaseUrl }}</p>
              <p><strong>Status:</strong> 
                <span class="inline-flex items-center">
                  <Icon 
                    :name="currentConfig.testConnection?.status === 'success' ? 'heroicons:check-circle' : 'heroicons:x-circle'"
                    :class="currentConfig.testConnection?.status === 'success' ? 'text-green-500' : 'text-red-500'"
                    class="w-4 h-4 mr-1"
                  />
                  {{ currentConfig.testConnection?.status || 'Unknown' }}
                </span>
              </p>
              <p v-if="currentConfig.testConnection?.lastTested">
                <strong>Last Tested:</strong> {{ formatDate(currentConfig.testConnection.lastTested) }}
              </p>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          @click="closeModal"
          class="btn btn-secondary mr-3"
        >
          Cancel
        </button>
        <button
          @click="saveConfiguration"
          :disabled="saving || !isFormValid"
          class="btn btn-primary"
        >
          <Icon v-if="saving" name="heroicons:arrow-path" class="w-4 h-4 animate-spin mr-2" />
          {{ saving ? 'Saving...' : (isEditing ? 'Update' : 'Save') }} Configuration
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  firmId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['close', 'saved'])

// Form data
const form = ref({
  configName: '',
  supabaseUrl: '',
  supabaseAnonKey: '',
  supabaseServiceKey: ''
})

// State
const testing = ref(false)
const saving = ref(false)
const testResult = ref(null)
const currentConfig = ref(null)
const isEditing = ref(false)

// Computed
const isFormValid = computed(() => {
  return form.value.configName && 
         form.value.supabaseUrl && 
         form.value.supabaseAnonKey && 
         form.value.supabaseServiceKey
})

// Methods
const closeModal = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  form.value = {
    configName: '',
    supabaseUrl: '',
    supabaseAnonKey: '',
    supabaseServiceKey: ''
  }
  testResult.value = null
  isEditing.value = false
}

const loadCurrentConfig = async () => {
  try {
    const response = await $fetch('/api/labor/config/supabase', {
      query: { firmId: props.firmId, userId: props.userId }
    })

    if (response.success && response.data) {
      currentConfig.value = response.data
      isEditing.value = true
      
      // Pre-fill form with current config
      form.value = {
        configName: response.data.configName,
        supabaseUrl: response.data.supabaseUrl,
        supabaseAnonKey: response.data.supabaseAnonKey,
        supabaseServiceKey: '' // Don't pre-fill service key for security
      }
    }
  } catch (error) {
    console.error('Error loading current config:', error)
  }
}

const testConnection = async () => {
  if (!form.value.supabaseUrl || !form.value.supabaseAnonKey) {
    return
  }

  testing.value = true
  testResult.value = null

  try {
    const response = await $fetch('/api/labor/config/test', {
      method: 'POST',
      body: {
        supabaseUrl: form.value.supabaseUrl,
        supabaseAnonKey: form.value.supabaseAnonKey
      }
    })

    testResult.value = response
  } catch (error) {
    testResult.value = {
      success: false,
      message: error.data?.message || 'Connection test failed'
    }
  } finally {
    testing.value = false
  }
}

const saveConfiguration = async () => {
  if (!isFormValid.value) return

  saving.value = true

  try {
    const response = await $fetch('/api/labor/config/supabase', {
      method: 'POST',
      body: {
        ...form.value,
        firmId: props.firmId,
        userId: props.userId
      }
    })

    if (response.success) {
      emit('saved', response.data)
      closeModal()
      
      // Show success notification
      // You can integrate with your notification system here
      console.log('Configuration saved successfully')
    }
  } catch (error) {
    console.error('Error saving configuration:', error)
    // Show error notification
    // You can integrate with your notification system here
  } finally {
    saving.value = false
  }
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}

// Watch for modal open to load current config
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    loadCurrentConfig()
  }
})

// Auto-test connection when URL and key are provided
watch([() => form.value.supabaseUrl, () => form.value.supabaseAnonKey], () => {
  testResult.value = null
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-screen overflow-y-auto;
}

.modal-header {
  @apply p-6 border-b border-gray-200 flex justify-between items-center;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply p-6 border-t border-gray-200 flex justify-end;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>