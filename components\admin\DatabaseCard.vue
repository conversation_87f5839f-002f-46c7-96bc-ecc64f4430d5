<template>
  <div
    class="bg-white rounded-lg border border-gray-200 overflow-hidden transition-all duration-300 transform hover:scale-102 hover:shadow-lg shadow-md"
    :class="{ 'animate-pulse': loading }"
  >
    <!-- Card Header with gradient background -->
    <div
      :class="{
        'bg-gradient-to-r from-blue-400 to-purple-400': !isFirestore,
        'bg-gradient-to-r from-orange-400 to-red-400': isFirestore
      }"
      class="p-4"
    >
      <div class="flex items-center justify-between">
        <h3 class="text-xl font-bold text-white drop-shadow-sm">{{ modelName }}</h3>
        <span v-if="isFirestore" class="px-2 py-1 bg-orange-600 text-white text-xs rounded-full shadow-sm">Firestore</span>
      </div>
    </div>

    <!-- Card Content -->
    <div class="p-6 space-y-4 bg-white">
      <div class="flex items-center justify-between">
        <span class="text-gray-700 font-medium">Total Records:</span>
        <div class="flex items-center">
          <span
            class="text-2xl font-bold text-indigo-600"
            v-if="!loading"
          >
            {{ count }}
          </span>
          <div v-else class="h-8 w-16 bg-gray-300 rounded animate-pulse"></div>
        </div>
      </div>

      <div class="h-2 w-full bg-gray-100 rounded-full overflow-hidden">
        <div
          class="h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full transition-all duration-1000"
          :style="{ width: `${Math.min(100, count > 0 ? 100 : 0)}%` }"
        ></div>
      </div>

      <div class="flex justify-end">
        <button
          @click="$emit('view-details')"
          class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-all duration-300 shadow-sm flex items-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
          </svg>
          View Details
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  modelName: {
    type: String,
    required: true
  },
  count: {
    type: Number,
    default: 0
  },
  loading: {
    type: Boolean,
    default: false
  },
  isFirestore: {
    type: Boolean,
    default: false
  }
});

defineEmits(['view-details']);
</script>

<style scoped>
/* Simplified gradient animation - removed to prevent conflicts */
.bg-gradient-to-r {
  /* Static gradient - no animation to prevent blinking */
  background-size: 100% 100%;
}
</style>
