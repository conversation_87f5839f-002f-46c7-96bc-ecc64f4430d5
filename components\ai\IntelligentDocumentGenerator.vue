<template>
  <div class="intelligent-document-generator bg-white rounded-lg shadow-xl max-w-6xl mx-auto">
    <!-- Header -->
    <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-lg">
      <h2 class="text-2xl font-bold flex items-center">
        <span class="mr-3">🤖</span>
        AI Document Intelligence System
      </h2>
      <p class="mt-2 opacity-90">
        Describe any document you need - AI will create it professionally in Excel, Word, and PDF formats
      </p>
    </div>

    <!-- AI Conversation -->
    <div class="ai-conversation p-6">
      <div class="messages mb-6 max-h-96 overflow-y-auto" ref="messagesContainer">
        <div v-for="message in conversation" :key="message.id" 
             :class="['message mb-4 p-4 rounded-lg', message.type === 'user' ? 'bg-blue-50 ml-8' : 'bg-gray-50 mr-8']">
          <div class="flex items-start">
            <div :class="['w-8 h-8 rounded-full flex items-center justify-center mr-3', 
                         message.type === 'user' ? 'bg-blue-500 text-white' : 'bg-gray-500 text-white']">
              {{ message.type === 'user' ? '👤' : '🤖' }}
            </div>
            <div class="flex-1">
              <div class="font-medium text-sm text-gray-600 mb-1">
                {{ message.type === 'user' ? 'You' : 'AI Assistant' }}
              </div>
              <div class="text-gray-800">{{ message.content }}</div>
              <div v-if="message.suggestions" class="mt-3 flex flex-wrap gap-2">
                <button v-for="suggestion in message.suggestions" :key="suggestion"
                        @click="applySuggestion(suggestion)"
                        class="px-3 py-1 bg-blue-100 text-blue-700 rounded-full text-sm hover:bg-blue-200 transition-colors">
                  {{ suggestion }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Input Section -->
      <div class="input-section border-t pt-6">
        <div class="flex flex-col space-y-4">
          <textarea v-model="userInput" 
                    :disabled="isProcessing"
                    placeholder="Describe the document you need... 

Examples:
• Create a service agreement for web development with ABC Corp for ₹5 lakhs
• Make a quotation for 100 laptops at ₹50,000 each for XYZ Company
• Generate an invoice for consulting services provided in March
• Create a project proposal for mobile app development
• Make a salary certificate for John Doe, Software Engineer

AI will understand your request and create a professional document!"
                    class="w-full h-32 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    @keydown.ctrl.enter="sendToAI"></textarea>
          
          <div class="flex justify-between items-center">
            <div class="text-sm text-gray-500">
              Press Ctrl+Enter to send, or click the button
            </div>
            <button @click="sendToAI" 
                    :disabled="isProcessing || !userInput.trim()"
                    class="px-6 py-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 flex items-center">
              <span v-if="!isProcessing" class="mr-2">✨</span>
              <span v-else class="mr-2 animate-spin">⚡</span>
              {{ isProcessing ? 'AI is thinking...' : 'Create Document' }}
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- AI Progress -->
    <div v-if="currentJob" class="ai-progress px-6 pb-6">
      <div class="bg-gray-50 rounded-lg p-4">
        <div class="flex items-center justify-between mb-2">
          <span class="text-sm font-medium text-gray-700">AI Processing Progress</span>
          <span class="text-sm text-gray-500">{{ currentJob.progress }}%</span>
        </div>
        <div class="w-full bg-gray-200 rounded-full h-2 mb-3">
          <div class="bg-gradient-to-r from-blue-600 to-purple-600 h-2 rounded-full transition-all duration-500" 
               :style="{ width: currentJob.progress + '%' }"></div>
        </div>
        <div class="flex items-center text-sm text-gray-600">
          <span class="animate-pulse mr-2">🤖</span>
          {{ currentJob.message }}
        </div>
        <div v-if="currentJob.estimatedRemaining" class="text-xs text-gray-500 mt-1">
          Estimated time remaining: {{ currentJob.estimatedRemaining }} seconds
        </div>
      </div>
    </div>
    
    <!-- Dynamic Download Options -->
    <div v-if="documentReady" class="intelligent-downloads px-6 pb-6">
      <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <div class="flex items-center mb-4">
          <span class="text-2xl mr-3">🎉</span>
          <div>
            <h3 class="text-lg font-bold text-green-800">AI has created your document!</h3>
            <p class="text-green-600">{{ documentData?.title || 'Professional document ready for download' }}</p>
          </div>
        </div>
        
        <div class="grid md:grid-cols-3 gap-4 mb-4">
          <div v-for="format in availableFormats" :key="format.type" 
               class="download-option bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
            <button @click="downloadFormat(format.type)" 
                    class="w-full flex items-center justify-center p-3 bg-gradient-to-r from-blue-600 to-purple-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 mb-3">
              <span class="text-xl mr-2">{{ format.icon }}</span>
              {{ format.label }}
            </button>
            <div class="ai-recommendation text-sm">
              <div class="font-medium text-gray-700 mb-1">AI Recommends:</div>
              <div class="text-gray-600">{{ format.aiReason }}</div>
            </div>
            <div class="text-xs text-gray-500 mt-2">
              Size: {{ formatFileSize(format.size) }}
            </div>
          </div>
        </div>

        <div v-if="documentData?.suggestedImprovements?.length" class="mt-4 p-4 bg-blue-50 rounded-lg">
          <h4 class="font-medium text-blue-800 mb-2">💡 AI Suggestions for Improvement:</h4>
          <ul class="text-sm text-blue-700 space-y-1">
            <li v-for="suggestion in documentData.suggestedImprovements" :key="suggestion" class="flex items-start">
              <span class="mr-2">•</span>
              {{ suggestion }}
            </li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, nextTick } from 'vue';

const conversation = ref([]);
const userInput = ref('');
const isProcessing = ref(false);
const currentJob = ref(null);
const documentReady = ref(false);
const availableFormats = ref([]);
const documentData = ref(null);
const messagesContainer = ref(null);

// Add welcome message
conversation.value.push({
  id: Date.now(),
  type: 'ai',
  content: 'Hello! I\'m your AI Document Assistant. I can create any type of professional document you need. Just describe what you want, and I\'ll use my intelligence to create it in Excel, Word, and PDF formats. What document would you like me to create today?',
  suggestions: [
    'Create a quotation for office supplies',
    'Generate a service agreement',
    'Make an invoice for consulting',
    'Create a project proposal'
  ]
});

const sendToAI = async () => {
  if (!userInput.value.trim() || isProcessing.value) return;
  
  // Add user message
  conversation.value.push({
    id: Date.now(),
    type: 'user',
    content: userInput.value
  });

  const request = userInput.value;
  userInput.value = '';
  isProcessing.value = true;
  documentReady.value = false;
  currentJob.value = null;

  // Scroll to bottom
  await nextTick();
  scrollToBottom();

  try {
    // Send to AI for intelligent processing
    const response = await $fetch('/api/ai/generate-document-intelligent', {
      method: 'POST',
      body: { userRequest: request }
    });
    
    currentJob.value = { 
      id: response.jobId, 
      progress: 0, 
      message: response.message,
      estimatedDuration: 60
    };
    
    // Add AI response
    conversation.value.push({
      id: Date.now(),
      type: 'ai',
      content: response.message
    });

    await nextTick();
    scrollToBottom();
    
    // Start polling for progress
    pollJobProgress();
    
  } catch (error) {
    console.error('AI request failed:', error);
    conversation.value.push({
      id: Date.now(),
      type: 'ai',
      content: `Sorry, I encountered an error: ${error.data?.message || error.message}. Please try again.`
    });
    
    await nextTick();
    scrollToBottom();
  } finally {
    isProcessing.value = false;
  }
};

const pollJobProgress = async () => {
  if (!currentJob.value) return;
  
  try {
    const job = await $fetch(`/api/ai/job-status/${currentJob.value.id}`);
    currentJob.value = { ...currentJob.value, ...job };
    
    if (job.status === 'completed') {
      documentReady.value = true;
      documentData.value = job.documentData;
      
      // Set available formats with AI recommendations
      availableFormats.value = job.availableFormats.map(format => ({
        type: format.format,
        label: `Download ${format.format.charAt(0).toUpperCase() + format.format.slice(1)}`,
        icon: format.format === 'excel' ? '📊' : format.format === 'word' ? '📄' : '📋',
        aiReason: job.documentData?.exportRecommendations?.[format.format] || `Professional ${format.format} format`,
        size: format.size,
        downloadUrl: format.downloadUrl
      }));
      
      conversation.value.push({
        id: Date.now(),
        type: 'ai',
        content: `Perfect! I've created your "${job.documentData?.documentType || 'document'}" with ${job.summary?.sectionsCount || 0} sections. Here are the download options with my recommendations:`
      });

      await nextTick();
      scrollToBottom();
      
    } else if (job.status === 'failed') {
      conversation.value.push({
        id: Date.now(),
        type: 'ai',
        content: `I encountered an issue: ${job.error}. Would you like me to try again with a different approach?`
      });
      
      await nextTick();
      scrollToBottom();
    } else {
      // Continue polling
      setTimeout(pollJobProgress, 2000);
    }
    
  } catch (error) {
    console.error('Polling error:', error);
    conversation.value.push({
      id: Date.now(),
      type: 'ai',
      content: 'I lost connection while processing. Please try your request again.'
    });
    
    await nextTick();
    scrollToBottom();
  }
};

const downloadFormat = async (format) => {
  try {
    const response = await fetch(`/api/ai/download-document/${currentJob.value.id}/${format}`);
    
    if (!response.ok) {
      throw new Error('Download failed');
    }
    
    const blob = await response.blob();
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = availableFormats.value.find(f => f.type === format)?.downloadUrl?.split('/').pop() || `document.${format === 'word' ? 'docx' : format}`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    // Add success message
    conversation.value.push({
      id: Date.now(),
      type: 'ai',
      content: `✅ ${format.charAt(0).toUpperCase() + format.slice(1)} file downloaded successfully! Is there anything else you'd like me to create?`
    });

    await nextTick();
    scrollToBottom();
    
  } catch (error) {
    console.error('Download error:', error);
    conversation.value.push({
      id: Date.now(),
      type: 'ai',
      content: `❌ Sorry, the download failed. Please try again or contact support if the issue persists.`
    });

    await nextTick();
    scrollToBottom();
  }
};

const applySuggestion = (suggestion) => {
  userInput.value = suggestion;
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};
</script>

<style scoped>
.intelligent-document-generator {
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.ai-conversation {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages {
  flex: 1;
  overflow-y: auto;
}

.message {
  animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.download-option {
  transition: all 0.2s ease;
}

.download-option:hover {
  transform: translateY(-2px);
}
</style>
