<template>
  <div class="bg-white shadow rounded-lg p-6 mb-6">
    <h2 class="text-lg font-medium text-gray-900 mb-4">Portfolio Summary</h2>
    
    <div v-if="isLoading" class="flex justify-center items-center h-40">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
    
    <div v-else-if="folioRecords.length === 0" class="text-center py-8">
      <p class="text-gray-500">No holdings found. Add transactions to see your portfolio summary.</p>
    </div>
    
    <div v-else>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-md font-medium text-gray-700 mb-3">Holdings by Value</h3>
          <div class="h-64">
            <canvas ref="pieChartCanvas"></canvas>
          </div>
        </div>
        
        <div>
          <h3 class="text-md font-medium text-gray-700 mb-3">Performance</h3>
          <div class="h-64">
            <canvas ref="barChartCanvas"></canvas>
          </div>
        </div>
      </div>
      
      <div class="mt-6">
        <h3 class="text-md font-medium text-gray-700 mb-3">Portfolio Allocation</h3>
        <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div v-for="(allocation, index) in portfolioAllocation" :key="index" class="bg-gray-50 p-3 rounded-lg">
            <div class="flex items-center">
              <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: getColor(index) }"></div>
              <h4 class="text-sm font-medium text-gray-700 truncate">{{ allocation.symbol }}</h4>
            </div>
            <p class="text-lg font-semibold mt-1">{{ allocation.percentage.toFixed(1) }}%</p>
            <p class="text-xs text-gray-500">₹{{ allocation.value.toLocaleString() }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  folioRecords: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

const pieChartCanvas = ref(null);
const barChartCanvas = ref(null);
const pieChart = ref(null);
const barChart = ref(null);

// Chart colors
const chartColors = [
  '#4F46E5', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', 
  '#EC4899', '#06B6D4', '#84CC16', '#F97316', '#6366F1'
];

// Get color by index (cycling through the array)
function getColor(index) {
  return chartColors[index % chartColors.length];
}

// Calculate portfolio allocation
const portfolioAllocation = computed(() => {
  if (!props.folioRecords || props.folioRecords.length === 0) return [];
  
  const totalValue = props.folioRecords.reduce((sum, record) => sum + (record.cprice * record.qnty), 0);
  
  // Group by symbol and calculate values
  const symbolMap = new Map();
  
  props.folioRecords.forEach(record => {
    const value = record.cprice * record.qnty;
    if (symbolMap.has(record.symbol)) {
      const existing = symbolMap.get(record.symbol);
      symbolMap.set(record.symbol, {
        value: existing.value + value,
        percentage: 0 // Will be calculated below
      });
    } else {
      symbolMap.set(record.symbol, {
        value,
        percentage: 0 // Will be calculated below
      });
    }
  });
  
  // Calculate percentages and convert to array
  const result = Array.from(symbolMap.entries()).map(([symbol, data]) => ({
    symbol,
    value: data.value,
    percentage: (data.value / totalValue) * 100
  }));
  
  // Sort by value (descending)
  return result.sort((a, b) => b.value - a.value);
});

// Initialize charts
function initCharts() {
  if (!pieChartCanvas.value || !barChartCanvas.value || props.folioRecords.length === 0) return;
  
  // Prepare data for pie chart (holdings by value)
  const pieData = {
    labels: portfolioAllocation.value.map(item => item.symbol),
    datasets: [{
      data: portfolioAllocation.value.map(item => item.value),
      backgroundColor: portfolioAllocation.value.map((_, index) => getColor(index)),
      hoverOffset: 4
    }]
  };
  
  // Prepare data for bar chart (top performers)
  const topPerformers = [...props.folioRecords]
    .sort((a, b) => b.pl - a.pl)
    .slice(0, 5);
  
  const barData = {
    labels: topPerformers.map(record => record.symbol),
    datasets: [{
      label: 'Profit/Loss',
      data: topPerformers.map(record => record.pl),
      backgroundColor: topPerformers.map(record => record.pl >= 0 ? '#10B981' : '#EF4444'),
      borderColor: topPerformers.map(record => record.pl >= 0 ? '#059669' : '#DC2626'),
      borderWidth: 1
    }]
  };
  
  // Create or update pie chart
  if (pieChart.value) {
    pieChart.value.data = pieData;
    pieChart.value.update();
  } else {
    pieChart.value = new Chart(pieChartCanvas.value, {
      type: 'pie',
      data: pieData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'right',
            labels: {
              boxWidth: 12,
              font: {
                size: 10
              }
            }
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const value = context.raw;
                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                const percentage = ((value / total) * 100).toFixed(1);
                return `₹${value.toLocaleString()} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
  }
  
  // Create or update bar chart
  if (barChart.value) {
    barChart.value.data = barData;
    barChart.value.update();
  } else {
    barChart.value = new Chart(barChartCanvas.value, {
      type: 'bar',
      data: barData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: function(context) {
                const value = context.raw;
                return `₹${value.toLocaleString()}`;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              callback: function(value) {
                return '₹' + value.toLocaleString();
              }
            }
          }
        }
      }
    });
  }
}

// Watch for changes in folio records
watch(() => props.folioRecords, () => {
  if (!props.isLoading) {
    initCharts();
  }
}, { deep: true });

// Initialize charts when component is mounted
onMounted(() => {
  if (!props.isLoading && props.folioRecords.length > 0) {
    initCharts();
  }
});
</script>
