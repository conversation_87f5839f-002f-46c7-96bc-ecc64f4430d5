import { ref, onUnmounted } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';

// Define types for our API responses
interface MarketData {
  gainers: any[];
  losers: any[];
  indices: any[];
  nifty50: any[];
  timestamp: string;
  error?: string;
}

interface StockDetails {
  quote: any;
  tradeInfo: any;
  companyInfo: any;
  timestamp: string;
  error?: string;
}

interface HistoricalData {
  symbol: string;
  period: string;
  interval: string;
  data: any[];
  timestamp: string;
  error?: string;
}

interface SearchResult {
  results: any[];
  count: number;
  timestamp: string;
  error?: string;
}

export function useStockMarket() {
  const isLoading = ref(true);
  const isSearching = ref(false);
  const error = ref<string | null>(null);
  const marketData = ref<MarketData>({
    gainers: [],
    losers: [],
    indices: [],
    nifty50: [],
    timestamp: ''
  });
  const stockDetails = ref<StockDetails | null>(null);
  const historicalData = ref<HistoricalData | null>(null);
  const searchResults = ref<any[]>([]);

  // Auto-update interval reference
  let autoUpdateInterval: number | null = null;

  // Function to fetch market overview data
  async function fetchMarketData(silentUpdate = false) {
    console.log('useStockMarket: Fetching market data...');
    if (!silentUpdate) {
      isLoading.value = true;
    }
    error.value = null;

    try {
      // Use the API with auth composable
      const api = useApiWithAuth();
      const response = await api.get<MarketData>('/api/stock-market');

      console.log('useStockMarket: Market data response:', {
        hasData: !!response,
        hasNifty50: response && !!response.nifty50,
        nifty50Length: response && response.nifty50 ? response.nifty50.length : 0
      });

      marketData.value = {
        ...response,
        timestamp: new Date().toISOString() // Add timestamp for last updated display
      };

      console.log('useStockMarket: Market data stored in reactive ref');
      isLoading.value = false;
      return marketData.value;
    } catch (err: any) {
      console.error('useStockMarket: Error fetching market data:', err);
      error.value = err.message || 'Failed to fetch stock market data';
      isLoading.value = false;
      throw err;
    }
  }

  // Function to fetch details for a specific stock
  async function fetchStockDetails(symbol: string) {
    isLoading.value = true;
    error.value = null;
    stockDetails.value = null;

    try {
      // Use the API with auth composable
      const api = useApiWithAuth();
      const response = await api.get<StockDetails>(`/api/stock-market?symbol=${encodeURIComponent(symbol)}`);

      stockDetails.value = response;
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || `Failed to fetch details for ${symbol}`;
      isLoading.value = false;
      throw err;
    }
  }

  // Function to fetch historical data for a stock
  async function fetchHistoricalData(symbol: string, period: string = '1y', interval: string = '1d') {
    isLoading.value = true;
    error.value = null;
    historicalData.value = null;

    try {
      // Use the API with auth composable
      const api = useApiWithAuth();
      const response = await api.get<HistoricalData>(
        `/api/stock-market/history?symbol=${encodeURIComponent(symbol)}&period=${period}&interval=${interval}`
      );

      historicalData.value = response;
      isLoading.value = false;
      return response;
    } catch (err: any) {
      error.value = err.message || `Failed to fetch historical data for ${symbol}`;
      isLoading.value = false;
      throw err;
    }
  }

  // Function to search for stocks
  async function searchStocks(query: string) {
    if (!query || query.length < 2) return [];

    isSearching.value = true;
    error.value = null;
    searchResults.value = [];

    try {
      // Use the API with auth composable
      const api = useApiWithAuth();
      const response = await api.get<SearchResult>(`/api/stock-market/search?q=${encodeURIComponent(query)}`);

      // Check if there's an error in the response
      if (response.error) {
        console.warn('Search API returned an error:', response.error);
        error.value = `Search error: ${response.error}`;
        isSearching.value = false;
        return [];
      }

      searchResults.value = response.results || [];
      isSearching.value = false;
      return response.results || [];
    } catch (err: any) {
      console.error('Error in search stocks function:', err);
      error.value = err.message || 'Failed to search for stocks';
      isSearching.value = false;
      return []; // Return empty array instead of throwing
    }
  }

  // Function to start auto-updating
  function startAutoUpdate(intervalMinutes: number = 5) {
    // Clear any existing interval
    if (autoUpdateInterval) {
      clearInterval(autoUpdateInterval);
    }

    // Convert minutes to milliseconds
    const intervalMs = intervalMinutes * 60 * 1000;

    // Set up the interval
    autoUpdateInterval = window.setInterval(async () => {
      try {
        // Pass true for silentUpdate to avoid showing loading indicator
        await fetchMarketData(true);
        console.log(`Auto-updated market data at ${new Date().toLocaleTimeString()}`);
      } catch (error) {
        console.error('Auto-update failed:', error);
      }
    }, intervalMs);

    console.log(`Auto-update started. Will refresh every ${intervalMinutes} minutes.`);

    // Return a function to stop the auto-update
    return stopAutoUpdate;
  }

  // Function to stop auto-updating
  function stopAutoUpdate() {
    if (autoUpdateInterval) {
      clearInterval(autoUpdateInterval);
      autoUpdateInterval = null;
      console.log('Auto-update stopped.');
    }
  }

  // Clean up interval when component is unmounted
  onUnmounted(() => {
    stopAutoUpdate();
  });

  // Function to format percentage change with color
  function formatPercentChange(change: number | string) {
    const numChange = typeof change === 'string' ? parseFloat(change) : change;
    const color = numChange >= 0 ? 'text-green-600' : 'text-red-600';
    const prefix = numChange >= 0 ? '+' : '';
    return {
      value: `${prefix}${numChange.toFixed(2)}%`,
      color
    };
  }

  // Function to format price with commas for thousands
  function formatPrice(price: number | string) {
    if (!price && price !== 0) return 'N/A';
    const numPrice = typeof price === 'string' ? parseFloat(price) : price;
    return numPrice.toLocaleString('en-IN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2
    });
  }

  // Function to format volume numbers (e.g., 1.2M, 450K)
  function formatVolume(volume: any) {
    if (!volume && volume !== 0) return 'N/A';

    const num = typeof volume === 'string' ? parseFloat(volume) : volume;
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toLocaleString('en-IN');
  }

  // Function to format large numbers (e.g., 1.2B, 450M)
  function formatLargeNumber(value: any) {
    if (!value && value !== 0) return 'N/A';

    const num = typeof value === 'string' ? parseFloat(value) : value;
    if (num >= 1000000000) {
      return (num / 1000000000).toFixed(2) + 'B';
    } else if (num >= 1000000) {
      return (num / 1000000).toFixed(2) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(2) + 'K';
    }
    return num.toLocaleString('en-IN');
  }

  return {
    isLoading,
    isSearching,
    error,
    marketData,
    stockDetails,
    historicalData,
    searchResults,
    fetchMarketData,
    fetchStockDetails,
    fetchHistoricalData,
    searchStocks,
    startAutoUpdate,
    stopAutoUpdate,
    formatPercentChange,
    formatPrice,
    formatVolume,
    formatLargeNumber
  };
}
