<template>
  <div class="bg-white rounded-lg border border-gray-200 p-4 shadow-sm">
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-800">API Logs JSON Backup</h3>
      <div class="flex items-center space-x-2">
        <div class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">
          JSON Format
        </div>
        <div class="w-3 h-3 rounded-full bg-green-500"></div>
      </div>
    </div>

    <!-- Manual Backup Alert -->
    <div v-if="currentCount >= 1000" class="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
      <div class="flex items-center">
        <svg class="h-5 w-5 text-orange-400 mr-2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
        </svg>
        <div>
          <h4 class="text-orange-800 font-medium">JSON Backup Required!</h4>
          <p class="text-orange-700 text-sm">{{ currentCount.toLocaleString() }} API logs need backup. Click button to download JSON and cleanup.</p>
        </div>
        <button
          @click="triggerManualBackup"
          :disabled="loading.manual"
          class="ml-auto px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors flex items-center disabled:opacity-50"
        >
          <svg v-if="loading.manual" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Download JSON Backup
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
      <!-- Current Count -->
      <div
        :class="{
          'bg-red-50': currentCount >= 1000,
          'bg-blue-50': currentCount < 1000
        }"
        class="p-3 rounded-lg"
      >
        <div
          :class="{
            'text-red-600': currentCount >= 1000,
            'text-blue-600': currentCount < 1000
          }"
          class="text-sm font-medium"
        >
          Current API Logs
        </div>
        <div
          :class="{
            'text-red-800': currentCount >= 1000,
            'text-blue-800': currentCount < 1000
          }"
          class="text-2xl font-bold"
        >
          <span v-if="loading.count" class="inline-block w-12 h-6 bg-gray-200 rounded animate-pulse"></span>
          <span v-else>{{ currentCount.toLocaleString() }}</span>
        </div>
        <div
          :class="{
            'text-red-600': currentCount >= 1000,
            'text-blue-600': currentCount < 1000
          }"
          class="text-xs mt-1"
        >
          {{ currentCount >= 1000 ? '⚠️ Backup needed!' : '✅ Within limits' }}
        </div>
      </div>

      <!-- Manual Backup Status -->
      <div class="bg-purple-50 p-3 rounded-lg">
        <div class="text-sm text-purple-600 font-medium">Backup Mode</div>
        <div class="text-2xl font-bold text-purple-800">
          Manual Only
        </div>
        <div class="text-xs text-purple-600 mt-1">Click to backup</div>
      </div>

      <!-- Last Backup -->
      <div class="bg-green-50 p-3 rounded-lg">
        <div class="text-sm text-green-600 font-medium">Last Backup</div>
        <div class="text-sm font-bold text-green-800">
          {{ lastBackupTime || 'Never' }}
        </div>
        <div class="text-xs text-green-600 mt-1">Excel format only</div>
      </div>
    </div>

    <!-- Configuration Info -->
    <div class="bg-gray-50 p-3 rounded-lg">
      <h4 class="font-medium text-gray-700 mb-2">JSON Backup Configuration</h4>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
        <div>
          <span class="text-gray-600">Recommended Threshold:</span>
          <span class="ml-2 font-medium">1,000 records</span>
        </div>
        <div>
          <span class="text-gray-600">Trigger Mode:</span>
          <span class="ml-2 font-medium">Manual Only</span>
        </div>
        <div>
          <span class="text-gray-600">Backup Format:</span>
          <span class="ml-2 font-medium">JSON (.json)</span>
        </div>
        <div>
          <span class="text-gray-600">Access Required:</span>
          <span class="ml-2 font-medium">Admin login</span>
        </div>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="mt-4 space-y-3">
      <!-- Test Console Button -->
      <div class="flex justify-center">
        <button
          @click="testConsole"
          class="px-3 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 transition-colors flex items-center text-sm"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Test Console
        </button>
      </div>

      <!-- Main Action Buttons -->
      <div class="flex justify-between">
        <!-- Cleanup Flood Button -->
        <button
          @click="cleanupFloodLogs"
          :disabled="loading.cleanup"
          class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600 transition-colors flex items-center disabled:opacity-50"
        >
        <svg v-if="loading.cleanup" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Cleanup Flood
      </button>

      <!-- Manual Backup Button -->
      <button
        @click="triggerManualBackup"
        :disabled="loading.manual || currentCount < 1000"
        class="px-4 py-2 bg-orange-500 text-white rounded-md hover:bg-orange-600 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <svg v-if="loading.manual" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 714 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        Download JSON Backup
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import useToast from '~/composables/ui/useToast';
import { useRealTimeStatus } from '~/composables/utils/useRealTimeStatus';

const { success, error } = useToast();

// Reactive data
const currentCount = ref(0);
const lastBackupTime = ref('');
const loading = ref({
  count: false,
  manual: false,
  cleanup: false
});

// Computed properties
const isActive = computed(() => {
  // Manual backup is available when admin is logged in
  return true; // Since this component only shows when admin is logged in
});

// Fetch current API logs count (SIMPLE - NO REAL-TIME STATUS TO PREVENT FLOODING)
async function fetchCurrentCount() {
  loading.value.count = true;

  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/firestore/api-logs-status');
    currentCount.value = response.count || 0;
  } catch (err) {
    console.error('Error fetching API logs count:', err);
    currentCount.value = 0;
  } finally {
    loading.value.count = false;
  }
}

// Test console panel function
function testConsole() {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `test_${Date.now()}`;

  // Start test operation
  startOperation(operationId, 'Console Test', 'Testing console panel visibility...');

  setTimeout(() => {
    updateProgress(operationId, 1, 'Console panel is working!', 50);
  }, 1000);

  setTimeout(() => {
    updateProgress(operationId, 2, 'Test completed successfully', 100);
    completeOperation(operationId, true, 'Console test finished');
  }, 2000);
}

// Trigger manual JSON backup with REAL-TIME STATUS
async function triggerManualBackup() {
  loading.value.manual = true;

  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `backup_${Date.now()}`;

  // Start real-time monitoring
  startOperation(operationId, 'JSON Backup', 'Preparing backup process...');

  try {
    const api = useApiWithAuth();

    updateProgress(operationId, 1, 'Initializing backup request...', 10);

    // Create a timestamp for the filename
    const timestamp = new Date().toISOString().replace(/:/g, '-').replace(/\./g, '-');
    const filename = `api_logs_backup_${timestamp}.json`;

    updateProgress(operationId, 2, 'Sending backup request to server...', 20);

    // Make the request with responseType: 'blob' to get binary data
    const response = await api.post('/api/firestore/auto-download-backup', {}, {
      responseType: 'blob'
    });

    updateProgress(operationId, 3, 'Processing server response...', 70);

    // Create a download link for the blob
    const url = window.URL.createObjectURL(response);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);

    updateProgress(operationId, 4, 'Triggering file download...', 90);

    // Trigger the download
    a.click();

    // Clean up
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    updateProgress(operationId, 5, 'Refreshing data...', 95);

    success(`JSON backup downloaded successfully! API logs collection cleaned up.`);
    lastBackupTime.value = new Date().toLocaleString();

    // Refresh count after backup
    await fetchCurrentCount();

    completeOperation(operationId, true, 'JSON backup completed successfully');

  } catch (err) {
    console.error('JSON backup error:', err);
    error('Failed to create JSON backup');
    completeOperation(operationId, false, `Backup failed: ${err.message}`);
  } finally {
    loading.value.manual = false;
  }
}

// Cleanup flood logs
async function cleanupFloodLogs() {
  loading.value.cleanup = true;

  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `cleanup_${Date.now()}`;

  // Start real-time monitoring
  startOperation(operationId, 'Cleanup Flood Logs', 'Starting cleanup process...');

  try {
    const api = useApiWithAuth();

    updateProgress(operationId, 1, 'Identifying flood logs...', 20);

    const response = await api.post('/api/admin/cleanup-api-logs');

    updateProgress(operationId, 2, 'Deleting flood logs...', 80);

    if (response.success) {
      success(`Cleanup completed! Deleted ${response.deletedCount} flood logs. ${response.remainingCount} legitimate logs remain.`);

      updateProgress(operationId, 3, 'Refreshing count...', 95);

      // Refresh count after cleanup
      await fetchCurrentCount();

      completeOperation(operationId, true, `Deleted ${response.deletedCount} flood logs`);
    } else {
      error(response.message || 'Cleanup failed');
      completeOperation(operationId, false, 'Cleanup failed');
    }

  } catch (err) {
    console.error('Cleanup error:', err);
    error('Failed to cleanup flood logs');
    completeOperation(operationId, false, `Cleanup failed: ${err.message}`);
  } finally {
    loading.value.cleanup = false;
  }
}

// Simple refresh control
let refreshInterval = null;
let isBackupProcessing = ref(false);

// Lifecycle hooks
onMounted(() => {
  fetchCurrentCount(); // Initial fetch

  // 🛑 REDUCED FREQUENCY: Refresh every 30 seconds to prevent flooding
  refreshInterval = setInterval(() => {
    fetchCurrentCount();
  }, 30000); // Changed from 10s to 30s
});

onBeforeUnmount(() => {
  if (refreshInterval) {
    clearInterval(refreshInterval);
  }
});
</script>
