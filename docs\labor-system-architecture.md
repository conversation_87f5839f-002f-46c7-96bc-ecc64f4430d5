# Labor Management System - Architecture Diagram

## System Architecture Overview

```mermaid
graph TB
    subgraph "Frontend Layer"
        UI[Nuxt.js Application]
        subgraph "Labor Components"
            LP[Labor Profiles]
            AS[Attendance System]
            PS[Payment System]
            DB[Dashboard]
            CM[Configuration Modal]
        end
    end

    subgraph "API Layer"
        subgraph "Labor APIs"
            LPA[Labor Profile API]
            ATA[Attendance API]
            PMA[Payment API]
            CFA[Config API]
            SYN[Firestore Sync API]
        end
    end

    subgraph "Database Layer"
        subgraph "Supabase (Primary)"
            LPT[labor_profiles]
            LGT[labor_groups]
            ATT[attendance_records]
            PMT[payment_records]
            SCT[supabase_config]
        end
        
        subgraph "MongoDB (Config)"
            SCC[supabase_configurations]
        end
        
        subgraph "Firestore (Ledger)"
            LED[ledger_entries]
            BNK[bank_records]
        end
    end

    UI --> LPA
    UI --> ATA
    UI --> PMA
    UI --> CFA
    
    LPA --> LPT
    LPA --> LGT
    ATA --> ATT
    ATA --> LPT
    PMA --> PMT
    PMA --> LGT
    CFA --> SCC
    CFA --> SCT
    
    SYN --> PMT
    SYN --> LED
    SYN --> BNK
```

## Data Flow Architecture

```mermaid
sequenceDiagram
    participant U as User
    participant UI as Frontend
    participant API as API Layer
    participant SB as Supabase
    participant MG as MongoDB
    participant FS as Firestore

    Note over U,FS: Labor Profile Creation
    U->>UI: Create Labor Profile
    UI->>API: POST /api/labor/profiles
    API->>SB: Insert into labor_profiles
    SB-->>API: Profile Created
    API-->>UI: Success Response
    UI-->>U: Profile Added

    Note over U,FS: Attendance Recording
    U->>UI: Record Attendance
    UI->>API: POST /api/labor/attendance
    API->>SB: Check conflicts
    SB-->>API: No conflicts
    API->>SB: Insert attendance_records
    SB-->>API: Attendance Saved
    API-->>UI: Success Response
    UI-->>U: Attendance Recorded

    Note over U,FS: Payment Processing
    U->>UI: Process Payment
    UI->>API: POST /api/labor/payments
    API->>SB: Insert payment_records
    SB-->>API: Payment Saved
    API->>FS: Sync to Ledger
    FS-->>API: Ledger Updated
    API-->>UI: Payment Complete
    UI-->>U: Payment Processed

    Note over U,FS: Configuration Management
    U->>UI: Save Supabase Config
    UI->>API: POST /api/labor/config
    API->>MG: Store encrypted config
    MG-->>API: Config Saved
    API->>SB: Test connection
    SB-->>API: Connection OK
    API-->>UI: Config Active
    UI-->>U: Configuration Saved
```

## Component Hierarchy

```mermaid
graph TD
    subgraph "Pages"
        LP[/labor/index.vue]
        LD[/labor/dashboard.vue]
        LA[/labor/attendance.vue]
        LPY[/labor/payments.vue]
    end

    subgraph "Layout Components"
        LN[LaborNavigation.vue]
        LS[LaborSidebar.vue]
    end

    subgraph "Feature Components"
        subgraph "Profile Management"
            LPM[LaborProfileModal.vue]
            LPT[LaborProfileTable.vue]
            LPF[LaborProfileForm.vue]
        end
        
        subgraph "Attendance System"
            ASM[AttendanceSystem.vue]
            ATB[AttendanceTable.vue]
            ATF[AttendanceFilters.vue]
            ATS[AttendanceSummary.vue]
        end
        
        subgraph "Payment System"
            PSM[PaymentSystem.vue]
            PMM[PaymentModal.vue]
            UAM[UnpaidAmounts.vue]
            PHT[PaymentHistory.vue]
        end
        
        subgraph "Dashboard"
            DST[DashboardStats.vue]
            DCH[DashboardCharts.vue]
            LMM[LaborMovementModal.vue]
            GMM[GroupManagementModal.vue]
        end
        
        subgraph "Configuration"
            SCM[SupabaseConfigModal.vue]
            CTT[ConfigurationTest.vue]
        end
    end

    subgraph "Shared Components"
        BTN[BaseButton.vue]
        INP[BaseInput.vue]
        MDL[BaseModal.vue]
        TBL[BaseTable.vue]
        DTP[DatePicker.vue]
        DRP[Dropdown.vue]
    end

    LP --> LPM
    LP --> LPT
    LD --> DST
    LD --> DCH
    LA --> ASM
    LA --> ATB
    LPY --> PSM
    LPY --> PMM

    LPM --> LPF
    ASM --> ATF
    ASM --> ATS
    PSM --> UAM
    PSM --> PHT

    LPF --> INP
    LPF --> DRP
    ATB --> BTN
    PMM --> DTP
    DST --> DCH
```

## Database Relationships

```mermaid
erDiagram
    labor_groups ||--o{ labor_profiles : contains
    labor_profiles ||--o{ attendance_records : has
    labor_groups ||--o{ payment_records : receives
    
    labor_groups {
        uuid id PK
        string name
        string description
        string color
        string firm_id
        string user_id
        boolean is_active
        timestamp created_at
        timestamp updated_at
    }
    
    labor_profiles {
        uuid id PK
        string name UK
        decimal daily_rate
        uuid group_id FK
        string phone
        text address
        string aadhar
        jsonb bank_details
        boolean is_active
        string firm_id
        string user_id
        timestamp created_at
        timestamp updated_at
    }
    
    attendance_records {
        uuid id PK
        uuid labor_id FK
        date attendance_date
        decimal days_worked
        decimal daily_rate
        decimal amount
        date period_start
        date period_end
        decimal site_expenses
        decimal site_materials
        decimal medical_expenses
        decimal other_expenses
        text notes
        string firm_id
        string user_id
        timestamp created_at
        timestamp updated_at
    }
    
    payment_records {
        uuid id PK
        uuid group_id FK
        date payment_date
        decimal amount
        string project
        string payment_method
        jsonb bank_details
        decimal advance_amount
        text description
        string firestore_sync_status
        string firestore_doc_id
        string firm_id
        string user_id
        timestamp created_at
        timestamp updated_at
    }
```

## API Architecture

```mermaid
graph LR
    subgraph "Client Requests"
        CR[Client Request]
    end
    
    subgraph "Middleware Stack"
        AUTH[Auth Middleware]
        CSRF[CSRF Protection]
        VALID[Validation]
        RATE[Rate Limiting]
    end
    
    subgraph "API Controllers"
        LPC[Labor Profile Controller]
        ATC[Attendance Controller]
        PMC[Payment Controller]
        CFC[Config Controller]
    end
    
    subgraph "Services"
        LPS[Labor Profile Service]
        ATS[Attendance Service]
        PMS[Payment Service]
        SYS[Sync Service]
    end
    
    subgraph "Data Access"
        SBD[Supabase DAO]
        MGD[MongoDB DAO]
        FSD[Firestore DAO]
    end

    CR --> AUTH
    AUTH --> CSRF
    CSRF --> VALID
    VALID --> RATE
    
    RATE --> LPC
    RATE --> ATC
    RATE --> PMC
    RATE --> CFC
    
    LPC --> LPS
    ATC --> ATS
    PMC --> PMS
    PMC --> SYS
    
    LPS --> SBD
    ATS --> SBD
    PMS --> SBD
    SYS --> FSD
    CFC --> MGD
```

## Security Architecture

```mermaid
graph TB
    subgraph "Security Layers"
        subgraph "Frontend Security"
            CSP[Content Security Policy]
            XSS[XSS Protection]
            HTTPS[HTTPS Enforcement]
        end
        
        subgraph "API Security"
            JWT[JWT Authentication]
            RBAC[Role-Based Access]
            CORS[CORS Configuration]
            RATE_LIMIT[Rate Limiting]
        end
        
        subgraph "Data Security"
            ENCRYPT[Data Encryption]
            HASH[Password Hashing]
            SANITIZE[Input Sanitization]
            PARAM[Parameterized Queries]
        end
        
        subgraph "Database Security"
            RLS[Row Level Security]
            BACKUP[Encrypted Backups]
            AUDIT[Audit Logging]
            ACCESS[Access Controls]
        end
    end

    CSP --> JWT
    XSS --> RBAC
    HTTPS --> CORS
    
    JWT --> ENCRYPT
    RBAC --> HASH
    CORS --> SANITIZE
    RATE_LIMIT --> PARAM
    
    ENCRYPT --> RLS
    HASH --> BACKUP
    SANITIZE --> AUDIT
    PARAM --> ACCESS
```

## Deployment Architecture

```mermaid
graph TB
    subgraph "Production Environment"
        subgraph "Frontend"
            CDN[CDN Distribution]
            STATIC[Static Assets]
            SSR[Server-Side Rendering]
        end
        
        subgraph "Backend Services"
            API_SERVER[API Server]
            WORKER[Background Workers]
            SCHEDULER[Task Scheduler]
        end
        
        subgraph "Databases"
            SB_PROD[Supabase Production]
            MG_PROD[MongoDB Production]
            FS_PROD[Firestore Production]
            REDIS[Redis Cache]
        end
        
        subgraph "Monitoring"
            LOGS[Centralized Logging]
            METRICS[Performance Metrics]
            ALERTS[Alert System]
            HEALTH[Health Checks]
        end
    end

    CDN --> API_SERVER
    SSR --> API_SERVER
    API_SERVER --> SB_PROD
    API_SERVER --> MG_PROD
    WORKER --> FS_PROD
    API_SERVER --> REDIS
    
    API_SERVER --> LOGS
    WORKER --> METRICS
    SCHEDULER --> ALERTS
    SB_PROD --> HEALTH
```

This architecture provides a comprehensive view of how all components interact within the labor management system, ensuring scalability, security, and maintainability.