<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-4">
    <div class="flex justify-between items-center mb-4">
      <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200">Mutual Fund Investments</h3>
      <div class="flex space-x-2">
        <button
          @click="$emit('refresh')"
          class="px-3 py-1 text-sm bg-gray-100 hover:bg-gray-200 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md flex items-center"
          :disabled="isRefreshing"
        >
          <span v-if="isRefreshing" class="animate-spin h-4 w-4 mr-1 border-2 border-gray-500 border-t-transparent rounded-full"></span>
          <span v-else class="mr-1">↻</span>
          Refresh
        </button>
        <button
          @click="$emit('add')"
          class="px-3 py-1 text-sm bg-indigo-600 hover:bg-indigo-700 text-white rounded-md flex items-center"
        >
          <span class="mr-1">+</span>
          <span class="whitespace-nowrap">Add Fund</span>
        </button>
      </div>
    </div>

    <div v-if="isLoading" class="flex justify-center items-center py-6">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
    </div>

    <div v-else-if="error" class="text-red-500 text-center py-4">
      {{ error }}
    </div>

    <div v-else-if="!mutualFunds || mutualFunds.length === 0" class="text-gray-500 dark:text-gray-400 text-center py-4">
      No mutual fund investments found. Click "Add Fund" to add your first mutual fund.
    </div>

    <div v-else>
      <!-- Filters -->
      <div class="mb-4 flex flex-wrap gap-2">
        <div class="relative">
          <input
            type="text"
            v-model="searchQuery"
            placeholder="Search funds..."
            class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200"
          />
          <button
            v-if="searchQuery"
            @click="searchQuery = ''"
            class="absolute right-2 top-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            ✕
          </button>
        </div>

        <select
          v-model="categoryFilter"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200"
        >
          <option value="">All Categories</option>
          <option v-for="category in uniqueCategories" :key="category" :value="category">
            {{ category }}
          </option>
        </select>

        <select
          v-model="fundHouseFilter"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200"
        >
          <option value="">All Fund Houses</option>
          <option v-for="fundHouse in uniqueFundHouses" :key="fundHouse" :value="fundHouse">
            {{ fundHouse }}
          </option>
        </select>

        <select
          v-model="sortBy"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200"
        >
          <option value="schemeName">Sort by Name</option>
          <option value="currentValue">Sort by Current Value</option>
          <option value="investmentAmount">Sort by Investment</option>
          <option value="profitLoss">Sort by Profit/Loss</option>
          <option value="profitLossPercentage">Sort by P/L %</option>
          <option value="purchaseDate">Sort by Purchase Date</option>
        </select>

        <button
          @click="sortDirection = sortDirection === 'asc' ? 'desc' : 'asc'"
          class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 dark:bg-gray-700 dark:text-gray-200"
        >
          {{ sortDirection === 'asc' ? '↑' : '↓' }}
        </button>
      </div>

      <!-- Table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead class="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Scheme Name</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Fund House</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Units</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">NAV</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Investment</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Current Value</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">P/L</th>
              <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">SIP</th>
              <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            <tr v-for="fund in filteredAndSortedFunds" :key="fund._id" class="hover:bg-gray-50 dark:hover:bg-gray-700">
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">{{ fund.schemeName }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ fund.category }}</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap">
                <div class="text-sm text-gray-900 dark:text-gray-100">{{ fund.fundHouse }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ formatDate(fund.purchaseDate) }}</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900 dark:text-gray-100">
                {{ formatNumber(fund.units, 3) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-right">
                <div class="text-sm text-gray-900 dark:text-gray-100">{{ formatNumber(fund.currentNAV, 3) }}</div>
                <div class="text-xs text-gray-500 dark:text-gray-400">{{ formatNumber(fund.purchaseNAV, 3) }}</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900 dark:text-gray-100">
                {{ formatNumber(fund.investmentAmount) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-right text-sm text-gray-900 dark:text-gray-100">
                {{ formatNumber(fund.currentValue) }}
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-right">
                <div :class="getProfitLossClass(fund.profitLoss)" class="text-sm">
                  {{ formatNumber(fund.profitLoss) }}
                </div>
                <div :class="getProfitLossClass(fund.profitLossPercentage)" class="text-xs">
                  {{ formatPercentage(fund.profitLossPercentage) }}%
                </div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-center">
                <div v-if="fund.sipFlag" class="text-xs bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 px-2 py-1 rounded-full inline-block">
                  {{ formatNumber(fund.sipAmount) }} / {{ fund.sipFrequency }}
                </div>
                <div v-else class="text-xs text-gray-400">-</div>
              </td>
              <td class="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                <button
                  @click="$emit('edit', fund)"
                  class="text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-200 mr-2"
                >
                  Edit
                </button>
                <button
                  @click="$emit('delete', fund)"
                  class="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-200"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  mutualFunds: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  isRefreshing: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
});

const emit = defineEmits(['refresh', 'add', 'edit', 'delete']);

// Filters and sorting
const searchQuery = ref('');
const categoryFilter = ref('');
const fundHouseFilter = ref('');
const sortBy = ref('schemeName');
const sortDirection = ref('asc');

// Get unique categories
const uniqueCategories = computed(() => {
  const categories = new Set(props.mutualFunds.map(fund => fund.category));
  return [...categories].sort();
});

// Get unique fund houses
const uniqueFundHouses = computed(() => {
  const fundHouses = new Set(props.mutualFunds.map(fund => fund.fundHouse));
  return [...fundHouses].sort();
});

// Filter and sort mutual funds
const filteredAndSortedFunds = computed(() => {
  // First, filter the funds
  let filtered = props.mutualFunds;

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    filtered = filtered.filter(fund =>
      fund.schemeName.toLowerCase().includes(query) ||
      fund.fundHouse.toLowerCase().includes(query) ||
      fund.category.toLowerCase().includes(query)
    );
  }

  if (categoryFilter.value) {
    filtered = filtered.filter(fund => fund.category === categoryFilter.value);
  }

  if (fundHouseFilter.value) {
    filtered = filtered.filter(fund => fund.fundHouse === fundHouseFilter.value);
  }

  // Then, sort the filtered funds
  return [...filtered].sort((a, b) => {
    let aValue = a[sortBy.value];
    let bValue = b[sortBy.value];

    // Special handling for dates
    if (sortBy.value === 'purchaseDate') {
      aValue = new Date(aValue).getTime();
      bValue = new Date(bValue).getTime();
    }

    // Handle null/undefined values
    if (aValue === null || aValue === undefined) return sortDirection.value === 'asc' ? -1 : 1;
    if (bValue === null || bValue === undefined) return sortDirection.value === 'asc' ? 1 : -1;

    // Compare values
    if (aValue < bValue) return sortDirection.value === 'asc' ? -1 : 1;
    if (aValue > bValue) return sortDirection.value === 'asc' ? 1 : -1;
    return 0;
  });
});

// Format number with commas for thousands
const formatNumber = (number, decimals = 2) => {
  if (number === null || number === undefined) return '-';
  return number.toLocaleString('en-IN', {
    maximumFractionDigits: decimals,
    minimumFractionDigits: 0
  });
};

// Format percentage
const formatPercentage = (percentage) => {
  if (percentage === null || percentage === undefined) return '-';
  return percentage.toFixed(2);
};

// Format date
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' });
};

// Get class for profit/loss text color
const getProfitLossClass = (value) => {
  if (value > 0) return 'text-green-600 dark:text-green-400';
  if (value < 0) return 'text-red-600 dark:text-red-400';
  return 'text-gray-800 dark:text-gray-200';
};
</script>
