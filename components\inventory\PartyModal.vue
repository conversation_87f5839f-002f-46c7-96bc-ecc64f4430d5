<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full lg:max-w-4xl xl:max-w-5xl">
        <!-- Gradient Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-3 rounded-t-lg shadow-md">
          <h3 class="text-lg leading-6 font-medium text-white">{{ partyEdit ? 'Edit Party' : 'Create New Party' }}</h3>
        </div>

        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">

              <form @submit.prevent="submitForm">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 lg:gap-6">
                  <!-- Supplier Name -->
                  <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Supplier Name*</label>
                    <input ref="firstInput" v-model="form.supply" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @keydown.esc="closeModal" />
                  </div>

                  <!-- Address -->
                  <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
                    <textarea v-model="form.addr" rows="3"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 resize-none"
                      placeholder="Enter complete address..."></textarea>
                  </div>

                  <!-- GSTIN -->
                  <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">GSTIN*</label>
                    <div class="flex gap-2">
                      <input v-model="form.gstin" type="text"
                        class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="UNREGISTERED"
                        maxlength="15"
                        @input="form.gstin = $event.target.value.toUpperCase()" />
                      <button type="button" @click="fetchPartyByGST"
                        class="px-3 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 text-sm"
                        title="Fetch Party Details by GST" :disabled="gstFetchLoading">
                        <span v-if="gstFetchLoading" class="animate-spin">⏳</span>
                        <span v-else>🔍</span>
                      </button>
                      <button type="button" @click="showApiSettings = true"
                        class="px-3 py-2 bg-green-500 hover:bg-green-600 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 text-sm"
                        title="API Settings">
                        ⚙️
                      </button>
                    </div>
                  </div>

                  <!-- State -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                    <select v-model="form.state" @change="updateStateCode(form.state)"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      <option value="">Select State</option>
                      <option v-for="state in indianStates" :key="state.code" :value="state.name">
                        {{ state.name }}
                      </option>
                    </select>
                  </div>

                  <!-- State Code -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">State Code</label>
                    <input v-model="form.state_code" type="number"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-100"
                      readonly />
                  </div>

                  <!-- PIN -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">PIN</label>
                    <input v-model.number="form.pin" type="number"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter PIN code" />
                  </div>

                  <!-- PAN -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">PAN</label>
                    <input v-model="form.pan" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter PAN number"
                      maxlength="10"
                      @input="form.pan = $event.target.value.toUpperCase()" />
                  </div>

                  <!-- Contact -->
                  <div class="lg:col-span-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">Contact</label>
                    <input v-model="form.contact" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter contact number" />
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="mt-6 sm:mt-8 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                  <button type="submit"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm">
                    {{ partyEdit ? 'Update Party' : 'Add Party' }}
                  </button>
                  <button type="button" @click="closeModal"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Gradient Footer -->
        <div class="bg-gradient-to-r from-purple-600 to-blue-500 px-4 py-3 rounded-b-lg shadow-md"></div>
      </div>
    </div>
  </div>

  <!-- API Settings Modal -->
  <div v-if="showApiSettings" class="fixed inset-0 z-60 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="showApiSettings = false">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-md sm:w-full">
        <!-- Header -->
        <div class="bg-gradient-to-r from-green-500 to-blue-600 px-4 py-3 rounded-t-lg shadow-md">
          <h3 class="text-lg leading-6 font-medium text-white">GST API Settings</h3>
        </div>

        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">RapidAPI Key</label>
              <input v-model="apiSettings.rapidApiKey" type="password"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your RapidAPI key" />
              <p class="mt-1 text-xs text-gray-500">
                Get your API key from <a href="https://rapidapi.com" target="_blank" class="text-blue-600 hover:underline">rapidapi.com</a>
              </p>
            </div>

            <div>
              <label class="flex items-center">
                <input v-model="apiSettings.enableLogging" type="checkbox" class="mr-2">
                <span class="text-sm text-gray-700">Enable API logging</span>
              </label>
            </div>
          </div>

          <!-- Form Buttons -->
          <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
            <button type="button" @click="saveApiSettings"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:col-start-2 sm:text-sm">
              Save Settings
            </button>
            <button type="button" @click="showApiSettings = false"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
              Cancel
            </button>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gradient-to-r from-blue-600 to-green-500 px-4 py-3 rounded-b-lg shadow-md"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, nextTick, onMounted } from 'vue';
import useLocalStorage from '~/composables/utils/useLocalStorage';
import useToast from '~/composables/ui/useToast';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  indianStates: {
    type: Array,
    required: true
  },
  partyEdit: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['update:show', 'submit-party']);

const { success, error: showError } = useToast();
const localStorage = useLocalStorage();

const firstInput = ref(null);
const showApiSettings = ref(false);
const gstFetchLoading = ref(false);

// API Settings
const apiSettings = ref({
  rapidApiKey: '',
  enableLogging: false
});

const form = ref({
  supply: '',
  addr: '',
  gstin: '',
  state: '',
  state_code: '',
  pin: null,
  pan: '',
  contact: ''
});

// Watch for modal visibility changes to focus the first input
watch(() => props.show, (newVal) => {
  if (newVal) {
    // Focus the first input when modal opens
    nextTick(() => {
      if (firstInput.value) {
        firstInput.value.focus();
      }
    });
  }
});

// Populate form if editing existing party
watch(() => props.partyEdit, (newVal) => {
  if (newVal) {
    form.value = {
      supply: newVal.supply || '',
      addr: newVal.addr || '',
      gstin: newVal.gstin || '',
      state: newVal.state || '',
      state_code: newVal.state_code || '',
      pin: newVal.pin || null,
      pan: newVal.pan || '',
      contact: newVal.contact || ''
    };
  }
}, { immediate: true });

const closeModal = () => {
  // Just close the modal without emitting any data
  emit('update:show', false);

  // Return focus to the party name input in the main form
  nextTick(() => {
    const partyNameInput = document.querySelector('input[list="partyList"]');
    if (partyNameInput) {
      partyNameInput.focus();
    }
  });
};

const close = () => {
  // When closing, emit the current form values to ensure parent component has latest data
  if (form.value.supply) {
    emit('submit-party', { ...form.value });
  }
  emit('update:show', false);
};

const updateStateCode = (stateName) => {
  const state = props.indianStates.find(s => s.name === stateName);
  if (state) {
    form.value.state_code = state.code;
  }
};

// Function to find state by code
const findStateByCode = (stateCode) => {
  // Convert stateCode to string to ensure consistent comparison
  const stateCodeStr = String(stateCode);
  // Try to find state by exact match first
  let state = props.indianStates.find(s => String(s.code) === stateCodeStr);

  // If not found, try to find by numeric comparison (in case codes are stored as numbers)
  if (!state && !isNaN(stateCode)) {
    const stateCodeNum = parseInt(stateCode, 10);
    state = props.indianStates.find(s => {
      const sCode = typeof s.code === 'string' ? parseInt(s.code, 10) : s.code;
      return sCode === stateCodeNum;
    });
  }

  return state;
};

// Watch GSTIN input for validation and auto-population
watch(() => form.value.gstin, (newVal) => {
  if (!newVal) return;

  // Convert to uppercase
  form.value.gstin = newVal.toUpperCase();

  // If GSTIN is exactly 15 characters
  if (form.value.gstin.length === 15) {
    // Extract first 2 digits for state code
    const stateCode = form.value.gstin.substring(0, 2);

    // Find state by code using our enhanced findStateByCode function
    const state = findStateByCode(stateCode);

    // Update state and state_code if found
    if (state) {
      form.value.state = state.name;
      form.value.state_code = state.code;
    }

    // Extract characters 3-12 for PAN
    form.value.pan = form.value.gstin.substring(2, 12);
  }
});

// Load API settings from localStorage
const loadApiSettings = () => {
  const savedSettings = localStorage.getItem('gstApiSettings', {
    rapidApiKey: '',
    enableLogging: false
  });
  apiSettings.value = { ...savedSettings };
};

// Save API settings to localStorage
const saveApiSettings = () => {
  localStorage.setItem('gstApiSettings', apiSettings.value);
  showApiSettings.value = false;
  success('API settings saved successfully!');
};

// Fetch party details by GST number
const fetchPartyByGST = async () => {
  const gstin = form.value.gstin?.trim();

  if (!gstin) {
    showError('Please enter a GSTIN number first');
    return;
  }

  // Validate GSTIN format (15 characters)
  if (gstin.length !== 15) {
    showError('GSTIN must be exactly 15 characters long');
    return;
  }

  if (!apiSettings.value.rapidApiKey) {
    showError('Please configure your RapidAPI key in settings first');
    showApiSettings.value = true;
    return;
  }

  gstFetchLoading.value = true;

  try {
    if (apiSettings.value.enableLogging) {
      console.log('Fetching party details for GSTIN:', gstin);
    }

    // RapidAPI endpoints
    const rapidApiEndpoints = [
      {
        url: `https://powerful-gstin-tool.p.rapidapi.com/v1/gstin/${gstin}/details`,
        headers: {
          'x-rapidapi-key': apiSettings.value.rapidApiKey,
          'x-rapidapi-host': 'powerful-gstin-tool.p.rapidapi.com'
        },
        parser: (data) => data
      }
    ];

    let apiSuccess = false;

    for (const endpoint of rapidApiEndpoints) {
      try {
        if (apiSettings.value.enableLogging) {
          console.log('Trying RapidAPI endpoint:', endpoint.url);
        }

        const fetchOptions = {
          method: endpoint.method || 'GET',
          headers: endpoint.headers
        };

        if (endpoint.body) {
          fetchOptions.body = endpoint.body;
        }

        const response = await fetch(endpoint.url, fetchOptions);

        if (response.ok) {
          const data = await response.json();

          // Always log the complete API response for debugging
          console.log('=== RapidAPI Complete Response ===');
          console.log('Raw Response:', data);
          console.log('Response structure:', JSON.stringify(data, null, 2));
          console.log('Response status:', response.status);
          console.log('Response headers:', Object.fromEntries(response.headers.entries()));

          // Parse the response based on the endpoint
          const parsedData = endpoint.parser(data);
          console.log('Parsed data:', parsedData);

          // Check for successful response in various formats
          const isSuccess = parsedData && (
            parsedData.status === 'success' ||
            parsedData.flag === true ||
            parsedData.success === true ||
            parsedData.data ||
            parsedData.result ||
            (parsedData.gstin && parsedData.legal_name) ||
            (parsedData.gstin && parsedData.trade_name) ||
            (parsedData.legal_name || parsedData.trade_name)
          );

          console.log('Success check result:', isSuccess);

          if (isSuccess) {
            // Extract party data from various possible locations
            const partyData = parsedData.data || parsedData.result || parsedData;
            console.log('Extracted party data for processing:', partyData);
            populatePartyFromGSTData(partyData, gstin);
            success('Party details fetched successfully from GST database!');
            apiSuccess = true;
            break;
          } else {
            console.log('❌ API response not recognized as success:', parsedData);
            console.log('Available fields in response:', Object.keys(parsedData || {}));
          }
        } else {
          console.log('❌ HTTP Response not OK:', response.status, response.statusText);
          const errorText = await response.text();
          console.log('Error response body:', errorText);
        }
      } catch (endpointError) {
        if (apiSettings.value.enableLogging) {
          console.log('RapidAPI endpoint failed:', endpoint.url, endpointError.message);
        }
        continue;
      }
    }

    if (!apiSuccess) {
      showError('No valid data found for this GSTIN. Please check the number and try again.');
    }

  } catch (error) {
    console.error('Error fetching party details:', error);
    showError('Failed to fetch party details. Please try again.');
  } finally {
    gstFetchLoading.value = false;
  }
};

// Populate party form from GST API data
const populatePartyFromGSTData = (partyData, gstin) => {
  console.log('=== GST API Response Debug ===');
  console.log('Raw API Response:', JSON.stringify(partyData, null, 2));

  // Extract data based on API response structure
  const tradeName = partyData.trade_name || '';
  const legalName = partyData.legal_name || '';

  // Use trade name first, then legal name
  const displayName = tradeName || legalName;

  if (displayName) {
    form.value.supply = displayName;
    console.log('✅ Set party name to:', displayName);
  } else {
    console.log('❌ No party name found in API response');
  }

  // Extract and format address
  console.log('🔍 Attempting to extract address...');
  const address = formatGSTAddress(partyData);
  if (address) {
    form.value.addr = address;
    console.log('✅ Set address to:', address);
  } else {
    console.log('❌ No address found in API response');
  }

  // Extract PIN code
  console.log('🔍 Attempting to extract PIN code...');
  const pinCode = extractGSTPinCode(partyData);
  if (pinCode) {
    form.value.pin = parseInt(pinCode);
    console.log('✅ Set PIN to:', pinCode);
  } else {
    console.log('❌ No PIN code found in API response');
  }

  // GSTIN is already set, but ensure it's uppercase
  form.value.gstin = gstin.toUpperCase();

  // Extract state from GSTIN (first 2 digits)
  const stateCode = gstin.substring(0, 2);
  const state = props.indianStates.find(s => String(s.code) === stateCode);
  if (state) {
    form.value.state = state.name;
    form.value.state_code = state.code;
    console.log('✅ Set state to:', state.name, 'with code:', state.code);
  } else {
    console.log('❌ No matching state found for code:', stateCode);
  }

  // Extract PAN from GSTIN (characters 3-12)
  const panCode = gstin.substring(2, 12);
  form.value.pan = panCode;
  console.log('✅ Set PAN to:', panCode);

};

// Format address from GST API response
const formatGSTAddress = (partyData) => {
  console.log('🔍 formatGSTAddress called with:', partyData);

  if (!partyData) {
    console.log('❌ No partyData provided to formatGSTAddress');
    return '';
  }

  // Handle the new API response structure: place_of_business_principal.address
  if (partyData.place_of_business_principal?.address) {
    const addr = partyData.place_of_business_principal.address;
    console.log('🔍 Found place_of_business_principal.address:', addr);

    // Build address from the new structure
    const parts = [
      addr.door_num,        // Door number
      addr.building_name,   // Building name
      addr.street,          // Street
      addr.location,        // Location
      addr.district,        // District
      addr.city,            // City
      addr.state,           // State
      addr.pincode          // PIN code
    ];

    console.log('🔍 Address parts found:', parts);
    const formattedAddress = parts.filter(p => p && p.toString().trim()).join(', ');
    if (formattedAddress) {
      console.log(`✅ Formatted address from place_of_business_principal:`, formattedAddress);
      return formattedAddress;
    }
  }

  // Fallback: Try old API response structure for backward compatibility
  const addressFields = [
    { name: 'partyData.pradr?.addr', value: partyData.pradr?.addr },
    { name: 'partyData.adadr?.addr', value: partyData.adadr?.addr },
    { name: 'partyData.address', value: partyData.address },
    { name: 'partyData.principalPlaceAddress', value: partyData.principalPlaceAddress },
    { name: 'partyData.principal_place_address', value: partyData.principal_place_address },
    { name: 'partyData.pradr', value: partyData.pradr },
    { name: 'partyData.businessAddress', value: partyData.businessAddress },
    { name: 'partyData.business_address', value: partyData.business_address }
  ];

  console.log('🔍 Checking fallback address fields:');
  for (const field of addressFields) {
    if (field.value) {
      if (typeof field.value === 'string') {
        const trimmedAddress = field.value.trim();
        console.log(`✅ Found string address in ${field.name}:`, trimmedAddress);
        return trimmedAddress;
      } else if (typeof field.value === 'object') {
        // Handle structured address from old API format
        const parts = [
          field.value.bno,        // Building number
          field.value.bnm,        // Building name
          field.value.flno,       // Floor number
          field.value.st,         // Street
          field.value.loc,        // Location
          field.value.city,       // City
          field.value.dst,        // District
          field.value.stcd,       // State
          // Fallback fields
          field.value.building_name,
          field.value.building_no,
          field.value.floor_no,
          field.value.street,
          field.value.location,
          field.value.district,
          field.value.state
        ];

        const formattedAddress = parts.filter(p => p && p.toString().trim()).join(', ');
        if (formattedAddress) {
          console.log(`✅ Formatted address from ${field.name}:`, formattedAddress);
          return formattedAddress;
        }
      }
    }
  }

  console.log('❌ No address could be extracted from any field');
  return '';
};

// Extract PIN code from GST API response
const extractGSTPinCode = (partyData) => {
  if (!partyData) return '';

  // Handle the new API response structure: place_of_business_principal.address.pin_code
  if (partyData.place_of_business_principal?.address?.pin_code) {
    const pinStr = String(partyData.place_of_business_principal.address.pin_code).trim();
    if (pinStr.length === 6 && /^\d{6}$/.test(pinStr)) {
      console.log('✅ Found PIN from place_of_business_principal.address.pin_code:', pinStr);
      return pinStr;
    }
  }

  // Also try pincode (without underscore) as fallback
  if (partyData.place_of_business_principal?.address?.pincode) {
    const pinStr = String(partyData.place_of_business_principal.address.pincode).trim();
    if (pinStr.length === 6 && /^\d{6}$/.test(pinStr)) {
      console.log('✅ Found PIN from place_of_business_principal.address.pincode:', pinStr);
      return pinStr;
    }
  }

  // Fallback: Try old API response structure for backward compatibility
  const pinFields = [
    partyData.pradr?.pncd,   // PIN code from principal address
    partyData.adadr?.pncd,   // PIN code from additional address
    partyData.pincode,
    partyData.pin,
    partyData.postalCode,
    partyData.postal_code,
    partyData.principalPlacePincode,
    partyData.principal_place_pincode
  ];

  for (const pinField of pinFields) {
    if (pinField) {
      const pinStr = String(pinField).trim();
      if (pinStr.length === 6 && /^\d{6}$/.test(pinStr)) {
        console.log('✅ Found PIN from fallback field:', pinStr);
        return pinStr;
      }
    }
  }

  // Also check if PIN is embedded in old address structure
  if (partyData.pradr && partyData.pradr.pncd) {
    const pinStr = String(partyData.pradr.pncd).trim();
    if (pinStr.length === 6 && /^\d{6}$/.test(pinStr)) {
      console.log('✅ Found PIN from pradr.pncd:', pinStr);
      return pinStr;
    }
  }

  console.log('❌ No valid PIN code found');
  return '';
};

const submitForm = () => {
  emit('submit-party', { ...form.value });
  close();
  // Reset form
  form.value = {
    supply: '',
    addr: '',
    gstin: '',
    state: '',
    state_code: '',
    pin: null,
    pan: '',
    contact: ''
  };
};

// Load API settings on component mount
onMounted(() => {
  loadApiSettings();
});
</script>