<template>
  <div class="container mx-auto px-4 py-4 mt-2">
    <!-- Back button -->
    <div class="mb-6">
      <NuxtLink to="/stock-market" class="inline-flex items-center text-indigo-600 hover:text-indigo-800">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        Back to Market Overview
      </NuxtLink>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline"> {{ error }}</span>
      <button @click="fetchStockDetails(symbol)" class="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
        Try Again
      </button>
    </div>

    <!-- Stock details -->
    <div v-else-if="stockDetails">
      <!-- This section is no longer needed as we're using real data -->

      <!-- Stock header -->
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-800">
          {{ stockDetails.quote?.info?.companyName || symbol }}
          <span class="text-lg text-gray-500 ml-2">({{ symbol }})</span>
        </h1>

        <div class="flex items-center mt-2">
          <span class="text-3xl font-semibold">₹{{ formatPrice(stockDetails.quote?.info?.lastPrice) }}</span>
          <span
            class="ml-3 text-lg"
            :class="parseFloat(stockDetails.quote?.info?.pChange) >= 0 ? 'text-green-600' : 'text-red-600'"
          >
            {{ stockDetails.quote?.info?.change > 0 ? '+' : '' }}{{ formatPrice(stockDetails.quote?.info?.change) }}
            ({{ stockDetails.quote?.info?.pChange > 0 ? '+' : '' }}{{ parseFloat(stockDetails.quote?.info?.pChange).toFixed(2) }}%)
          </span>
        </div>

        <div class="text-sm text-gray-500 mt-1">
          Last updated: {{ new Date(stockDetails.timestamp).toLocaleString() }}
        </div>
      </div>

      <!-- Stock details grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <!-- Key Statistics -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-indigo-600 text-white px-4 py-3">
            <h2 class="text-xl font-semibold">Key Statistics</h2>
          </div>
          <div class="p-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500">Open</p>
                <p class="font-medium">₹{{ formatPrice(stockDetails.tradeInfo?.open) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Previous Close</p>
                <p class="font-medium">₹{{ formatPrice(stockDetails.tradeInfo?.previousClose) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Day High</p>
                <p class="font-medium">₹{{ formatPrice(stockDetails.tradeInfo?.dayHigh) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Day Low</p>
                <p class="font-medium">₹{{ formatPrice(stockDetails.tradeInfo?.dayLow) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">52 Week High</p>
                <p class="font-medium">₹{{ formatPrice(stockDetails.quote?.info?.yearHigh) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">52 Week Low</p>
                <p class="font-medium">₹{{ formatPrice(stockDetails.quote?.info?.yearLow) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Trading Information -->
        <div class="bg-white rounded-lg shadow-md overflow-hidden">
          <div class="bg-indigo-600 text-white px-4 py-3">
            <h2 class="text-xl font-semibold">Trading Information</h2>
          </div>
          <div class="p-4">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <p class="text-sm text-gray-500">Volume</p>
                <p class="font-medium">{{ formatVolume(stockDetails.tradeInfo?.totalTradedVolume) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Value</p>
                <p class="font-medium">₹{{ formatLargeNumber(stockDetails.tradeInfo?.totalTradedValue) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Market Cap</p>
                <p class="font-medium">₹{{ formatLargeNumber(stockDetails.tradeInfo?.totalMarketCap) }}</p>
              </div>
              <div>
                <p class="text-sm text-gray-500">Average Volume</p>
                <p class="font-medium">{{ formatVolume(stockDetails.tradeInfo?.averageVolume) }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Price Chart -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="p-4">
          <StockChart :symbol="symbol" :title="`${symbol} Price History`" @period-change="handlePeriodChange" />
        </div>
      </div>

      <!-- Company Information -->
      <div class="bg-white rounded-lg shadow-md overflow-hidden mb-8">
        <div class="bg-indigo-600 text-white px-4 py-3">
          <h2 class="text-xl font-semibold">Company Information</h2>
        </div>
        <div class="p-4">
          <div v-if="stockDetails.companyInfo?.businessSummary && stockDetails.companyInfo.businessSummary !== 'N/A'" class="mb-4">
            <p class="text-sm text-gray-500 mb-1">Business Summary</p>
            <p class="text-sm">{{ stockDetails.companyInfo.businessSummary }}</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <p class="text-sm text-gray-500">Industry</p>
              <p class="font-medium">{{ stockDetails.companyInfo?.industry || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Sector</p>
              <p class="font-medium">{{ stockDetails.companyInfo?.sector || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Country</p>
              <p class="font-medium">{{ stockDetails.companyInfo?.country || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">City</p>
              <p class="font-medium">{{ stockDetails.companyInfo?.city || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Employees</p>
              <p class="font-medium">{{ stockDetails.companyInfo?.fullTimeEmployees || 'N/A' }}</p>
            </div>
            <div>
              <p class="text-sm text-gray-500">Website</p>
              <p class="font-medium">
                <a v-if="stockDetails.companyInfo?.website && stockDetails.companyInfo.website !== 'N/A'"
                   :href="stockDetails.companyInfo.website"
                   target="_blank"
                   class="text-indigo-600 hover:text-indigo-800 hover:underline">
                  Visit Website
                </a>
                <span v-else>N/A</span>
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Action buttons -->
      <div class="flex justify-between items-center">
        <div>
          <button
            @click="showAddToViewModal = true"
            class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded flex items-center"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add to View
          </button>
        </div>
        <div>
          <button @click="fetchStockDetails(symbol)" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded">
            Refresh Data
          </button>
        </div>
      </div>

      <!-- Add to View Modal -->
      <div v-if="showAddToViewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Add {{ symbol }} to View</h3>

          <div v-if="views.length === 0" class="mb-4 text-center py-4">
            <p class="text-gray-500 mb-2">You don't have any views yet.</p>
            <button
              @click="showCreateViewModal = true; showAddToViewModal = false"
              class="text-indigo-600 hover:text-indigo-800 font-medium"
            >
              Create your first view
            </button>
          </div>

          <div v-else class="mb-4">
            <div class="space-y-2">
              <div v-for="view in views" :key="view.id" class="flex items-center">
                <input
                  :id="'view-' + view.id"
                  type="checkbox"
                  :checked="view.symbols.includes(symbol)"
                  @change="toggleSymbolInView(view.id, ($event.target as HTMLInputElement).checked)"
                  class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                />
                <label :for="'view-' + view.id" class="ml-2 block text-sm text-gray-900">
                  {{ view.name }} ({{ view.symbols.length }} symbols)
                </label>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3">
            <button
              @click="showAddToViewModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Close
            </button>
          </div>
        </div>
      </div>

      <!-- Create View Modal -->
      <div v-if="showCreateViewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Create New View</h3>
          <div class="mb-4">
            <label for="viewName" class="block text-sm font-medium text-gray-700 mb-1">View Name</label>
            <input
              id="viewName"
              v-model="newViewName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter view name"
            />
          </div>
          <div class="flex justify-end space-x-3">
            <button
              @click="showCreateViewModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              @click="createNewView"
              class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700"
              :disabled="!newViewName.trim()"
            >
              Create
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRoute } from '#app';
import { useHead } from 'nuxt/app';
import { useStockMarket } from '~/composables/stock-market/useStockMarket';
import { useStockViews } from '~/composables/stock-market/useStockViews';
import StockChart from '~/components/StockChart.vue';

const route = useRoute();
const symbol = route.params.symbol as string;

// Set page metadata
useHead({
  title: `${symbol} - Stock Details`,
  meta: [
    { name: 'description', content: `Detailed information about ${symbol} stock from NSE India` }
  ]
});

const { isLoading, error, stockDetails, fetchStockDetails, formatPrice, formatLargeNumber } = useStockMarket();
const { views, fetchViews, createView, addSymbolToView, removeSymbolFromView } = useStockViews();

const selectedPeriod = ref('1y');
const showAddToViewModal = ref(false);
const showCreateViewModal = ref(false);
const newViewName = ref('');

// Function to handle period change from the chart component
function handlePeriodChange(period: string) {
  selectedPeriod.value = period;
  console.log(`Period changed to ${period}`);
}

// Function to format volume numbers (e.g., 1.2M, 450K)
function formatVolume(volume: any) {
  if (!volume) return 'N/A';

  const num = parseFloat(volume);
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toLocaleString('en-IN');
}

// Format date function is no longer needed as we're using the data directly from Yahoo Finance

// Toggle symbol in a view
const toggleSymbolInView = async (viewId: string, isChecked: boolean) => {
  try {
    if (isChecked) {
      await addSymbolToView(viewId, symbol);
    } else {
      await removeSymbolFromView(viewId, symbol);
    }
  } catch (err) {
    console.error('Error toggling symbol in view:', err);
  }
};

// Create a new view
const createNewView = async () => {
  if (!newViewName.value.trim()) return;

  try {
    const view = await createView(newViewName.value.trim(), [symbol]);
    if (view) {
      showCreateViewModal.value = false;
      newViewName.value = '';
      // Refresh views list
      await fetchViews();
    }
  } catch (err) {
    console.error('Error creating view:', err);
  }
};

// Fetch stock details and views on component mount
onMounted(async () => {
  try {
    await Promise.all([
      fetchStockDetails(symbol),
      fetchViews()
    ]);
  } catch (err) {
    console.error(`Failed to fetch data for ${symbol}:`, err);
  }
});
</script>
