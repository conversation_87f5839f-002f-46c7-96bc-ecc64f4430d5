<template>
  <div v-if="isOpen" class="modal-overlay" @click="closeModal">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ editingGroup ? 'Edit' : 'Create' }} Labor Group
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="saveGroup" class="space-y-6">
          <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            <!-- Group Name -->
            <div>
              <label for="groupName" class="block text-sm font-medium text-gray-700 mb-2">
                Group Name <span class="text-red-500">*</span>
              </label>
              <input
                id="groupName"
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'border-red-500': errors.name }"
                placeholder="e.g., Skilled Workers, Helpers, etc."
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
            </div>

            <!-- Phone -->
            <div>
              <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">
                Phone Number
              </label>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="10-digit phone number"
              />
            </div>

            <!-- Aadhar -->
            <div>
              <label for="aadhar" class="block text-sm font-medium text-gray-700 mb-2">
                Aadhar Number
              </label>
              <input
                id="aadhar"
                v-model="form.aadhar"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="12-digit Aadhar number"
              />
            </div>

            <!-- Color -->
            <div>
              <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
                Group Color
              </label>
              <div class="flex items-center space-x-4">
                <input
                  id="color"
                  v-model="form.color"
                  type="color"
                  class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer"
                />
                <input
                  v-model="form.color"
                  type="text"
                  class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="#3B82F6"
                  pattern="^#[0-9A-Fa-f]{6}$"
                />
              </div>
            </div>

            <!-- Description -->
            <div class="lg:col-span-2">
              <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                Description
              </label>
              <textarea
                id="description"
                v-model="form.description"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Brief description of this group"
              ></textarea>
            </div>

            <!-- Address -->
            <div class="lg:col-span-2">
              <label for="address" class="block text-sm font-medium text-gray-700 mb-2">
                Address
              </label>
              <textarea
                id="address"
                v-model="form.address"
                rows="3"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter complete address"
              ></textarea>
            </div>

            <!-- Bank Details -->
            <div class="bg-gray-50 p-4 rounded-lg lg:col-span-4">
              <h4 class="text-sm font-medium text-gray-900 mb-4">Bank Details</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div>
                  <label for="bankName" class="block text-sm font-medium text-gray-700 mb-2">
                    Bank Name
                  </label>
                  <input
                    id="bankName"
                    v-model="form.bank_details.bank_name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., State Bank of India"
                  />
                </div>

                <div>
                  <label for="accountNumber" class="block text-sm font-medium text-gray-700 mb-2">
                    Account Number
                  </label>
                  <input
                    id="accountNumber"
                    v-model="form.bank_details.account_number"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Account number"
                  />
                </div>

                <div>
                  <label for="ifscCode" class="block text-sm font-medium text-gray-700 mb-2">
                    IFSC Code
                  </label>
                  <input
                    id="ifscCode"
                    v-model="form.bank_details.ifsc_code"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="e.g., SBIN0001234"
                  />
                </div>

                <div>
                  <label for="branchName" class="block text-sm font-medium text-gray-700 mb-2">
                    Branch Name
                  </label>
                  <input
                    id="branchName"
                    v-model="form.bank_details.branch_name"
                    type="text"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Branch name"
                  />
                </div>
              </div>
            </div>
          </div>

          <!-- Color -->
          <div>
            <label for="color" class="block text-sm font-medium text-gray-700 mb-2">
              Group Color
            </label>
            <div class="flex items-center space-x-4">
              <input
                id="color"
                v-model="form.color"
                type="color"
                class="w-12 h-10 border border-gray-300 rounded-md cursor-pointer"
              />
              <input
                v-model="form.color"
                type="text"
                class="flex-1 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="#3B82F6"
                pattern="^#[0-9A-Fa-f]{6}$"
              />
            </div>
            <p class="mt-1 text-sm text-gray-500">
              This color will be used to identify the group in various views
            </p>
          </div>

          <!-- Color Presets -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              Quick Colors
            </label>
            <div class="flex flex-wrap gap-2">
              <button
                v-for="preset in colorPresets"
                :key="preset.color"
                type="button"
                @click="form.color = preset.color"
                class="w-8 h-8 rounded-full border-2 border-gray-300 hover:border-gray-400 transition-colors"
                :style="{ backgroundColor: preset.color }"
                :class="{ 'ring-2 ring-blue-500': form.color === preset.color }"
                :title="preset.name"
              ></button>
            </div>
          </div>

          <!-- Preview -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 mb-2">Preview</h4>
            <div class="flex items-center space-x-3">
              <div
                class="w-4 h-4 rounded-full"
                :style="{ backgroundColor: form.color }"
              ></div>
              <span class="text-sm font-medium">{{ form.name || 'Group Name' }}</span>
              <span class="text-sm text-gray-500">{{ form.description || 'No description' }}</span>
            </div>
          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          @click="closeModal"
          class="btn btn-secondary mr-3"
        >
          Cancel
        </button>
        <button
          @click="saveGroup"
          :disabled="saving || !isFormValid"
          class="btn btn-primary"
        >
          <Icon v-if="saving" name="heroicons:arrow-path" class="w-4 h-4 animate-spin mr-2" />
          {{ saving ? 'Saving...' : (editingGroup ? 'Update' : 'Create') }} Group
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  group: {
    type: Object,
    default: null
  },
  firmId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['close', 'saved'])

// Form data
const form = ref({
  name: '',
  description: '',
  color: '#3B82F6',
  phone: '',
  address: '',
  aadhar: '',
  bank_details: {
    bank_name: '',
    account_number: '',
    ifsc_code: '',
    branch_name: ''
  }
})

// State
const saving = ref(false)
const errors = ref({})

// Color presets
const colorPresets = ref([
  { name: 'Blue', color: '#3B82F6' },
  { name: 'Green', color: '#10B981' },
  { name: 'Purple', color: '#8B5CF6' },
  { name: 'Pink', color: '#EC4899' },
  { name: 'Yellow', color: '#F59E0B' },
  { name: 'Red', color: '#EF4444' },
  { name: 'Indigo', color: '#6366F1' },
  { name: 'Teal', color: '#14B8A6' },
  { name: 'Orange', color: '#F97316' },
  { name: 'Cyan', color: '#06B6D4' },
  { name: 'Lime', color: '#84CC16' },
  { name: 'Emerald', color: '#059669' }
])

// Computed
const editingGroup = computed(() => !!props.group)
const isFormValid = computed(() => {
  return form.value.name.trim().length > 0 && 
         /^#[0-9A-Fa-f]{6}$/.test(form.value.color)
})

// Methods
const closeModal = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  form.value = {
    name: '',
    description: '',
    color: '#3B82F6',
    phone: '',
    address: '',
    aadhar: '',
    bank_details: {
      bank_name: '',
      account_number: '',
      ifsc_code: '',
      branch_name: ''
    }
  }
  errors.value = {}
}

const loadGroup = () => {
  if (props.group) {
    form.value = {
      name: props.group.name || '',
      description: props.group.description || '',
      color: props.group.color || '#3B82F6',
      phone: props.group.phone || '',
      address: props.group.address || '',
      aadhar: props.group.aadhar || '',
      bank_details: {
        bank_name: props.group.bank_details?.bank_name || '',
        account_number: props.group.bank_details?.account_number || '',
        ifsc_code: props.group.bank_details?.ifsc_code || '',
        branch_name: props.group.bank_details?.branch_name || ''
      }
    }
  }
}

const saveGroup = async () => {
  if (!isFormValid.value) return

  // Validate name
  if (!form.value.name.trim()) {
    errors.value.name = 'Group name is required'
    return
  }

  saving.value = true
  errors.value = {}

  try {
    const groupData = {
      name: form.value.name.trim(),
      description: form.value.description.trim() || null,
      color: form.value.color,
      phone: form.value.phone,
      address: form.value.address,
      aadhar: form.value.aadhar,
      bank_details: form.value.bank_details,
      firm_id: props.firmId,
      user_id: props.userId
    }

    let response
    if (editingGroup.value) {
      response = await $fetch(`/api/labor/groups/${props.group.id}`, {
        method: 'PUT',
        body: groupData
      })
    } else {
      response = await $fetch('/api/labor/groups', {
        method: 'POST',
        body: groupData
      })
    }

    if (response.success) {
      emit('saved', response.data)
      closeModal()
    }
  } catch (error) {
    console.error('Error saving group:', error)
    
    // Handle specific error cases
    if (error.data?.statusMessage?.includes('already exists')) {
      errors.value.name = 'A group with this name already exists'
    } else {
      errors.value.general = error.data?.statusMessage || 'Failed to save group'
    }
  } finally {
    saving.value = false
  }
}

// Watch for modal open to load group data
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      loadGroup()
    })
  }
})

// Watch for group changes
watch(() => props.group, () => {
  if (props.isOpen) {
    loadGroup()
  }
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl w-full mx-4 max-h-screen overflow-y-auto lg:max-w-7xl;
}

.modal-header {
  @apply p-6 border-b border-gray-200 flex justify-between items-center;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply p-6 border-t border-gray-200 flex justify-end;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>