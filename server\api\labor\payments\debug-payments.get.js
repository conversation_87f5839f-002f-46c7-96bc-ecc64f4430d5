import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'
import { LaborPaymentService } from '~/utils/laborPaymentService.js'

/**
 * Debug endpoint to analyze payment data and compare old vs new logic
 * This helps validate the new payment calculation logic
 */
export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { firmId, groupId } = query

    if (!firmId || !groupId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: firmId and groupId are required'
      })
    }

    // Get Supabase configuration
    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found for this firm'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Initialize the payment service
    const paymentService = new LaborPaymentService(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Get raw data
    const payments = await paymentService.getGroupPayments(groupId)
    const periods = await paymentService.getAttendancePeriods(groupId)
    
    // Categorize payments
    const categorized = paymentService.categorizePayments(payments, periods)
    
    // Get validation results
    const validation = await paymentService.validatePaymentData(groupId)

    // Calculate using old logic (for comparison)
    const oneYearAgo = new Date()
    oneYearAgo.setFullYear(oneYearAgo.getFullYear() - 1)

    const { data: profiles } = await supabase.from('labor_profiles').select('id').eq('group_id', groupId)
    const laborIds = profiles.map(p => p.id)

    const { data: attendancePeriods } = await supabase
      .from('attendance_records')
      .select('period_start, period_end')
      .in('labor_id', laborIds)
      .gte('period_start', oneYearAgo.toISOString())

    const uniquePeriods = [...new Set(attendancePeriods.map(p => `${p.period_start}|${p.period_end}`))].map(p => {
        const [start, end] = p.split('|')
        return { period_start: start, period_end: end }
    })
    
    const { data: allPayments } = await supabase
        .from('payment_records')
        .select('*')
        .eq('group_id', groupId)

    const oldLogicResults = await Promise.all(uniquePeriods.map(async (period) => {
        const { data: earnings } = await supabase
            .from('attendance_records')
            .select('amount, site_expenses')
            .in('labor_id', laborIds)
            .gte('attendance_date', period.period_start)
            .lte('attendance_date', period.period_end)

        const totalEarnings = earnings.reduce((sum, record) => sum + record.amount + (record.site_expenses || 0), 0)

        const paymentsInPeriod = allPayments.filter(p => p.payment_date >= period.period_start && p.payment_date <= period.period_end)
        const totalPayments = paymentsInPeriod.reduce((sum, record) => sum + record.amount, 0)
        
        const finalPayment = allPayments.find(p => p.payment_type === 'Final Payment' && p.payment_date > period.period_end)

        let unpaidAmount = totalEarnings - totalPayments
        if (finalPayment && unpaidAmount > 0) {
            unpaidAmount -= finalPayment.amount
        }

        return {
            period,
            totalPayments: totalPayments + (finalPayment ? finalPayment.amount : 0),
            unpaidAmount: unpaidAmount < 0 ? 0 : unpaidAmount,
            totalEarnings,
            finalPayment: finalPayment || null
        }
    }))

    // Calculate using new logic
    const newLogicResults = await paymentService.calculateUnpaidAmounts(groupId)

    return {
      success: true,
      debug: {
        rawData: {
          totalPayments: payments.length,
          totalPeriods: periods.length,
          paymentTypes: [...new Set(payments.map(p => p.payment_type))],
          dateRange: {
            firstPeriod: periods[0]?.period_start || null,
            lastPeriod: periods[periods.length - 1]?.period_end || null,
            firstPayment: payments[0]?.payment_date || null,
            lastPayment: payments[payments.length - 1]?.payment_date || null
          }
        },
        categorization: {
          settledPeriods: categorized.settledPeriods.length,
          ongoingPeriods: categorized.ongoingPeriods.length,
          postFinalPayments: categorized.postFinalPayments.length,
          latestFinalPaymentDate: paymentService.getLatestFinalPaymentDate(payments)
        },
        validation,
        comparison: {
          oldLogic: oldLogicResults,
          newLogic: newLogicResults,
          differences: {
            recordCount: {
              old: oldLogicResults.length,
              new: newLogicResults.length
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('Error in debug-payments API:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to debug payment data'
    })
  }
})
