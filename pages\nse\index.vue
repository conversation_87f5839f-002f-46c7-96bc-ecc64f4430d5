<template>
  <!-- Loading Overlay -->
  <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="flex flex-col items-center justify-center p-4 bg-white rounded-lg shadow-lg">
      <div class="w-16 h-16 border-4 border-red-600 border-t-transparent rounded-full animate-spin mb-4"></div>
      <p class="text-lg font-semibold text-gray-800">Loading NSE Data...</p>
    </div>
  </div>

  <div>
    <!-- Top Navigation Bar -->
    <div class="bg-gradient-to-r from-red-600 to-black min-h-[50px] flex flex-col sm:flex-row items-center justify-between shadow-lg p-2" id="top-nav">
      <div class="flex flex-wrap items-center justify-center sm:justify-start gap-2 w-full sm:w-auto mb-2 sm:mb-0">
        <span class="text-white font-bold text-xl md:text-2xl flex items-center">
          <Icon name="heroicons:chart-bar" class="w-6 h-6 mr-2" />
          NSE Portfolio Dashboard
        </span>
        <button class="bg-red-700 hover:bg-red-800 text-white px-4 py-1 rounded" @click="toggleCnNote">CN Notes</button>
        <button class="bg-red-700 hover:bg-red-800 text-white px-4 py-1 rounded" @click="toggleAllRec">Show All</button>
        <div :class="{'animate-pulse': timerMinutes === 0 && timerSeconds <= 30}"
             class="text-white font-mono bg-red-600 px-3 py-1 rounded flex items-center">
          <Icon v-if="timerMinutes === 0 && timerSeconds <= 30" name="heroicons:clock" class="w-4 h-4 mr-2 text-white" />
          Next Refresh: {{ formatTime }}
        </div>
      </div>
      <div class="flex flex-wrap items-center justify-center gap-2">
        <button class="bg-black hover:bg-gray-900 text-white px-4 py-1 rounded">Settings</button>
        <button class="bg-black hover:bg-gray-900 text-white px-4 py-1 rounded">Profile</button>
        <button class="bg-red-700 hover:bg-red-800 text-white px-4 py-1 rounded">Logout</button>
      </div>
    </div>

    <!-- Dashboard Section -->
    <div class="flex flex-col lg:flex-row justify-between gap-4 p-4">
      <div class="w-full lg:w-1/2 bg-white rounded-lg shadow-lg" v-if="!isLoading">
        <div class="p-4">
          <!-- Investment Values -->
          <div class="space-y-4">
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <h1 class="text-xl sm:text-3xl font-bold flex items-center">Total Investment Value:</h1>
              <h1 class="text-xl sm:text-3xl font-bold">
                <template v-if="!isLoading">{{ totalInvestment.toFixed(2) }}</template>
              </h1>
            </div>
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <h1 class="text-xl sm:text-3xl font-bold">Current Investment Value:</h1>
              <h1 class="text-xl sm:text-3xl font-bold">
                <template v-if="!isLoading">
                  <div :style="{ color: dynamicColor }">{{ currentValue.toFixed(2) }}</div>
                </template>
              </h1>
            </div>
            <hr />
            <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center">
              <h1 class="text-xl sm:text-3xl font-bold">Overall Gain/Loss:</h1>
              <h1 class="text-xl sm:text-3xl font-bold">
                <template v-if="!isLoading">
                  <div :style="{ color: dynamicColor }">{{ overallGain.toFixed(2) }}</div>
                </template>
              </h1>
            </div>
          </div>
        </div>
      </div>
      <!-- Monthly Chart -->
      <div class="w-full lg:w-1/2 bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="p-4">
          <div class="h-[300px] lg:h-[32vh] min-h-[300px]">
            <h2 class="text-xl sm:text-2xl font-bold mb-4">Monthly Investment Summary</h2>
            <div class="h-[calc(100%-3rem)]">
              <canvas ref="monthlyChart" class="w-full h-full"></canvas>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Trading View Widget Section -->
    <div class="flex flex-col lg:flex-row gap-4 mt-4 mx-4">
      <div class="w-full lg:w-2/3 bg-white rounded-lg shadow-lg overflow-hidden">
        <div class="p-4">
          <div class="h-[50vh]">
            <div ref="tradingViewContainer" class="h-full w-full">
              <!-- TradingView widget will be inserted here by initTradingViewWidget() -->
            </div>
          </div>
        </div>
        <!-- Document section removed -->
      </div>

      <!-- Top Performers List -->
      <div class="w-full lg:w-1/3 space-y-4">
        <!-- Price Change Display Component -->
        <PriceChangeDisplay
          :priceGainers="historyPriceGainers"
          :priceLosers="historyPriceLosers"
        />
      </div>
    </div>
  </div>

  <div>
    <!-- Existing code -->
    <CNNoteModal
      :show="showCnNote"
      :is-edit="isEdit"
      :initial-data="selectedCNNote"
      :cn-notes="cn_notes"
      @close="closeCNNoteModal"
      @submit="handleCNNoteSubmit"
      @delete="deleteCNNote"
    />
    <NSEAllRecordsModal
      :show="showAllRec"
      @close="showAllRec = false"
    />
    <!-- Price Change Calculator (invisible component) -->
    <PriceChangeCalculator
      ref="priceChangeCalculator"
      :folioData="folioData"
      :gsDb="gsDb"
      @priceChangesCalculated="handlePriceChangesCalculated"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
import { useHead } from '#imports'

// Check if we're in a browser environment
const isBrowser = typeof window !== 'undefined'
import NSEAllRecordsModal from '~/components/nse/NSEAllRecordsModal.vue'
import CNNoteModal from '~/components/nse/CNNoteModal.vue'
import PriceChangeCalculator from '~/components/nse/PriceChangeCalculator.vue'
import PriceChangeDisplay from '~/components/nse/PriceChangeDisplay.vue'
import { Chart } from 'chart.js/auto'
import type { ChartData, ChartOptions, ChartDataset } from 'chart.js'
import { useFolio } from '~/composables/nse/useFolio'
import PouchDB from 'pouchdb'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'
import { useLogout } from '~/composables/auth/useLogout'
// Import new utility services
import {
  initPouchDBs,
  gsHistoryDb,
  gsCurrentPricesDb,
  nseDb,
  shouldRefreshHistory,
  shouldRefreshPrices,
  storeDataInPouchDB,
  getAllFromPouchDB,
  updateCacheMetadata,
  CURRENT_PRICES_REFRESH_MINUTES
} from '~/utils/pouchDBService'
import {
  timerMinutes,
  timerSeconds,
  timerActive,
  startTimer,
  resetTimer,
  initTimerService
} from '~/utils/timerService'

// Timer storage keys for direct access in component
const TIMER_STATE_KEY = 'nseTimerState'
const TIMER_LAST_ACTIVE_KEY = 'nseTimerLastActive'

// Set page title and meta tags
useHead({
  title: 'NSE Portfolio Dashboard',
  meta: [
    { name: 'description', content: 'Track and manage your NSE portfolio investments with real-time data and analytics.' }
  ]
})
// Import Nuxt composables
const useCookie = (name: string) => {
  // Simple cookie getter
  const getCookie = (name: string) => {
    const value = `; ${document.cookie}`
    const parts = value.split(`; ${name}=`)
    if (parts.length === 2) return parts.pop()?.split(';').shift() || ''
    return ''
  }

  return {
    value: getCookie(name)
  }
}

const navigateTo = (path: string) => { window.location.href = path }

// Define interfaces for NSE data
interface PriceChange {
  symbol: string
  difference: number
  percentageChange: number
  currentPrice: number
  investmentPrice: number
}

// Initialize composables
const { folioData, folioSummary } = useFolio()
const api = useApiWithAuth()

// Import PouchDB utilities
import { createDatabase } from '~/utils/pouchDBUtils';

// Legacy PouchDB instances (for compatibility with existing code)
const db = ref<any>(null)
const gsDb = ref<any>(null)

// State
const showCnNote = ref(false)
const showAllRec = ref(false)
const showPopup = ref(false)
const isLoading = ref(true)
const dynamicColor = ref('#ff0000')
const totalInvestment = ref(1)
const currentValue = ref(1)
const overallGain = ref(1)
const priceGainers = ref<PriceChange[]>([])
const priceLosers = ref<PriceChange[]>([])
// New state variables for history-based price changes
const historyPriceGainers = ref<any[]>([])
const historyPriceLosers = ref<any[]>([])
const nseRecords_all = ref<any[]>([])
const cn_notes = ref<any[]>([])
const error = ref<string | null>(null)
const isEdit = ref(false)
const oth_chg1 = ref(0)
const famt1 = ref(0)
const chartInstance = ref<Chart | null>(null)
const priceChangeCalculator = ref<InstanceType<typeof PriceChangeCalculator> | null>(null)

// Form and table state
const documentForm = ref<{
  id?: string;
  cn_no: string;
  broker: string;
  cn_date: string;
  folio: string;
  type: string;
  famt: number;
  oth_chg?: number;
}>({
  cn_no: '',
  broker: '',
  cn_date: '',
  folio: '',
  type: '',
  famt: 0
});

const nseRecords = ref<any[]>([])

const tableData = ref({
  cn_no: '',
  symbol: '',
  price: '',
  qnty: '',
  amt: '',
  brokerage: '',
  broker: '',
  pdate: '',
  namt: '',
  folio: '',
  type: '',
  rid: '',
  sector: ''
})

// Refs
const tradingViewContainer = ref<HTMLElement | null>(null)
const monthlyChart = ref<HTMLCanvasElement | null>(null)

// Computed
const formatTime = computed(() => {
  return `${timerMinutes.value.toString().padStart(2, '0')}:${timerSeconds.value.toString().padStart(2, '0')}`
})

// Methods
interface MonthlySummary {
  month: string
  sum_namt: number
  sum_cval: number
}

// Main data fetching function that calls all other fetch functions
const fetchNSEData = async () => {
  try {
    isLoading.value = true
    error.value = null

    // Fetch all data in sequence
    await fetchCnNoteData()
    await fetchFolioData()
    await gs_sheet()
    await fetchNSERecords()

    // Calculate investment values
    if (folioData.value && folioData.value.length > 0) {
      // Calculate with proper decimal precision
      totalInvestment.value = Number(folioData.value.reduce((sum, item) => sum + Number(item.namt || 0), 0).toFixed(2))
      currentValue.value = Number(folioData.value.reduce((sum, item) => sum + Number(item.cval || 0), 0).toFixed(2))
      overallGain.value = Number((currentValue.value - totalInvestment.value).toFixed(2))

      // Update top performers
      updateTopPerformers()

      // Update dynamic color based on gain/loss
      dynamicColor.value = overallGain.value >= 0 ? '#22c55e' : '#ef4444'
    }
  } catch (err) {
    error.value = 'Error fetching NSE data'
    console.error('Error fetching NSE data:', err)

    // Show error notification to user
    if (typeof window !== 'undefined') {
      alert('Error loading NSE data. Please try again later.')
    }
  } finally {
    isLoading.value = false
  }
}

// Fetch CN Note data from API and store in state
const fetchCnNoteData = async (): Promise<void> => {
  return new Promise<void>(async (resolve, reject) => {
    try {
      const api = useApiWithAuth()
      const response = await api.get('/api/nse/cn_note')
      cn_notes.value = response
      resolve()
    } catch (error) {
      console.error('Error loading CN Notes:', error)
      reject(error)
    }
  })
}

// Fetch Folio data from API and store in state
const fetchFolioData = async (): Promise<void> => {
  return new Promise<void>(async (resolve, reject) => {
    try {
      const api = useApiWithAuth()
      const response = await api.get('/api/nse/folio')

      // Update the folioData from the useFolio composable
      folioData.value = response

      // Create summary by grouping data
      const groupedData = folioData.value.reduce((acc: any, curr: any) => {
        if (!acc[curr.symbol]) {
          acc[curr.symbol] = {
            symbol: curr.symbol,
            totalNamt: 0,
            totalCval: 0,
            totalBrokerage: 0,
            totalQnty: 0,
            priceSum: 0,
            count: 0,
            sector: curr.sector || 'Unknown',
            cprice: curr.cprice,
            records: []
          }
        }

        acc[curr.symbol].totalNamt += Number(curr.namt || 0)
        acc[curr.symbol].totalCval += Number(curr.cval || 0)
        acc[curr.symbol].totalBrokerage += Number(curr.brokerage || 0)
        acc[curr.symbol].totalQnty += Number(curr.qnty || 0)
        acc[curr.symbol].priceSum += Number(curr.price || 0)
        acc[curr.symbol].count++
        acc[curr.symbol].records.push(curr)

        return acc
      }, {})

      // Convert to array and calculate averages
      folioSummary.value = Object.values(groupedData).map((group: any) => ({
        symbol: group.symbol,
        sector: group.sector,
        qnty: group.totalQnty,
        avgPrice: group.priceSum / group.count,
        cprice: group.cprice,
        namt: group.totalNamt,
        cval: group.totalCval,
        gainLoss: group.totalCval - group.totalNamt,
        brokerage: group.totalBrokerage,
        age: group.records[0].age || 0
      }))

      resolve()
    } catch (error) {
      console.error('Error loading Folio data:', error)
      reject(error)
    }
  })
}

// Fetch Google Sheets data with optimized caching strategy
const gs_sheet = async (): Promise<void> => {
  return new Promise<void>(async (resolve, reject) => {
    try {
      console.log(`[${new Date().toISOString()}] Starting gs_sheet function`);

      // Sync legacy gsDb with our new databases for compatibility
      if (gsCurrentPricesDb.value) {
        gsDb.value = gsCurrentPricesDb.value;
      }

      // Check if we need to refresh history data (once per day)
      let needHistoryRefresh = shouldRefreshHistory();

      // Check if we need to refresh current prices (every 20 minutes)
      let needPricesRefresh = shouldRefreshPrices();

      console.log(`[${new Date().toISOString()}] Cache status: History refresh needed: ${needHistoryRefresh}, Prices refresh needed: ${needPricesRefresh}`);

      let historyData: any[] = [];
      let currentPricesData: any[] = [];

      // Try to get history data from PouchDB first
      if (!needHistoryRefresh && gsHistoryDb.value) {
        try {
          historyData = await getAllFromPouchDB(gsHistoryDb.value);
          console.log(`[${new Date().toISOString()}] Using cached history data (${historyData.length} records)`);
        } catch (cacheError) {
          console.warn(`[${new Date().toISOString()}] Error reading history cache:`, cacheError);
          // Force refresh if cache read fails
          needHistoryRefresh = true;
        }
      }

      // Try to get current prices from PouchDB first
      if (!needPricesRefresh && gsCurrentPricesDb.value) {
        try {
          currentPricesData = await getAllFromPouchDB(gsCurrentPricesDb.value);
          console.log(`[${new Date().toISOString()}] Using cached current prices (${currentPricesData.length} records)`);
        } catch (cacheError) {
          console.warn(`[${new Date().toISOString()}] Error reading prices cache:`, cacheError);
          // Force refresh if cache read fails
          needPricesRefresh = true;
        }
      }

      // If either cache needs refreshing, fetch from API
      if (needHistoryRefresh || needPricesRefresh) {
        console.log(`[${new Date().toISOString()}] Fetching fresh data from API`);

        // Fetch data from API
        const api = useApiWithAuth()
        const apiData = await api.get('/api/nse/gs_record');

        // Add defensive check to ensure data is an array
        if (!Array.isArray(apiData)) {
          console.warn(`[${new Date().toISOString()}] API response is not an array:`, apiData);
          resolve();
          return;
        }

        // Process the data
        const processedData = apiData.map((item: any) => ({
          ...item,
          symbol: item.symbol ? item.symbol.replace('NSE:', '') : ''
        }));

        // If history refresh is needed, update history cache
        if (needHistoryRefresh && gsHistoryDb.value) {
          console.log(`[${new Date().toISOString()}] Updating history cache`);
          await storeDataInPouchDB(gsHistoryDb.value, processedData, 'history');
          historyData = processedData;
        }

        // If prices refresh is needed, update prices cache
        if (needPricesRefresh && gsCurrentPricesDb.value) {
          console.log(`[${new Date().toISOString()}] Updating current prices cache`);
          await storeDataInPouchDB(gsCurrentPricesDb.value, processedData, 'prices');
          currentPricesData = processedData;
        }
      }

      // Combine data for use in the application (prioritize current prices)
      const combinedData = [...historyData];

      // If we have current prices data, use it to update the combined data
      if (currentPricesData.length > 0) {
        // Create a map of symbols to current price records
        const priceMap = new Map();
        currentPricesData.forEach(item => {
          if (item.symbol) {
            priceMap.set(item.symbol, item);
          }
        });

        // Update combined data with current prices
        combinedData.forEach(item => {
          const currentPriceItem = priceMap.get(item.symbol);
          if (currentPriceItem) {
            // Update price-related fields
            item.price = currentPriceItem.price;
            item.currentValue = currentPriceItem.currentValue;
            // Add any other fields that should be updated from current prices
          }
        });
      }

      // Filter data to only include symbols in the portfolio
      if (folioData.value && folioData.value.length > 0) {
        const folioSymbols = new Set(folioData.value.map((item: any) => item.symbol));
        const filteredData = combinedData.filter(item => folioSymbols.has(item.symbol));
        console.log(`[${new Date().toISOString()}] Filtered data to ${filteredData.length} portfolio symbols`);
      }

      console.log(`[${new Date().toISOString()}] gs_sheet function completed successfully`);
      resolve();
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error in gs_sheet function:`, error);
      reject(error);
    }
  });
}

// Fetch NSE Records with optimized caching strategy
const fetchNSERecords = async (): Promise<void> => {
  return new Promise<void>(async (resolve, reject) => {
    try {
      console.log(`[${new Date().toISOString()}] Starting fetchNSERecords`);

      // Sync legacy db with our new database for compatibility
      if (nseDb.value) {
        db.value = nseDb.value;
      }

      // Try to get data from PouchDB first
      if (nseDb.value) {
        try {
          const cachedData = await getAllFromPouchDB(nseDb.value);

          if (cachedData && cachedData.length > 0) {
            console.log(`[${new Date().toISOString()}] Using cached NSE records (${cachedData.length} records)`);
            nseRecords_all.value = cachedData;
            resolve();
            return;
          }
        } catch (cacheError) {
          console.warn(`[${new Date().toISOString()}] Error reading NSE cache:`, cacheError);
        }
      }

      // If no cached data or cache read failed, fetch from API
      console.log(`[${new Date().toISOString()}] Fetching fresh NSE records from API`);
      const api = useApiWithAuth()
      const data = await api.get('/api/nse/get_nse');

      if (!Array.isArray(data)) {
        console.warn(`[${new Date().toISOString()}] NSE API response is not an array:`, data);
        resolve();
        return;
      }

      nseRecords_all.value = data;

      // Store the records in PouchDB if available
      if (nseDb.value) {
        // Process records before storing
        const processedRecords = data.map(record => {
          const { __v, ...cleanRecord } = record;

          // Ensure all numeric fields are properly converted to numbers
          return {
            ...cleanRecord,
            // Convert numeric fields to ensure they're numbers
            PREV_CLOSE: Number(cleanRecord.PREV_CLOSE || 0),
            OPEN_PRICE: Number(cleanRecord.OPEN_PRICE || 0),
            HIGH_PRICE: Number(cleanRecord.HIGH_PRICE || 0),
            LOW_PRICE: Number(cleanRecord.LOW_PRICE || 0),
            LAST_PRICE: Number(cleanRecord.LAST_PRICE || 0),
            CLOSE_PRICE: Number(cleanRecord.CLOSE_PRICE || 0),
            AVG_PRICE: Number(cleanRecord.AVG_PRICE || 0),
            TTL_TRD_QNTY: Number(cleanRecord.TTL_TRD_QNTY || 0),
            TURNOVER_LACS: Number(cleanRecord.TURNOVER_LACS || 0),
            NO_OF_TRADES: Number(cleanRecord.NO_OF_TRADES || 0),
            DELIV_QTY: Number(cleanRecord.DELIV_QTY || 0),
            DELIV_PER: Number(cleanRecord.DELIV_PER || 0)
          };
        });

        // Store in PouchDB
        console.log(`[${new Date().toISOString()}] Storing ${processedRecords.length} NSE records in cache`);
        await storeDataInPouchDB(nseDb.value, processedRecords, 'prices');
      }

      console.log(`[${new Date().toISOString()}] fetchNSERecords completed successfully`);
      resolve();
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error in fetchNSERecords:`, error);
      reject(error);
    }
  });
}

// Setup PouchDB refresh function to periodically refresh data
const setupPouchDBRefresh = async (): Promise<void> => {
  try {
    // Only refresh current prices (not history) on the regular timer
    // History data is refreshed once per day when needed

    // Fetch fresh NSE data
    const api = useApiWithAuth()
    const data = await api.get('/api/nse/get_nse');

    if (Array.isArray(data)) {
      nseRecords_all.value = data;

      // Store NSE records in PouchDB if available
      if (nseDb.value) {
        // Process records before storing
        const processedRecords = data.map(record => {
          const { __v, ...cleanRecord } = record;

          // Ensure all numeric fields are properly converted to numbers
          return {
            ...cleanRecord,
            // Convert numeric fields to ensure they're numbers
            PREV_CLOSE: Number(cleanRecord.PREV_CLOSE || 0),
            OPEN_PRICE: Number(cleanRecord.OPEN_PRICE || 0),
            HIGH_PRICE: Number(cleanRecord.HIGH_PRICE || 0),
            LOW_PRICE: Number(cleanRecord.LOW_PRICE || 0),
            LAST_PRICE: Number(cleanRecord.LAST_PRICE || 0),
            CLOSE_PRICE: Number(cleanRecord.CLOSE_PRICE || 0),
            AVG_PRICE: Number(cleanRecord.AVG_PRICE || 0),
            TTL_TRD_QNTY: Number(cleanRecord.TTL_TRD_QNTY || 0),
            TURNOVER_LACS: Number(cleanRecord.TURNOVER_LACS || 0),
            NO_OF_TRADES: Number(cleanRecord.NO_OF_TRADES || 0),
            DELIV_QTY: Number(cleanRecord.DELIV_QTY || 0),
            DELIV_PER: Number(cleanRecord.DELIV_PER || 0)
          };
        });

        // Store in PouchDB
        await storeDataInPouchDB(nseDb.value, processedRecords, 'prices');

        // Update legacy db reference for compatibility
        db.value = nseDb.value;
      }
    }

    // Refresh current prices (not history)
    await gs_sheet();
  } catch (error) {
    console.error(`Error during data refresh:`, error);
  }
}

const toggleCnNote = async () => {
  try {
    showCnNote.value = !showCnNote.value
    if (showCnNote.value) {
      // Fetch CN Notes data
      await fetchCnNoteData()
    }
  } catch (err) {
    error.value = 'Error fetching CN notes'
    console.error('CN Notes Error:', err)
  }
}

const toggleAllRec = async () => {
  try {
    showAllRec.value = !showAllRec.value
    // The NSEAllRecordsModal component will fetch the data when shown
  } catch (err) {
    error.value = 'Error fetching all records'
    console.error('All Records Error:', err)
  }
}

const handleSettings = () => {
  navigateTo('/settings')
}

const handleProfile = () => {
  navigateTo('/profile')
}

// Use centralized logout composable
const { performLogout } = useLogout()

const handleLogout = async () => {
  try {
    await performLogout()
  } catch (err) {
    error.value = 'Error during logout'
    console.error('Logout Error:', err)
  }
}

const updateRowData = (rowData: any) => {
  // Update rowData with values from documentForm
  rowData.cn_no = documentForm.value.cn_no
  rowData.broker = documentForm.value.broker
  rowData.pdate = documentForm.value.cn_date
  rowData.folio = documentForm.value.folio
  rowData.type = documentForm.value.type

  // Check if rowData already exists in nseRecords
  const existingRecordIndex = nseRecords.value.findIndex(record =>
    record.symbol === rowData.symbol && record.rid === rowData.rid
  )

  if (existingRecordIndex !== -1) {
    nseRecords.value[existingRecordIndex] = rowData
  } else {
    nseRecords.value.push(rowData)
  }

  // Reset tableData with the same values
  tableData.value = {
    cn_no: '',
    symbol: '',
    price: '',
    qnty: '',
    amt: '',
    brokerage: '',
    broker: rowData.broker,
    pdate: rowData.pdate,
    namt: '',
    folio: rowData.folio,
    type: rowData.type,
    rid: "RID" + Date.now(),
    sector: ''
  }

  // Sum the namt values in nseRecords and update famt in documentForm
  const totalNamt = nseRecords.value.reduce((sum, record) =>
    sum + parseFloat(record.namt || 0), 0
  )
  documentForm.value.famt = totalNamt

  // Update famt1 input if it exists
  const famt1Input = document.getElementById('famt1') as HTMLInputElement
  if (famt1Input) {
    famt1Input.value = totalNamt.toString()
  }

  // Focus on first input in the data table
  nextTick(() => {
    const firstInput = document.querySelector('.data-table tbody tr:first-child td input') as HTMLInputElement
    if (firstInput) {
      firstInput.focus()
    }
  })
}

const deleteRowData = (rowData: any) => {
  if (!confirm('Are you sure you want to delete this row?')) return

  const existingRecordIndex = nseRecords.value.findIndex(record =>
    record.symbol === rowData.symbol && record.rid === rowData.rid
  )

  if (existingRecordIndex !== -1) {
    nseRecords.value.splice(existingRecordIndex, 1)
  }

  // Recalculate total net amount
  const totalNamt = nseRecords.value.reduce((sum, record) =>
    sum + parseFloat(record.namt || 0), 0
  )
  documentForm.value.famt = totalNamt

  // Update famt1 input if it exists
  const famt1Input = document.getElementById('famt1') as HTMLInputElement
  if (famt1Input) {
    famt1Input.value = totalNamt.toString()
  }

  // Focus on first input in the data table
  nextTick(() => {
    const firstInput = document.querySelector('.data-table tbody tr:first-child td input') as HTMLInputElement
    if (firstInput) {
      firstInput.focus()
    }
  })
}

const formatNumber = (value: number, decimals: number = 2) => {
  return value.toFixed(decimals)
}

const initTradingViewWidget = () => {
  if (!tradingViewContainer.value) return

  // Clear container first
  tradingViewContainer.value.innerHTML = ''

  // Create script element
  const script = document.createElement('script')
  script.src = 'https://s3.tradingview.com/external-embedding/embed-widget-advanced-chart.js'
  script.type = 'text/javascript'
  script.async = true

  // Set widget configuration
  script.innerHTML = `
    {
      "autosize": true,
      "symbol": "BSE:SENSEX",
      "interval": "D",
      "timezone": "Asia/Kolkata",
      "theme": "light",
      "style": "2",
      "locale": "en",
      "allow_symbol_change": true,
      "calendar": false,
      "support_host": "https://www.tradingview.com"
    }
  `

  // Append script to container
  tradingViewContainer.value.appendChild(script)
}

const { getMonthlySummary } = useFolio()

const initMonthlyChart = async () => {
  if (!monthlyChart.value) {
    return
  }

  const ctx = monthlyChart.value.getContext('2d')
  if (!ctx) {
    return
  }

  try {
    // Try to get data from cache first
    let gsSheetData: any;

    try {
      // Use history data for the chart since it contains all records
      if (gsHistoryDb.value) {
        const cachedData = await getAllFromPouchDB(gsHistoryDb.value);
        if (cachedData && cachedData.length > 0) {
          console.log(`[${new Date().toISOString()}] Using cached data for monthly chart (${cachedData.length} records)`);
          gsSheetData = cachedData;
        }
      }

      // If no cached data, fetch from API
      if (!gsSheetData || gsSheetData.length === 0) {
        console.log(`[${new Date().toISOString()}] Fetching fresh data for monthly chart`);
        const api = useApiWithAuth()
        gsSheetData = await api.get('/api/nse/gs_record');
      }
    } catch (error) {
      console.error(`[${new Date().toISOString()}] Error getting data for monthly chart:`, error);
      // Fallback to API if cache fails
      const api = useApiWithAuth()
      gsSheetData = await api.get('/api/nse/gs_record');
    }

    // Process data for monthly chart
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
    const currentYear = new Date().getFullYear()

    // Initialize monthly data arrays
    const investmentData = Array(12).fill(0)
    const currentValueData = Array(12).fill(0)

    // Process Google Sheets data
    if (!Array.isArray(gsSheetData)) {
      console.warn(`[${new Date().toISOString()}] Data for monthly chart is not an array:`, gsSheetData);
    }

    if (Array.isArray(gsSheetData)) {
      gsSheetData.forEach((item: any) => {
        if (item.date) {
          const date = new Date(item.date)
          if (date.getFullYear() === currentYear) {
            const month = date.getMonth()
            investmentData[month] += Number(item.investment || 0)
            currentValueData[month] += Number(item.currentValue || 0)
          }
        }
      })
    }

    // If no Google Sheets data, fall back to folio data
    if (investmentData.every(val => val === 0) && folioData.value && folioData.value.length > 0) {
      const monthlySummaryData = getMonthlySummary(folioData.value)

      // Convert YYYY-MM format to month names
      monthlySummaryData.forEach(item => {
        if (item.month && item.month.includes('-')) {
          const [year, month] = item.month.split('-')
          const monthIndex = parseInt(month) - 1 // Convert to 0-based index
          if (monthIndex >= 0 && monthIndex < 12) {
            investmentData[monthIndex] = item.sum_namt
            currentValueData[monthIndex] = item.sum_cval
          }
        } else {
          const monthIndex = months.indexOf(item.month)
          if (monthIndex >= 0) {
            investmentData[monthIndex] = item.sum_namt
            currentValueData[monthIndex] = item.sum_cval
          }
        }
      })
    }

    // Create chart configuration
    const chartConfig: {
      type: 'bar',
      data: ChartData<'bar'>,
      options: ChartOptions<'bar'>
    } = {
      type: 'bar',
      data: {
        labels: months,
        datasets: [
          {
            label: 'Investment Value',
            data: investmentData.map(val => Number(val.toFixed(2))),
            backgroundColor: 'rgba(79, 70, 229, 0.7)',
            borderColor: '#4F46E5',
            borderWidth: 1
          },
          {
            label: 'Current Value',
            data: currentValueData.map(val => Number(val.toFixed(2))),
            backgroundColor: 'rgba(34, 197, 94, 0.7)',
            borderColor: '#22C55E',
            borderWidth: 1
          }
        ]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
          y: {
            beginAtZero: true,
            title: {
              display: true,
              text: 'Amount (₹)',
              font: {
                size: 14,
                weight: 'bold'
              }
            },
            ticks: {
              callback: function(value) {
                return '₹' + value.toLocaleString('en-IN')
              }
            }
          },
          x: {
            title: {
              display: true,
              text: 'Month',
              font: {
                size: 14,
                weight: 'bold'
              }
            }
          }
        },
        plugins: {
          tooltip: {
            callbacks: {
              label: function(context) {
                return '₹' + (context.raw as number).toLocaleString('en-IN')
              }
            }
          },
          legend: {
            position: 'top',
            labels: {
              font: {
                size: 12
              }
            }
          }
        },
        barPercentage: 0.8,
        categoryPercentage: 0.7
      }
    }

    // Create or update chart
    if (chartInstance.value) {
      chartInstance.value.data = chartConfig.data
      chartInstance.value.update()
    } else {
      chartInstance.value = new Chart(ctx, chartConfig)
    }
  } catch (error) {
    // Handle error silently
  }

  // Chart configuration is now handled in the initMonthlyChart function
}

// Timer reference
let timer: ReturnType<typeof setInterval> | null = null;

// Handler for price changes calculated by the PriceChangeCalculator component
const handlePriceChangesCalculated = (data: { priceGainers: any[], priceLosers: any[] }) => {
  historyPriceGainers.value = data.priceGainers;
  historyPriceLosers.value = data.priceLosers;
}

// Watch for changes in folioData to recalculate price changes
watch(folioData, () => {
  if (priceChangeCalculator.value && folioData.value.length > 0) {
    nextTick(() => {
      priceChangeCalculator.value?.calculatePriceChanges();
    });
  }
});

// Lifecycle hooks
onMounted(async () => {
  console.log(`[${new Date().toISOString()}] Component mounted`);

  // Initialize our new PouchDB service
  await initPouchDBs();

  // Initialize timer service
  initTimerService();

  // Sync legacy references for compatibility
  if (nseDb.value) db.value = nseDb.value;
  if (gsCurrentPricesDb.value) gsDb.value = gsCurrentPricesDb.value;

  // Fetch initial data
  await fetchNSEData();

  // Initialize components
  initTradingViewWidget();
  await nextTick(); // Ensure DOM is updated
  initMonthlyChart();

  // Calculate price changes based on history data
  if (priceChangeCalculator.value) {
    await nextTick(); // Ensure the component is mounted
    priceChangeCalculator.value.calculatePriceChanges();
  }

  // Initialize timer service
  initTimerService();

  // Start timer with refresh callback
  startTimer(() => {
    setupPouchDBRefresh();

    // Recalculate price changes after refresh
    if (priceChangeCalculator.value) {
      priceChangeCalculator.value.calculatePriceChanges();
    }
  });
});

onUnmounted(() => {
  // Save timer state before unmounting
  if (isBrowser && timerActive.value) {
    try {
      // Force save the timer state
      const state = {
        minutes: timerMinutes.value,
        seconds: timerSeconds.value,
        timestamp: Date.now(),
        active: timerActive.value
      };

      localStorage.setItem(TIMER_STATE_KEY, JSON.stringify(state));
    } catch (error) {
      console.error(`Error saving timer state before unmounting:`, error);
    }
  }

  // No need to close PouchDB connections as they're now managed by the service
  // and persist across page navigation
})

const updateCountry = (index: number) => {
  nseRecords.value[index] = { ...nseRecords.value[index], ...tableData.value }

  tableData.value = {
    symbol: '',
    price: '',
    qnty: '',
    amt: '',
    brokerage: '',
    cn_no: (document.getElementById('cn_no') as HTMLInputElement)?.value || '',
    broker: (document.getElementById('broker') as HTMLInputElement)?.value || '',
    pdate: (document.getElementById('cn_date') as HTMLInputElement)?.value || '',
    namt: '',
    folio: (document.getElementById('folio') as HTMLInputElement)?.value || '',
    type: (document.getElementById('type') as HTMLInputElement)?.value || '',
    rid: "RID" + Date.now(),
    sector: ''
  }
}

const openModal = () => {
  showCnNote.value = true;
  nextTick(() => {
    const cnNoInput = document.getElementById('cn_no') as HTMLInputElement;
    if (cnNoInput) {
      cnNoInput.focus();
    }
  });
}

const closeModal = () => {
  nseRecords.value = [];
  documentForm.value = {
    cn_no: '',
    broker: '',
    cn_date: '',
    folio: '',
    type: '',
    famt: 0
  };
  showCnNote.value = false;
  showAllRec.value = false;
}

const onSubmit = async () => {
  // Get form values
  const othChg1Element = document.getElementById('oth_chg1') as HTMLInputElement;
  const famt1Element = document.getElementById('famt1') as HTMLInputElement;
  const formData = {
    ...documentForm.value,
    famt: famt1Element?.value,
    oth_chg: othChg1Element?.value
  };

  try {
    const requestData = {
      formData,
      nseRecords: nseRecords.value
    };

    const api = useApiWithAuth()
    let response;
    if (!isEdit.value) {
      response = await api.post('/api/nse/submit', requestData);
    } else {
      response = await api.put('/api/nse/update', requestData);
    }
    closeModal();
  } catch (error) {
    alert('Error submitting folio data. Please try again.');
  }
};

const handleKeyDown = (event: KeyboardEvent) => {
  const focusedElement = document.activeElement as HTMLElement;
  if (event.key === 'Enter') {
    event.preventDefault();
    if (focusedElement.tagName === 'INPUT') {
      const nextElement = getNextVisibleElement(focusedElement);
      if (nextElement) {
        nextElement.focus();
      }
    }
  } else if (event.key === 'Escape') {
    if (confirm('Are you sure you want to close the modal?')) {
      showPopup.value = false;
    }
  }
};

const placeCaret = (element: HTMLElement) => {
  const range = document.createRange();
  const selection = window.getSelection();
  if (selection) {
    range.selectNodeContents(element);
    range.collapse(false);
    selection.removeAllRanges();
    selection.addRange(range);
  }
};

const getNextVisibleElement = (current: HTMLElement): HTMLElement | null => {
  const focusableElements = Array.from(
    document.querySelectorAll('input, [contenteditable="true"]')
  ) as HTMLElement[];
  const index = focusableElements.indexOf(current);
  for (let i = index + 1; i < focusableElements.length; i++) {
    if (focusableElements[i].offsetParent !== null) {
      return focusableElements[i];
    }
  }
  return null;
};



const calculateAmount = (row: any) => {
  if (row.price && row.qnty) {
    row.amt = row.price * row.qnty;
  }
};

const calculatenAmount = (row: any) => {
  if (row.amt !== undefined && row.brokerage !== undefined) {
    row.namt = parseFloat(row.amt) + (parseFloat(row.brokerage) * parseFloat(row.qnty));
  }
};

const updateCharges = () => {
  const othChgElement = document.getElementById('oth_chg') as HTMLInputElement;
  const othChg1Element = document.getElementById('oth_chg1') as HTMLInputElement;
  const famtElement = document.getElementById('famt') as HTMLInputElement;
  const famt1Element = document.getElementById('famt1') as HTMLInputElement;

  if (othChg1Element && famt1Element) {
    othChgElement.value = othChg1Element.value;

    const othChgValue = parseFloat(othChg1Element.value) || 0;
    const totalNamt = nseRecords.value.reduce((sum, record) => sum + parseFloat(record.namt || 0), 0);
    const sumValue = othChgValue + totalNamt;

    famtElement.value = Number(sumValue.toString()).toFixed(2);
    famt1Element.value = Number(sumValue.toString()).toFixed(2);
  }
};

const editCNNote = (note: any) => {
  isEdit.value = true;
  documentForm.value = {
    id: note._id,
    cn_no: note.cn_no,
    cn_date: formatDate(note.cn_date),
    broker: note.broker,
    type: note.type,
    folio: note.folio,
    oth_chg: note.oth_chg,
    famt: note.famt
  };

  oth_chg1.value = note.oth_chg;
  famt1.value = note.famt;
  nseRecords.value = note.Folio_rec;
  openModal();
};

const formatDate = (date: Date | string | undefined): string => {
  if (!date) return '';
  const d = new Date(date);
  return d.toISOString().split('T')[0];
};

const getInvestmentClass = (): string => {
  return currentValue.value > totalInvestment.value ? 'text-green-500' : 'text-red-500';
};

const updateTopPerformers = () => {
  // Sort by gainLoss for gainers (highest to lowest)
  priceGainers.value = [...folioSummary.value]
    .filter(item => item.gainLoss > 0)
    .sort((a, b) => b.gainLoss - a.gainLoss)
    .slice(0, 5)
    .map(item => ({
      symbol: item.symbol,
      difference: item.gainLoss,
      percentageChange: (item.gainLoss / item.namt) * 100,
      currentPrice: item.cval / item.qnty, // Current price per share
      investmentPrice: item.namt / item.qnty // Investment price per share
    }))

  // Sort by gainLoss for losers (lowest to highest)
  priceLosers.value = [...folioSummary.value]
    .filter(item => item.gainLoss < 0)
    .sort((a, b) => a.gainLoss - b.gainLoss)
    .slice(0, 5)
    .map(item => ({
      symbol: item.symbol,
      difference: item.gainLoss,
      percentageChange: (item.gainLoss / item.namt) * 100,
      currentPrice: item.cval / item.qnty, // Current price per share
      investmentPrice: item.namt / item.qnty // Investment price per share
    }))
}
const selectedCNNote = ref(null)

const closeCNNoteModal = () => {
  showCnNote.value = false
  selectedCNNote.value = null
  isEdit.value = false
}

const deleteCNNote = async (id: string) => {
  try {
    if (confirm('Are you sure you want to delete this CN Note?')) {
      const api = useApiWithAuth()
      await api.delete(`/api/nse/cn_note/${id}`)

      // Refresh CN Notes data
      await fetchCnNoteData()

      // Show success message
      alert('CN Note deleted successfully!')
    }
  } catch (err: any) {
    alert(`Error: ${err.message || 'Failed to delete CN Note'}`)
  }
}



const handleCNNoteSubmit = async (payload: any) => {
  try {

    // Validate CN Number
    if (!payload.formData.cn_no || payload.formData.cn_no.trim() === '') {
      alert('CN Number cannot be empty')
      return
    }

    // Ensure all required fields are present in each record
    const requiredFields = ['cn_no', 'broker', 'pdate', 'folio', 'type', 'rid', 'sector']
    const missingFields: string[] = []

    payload.recordsData.forEach((record: any, index: number) => {
      requiredFields.forEach(field => {
        if (!record[field]) {
          missingFields.push(`Record #${index + 1} is missing ${field}`)
        }
      })
    })

    if (missingFields.length > 0) {
      alert(`Missing required fields:\n${missingFields.join('\n')}`)
      return
    }

    try {
      const api = useApiWithAuth()
      if (isEdit.value) {
        const response = await api.post('/api/nse/updnse_data', payload)
      } else {
        const response = await api.post('/api/nse/addnse_data', payload)
      }

      // Show success message
      alert('CN Note saved successfully!')

      closeCNNoteModal()
      // Refresh data if needed
      await fetchNSEData()
    } catch (apiError: any) {
      // Handle specific error cases
      if (apiError.response?.data?.message) {
        // Extract the error message from the API response
        const apiErrorMessage = apiError.response.data.message

        if (apiErrorMessage.includes('duplicate key error') ||
            apiErrorMessage.includes('already exists')) {
          alert(`Error: A CN Note with this CN Number already exists. Please use a different CN Number.`)
        } else {
          alert(`Error: ${apiErrorMessage}`)
        }
      } else {
        // Generic error message
        alert(`Error: ${apiError.message || 'Failed to save CN Note'}`)
      }

      throw apiError // Re-throw to be caught by the outer catch block
    }
  } catch (err: any) {
    // Set error state but don't show another alert (already shown in inner catch)
    error.value = err.message || 'Error saving CN Note'
  }
}

// Document-related functions removed
</script>