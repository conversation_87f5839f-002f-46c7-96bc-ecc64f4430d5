<template>
  <div class="max-w-10xl mx-auto py-4 sm:py-6 px-2 sm:px-6 lg:px-8">
    <!-- Cancelled Bill Message -->
    <div v-if="isCancelledBill" class="bg-red-50 border-l-4 border-red-500 p-4 mb-6 rounded-md shadow">
      <div class="flex items-center">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-lg font-medium text-red-800">This bill has been cancelled and cannot be edited</h3>
          <div class="mt-2 text-red-700">
            <p>You will be redirected to the bills list in a few seconds.</p>
            <p class="mt-1">If you are not redirected automatically, <a href="/inventory/bills"
                class="font-medium underline">click here</a> to go back to the bills list.</p>
          </div>
        </div>
      </div>
    </div>

    <div v-if="!isCancelledBill" class="px-0 relative">
      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-6 mb-6">
        <!-- Bill Form (8 columns on desktop, full width on mobile) -->
        <div class="bg-white p-3 md:p-4 rounded-lg shadow col-span-1 md:col-span-12">
          <div class="flex justify-between">
            <h2 class="text-xl font-semibold mb-4">
              {{ isEditMode ? 'Edit' : 'Create New' }}
              {{ billForm.type === 'CREDIT NOTE' ? 'Credit Note' : billForm.type === 'DEBIT NOTE' ? 'Debit Note' :
              'Invoice' }}
            </h2>
            <!-- Configuration hint for desktop and mobile settings button -->
            <div class="flex items-center gap-2">
              <!-- Desktop hint -->
              <div class="hidden md:flex text-sm text-gray-500 items-center">
                <kbd class="bg-gray-100 px-2 py-1 rounded text-xs mr-1">F11</kbd>
                <span>for settings</span>
              </div>
              <!-- ✅ Enhanced Mobile settings button -->
              <button
                type="button"
                @click="showBillConfigModal = true"
                class="md:hidden bg-blue-100 hover:bg-blue-200 text-blue-700 p-2 rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 transform hover:scale-105"
                title="Open Settings & Shortcuts (F11)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </button>
            </div>
          </div>
          <form @submit.prevent="submitBillForm">

            <!-- Three cards in a row for main form fields with optimized widths -->
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-4 mb-4">
              <!-- Card 1: Bill Information (25% width - 3 columns) -->
              <div ref="billInfoRef" class="lg:col-span-3 bg-white p-4 rounded-lg shadow-sm border border-blue-200">
                <div ref="billInfoRef" class="bg-white p-4 rounded-lg shadow-sm border border-blue-200">
                  <h3 class="text-md font-medium text-gray-800 mb-3 pb-2 border-b border-blue-200">Bill Information</h3>
                  <div class="space-y-3">
                    <!-- Bill Type -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Bill Type</label>
                      <select v-model="billForm.type"
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required>
                        <option value="">Select Bill Type</option>
                        <option value="SALES">SALES</option>
                        <option value="PURCHASE">PURCHASE</option>
                        <option value="DEBIT NOTE">DEBIT NOTE</option>
                        <option value="CREDIT NOTE">CREDIT NOTE</option>
                      </select>
                    </div>

                    <!-- Bill Number -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Bill Number</label>
                      <input v-model="billForm.bno" type="text"
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required />
                    </div>

                    <!-- Bill Date -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Bill Date</label>
                      <input v-model="billForm.bdate" type="date"
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        required />
                    </div>

                    <!-- Credit/Debit Note specific fields - only visible for credit/debit notes -->
                    <template v-if="billForm.type === 'CREDIT NOTE' || billForm.type === 'DEBIT NOTE'">
                      <!-- Reason for Note -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Reason for {{ billForm.type ===
                          'CREDIT NOTE' ? 'Credit' : 'Debit' }} Note</label>
                        <input v-model="billForm.reasonForNote" type="text"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      </div>

                      <!-- Original Bill Number -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Original Bill Number</label>
                        <input v-model="billForm.originalBillNo" type="text"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      </div>

                      <!-- Original Bill Date -->
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Original Bill Date</label>
                        <input v-model="billForm.originalBillDate" type="date"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      </div>
                    </template>
                  </div>
                </div>
              </div>

              <!-- Card 2: Party Details (45% width - 5 columns) -->
              <div ref="partyDetailsRef"
                class="lg:col-span-5 bg-white p-4 rounded-lg shadow-sm border border-green-200">
                <h3 class="text-md font-medium text-gray-800 mb-3 pb-2 border-b border-green-200">Party Details</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                  <!-- Party Name -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Party Name</label>
                    <input v-model="billForm.partyName" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required list="partyList" @change="handlePartySelection" @blur="openOrderDispatchModal"
                      @keydown.alt.67.exact="showPartyModal = true" @keydown.ctrl.enter="editSelectedParty"
                      @keydown="handlePartyNameKeydown" />
                    <datalist id="partyList">
                      <option v-for="party in inventoryData?.parties || []" :key="party._id" :value="party.supply">
                      </option>
                      <option value="Create New Party"></option>
                    </datalist>
                  </div>

                  <!-- Party Address -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Party Address</label>
                    <input v-model="billForm.partyAddress" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- Firm GSTIN Selection -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      Firm GSTIN
                      <span v-if="inventoryData?.firmGSTs?.length > 1" class="text-xs text-gray-500">
                        ({{ inventoryData.firmGSTs.length }} available)
                      </span>
                    </label>

                    <!-- Multi-GST dropdown if firm has multiple GSTs -->
                    <select v-if="inventoryData?.firmGSTs?.length > 1" v-model="selectedFirmGSTIndex"
                      @change="onFirmGSTChange"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      <option v-for="(gst, index) in inventoryData.firmGSTs" :key="index" :value="index"
                        :class="{ 'font-bold': gst.isPrimary }">
                        {{ gst.gstNumber }} - {{ gst.state }} {{ gst.isPrimary ? '(Primary)' : `(${gst.locationName})`
                        }}
                      </option>
                    </select>

                    <!-- Single GST display -->
                    <input v-else :value="selectedFirmGST?.gstNumber || 'No GST configured'" type="text" readonly
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm bg-gray-50 text-gray-600" />
                  </div>

                  <!-- Party GSTIN Selection -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                      Party GSTIN
                      <span v-if="selectedPartyGSTs?.length > 1" class="text-xs text-gray-500">
                        ({{ selectedPartyGSTs.length }} available)
                      </span>
                    </label>

                    <!-- Multi-GST dropdown if party has multiple GSTs -->
                    <select v-if="selectedPartyGSTs?.length > 1" v-model="selectedPartyGSTIndex"
                      @change="onPartyGSTChange"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      <option v-for="(gst, index) in selectedPartyGSTs" :key="index" :value="index"
                        :class="{ 'font-bold': gst.isDefault }">
                        {{ gst.gstNumber }} - {{ gst.state }} {{ gst.isPrimary ? '(Primary)' : '' }}
                      </option>
                    </select>

                    <!-- Single GST input if party has only one GST -->
                    <input v-else v-model="billForm.partyGstin" type="text"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                  </div>

                  <!-- GST Transaction Type Indicator -->
                  <div v-if="selectedFirmGST && selectedPartyGST && selectedFirmGST.state && selectedPartyGST.state"
                    class="md:col-span-2 bg-gray-50 p-2 rounded text-sm">
                    <div class="flex items-center justify-between">
                      <span class="font-medium">Transaction Type:</span>
                      <span :class="transactionTypeClass">
                        {{ transactionTypeText }}
                      </span>
                    </div>
                    <div class="text-xs text-gray-600 mt-1">
                      {{ selectedFirmGST.state }} ↔ {{ selectedPartyGST.state }}
                    </div>
                  </div>

                  <!-- Add Party GST Button -->
                  <div v-if="billForm.partyName" class="md:col-span-2 mt-2">
                    <button type="button" @click="openAddPartyGSTModal"
                      class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded hover:bg-green-200">
                      + Add New Party GST
                    </button>
                  </div>
                </div>
              </div>

              <!-- Card 3: Amount Details (30% width - 4 columns) -->
              <div ref="amountDetailsRef"
                class="lg:col-span-4 bg-white p-4 rounded-lg shadow-sm border border-purple-200">
                <h3 class="text-md font-medium text-gray-800 mb-3 pb-2 border-b border-purple-200">Amount Details</h3>
                <div class="space-y-3">
                  <!-- Gross Total -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Gross Total</label>
                    <input v-model.number="billForm.gtot" type="number" step="0.01" min="0"
                      class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required />
                  </div>

                  <!-- Tax Details (CGST, SGST, IGST) -->
                  <div class="grid grid-cols-3 gap-2">
                    <!-- CGST -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">CGST</label>
                      <input :value="billForm.cgst.toFixed(2)" type="text" readonly
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-50" />
                    </div>

                    <!-- SGST -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">SGST</label>
                      <input :value="billForm.sgst.toFixed(2)" type="text" readonly
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-50" />
                    </div>

                    <!-- IGST -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">IGST</label>
                      <input :value="billForm.igst.toFixed(2)" type="text" readonly
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-50" />
                    </div>
                  </div>

                  <!-- Round Off and Net Total -->
                  <div class="grid grid-cols-2 gap-2">
                    <!-- Round Off -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Round Off</label>
                      <input v-model.number="billForm.rof" type="number" step="0.01"
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                    </div>

                    <!-- Net Total -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Net Total</label>
                      <input :value="billForm.ntot.toFixed(2)" type="text"
                        class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-50 font-bold text-blue-700"
                        readonly />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- File Upload Section (Only for PURCHASE and DEBIT NOTE) -->
            <div v-if="billForm.type === 'PURCHASE' || billForm.type === 'DEBIT NOTE'"
              class="bg-white p-4 rounded-lg shadow-sm border border-orange-200 mt-4">
              <h3 class="text-md font-medium text-gray-800 mb-3 pb-2 border-b border-orange-200">
                Document Attachment
                <span class="text-xs text-gray-500 ml-2">(PDF only, max 200KB)</span>
              </h3>

              <div class="space-y-3">
                <!-- File Upload Input -->
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-1">
                    Upload Supporting Document
                  </label>
                  <input ref="fileInput" type="file" accept=".pdf" @change="handleFileUpload"
                    class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                  <p class="text-xs text-gray-500 mt-1">
                    Only PDF files are allowed. Maximum file size: 200KB
                  </p>
                </div>

                <!-- Current File Information -->
                <div v-if="billForm.attachmentUrl" class="mt-3 p-3 border border-gray-200 rounded-md bg-gray-50">
                  <div class="flex items-center justify-between">
                    <div>
                      <h4 class="text-sm font-medium text-gray-700">Current Attachment</h4>
                      <p class="text-xs text-gray-500 mt-1">File ID: {{ billForm.attachmentFileId || 'Not available' }}
                      </p>
                    </div>
                    <div class="flex space-x-2">
                      <a :href="billForm.attachmentUrl" target="_blank"
                        class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-green-700 bg-green-100 hover:bg-green-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View
                      </a>
                      <button type="button" @click="removeAttachment"
                        class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-red-700 bg-red-100 hover:bg-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24"
                          stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Remove
                      </button>
                    </div>
                  </div>
                  <p class="text-xs text-gray-500 mt-2">Uploading a new file will replace the current one.</p>
                </div>

                <!-- Selected File Information -->
                <div v-if="selectedFile" class="mt-3 p-3 border border-green-200 rounded-md bg-green-50">
                  <h4 class="text-sm font-medium text-gray-700">New File Selected</h4>
                  <p class="text-xs text-gray-600 mt-1">{{ selectedFile.name }} ({{ formatFileSize(selectedFile.size)
                    }})</p>
                </div>

                <!-- Upload Progress -->
                <div v-if="isUploadingFile" class="mt-3 p-3 border border-blue-200 rounded-md bg-blue-50">
                  <div class="flex items-center">
                    <svg class="animate-spin -ml-1 mr-3 h-4 w-4 text-blue-600" xmlns="http://www.w3.org/2000/svg"
                      fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                    <span class="text-sm text-blue-700">Uploading file... This happens after bill creation.</span>
                  </div>
                </div>

                <!-- Upload Status Messages -->
                <div v-if="selectedFile && !isUploadingFile"
                  class="mt-3 p-3 border border-yellow-200 rounded-md bg-yellow-50">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-yellow-600 mr-2" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                          d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                      <span class="text-sm text-yellow-700">File will be uploaded after saving the bill.</span>
                    </div>
                    <button v-if="billId" type="button" @click="retryFileUpload"
                      class="inline-flex items-center px-2.5 py-1.5 border border-transparent text-xs font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                      Upload Now
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Stock Items Section (Full width) -->
            <div ref="stockItemsRef" class="bg-white p-4 rounded-lg shadow-sm border border-amber-200 mt-6">
              <div
                class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 sm:mb-2 gap-2 sm:gap-0">
                <h3 class="text-lg font-medium">Stock Items</h3>
                <div class="flex items-center gap-2 w-full sm:w-auto">
                  <button id="addNewItemButton" type="button" @click="addStockItem"
                    class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center gap-1 w-full sm:w-auto justify-center sm:justify-start">
                    <PlusCircleIcon class="h-5 w-5" /> Add New Item <span class="text-xs opacity-75 ml-1">(Alt+A)</span>
                  </button>
                  <!-- Desktop shortcuts hint and mobile settings button -->
                  <div class="flex items-center gap-2 ml-2">
                    <!-- Desktop hint -->
                    <div class="hidden md:flex text-xs text-gray-500 items-center">
                      <kbd class="bg-gray-100 px-1 py-0.5 rounded text-xs mr-1">F11</kbd>
                      <span>shortcuts</span>
                    </div>
                    <!-- Mobile settings button -->
                    <button
                      type="button"
                      @click="showBillConfigModal = true"
                      class="md:hidden bg-blue-100 hover:bg-blue-200 text-blue-700 p-1.5 rounded shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1 transition-all duration-200 transform hover:scale-105"
                      title="Open Settings & Shortcuts (F11)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>


              <!-- Stock Items Table -->
              <div class="overflow-x-auto -mx-3 md:mx-0">
                <table id="stockItemsTable" class="min-w-full divide-y divide-gray-200"
                  style="table-layout: fixed; width: 100%;">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 180px; min-width: 150px;">Item</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 60px;">Rate</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 70px; min-width: 50px;">Qty</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 50px;">UOM</th>
                      <th scope="col"
                        class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 60px;">GST (%)
                      </th>
                      <th scope="col" v-if="columnVisibility.mrp"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">MRP
                      </th>
                      <th scope="col" v-if="columnVisibility.expiryDate"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 100px; min-width: 100px;">Expiry Date
                      </th>
                      <th scope="col" v-if="columnVisibility.cgst"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">CGST</th>
                      <th scope="col" v-if="columnVisibility.sgst"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">SGST</th>
                      <th scope="col" v-if="columnVisibility.igst"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">IGST</th>
                      <th scope="col" v-if="columnVisibility.disc"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">Disc (%)
                      </th>
                      <th scope="col" v-if="columnVisibility.project"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 100px; min-width: 100px;">Project
                      </th>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 100px; min-width: 100px;">Total</th>
                      <th scope极客时间="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                        style="width: 80px; min-width: 80px;">Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-if="billForm.stockItems.length === 0">
                      <td colspan="14" class="px-3 py-3 text-center text-sm text-gray-500">No items added</td>
                    </tr>
                    <tr v-for="(item, index) in billForm.stockItems" :key="index" class="hover:bg-gray-50"
                      @focus="trackTableFocus('stockItems', index, 0)" tabindex="-1">
                      <td class="px-2 py-1 whitespace-nowrap relative">
                        <StockItemDropdown :key="`stock-dropdown-${billForm.type}-${index}`" v-model="item.item"
                          :stocks="reactiveStocks"
                          :input-class="'w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm'"
                          @stock-selected="(stockItem) => handleStockItemDropdownSelection(index, stockItem)"
                          @refresh-stocks="forceRefreshStocks" @keydown.alt.67.exact="showStockItemModal = true"
                          @keydown="handleStockItemKeydown" @focus="trackTableFocus('stockItems', index, 0)" required />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.rate" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" required
                          @focus="trackTableFocus('stockItems', index, 1)" />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.qty" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" @blur="showNarrationModal(index)"
                          @keydown.enter.prevent="addStockItem" required
                          @focus="trackTableFocus('stockItems', index, 2)" />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.uom" type="text"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @focus="trackTableFocus('stockItems', index, 3)" />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.grate" type="number" min="0" max="100" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" @focus="trackTableFocus('stockItems', index, 4)" />
                      </td>
                      <td v-if="columnVisibility.mrp" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.mrp" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td v-if="columnVisibility.expiryDate" class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.expiryDate" type="date"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td v-if="columnVisibility.cgst" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.cgst" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)"
                          :disabled="Firm_state?.toLowerCase()?.trim() !== inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state?.toLowerCase()?.trim()" />
                      </td>
                      <td v-if="columnVisibility.sgst" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.sgst" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)"
                          :disabled="Firm_state?.toLowerCase()?.trim() !== inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state?.toLowerCase()?.trim()" />
                      </td>
                      <td v-if="columnVisibility.igst" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.igst" type="number" min="0" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)"
                          :disabled="Firm_state?.toLowerCase()?.trim() === inventoryData?.parties?.find(p => p.supply === billForm.partyName)?.state?.toLowerCase()?.trim()" />
                      </td>
                      <td v-if="columnVisibility.disc" class="px-2 py-1 whitespace-nowrap">
                        <input v-model.number="item.disc" type="number" min="0" max="100" step="0.01"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
                          @input="calculateItemTotal(index)" @keydown.enter.prevent="addStockItem" />
                      </td>
                      <td v-if="columnVisibility.project" class="px-2 py-1 whitespace-nowrap">
                        <input v-model="item.project" type="text"
                          class="w-full px-2 py-1 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm" />
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                        ₹{{ item.total.toFixed(2) }}
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap">
                        <button type="button" @click="removeStockItem(index)"
                          class="text-red-600 hover:text-red-900 focus:outline-none"
                          @focus="trackTableFocus('stockItems', index, 10)">
                          <TrashIcon class="h-4 w-4 sm:h-5 sm:w-5" />
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Other Charges Section -->
            <div ref="otherChargesRef"
              class="bg-white p-3 md:p-4 rounded-lg shadow-sm border border-red-200 col-span-1 md:col-span-12 mt-4">
              <div
                class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 sm:mb-2 gap-2 sm:gap-0">
                <h3 class="text-lg font-medium">Other Charges</h3>
                <button id="addOtherChargesButton" type="button" @click="showOtherChargesModal = true"
                  class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center gap-1 w-full sm:w-auto justify-center sm:justify-start">
                  <PlusCircleIcon class="h-4 w-4" /> Add Other Charges <span
                    class="text-xs opacity-75 ml-1">(Alt+O)</span>
                </button>
              </div>

              <!-- Other Charges Table -->
              <div class="overflow-x-auto -mx-3 md:mx-0">
                <table id="otherChargesTable" class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description</th>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount
                      </th>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GST
                      </th>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total
                      </th>
                      <th scope="col"
                        class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-if="billForm.oth_chg.length === 0">
                      <td colspan="5" class="px-3 py-3 text-center text-sm text-gray-500">No charges added</td>
                    </tr>
                    <tr v-for="(charge, index) in billForm.oth_chg" :key="index" class="hover:bg-gray-50"
                      @focus="trackTableFocus('otherCharges', index, 0)" tabindex="-1">
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">{{ charge.description }}
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">₹{{
                        charge.oth_amt.toFixed(2) }}
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                        <span v-if="charge.oth_igst > 0">IGST: ₹{{ charge.oth_igst.toFixed(2) }}</span>
                        <span v-else>CGST: ₹{{ charge.oth_cgst.toFixed(2) }} + SGST: ₹{{ charge.oth_sgst.toFixed(2)
                          }}</span>
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">₹{{
                        charge.oth_tot.toFixed(2) }}
                      </td>
                      <td class="px-2 py-1 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                        <div class="flex space-x-2">
                          <button type="button" @click="editOtherCharge(index)"
                            class="text-blue-600 hover:text-blue-900 focus:outline-none"
                            @focus="trackTableFocus('otherCharges', index, 4)">
                            <PencilSquareIcon class="h-4 w-4 sm:h-5 sm:w-5" />
                          </button>
                          <button type="button" @click="removeOtherCharge(index)"
                            class="text-red-600 hover:text-red-900 focus:outline-none"
                            @focus="trackTableFocus('otherCharges', index, 4)">
                            <TrashIcon class="h-4 w-4 sm:h-5 sm:w-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Invoice Narration -->
            <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mt-4">
              <div>
                <label class="block text-md font-medium text-gray-800 mb-3">Invoice Narration - Additional
                  Notes/Comments</label>
                <textarea v-model="billForm.narration" rows="3"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter any additional notes or comments for this invoice..."></textarea>
              </div>
            </div>

            <!-- Form Buttons -->
            <div ref="actionButtonsRef"
              class="mt-6 flex flex-col sm:flex-row justify-end space-y-3 sm:space-y-0 sm:space-x-3">
              <!-- Reset button is always visible -->
              <button type="button" @click="resetBillForm"
                class="bg-red-500 hover:bg-red-600 text-white px-3 py-1 text-sm rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                <XCircleIcon class="h-4 w-4" />
                Reset <span class="text-xs opacity-75 ml-1">(Alt+R)</span>
              </button>

              <!-- Show Save/Update button when invoice is not yet submitted -->
              <template v-if="!invoiceSubmitted">
                <button type="submit" :disabled="isSubmitting"
                  class="bg-blue-500 hover:bg-blue-600 text-white px-3 py-1 text-sm rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                  <template v-if="isSubmitting">
                    <svg class="animate-spin h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none"
                      viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor"
                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                      </path>
                    </svg>
                    <span class="ml-1">{{ isEditMode ? 'Updating...' : 'Saving...' }}</span>
                  </template>
                  <template v-else>
                    <DocumentArrowDownIcon class="h-4 w-4" />
                    {{ isEditMode ? 'Update Invoice' : 'Save Invoice' }} <span
                      class="text-xs opacity-75 ml-1">(Alt+S)</span>
                  </template>
                </button>
              </template>

              <!-- Print Document button is visible when in edit mode or after submission -->
              <button v-if="isEditMode || invoiceSubmitted" type="button" @click="showPrintModal = true"
                class="bg-green-500 hover:bg-green-600 text-white px-3 py-1 text-sm rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                <PrinterIcon class="h-4 w-4" />
                Print {{ billForm.type === 'CREDIT NOTE' ? 'Credit Note' : billForm.type === 'DEBIT NOTE' ? 'Debit Note'
                : 'Invoice'
                }} <span class="text-xs opacity-75 ml-1">(Alt+P)</span>
              </button>

              <!-- Create New Document button is visible when in edit mode or after submission -->
              <button v-if="isEditMode || invoiceSubmitted" type="button" @click="createNewInvoice"
                class="bg-indigo-500 hover:bg-indigo-600 text-white px-3 py-1 text-sm rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 flex items-center justify-center gap-1 w-full sm:w-auto">
                <DocumentPlusIcon class="h-4 w-4" />
                Create New {{ billForm.type === 'CREDIT NOTE' ? 'Credit Note' : billForm.type === 'DEBIT NOTE' ? 'DebitNote' :'Invoice' }} <span class="text-xs opacity-75 ml-1">(Alt+N)</span>
              </button>
            </div>
          </form>
          <OtherChargesModal v-model:show="showOtherChargesModal" :firm-state="selectedFirmGST?.state || Firm_state"
            :party-state="selectedPartyGST?.state || billForm.partyState || ''" @add-charge="addOtherCharge"
            :edit-charge="currentCharge" :existing-charges="getAllExistingCharges()" />
        </div>

      </div>
    </div>
  </div>

  <!-- ✅ FIX: Mobile Floating Action Button for F11 Settings -->
  <div class="md:hidden fixed bottom-4 right-4 z-50">
    <button
      @click="showBillConfigModal = true"
      class="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-full shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 transform hover:scale-105"
      title="Open Settings & Shortcuts (F11)"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    </button>
  </div>

  <PartyModal v-model:show="showPartyModal" :indian-states="indianStates" :party-edit="partyToEdit"
    @submit-party="submitPartyForm" />
  <StockItemModal v-model:show="showStockItemModal" :inventory-data="inventoryData" :edit-item="editStockItem"
    @submit-stock-item="submitStockItemForm" />
  <StockRegModal v-model:show="showStockRegModal" :stock-reg="inventoryData?.stockReg || []"
    :party-name="billForm.partyName" :item-name="selectedStockItem" @close="showStockRegModal = false" />

  <NarrationModal v-model:show="isNarrationModalVisible" @submit="updateItemNarration"
    @close="showNarrationModal = false"
    :initialNarration="currentItemIndex >= 0 ? billForm.stockItems[currentItemIndex]?.item_narration || '' : ''" />
  <OrderDispatchModal v-model:show="showOrderDispatchModal" :order-details="orderDetails"
    :feature-config="featureConfig.orderDispatch" @submit-order-details="submitOrderDetails" />

  <!-- Bill Configuration Modal -->
  <BillConfigurationModal :is-open="showBillConfigModal" @close="showBillConfigModal = false"
    @config-updated="handleConfigUpdate" />

  <!-- Print Options Modal -->
  <div v-if="showPrintModal" class="fixed inset-0 z-[9999] overflow-y-auto" aria-labelledby="print-modal-title" role="dialog" aria-modal="true">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showPrintModal = false"></div>

    <!-- Modal panel -->
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full max-w-4xl mx-auto" @click.stop>

        <!-- Modal header -->
        <div class="bg-gradient-to-r from-green-600 to-blue-600 px-4 py-3 sm:px-6">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium leading-6 text-white" id="print-modal-title">
              <PrinterIcon class="inline-block w-6 h-6 mr-2" />
              Choose Print Format
            </h3>
            <button
              type="button"
              class="rounded-md bg-white bg-opacity-20 p-2 text-white hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-green-600"
              @click="showPrintModal = false"
            >
              <span class="sr-only">Close</span>
              <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Modal body -->
        <div class="bg-white px-4 py-5 sm:p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">

            <!-- Existing Print Format -->
            <div class="border-2 border-gray-200 rounded-lg p-4 hover:border-blue-400 transition-colors cursor-pointer" @click="printWithExistingFormat">
              <div class="flex flex-col items-center text-center">
                <!-- Preview thumbnail placeholder -->
                <div class="w-full h-48 bg-gray-100 rounded-lg mb-4 flex items-center justify-center border">
                  <div class="text-center">
                    <svg class="w-16 h-16 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-sm text-gray-500">Standard Format</p>
                  </div>
                </div>

                <h4 class="text-lg font-semibold text-gray-900 mb-2">Standard Invoice Format</h4>
                <p class="text-sm text-gray-600 mb-4">
                  Current invoice format with standard layout and styling. Includes all essential invoice elements with professional appearance.
                </p>

                <div class="w-full">
                  <button class="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                    Use Standard Format
                  </button>
                </div>

                <div class="mt-3 text-xs text-gray-500">
                  <span class="inline-flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    Current system format
                  </span>
                </div>
              </div>
            </div>

            <!-- New Professional Print Format -->
            <div class="border-2 border-green-300 rounded-lg p-4 hover:border-green-500 transition-colors cursor-pointer bg-green-50" @click="printWithProfessionalFormat">
              <div class="flex flex-col items-center text-center">
                <!-- Preview thumbnail placeholder -->
                <div class="w-full h-48 bg-gradient-to-br from-blue-50 to-green-50 rounded-lg mb-4 flex items-center justify-center border-2 border-green-200">
                  <div class="text-center">
                    <svg class="w-16 h-16 text-green-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <p class="text-sm text-green-600 font-medium">Professional Format</p>
                  </div>
                </div>

                <h4 class="text-lg font-semibold text-gray-900 mb-2">Professional Invoice Format</h4>
                <p class="text-sm text-gray-600 mb-4">
                  Enhanced professional format with improved styling, better organization, and comprehensive sections including HSN summary, bank details, and jurisdiction.
                </p>

                <div class="w-full">
                  <button class="w-full bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-md transition-colors">
                    Use Professional Format
                  </button>
                </div>

                <div class="mt-3 text-xs text-green-600">
                  <span class="inline-flex items-center">
                    <svg class="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                    </svg>
                    New enhanced format
                  </span>
                </div>
              </div>
            </div>

          </div>

          <!-- Additional options -->
          <div class="mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center justify-between">
              <div class="text-sm text-gray-600">
                <span class="font-medium">Quick tip:</span> You can also use <kbd class="bg-gray-100 px-2 py-1 rounded text-xs">Alt+P</kbd> to open this dialog
              </div>
              <button
                type="button"
                class="text-gray-400 hover:text-gray-600 text-sm"
                @click="showPrintModal = false"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Add Firm GST Modal -->
  <div v-if="showAddFirmGSTModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
      <h3 class="text-lg font-semibold mb-4">Add New Firm GST Registration</h3>

      <form @submit.prevent="submitFirmGST">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">GST Number *</label>
            <input v-model="firmGSTForm.gstNumber" type="text" placeholder="27AAAAA0000A1Z5"
              class="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Location Name *</label>
            <input v-model="firmGSTForm.locationName" type="text" placeholder="Mumbai Branch"
              class="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
            <textarea v-model="firmGSTForm.address" placeholder="Complete address with city and pincode"
              class="w-full px-3 py-2 border border-gray-300 rounded-md" rows="3" required></textarea>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
              <input v-model="firmGSTForm.city" type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
              <input v-model="firmGSTForm.pincode" type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Registration Type</label>
            <select v-model="firmGSTForm.registrationType" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="regular">Regular</option>
              <option value="composition">Composition</option>
              <option value="casual">Casual</option>
              <option value="sez">SEZ</option>
            </select>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" @click="showAddFirmGSTModal = false"
            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
            Cancel
          </button>
          <button type="submit" :disabled="isSubmittingGST"
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
            {{ isSubmittingGST ? 'Adding...' : 'Add GST Registration' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Add Party GST Modal -->
  <div v-if="showAddPartyGSTModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
      <h3 class="text-lg font-semibold mb-4">Add New Party GST Registration</h3>
      <p class="text-sm text-gray-600 mb-4">Adding GST for: <strong>{{ billForm.partyName }}</strong></p>

      <form @submit.prevent="submitPartyGST">
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">GST Number *</label>
            <input v-model="partyGSTForm.gstNumber" type="text" placeholder="29BBBBB0000B1Z5 or UNREGISTERED"
              class="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Location Name *</label>
            <input v-model="partyGSTForm.locationName" type="text" placeholder="Bangalore Branch"
              class="w-full px-3 py-2 border border-gray-300 rounded-md" required />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Address</label>
            <textarea v-model="partyGSTForm.address" placeholder="Complete address"
              class="w-full px-3 py-2 border border-gray-300 rounded-md" rows="3"></textarea>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">City *</label>
              <input v-model="partyGSTForm.city" type="text" placeholder="City name"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" required />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Pincode *</label>
              <input v-model="partyGSTForm.pincode" type="text" placeholder="6-digit pincode"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" maxlength="6" required />
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Contact Person</label>
              <input v-model="partyGSTForm.contactPerson" type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Contact Number</label>
              <input v-model="partyGSTForm.contactNumber" type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md" />
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Registration Type</label>
            <select v-model="partyGSTForm.registrationType" class="w-full px-3 py-2 border border-gray-300 rounded-md">
              <option value="regular">Regular</option>
              <option value="composition">Composition</option>
              <option value="unregistered">Unregistered</option>
            </select>
          </div>
        </div>

        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" @click="showAddPartyGSTModal = false"
            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
            Cancel
          </button>
          <button type="submit" :disabled="isSubmittingGST"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50">
            {{ isSubmittingGST ? 'Adding...' : 'Add Party GST' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Toast notifications are handled globally -->
</template>



<script setup>
import { ref, onMounted, onUnmounted, watch, computed, nextTick } from 'vue';
import { useRoute, useRouter } from '#app';
import useToast from '~/composables/ui/useToast';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import OtherChargesModal from '~/components/inventory/OtherChargesModal.vue';
import PartyModal from '~/components/inventory/PartyModal.vue';
import StockItemModal from '~/components/inventory/StockItemModal.vue';
import StockRegModal from '~/components/inventory/StockRegModal.vue';
import OrderDispatchModal from '~/components/inventory/OrderDispatchModal.vue';
import BillConfigurationModal from '~/components/inventory/BillConfigurationModal.vue';
import StockItemDropdown from '~/components/inventory/StockItemDropdown.vue';
import useLocalStorage from '~/composables/utils/useLocalStorage';
import {
  PlusCircleIcon,
  DocumentArrowDownIcon,
  PencilSquareIcon,
  TrashIcon,
  XCircleIcon,
  PrinterIcon,
  DocumentPlusIcon
} from '@heroicons/vue/24/solid'
import NarrationModal from '~/components/inventory/NarrationModal.vue'

const { toast, success, info, error: showError } = useToast();
const route = useRoute();
const router = useRouter();

const inventoryData = ref(null);
const error = ref(null);
const showPartyModal = ref(false);
const showStockItemModal = ref(false);
const showStockRegModal = ref(false);
const isNarrationModalVisible = ref(false);
const showOrderDispatchModal = ref(false);
const showBillConfigModal = ref(false);
const showPrintModal = ref(false);

// Feature configuration state
const featureConfig = ref({
  itemNarration: {
    enabled: true,
    showModal: true
  },
  orderDispatch: {
    enabled: true,
    showOrderInfo: true,
    showDispatchInfo: true,
    showVehicleNumber: true,
    showConsigneeDetails: true
  }
});
const invoiceSubmitted = ref(false);
const submittedInvoiceData = ref(null);
const fetchedBillData = ref(null); // Store fetched bill data for edit mode
const isEditMode = ref(false);
const billId = ref(null);
const isCancelledBill = ref(false);
const gstFetchLoading = ref(false);
const orderDetails = ref({
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: ''
});
const columnVisibility = ref({
  project: true,
  disc: true,
  cgst: true,
  sgst: true,
  igst: true,
  mrp: true,
  expiryDate: true
});

// Initialize the localStorage composable
const localStorage = useLocalStorage();

// Function to save column visibility settings to IndexedDB
const saveColumnSettings = () => {
  try {
    localStorage.setItem('inventoryColumnSettings', JSON.stringify(columnVisibility.value));
  } catch (error) {
    // Error saving column settings
  }
};

// Function to load column visibility settings from localStorage
const loadColumnSettings = () => {
  try {
    const savedSettings = localStorage.getItem('inventoryColumnSettings');
    if (savedSettings && savedSettings !== 'undefined' && savedSettings !== 'null') {
      // Check if it's valid JSON
      if (typeof savedSettings === 'string' && savedSettings.startsWith('{')) {
        const parsedSettings = JSON.parse(savedSettings);
        // Merge saved settings with default settings to ensure all properties exist
        columnVisibility.value = {
          ...columnVisibility.value,
          ...parsedSettings
        };
      } else {
        // If it's not valid JSON, clear it and use defaults
        console.warn('Invalid inventoryColumnSettings data, clearing and using defaults');
        localStorage.removeItem('inventoryColumnSettings');
      }
    }
  } catch (error) {
    console.error('Error loading column settings:', error);
    // Clear corrupted data and use defaults
    localStorage.removeItem('inventoryColumnSettings');
  }
};

// Load feature configuration from localStorage (from the main billConfiguration key)
const loadFeatureConfig = () => {
  try {
    console.log('🔧 loadFeatureConfig() starting...');
    // useLocalStorage composable automatically parses JSON, so we get an object directly
    const parsedConfig = localStorage.getItem('billConfiguration', null);
    console.log('🔧 loadFeatureConfig() parsed config:', parsedConfig);

    if (parsedConfig && parsedConfig.features) {
      featureConfig.value = { ...featureConfig.value, ...parsedConfig.features };
      console.log('🔧 loadFeatureConfig() loaded features:', parsedConfig.features);
    } else {
      console.log('🔧 loadFeatureConfig() no features found in config');
    }
    console.log('🔧 loadFeatureConfig() completed');
  } catch (error) {
    console.error('❌ loadFeatureConfig() Error loading feature config:', error);
  }
};

// Load saved GST selections from localStorage
const loadSavedGSTSelections = () => {
  try {
    // Load last used firm GST
    const savedFirmGSTIndex = localStorage.getItem('lastSelectedFirmGSTIndex');
    if (savedFirmGSTIndex) {
      selectedFirmGSTIndex.value = parseInt(savedFirmGSTIndex);
      console.log('🏢 Loaded saved firm GST index:', savedFirmGSTIndex);
    }

    // Load last used party GST
    const savedPartyGSTIndex = localStorage.getItem('lastSelectedPartyGSTIndex');
    if (savedPartyGSTIndex) {
      selectedPartyGSTIndex.value = parseInt(savedPartyGSTIndex);
      console.log('🏪 Loaded saved party GST index:', savedPartyGSTIndex);
    }
  } catch (error) {
    console.error('❌ Error loading saved GST selections:', error);
  }
};

// Set GST selections based on bill data (for edit mode)
const setGSTSelectionsFromBillData = () => {
  try {
    console.log('🔍 Setting GST selections from bill data...');
    console.log('📋 Current bill form party data:', {
      partyName: billForm.value.partyName,
      partyGstin: billForm.value.partyGstin,
      partyState: billForm.value.partyState
    });

    // Check if bill has gstSelection data (newer bills)
    const billGSTSelection = fetchedBillData.value?.gstSelection;
    console.log('📊 Bill GST selection data:', billGSTSelection);

    // Find the correct party GST index based on the bill's party GSTIN
    if (billForm.value.partyName && billForm.value.partyGstin) {
      const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
      const partyGSTs = selectedParty?.allGSTs || [];

      console.log('🏪 Available party GSTs:', partyGSTs.map(gst => ({ gstNumber: gst.gstNumber, state: gst.state })));
      console.log('🎯 Looking for party GST:', billForm.value.partyGstin);

      if (partyGSTs.length > 0) {
        const partyGSTIndex = partyGSTs.findIndex(gst =>
          gst.gstNumber === billForm.value.partyGstin
        );

        console.log('🔍 Found party GST at index:', partyGSTIndex);

        if (partyGSTIndex !== -1) {
          selectedPartyGSTIndex.value = partyGSTIndex;
          console.log('✅ Set party GST index to:', partyGSTIndex);

          // Ensure the form fields are correct (they should already be set by fetchBillById)
          const selectedGST = partyGSTs[partyGSTIndex];
          if (selectedGST) {
            billForm.value.partyGstin = selectedGST.gstNumber;
            billForm.value.partyState = selectedGST.state;
            if (selectedGST.address) {
              billForm.value.partyAddress = selectedGST.address;
            }
            console.log('📝 Confirmed party form fields match selected GST');
          }
        } else {
          console.warn('⚠️ Bill party GST not found in available party GSTs');
          console.warn('Bill GSTIN:', billForm.value.partyGstin);
          console.warn('Available GSTs:', partyGSTs.map(gst => gst.gstNumber));
        }
      } else {
        console.log('📝 Party has no additional GSTs, using single GST input');
      }
    }

    // Find the correct firm GST index based on bill's saved GST selection
    if (billGSTSelection?.firmGST && inventoryData.value?.firmGSTs) {
      const firmGSTIndex = inventoryData.value.firmGSTs.findIndex(gst =>
        gst.gstNumber === billGSTSelection.firmGST.gstNumber
      );

      if (firmGSTIndex !== -1) {
        selectedFirmGSTIndex.value = firmGSTIndex;
        console.log('🏢 Set firm GST index from bill data:', firmGSTIndex);
      } else {
        console.warn('⚠️ Bill firm GST not found in available firm GSTs, using primary');
        selectedFirmGSTIndex.value = 0;
      }
    } else if (inventoryData.value?.firmGSTs && inventoryData.value.firmGSTs.length > 1) {
      // Fallback: use primary firm GST if no gstSelection data
      console.log('🏢 No firm GST selection data, using primary (index 0)');
      selectedFirmGSTIndex.value = 0;
    } else {
      console.log('🏢 Single firm GST, no index change needed');
    }

    console.log('✅ GST selections updated from bill data');
  } catch (error) {
    console.error('❌ Error setting GST selections from bill data:', error);
  }
};

const currentItemIndex = ref(-1);

// Utility function to clear potentially corrupted localStorage data
const clearCorruptedData = () => {
  try {
    console.log('🧹 clearCorruptedData() starting...');
    // List of keys that might have corrupted data
    const keysToCheck = ['billConfiguration', 'inventoryColumnSettings', 'billFeatureConfig'];

    keysToCheck.forEach(key => {
      const data = localStorage.getItem(key);
      console.log(`🔍 Checking key "${key}":`, data ? `Found (${data.length} chars)` : 'Not found');

      if (data && data !== 'undefined' && data !== 'null') {
        // Try to parse, if it fails, remove it
        try {
          // Check if it's valid JSON by trying to parse it
          const parsed = JSON.parse(data);
          console.log(`✅ Key "${key}" is valid JSON, keeping it`);
        } catch (e) {
          console.warn(`❌ Clearing corrupted data for key: ${key}`, e);
          console.warn(`❌ Corrupted data was:`, data);
          localStorage.removeItem(key);
        }
      }
    });
    console.log('🧹 clearCorruptedData() completed');
  } catch (error) {
    console.error('Error clearing corrupted data:', error);
  }
};

const showNarrationModal = (index) => {
  // Only show narration modal if the feature is enabled
  if (featureConfig.value.itemNarration?.enabled && featureConfig.value.itemNarration?.showModal) {
    currentItemIndex.value = index;
    isNarrationModalVisible.value = true;
  }
};

const updateItemNarration = (narration) => {
  if (narration && currentItemIndex.value >= 0) {
    billForm.value.stockItems[currentItemIndex.value].item_narration = narration;
  }

  // Focus on the discount cell of the selected row after modal is closed
  nextTick(() => {
    const rowIndex = currentItemIndex.value;
    if (rowIndex >= 0) {
      // Find the discount input in the current row
      const tableRows = document.querySelectorAll('#stockItemsTable tbody tr');
      if (tableRows[rowIndex]) {
        // Find the discount cell - it's the 8th column (index 7) if visible
        const discountCell = tableRows[rowIndex].querySelector('td:nth-child(9)');
        if (discountCell) {
          const discountInput = discountCell.querySelector('input');
          if (discountInput) {
            discountInput.focus();
            // Update the table focus tracking
            trackTableFocus('stockItems', rowIndex, 8); // 8 is the column index for discount
          }
        }
      }
    }
  });
};
const partyToEdit = ref(null);
const selectedStockItem = ref('');
const currentCharge = ref(null);
const chargeIndex = ref(-1);

// Variables to store userId and firm details
const userId = ref('');
const firmName = ref('');
const firmGst = ref('');
const Firm_state = ref('');
const Firm_name = ref('');
const Firm_address = ref('');
const Firm_gstin = ref('');

// Indian states data with codes
const indianStates = [
  { code: 1, name: 'Jammu & Kashmir' },
  { code: 2, name: 'Himachal Pradesh' },
  { code: 3, name: 'Punjab' },
  { code: 4, name: 'Chandigarh' },
  { code: 5, name: 'Uttarakhand' },
  { code: 6, name: 'Haryana' },
  { code: 7, name: 'Delhi' },
  { code: 8, name: 'Rajasthan' },
  { code: 9, name: 'Uttar Pradesh' },
  { code: 10, name: 'Bihar' },
  { code: 11, name: 'Sikkim' },
  { code: 12, name: 'Arunachal Pradesh' },
  { code: 13, name: 'Nagaland' },
  { code: 14, name: 'Manipur' },
  { code: 15, name: 'Mizoram' },
  { code: 16, name: 'Tripura' },
  { code: 17, name: 'Meghalaya' },
  { code: 18, name: 'Assam' },
  { code: 19, name: 'West Bengal' },
  { code: 20, name: 'Jharkhand' },
  { code: 21, name: 'Odisha' },
  { code: 22, name: 'Chhattisgarh' },
  { code: 23, name: 'Madhya Pradesh' },
  { code: 24, name: 'Gujarat' },
  { code: 25, name: 'Daman & Diu' },
  { code: 26, name: 'Dadra & Nagar Haveli' },
  { code: 27, name: 'Maharashtra' },
  { code: 28, name: 'Andra Pradesh (Old)' },
  { code: 29, name: 'Karnataka' },
  { code: 30, name: 'Goa' },
  { code: 31, name: 'Lakshadweep' },
  { code: 32, name: 'Kerala' },
  { code: 33, name: 'Tamil Nadu' },
  { code: 34, name: 'Puducherry' },
  { code: 35, name: 'Andaman & Nicobar Islands' },
  { code: 36, name: 'Telangana' },
  { code: 37, name: 'Andhra Pradesh' },
  { code: 38, name: 'Ladakh' }
];

// New variable to store party data from modal
const newParty = ref(null);

// File upload related variables
const selectedFile = ref(null);
const fileInput = ref(null);
const isUploadingFile = ref(false);

// GST Selection reactive data
const selectedFirmGSTIndex = ref(0);
const selectedPartyGSTIndex = ref(0);

// GST Modal reactive data
const showAddFirmGSTModal = ref(false);
const showAddPartyGSTModal = ref(false);
const isSubmittingGST = ref(false);

const firmGSTForm = ref({
  gstNumber: '',
  locationName: '',
  address: '',
  city: '',
  pincode: '',
  registrationType: 'regular'
});

const partyGSTForm = ref({
  gstNumber: '',
  locationName: '',
  address: '',
  city: '',
  pincode: '',
  contactPerson: '',
  contactNumber: '',
  registrationType: 'regular'
});

// Function to fetch inventory data
const fetchInventoryData = async () => {
  error.value = null;

  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/inventory');
    inventoryData.value = response;
    userId.value = response.userId;
    firmName.value = response.firmName;
    firmGst.value = response.gstNo;
    Firm_state.value = response.Firm_state;
    Firm_name.value = response.firmName;
    Firm_address.value = response.Firm_address || '';
    Firm_gstin.value = response.gstNo || '';
  } catch (err) {
    error.value = 'Failed to load inventory data. Please try again.';
  }
};

// Set page title and meta tags
const pageTitle = computed(() => isEditMode.value ? 'Edit Invoice' : 'Create New Invoice');
const pageDescription = computed(() => isEditMode.value ? 'Edit an existing invoice' : 'Create a new invoice');

usePageTitle(pageTitle, pageDescription);

// ✅ FIX: Reactive computed property for stocks to ensure freshness
const reactiveStocks = computed(() => {
  const stocks = inventoryData.value?.stocks || [];
  console.log('🔄 Reactive stocks computed - Available stocks:', stocks.length, 'for bill type:', billForm.value.type);
  return stocks;
});

// ✅ FIX: Force refresh function for manual troubleshooting
const forceRefreshStocks = async () => {
  console.log('🔄 Force refreshing stocks data...');
  await fetchInventoryData();
  console.log('✅ Force refresh completed. Stocks available:', inventoryData.value?.stocks?.length || 0);
};

// GST Selection computed properties
const selectedFirmGST = computed(() => {
  const firmGSTs = inventoryData.value?.firmGSTs;
  if (!firmGSTs || selectedFirmGSTIndex.value >= firmGSTs.length) return null;

  const gst = firmGSTs[selectedFirmGSTIndex.value];
  // Ensure the GST object has required properties
  if (!gst || !gst.state || typeof gst.state !== 'string') return null;

  return gst;
});

const selectedPartyGSTs = computed(() => {
  if (!billForm.value.partyName) return [];
  const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
  return selectedParty?.allGSTs || [];
});

const selectedPartyGST = computed(() => {
  const partyGSTs = selectedPartyGSTs.value;
  if (!partyGSTs || selectedPartyGSTIndex.value >= partyGSTs.length) {
    // Fallback: create a basic GST object from bill form data
    if (billForm.value.partyGstin && billForm.value.partyState && typeof billForm.value.partyState === 'string') {
      return {
        gstNumber: billForm.value.partyGstin,
        state: billForm.value.partyState,
        stateCode: billForm.value.partyGstin !== 'UNREGISTERED' ? parseInt(billForm.value.partyGstin.substring(0, 2)) : 0,
        locationName: 'Primary Location',
        isPrimary: true
      };
    }
    return null;
  }

  const gst = partyGSTs[selectedPartyGSTIndex.value];
  // Ensure the GST object has required properties
  if (!gst || !gst.state || typeof gst.state !== 'string') return null;

  return gst;
});

const transactionTypeText = computed(() => {
  if (!selectedFirmGST.value || !selectedPartyGST.value) return '';

  const firmState = selectedFirmGST.value.state;
  const partyState = selectedPartyGST.value.state;

  if (!firmState || !partyState || typeof firmState !== 'string' || typeof partyState !== 'string') return '';

  const isSameState = firmState.toLowerCase() === partyState.toLowerCase();
  return isSameState ? 'Same State (CGST + SGST)' : 'Inter State (IGST)';
});

const transactionTypeClass = computed(() => {
  if (!selectedFirmGST.value || !selectedPartyGST.value) return '';

  const firmState = selectedFirmGST.value.state;
  const partyState = selectedPartyGST.value.state;

  if (!firmState || !partyState || typeof firmState !== 'string' || typeof partyState !== 'string') return '';

  const isSameState = firmState.toLowerCase() === partyState.toLowerCase();
  return isSameState ? 'text-green-600 font-medium' : 'text-blue-600 font-medium';
});

// Function to fetch bill data by ID
const fetchBillById = async (id) => {
  try {
    const api = useApiWithAuth();

    // First, try to get the bill type to determine the correct endpoint
    let billType = '';
    try {
      // Try to get the bill from the general bills endpoint first
      const billInfo = await api.get(`/api/inventory/bills/${id}`);
      if (billInfo && billInfo.btype) {
        billType = billInfo.btype;
      }
    } catch (e) {
      // If this fails, we'll try specific endpoints below
      console.log('Could not determine bill type from general endpoint, will try specific endpoints');
    }

    let response;

    // Use the appropriate endpoint based on bill type
    if (billType === 'PURCHASE') {
      console.log('Fetching purchase bill with ID:', id);
      const purchaseResponse = await api.get(`/api/inventory/purchase/${id}`);
      console.log('Purchase response:', purchaseResponse);

      // For purchase bills, the response structure is different
      if (purchaseResponse && purchaseResponse.bill) {
        response = purchaseResponse.bill;

        // Store the stock items separately
        if (purchaseResponse.stockItems && Array.isArray(purchaseResponse.stockItems)) {
          console.log(`Found ${purchaseResponse.stockItems.length} stock items in purchase response`);
          billForm.value.stockItems = purchaseResponse.stockItems.map(item => ({
            item: item.item || '',
            hsn: item.hsn || '',
            batch: item.batch || '',
            qty: item.qty || 0,
            oem: item.oem || '',
            pno: item.pno || '',
            uom: item.uom || '',
            rate: item.rate || 0,
            grate: item.grate || 0,
            cgst: item.cgst || 0,
            sgst: item.sgst || 0,
            igst: item.igst || 0,
            disc: item.disc || 0,
            project: item.project || '',
            total: item.total || 0,
            item_narration: item.item_narration || '',
            mrp: item.mrp || null,
            expiryDate: item.expiryDate ? new Date(item.expiryDate).toISOString().slice(0, 10) : null
          }));
        } else {
          console.log('No stock items found in purchase response');
          billForm.value.stockItems = [];
        }
      }
    } else {
      // Default to the bills endpoint for other types
      response = await api.get(`/api/inventory/bills/${id}`);
    }

    if (response) {
      // Store the fetched bill data for GST selection reference
      fetchedBillData.value = response;

      // Check if the bill is cancelled
      if (response.status === 'CANCELLED') {
        // Set error state but don't throw an error
        isLoading.value = false;
        error.value = 'This bill has been cancelled and cannot be edited.';
        showError('This bill has been cancelled and cannot be edited.');

        // Clear form data to prevent partial loading
        resetBillForm();

        // Set a flag to show a cancelled bill message
        isCancelledBill.value = true;

        // Wait a moment before redirecting
        setTimeout(() => {
          router.push('/inventory/bills');
        }, 5000);

        return;
      }

      // Update the bill form with the fetched data
      billForm.value.type = response.btype || '';
      billForm.value.bno = response.bno || '';
      billForm.value.bdate = response.bdate ? new Date(response.bdate).toISOString().slice(0, 10) : '';
      billForm.value.partyName = response.supply || '';
      billForm.value.partyAddress = response.addr || '';
      billForm.value.partyGstin = response.gstin || '';
      billForm.value.partyState = response.state || '';
      billForm.value.partyPin = response.pin || '';
      billForm.value.gtot = response.gtot || 0;
      billForm.value.disc = response.disc || 0;
      billForm.value.cgst = response.cgst || 0;
      billForm.value.sgst = response.sgst || 0;
      billForm.value.igst = response.igst || 0;
      billForm.value.rof = response.rof || 0;
      billForm.value.ntot = response.ntot || 0;
      billForm.value.orderNo = response.orderNo || '';
      billForm.value.orderDate = response.orderDate ? new Date(response.orderDate).toISOString().slice(0, 10) : '';
      billForm.value.dispatchThrough = response.dispatchThrough || '';
      billForm.value.docketNo = response.docketNo || '';
      billForm.value.vehicleNo = response.vehicleNo || '';
      billForm.value.consigneeName = response.consigneeName || '';
      billForm.value.consigneeGstin = response.consigneeGstin || '';
      billForm.value.consigneeAddress = response.consigneeAddress || '';
      billForm.value.consigneeState = response.consigneeState || '';
      billForm.value.consigneePin = response.consigneePin || '';

      // Handle credit/debit note specific fields
      if (billForm.value.type === 'CREDIT NOTE' || billForm.value.type === 'DEBIT NOTE') {
        billForm.value.reasonForNote = response.reasonForNote || '';
        billForm.value.originalBillNo = response.originalBillNo || '';
        billForm.value.originalBillDate = response.originalBillDate ? new Date(response.originalBillDate).toISOString().slice(0, 10) : '';
      }

      // Handle attachment fields for PURCHASE and DEBIT NOTE
      if (billForm.value.type === 'PURCHASE' || billForm.value.type === 'DEBIT NOTE') {
        billForm.value.attachmentUrl = response.attachmentUrl || '';
        billForm.value.attachmentFileId = response.attachmentFileId || '';
      }

      // Handle other charges
      if (response.oth_chg && Array.isArray(response.oth_chg)) {
        billForm.value.oth_chg = response.oth_chg.map(charge => ({
          description: charge.description || '',
          oth_amt: charge.oth_amt || 0,
          oth_grate: charge.oth_grate || 0,
          oth_cgst: charge.oth_cgst || 0,
          oth_sgst: charge.oth_sgst || 0,
          oth_igst: charge.oth_igst || 0,
          oth_hsn: charge.oth_hsn || '',
          oth_tot: charge.oth_tot || 0
        }));
      }

      // Handle stock items - only for non-PURCHASE bills
      // For PURCHASE bills, we've already set the stock items above
      if (billForm.value.type !== 'PURCHASE' && response.stockItems && Array.isArray(response.stockItems)) {
        billForm.value.stockItems = response.stockItems.map(item => ({
          item: item.item || '',
          hsn: item.hsn || '',
          batch: item.batch || null, // ✅ Changed from '' to null
          qty: item.qty || 0,
          oem: item.oem || '',
          pno: item.pno || null, // ✅ Changed from '' to null
          uom: item.uom || '',
          rate: item.rate || 0,
          grate: item.grate || 0,
          cgst: item.cgst || 0,
          sgst: item.sgst || 0,
          igst: item.igst || 0,
          disc: item.disc || 0,
          project: item.project || '',
          total: item.total || 0,
          item_narration: item.item_narration || '',
          mrp: item.mrp || null,
          expiryDate: item.expiryDate ? new Date(item.expiryDate).toISOString().slice(0, 10) : null
        }));
      }

      success('Invoice loaded successfully');
    }
  } catch (err) {
    console.error('Error fetching bill:', err);
    error.value = 'Failed to load bill data. Please try again.';
    info('Failed to load bill data. Please try again.');
  }
};

// Handle global keyboard shortcuts
const handleGlobalKeydown = (event) => {
  // Skip if user is typing in an input field and not using Alt key
  const isInputActive = ['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName);
  const isAltKey = event.altKey;
  const isGlobalKey = ['F1', 'F11'].includes(event.key); // Global keys that work everywhere

  // Allow Alt key shortcuts and global keys even when in input fields
  if (isInputActive && !isAltKey && !isGlobalKey) return;

  // Open Other Charges Modal with Alt+O
  if (isAltKey && (event.key === 'o' || event.key === 'O')) {
    event.preventDefault();
    showOtherChargesModal.value = true;
    return;
  }

  // Section navigation shortcuts (Alt + number)
  if (isAltKey && !event.ctrlKey && !event.shiftKey) {
    switch (event.key) {
      // Section navigation
      case '1': // Bill Info
        event.preventDefault();
        focusSection('billInfo');
        break;
      case '2': // Party Details
        event.preventDefault();
        focusSection('partyDetails');
        break;
      case '3': // Amount Details
        event.preventDefault();
        focusSection('amountDetails');
        break;
      case '4': // Stock Items
        event.preventDefault();
        focusSection('stockItems');
        break;
      case '5': // Other Charges
        event.preventDefault();
        focusSection('otherCharges');
        break;
      case '6': // Action Buttons
        event.preventDefault();
        focusSection('actionButtons');
        break;

      // Form actions
      case 's': // Submit form
      case 'S':
        event.preventDefault();
        if (!invoiceSubmitted.value) {
          submitBillForm();
        }
        break;
      case 'r': // Reset form
      case 'R':
        event.preventDefault();
        resetBillForm();
        break;
      case 'p': // Print invoice
      case 'P':
        event.preventDefault();
        if (isEditMode.value || invoiceSubmitted.value) {
          showPrintModal.value = true;
        }
        break;
      case 'n': // Create new invoice
      case 'N':
        event.preventDefault();
        if (isEditMode.value || invoiceSubmitted.value) {
          createNewInvoice();
        }
        break;

      // Table operations
      case 'a': // Add new row
      case 'A':
        event.preventDefault();
        if (currentFocusSection.value === 'stockItems') {
          addStockItem();
        } else if (currentFocusSection.value === 'otherCharges') {
          showOtherChargesModal.value = true;
        }
        break;
      case 'd': // Delete current row
      case 'D':
        event.preventDefault();
        if (currentFocusSection.value === 'stockItems' && currentTableFocus.value.row >= 0) {
          removeStockItem(currentTableFocus.value.row);
        } else if (currentFocusSection.value === 'otherCharges' && currentTableFocus.value.row >= 0) {
          removeOtherCharge(currentTableFocus.value.row);
        }
        break;
      case 'e': // Edit current row
      case 'E':
        event.preventDefault();
        if (currentFocusSection.value === 'stockItems' && currentTableFocus.value.row >= 0) {
          const currentItem = billForm.value.stockItems[currentTableFocus.value.row];
          if (currentItem && currentItem.item) {
            editStockItem.value = { ...currentItem };
            showStockItemModal.value = true;
          }
        } else if (currentFocusSection.value === 'otherCharges' && currentTableFocus.value.row >= 0) {
          editOtherCharge(currentTableFocus.value.row);
        }
        break;
    }
  }

  // Show keyboard shortcuts help dialog (now redirects to F11 configuration modal)
  if (event.key === 'F1' || (event.key === '/' && event.shiftKey)) {
    event.preventDefault();
    showBillConfigModal.value = true;
    // You could also set the active tab to 'shortcuts' here if needed
  }

  // Show bill configuration modal
  if (event.key === 'F11') {
    event.preventDefault();
    showBillConfigModal.value = true;
  }

  // Handle arrow key navigation in tables
  if (currentTableFocus.value.table && ['ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(event.key)) {
    handleTableNavigation(event);
  }
};

// Focus a specific section of the form
const focusSection = (section) => {
  currentFocusSection.value = section;

  // Focus the first focusable element in the section
  nextTick(() => {
    let sectionRef;
    let specificElement = null;

    switch (section) {
      case 'billInfo':
        sectionRef = billInfoRef.value;
        break;
      case 'partyDetails':
        sectionRef = partyDetailsRef.value;
        break;
      case 'amountDetails':
        sectionRef = amountDetailsRef.value;
        break;
      case 'stockItems':
        sectionRef = stockItemsRef.value;
        // Specifically target the Add New Item button for stockItems section
        specificElement = document.querySelector('#addNewItemButton');
        break;
      case 'otherCharges':
        sectionRef = otherChargesRef.value;
        // Specifically target the Add Other Charges button for otherCharges section
        specificElement = document.querySelector('#addOtherChargesButton');
        break;
      case 'actionButtons':
        sectionRef = actionButtonsRef.value;
        break;
    }

    // If we have a specific element to focus, use that
    if (specificElement) {
      specificElement.focus();
    }
    // Otherwise find the first focusable element in the section
    else if (sectionRef) {
      const focusableElements = sectionRef.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      if (focusableElements.length > 0) {
        focusableElements[0].focus();
      }
    }
  });
};

// Handle arrow key navigation within tables
const handleTableNavigation = (event) => {
  event.preventDefault();

  const { table, row, col } = currentTableFocus.value;
  let newRow = row;
  let newCol = col;

  // Get the table data
  const tableData = table === 'stockItems' ? billForm.value.stockItems : billForm.value.oth_chg;
  const maxRow = tableData.length - 1;

  // Calculate max columns based on the table
  const maxCol = table === 'stockItems' ?
    (document.querySelectorAll('#stockItemsTable thead th').length - 1) :
    (document.querySelectorAll('#otherChargesTable thead th').length - 1);

  switch (event.key) {
    case 'ArrowUp':
      newRow = Math.max(0, row - 1);
      break;
    case 'ArrowDown':
      newRow = Math.min(maxRow, row + 1);
      break;
    case 'ArrowLeft':
      newCol = Math.max(0, col - 1);
      break;
    case 'ArrowRight':
      newCol = Math.min(maxCol, col + 1);
      break;
  }

  // Update current focus
  currentTableFocus.value = { table, row: newRow, col: newCol };

  // Focus the cell
  nextTick(() => {
    const tableSelector = table === 'stockItems' ? '#stockItemsTable' : '#otherChargesTable';
    const rows = document.querySelectorAll(`${tableSelector} tbody tr`);

    if (rows[newRow]) {
      const cells = rows[newRow].querySelectorAll('td');
      if (cells[newCol]) {
        const focusableElement = cells[newCol].querySelector(
          'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
        );

        if (focusableElement) {
          focusableElement.focus();
        } else {
          // If no focusable element, make the cell itself focusable and focus it
          cells[newCol].setAttribute('tabindex', '0');
          cells[newCol].focus();
        }
      }
    }
  });
};

// Track table focus when an element within a table gets focus
const trackTableFocus = (table, row, col) => {
  currentFocusSection.value = table === 'stockItems' ? 'stockItems' : 'otherCharges';
  currentTableFocus.value = { table, row, col };
};

// Fetch data when component is mounted
onMounted(async () => {
  // TEMPORARILY DISABLED: Clear any corrupted localStorage data first
  // clearCorruptedData();
  console.log('🚫 clearCorruptedData() DISABLED for debugging');
  // Load column visibility settings from localStorage
  loadColumnSettings();
  // Load feature configuration from localStorage
  loadFeatureConfig();
  await fetchInventoryData();

  // Check if we have an ID in the query parameters
  if (route.query.id) {
    billId.value = route.query.id;

    // Check if this is a duplicate request
    const isDuplicate = route.query.duplicate === 'true';

    // Set edit mode based on whether this is a duplicate
    isEditMode.value = !isDuplicate;

    // Fetch the bill data
    await fetchBillById(billId.value);

    // For edit mode, DON'T load localStorage GST selections
    // Instead, keep the GST selections that match the bill's actual data
    if (!isDuplicate) {
      console.log('✏️ Edit mode: Using bill\'s actual GST data, not localStorage');
      // The bill data has already been loaded into the form by fetchBillById
      // We need to set the dropdown indices to match the bill's GST data
      // Use nextTick to ensure reactive data is updated
      await nextTick();
      setGSTSelectionsFromBillData();
    } else {
      // For duplicate mode, load localStorage preferences for new bill
      console.log('📋 Duplicate mode: Loading localStorage GST preferences');
      loadSavedGSTSelections();

      // Clear the bill number to force a new one
      billForm.value.bno = '';

      // Update the bill date to today
      billForm.value.bdate = new Date().toISOString().slice(0, 10);

      // Reset edit mode and bill ID to create a new bill
      isEditMode.value = false;
      billId.value = null;

      // Update the URL to remove the id and duplicate parameters
      router.replace('/inventory/edit-bill');

      // Show success message
      success('Bill data loaded for duplication. Please enter a new bill number and make any necessary changes before saving.');
    }
  } else {
    // For new bills, load localStorage GST preferences
    console.log('📝 New bill mode: Loading localStorage GST preferences');
    loadSavedGSTSelections();
  }

  // Add global keyboard event listener
  window.addEventListener('keydown', handleGlobalKeydown);

  // Clean up event listener on unmount
  onUnmounted(() => {
    window.removeEventListener('keydown', handleGlobalKeydown);
  });
});

// Watch for changes in column visibility and save to localStorage
watch(columnVisibility, () => {
  saveColumnSettings();
}, { deep: true });

// Handle configuration updates from the modal
const handleConfigUpdate = (newConfig) => {
  // Apply display settings
  if (newConfig.display) {
    columnVisibility.value = {
      ...columnVisibility.value,
      mrp: newConfig.display.showMRP,
      expiryDate: newConfig.display.showExpiryDate,
      project: newConfig.display.showProject,
      disc: newConfig.display.showDiscount,
      cgst: newConfig.display.showCGST,
      sgst: newConfig.display.showSGST,
      igst: newConfig.display.showIGST
    };

    // Save column visibility settings
    saveColumnSettings();
  }

  // Apply feature settings
  if (newConfig.features) {
    // Update reactive feature configuration
    featureConfig.value = { ...newConfig.features };

    // Note: Feature configuration is now saved as part of the main billConfiguration
    // No need to save separately as it's handled by the modal component
  }

  // Apply other settings as needed
  // For example, you could apply default values to new items
  if (newConfig.defaults && newConfig.defaults.billType && !billForm.value.type) {
    billForm.value.type = newConfig.defaults.billType;
  }

  // Apply default firm GST selection
  if (newConfig.defaults && typeof newConfig.defaults.firmGSTIndex === 'number') {
    selectedFirmGSTIndex.value = newConfig.defaults.firmGSTIndex;
    // Trigger GST recalculation
    onFirmGSTChange();
  }

  // Apply any other configuration changes that affect the UI
  success('Configuration applied successfully');
};

// Keyboard navigation state
const currentFocusSection = ref('billInfo'); // Current focused section
const currentTableFocus = ref({ table: null, row: -1, col: -1 }); // Current focused cell in tables

// Focus references for section navigation
const billInfoRef = ref(null);
const partyDetailsRef = ref(null);
const amountDetailsRef = ref(null);
const stockItemsRef = ref(null);
const otherChargesRef = ref(null);
const actionButtonsRef = ref(null);


// Bill form data
const billForm = ref({
  type: '',
  bno: '',
  bdate: new Date().toISOString().slice(0, 10),
  partyName: '',
  partyAddress: '',
  partyGstin: '',
  partyState: '',
  partyPin: '',
  gtot: 0,
  cgst: 0,
  sgst: 0,
  igst: 0,
  rof: 0,
  ntot: 0,
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: '',
  vehicleNo: '',
  consigneeName: '',
  consigneeGstin: '',
  consigneeAddress: '',
  consigneeState: '',
  consigneePin: '',
  reasonForNote: '',
  originalBillNo: '',
  originalBillDate: '',
  narration: '',
  oth_chg: [],
  stockItems: [],
  // File upload fields (not sent with bill creation)
  attachmentUrl: '',
  attachmentFileId: ''
});

// Watch for changes in party GSTIN to debug
watch(() => billForm.value.partyGstin, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    console.log('👀 Party GSTIN changed:', { from: oldVal, to: newVal });
  }
});

// Watch for changes in selected party GST index
watch(() => selectedPartyGSTIndex.value, (newVal, oldVal) => {
  if (newVal !== oldVal) {
    console.log('👀 Selected party GST index changed:', { from: oldVal, to: newVal });
  }
});

// ✅ FIX: Watch for bill type changes to refresh inventory data for autocomplete
watch(() => billForm.value.type, async (newType, oldType) => {
  if (newType !== oldType && (newType === 'PURCHASE' || newType === 'SALES')) {
    console.log('🔄 Bill type changed from', oldType, 'to', newType);
    console.log('🔄 Refreshing inventory data for autocomplete...');

    try {
      await fetchInventoryData();
      console.log('✅ Inventory data refreshed. Stocks available:', inventoryData.value?.stocks?.length || 0);

      // Force reactivity update
      nextTick(() => {
        console.log('✅ Next tick completed - autocomplete should work now');
      });
    } catch (error) {
      console.error('❌ Error refreshing inventory data:', error);
    }
  }
});

// Reset bill form
const resetBillForm = () => {
  billForm.value = {
    type: '',
    bno: '',
    bdate: new Date().toISOString().slice(0, 10),
    partyName: '',
    partyAddress: '',
    partyGstin: '',
    partyState: '',
    partyPin: '',
    gtot: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    rof: 0,
    ntot: 0,
    orderNo: '',
    orderDate: '',
    dispatchThrough: '',
    docketNo: '',
    vehicleNo: '',
    reasonForNote: '',
    originalBillNo: '',
    originalBillDate: '',
    oth_chg: [],
    stockItems: [],
    // Reset file upload fields
    attachmentUrl: '',
    attachmentFileId: ''
  };
  // Reset file upload state
  selectedFile.value = null;
  if (fileInput.value) {
    fileInput.value.value = '';
  }
  // Reset the submitted state
  invoiceSubmitted.value = false;
  submittedInvoiceData.value = null;
};

// Removed printInvoice function as it's now combined with generatePDF







































// Function to get all existing charge descriptions from inventory data
const getAllExistingCharges = () => {
  const charges = [];

  // Add charges from current bill
  if (billForm.value.oth_chg && billForm.value.oth_chg.length > 0) {
    charges.push(...billForm.value.oth_chg);
  }

  // Add charges from all bills in inventory data
  if (inventoryData.value?.bills && Array.isArray(inventoryData.value.bills)) {
    inventoryData.value.bills.forEach(bill => {
      if (bill.oth_chg && Array.isArray(bill.oth_chg)) {
        charges.push(...bill.oth_chg);
      }
    });
  }

  return charges;
};

// Function to create a new document (reset the form)
const createNewInvoice = () => {
  // Save the current document type before resetting
  const currentType = billForm.value.type;

  resetBillForm();

  // Restore the document type if it was set
  if (currentType) {
    billForm.value.type = currentType;
  }

  isEditMode.value = false;
  billId.value = null;
  invoiceSubmitted.value = false;
  submittedInvoiceData.value = null;

  // Update the URL to remove the id parameter
  router.push('/inventory/edit-bill');

  const documentType = currentType === 'CREDIT NOTE' ? 'credit note' :
    currentType === 'DEBIT NOTE' ? 'debit note' :
      'invoice';
  success(`Ready to create a new ${documentType}`);
};

// Function to generate PDF and print invoice
const generatePDF = async () => {
  try {
    // Check if we have a billId from a saved invoice
    if (!billId.value && !submittedInvoiceData.value?.bill?._id) {
      info('Please save the invoice first before printing.');
      return;
    }

    // Use billId if available, otherwise get it from the submitted invoice data
    const invoiceId = billId.value || submittedInvoiceData.value?.bill?._id;

    // Get API instance
    const api = useApiWithAuth();

    // Determine the correct endpoint based on bill type
    let pdfEndpoint = `/api/inventory/bills-pdf/${invoiceId}?id=${invoiceId}`;
    let documentType = 'Invoice';

    switch (billForm.value.type) {
      case 'PURCHASE':
        pdfEndpoint = `/api/inventory/purchase-pdf/${invoiceId}?id=${invoiceId}`;
        documentType = 'Purchase Invoice';
        break;
      case 'CREDIT NOTE':
        pdfEndpoint = `/api/inventory/credit-note-pdf/${invoiceId}?id=${invoiceId}`;
        documentType = 'Credit Note';
        break;
      case 'DEBIT NOTE':
        pdfEndpoint = `/api/inventory/debit-note-pdf/${invoiceId}?id=${invoiceId}`;
        documentType = 'Debit Note';
        break;
    }

    console.log(`Generating ${documentType} PDF from endpoint:`, pdfEndpoint);

    try {
      console.log('Calling PDF endpoint:', pdfEndpoint);

      // Get print configuration from localStorage
      let printConfig = null;
      try {
        const savedConfig = localStorage.getItem('billConfiguration');

        if (savedConfig) {
          let parsedConfig;

          // Check if it's already an object or a JSON string
          if (typeof savedConfig === 'string') {
            parsedConfig = JSON.parse(savedConfig);
          } else {
            parsedConfig = savedConfig;
          }

          printConfig = parsedConfig.printing || null;
        }
      } catch (err) {
        console.warn('Failed to load print configuration:', err);
      }

      // Call the API to generate the PDF with the ID as a query parameter
      // Send print configuration as both header and query parameter
      const headers = {};
      let finalEndpoint = pdfEndpoint;

      if (printConfig) {
        // Try header approach
        headers['X-Print-Config'] = JSON.stringify(printConfig);

        // Also try query parameter approach as fallback
        const configParam = encodeURIComponent(JSON.stringify(printConfig));
        finalEndpoint += `&printConfig=${configParam}`;
      }

      const response = await api.get(finalEndpoint, {
        responseType: 'blob',
        headers
      });

      console.log('PDF response received, type:', response.type);

      // Create a URL for the blob
      const url = window.URL.createObjectURL(response);

      // Open the PDF in a new window for printing
      const printWindow = window.open(url, '_blank');
      if (!printWindow) {
        info('Please allow pop-ups to print the document.');
        return;
      }

      // If the browser supports it, trigger print when the PDF loads
      if (printWindow.addEventListener) {
        printWindow.addEventListener('load', () => {
          setTimeout(() => {
            printWindow.print();
          }, 1000); // Delay to ensure PDF is fully loaded
        });
      }

      success(`${documentType} ready for printing.`);
    } catch (apiError) {
      console.error('API error:', apiError);
      throw apiError;
    }
  } catch (error) {
    console.error('Error generating PDF:', error);
    info('Failed to print document. Please try again.');
  }
};

// Function to print with existing format
const printWithExistingFormat = async () => {
  showPrintModal.value = false;
  await generatePDF();
};

// Function to print with new professional format
const printWithProfessionalFormat = async () => {
  showPrintModal.value = false;

  try {
    // Check if we have a billId from a saved invoice
    if (!billId.value && !submittedInvoiceData.value?.bill?._id) {
      info('Please save the invoice first before printing.');
      return;
    }

    // Use billId if available, otherwise get it from the submitted invoice data
    const invoiceId = billId.value || submittedInvoiceData.value?.bill?._id;

    // Get API instance
    const api = useApiWithAuth();

    // Use the new professional PDF endpoint
    const pdfEndpoint = `/api/inventory/bills-professional-pdf/${invoiceId}?id=${invoiceId}`;
    const documentType = 'Professional Invoice';

    console.log(`Generating ${documentType} PDF from endpoint:`, pdfEndpoint);

    try {
      console.log('Calling Professional PDF endpoint:', pdfEndpoint);

      // Get print configuration from localStorage (same as generatePDF function)
      let printConfig = null;
      try {
        const savedConfig = localStorage.getItem('billConfiguration');

        if (savedConfig) {
          let parsedConfig;
          try {
            parsedConfig = JSON.parse(savedConfig);
          } catch (parseErr) {
            // If parsing fails, assume it's already an object
            parsedConfig = savedConfig;
          }

          printConfig = parsedConfig.printing || null;
        }
      } catch (err) {
        console.warn('Failed to load print configuration:', err);
      }

      // Prepare headers and endpoint with print config
      const headers = {};
      let finalEndpoint = pdfEndpoint;

      if (printConfig) {
        // Try header approach
        headers['X-Print-Config'] = JSON.stringify(printConfig);

        // Also try query parameter approach as fallback
        const configParam = encodeURIComponent(JSON.stringify(printConfig));
        finalEndpoint += `&printConfig=${configParam}`;
      }

      const response = await api.get(finalEndpoint, {
        responseType: 'blob',
        headers
      });

      console.log('Professional PDF response received, type:', response.type);

      // Create a URL for the blob
      const url = window.URL.createObjectURL(response);

      // Open the PDF in a new window for printing
      const printWindow = window.open(url, '_blank');
      if (!printWindow) {
        info('Please allow pop-ups to print the document.');
        return;
      }

      // If the browser supports it, trigger print when the PDF loads
      if (printWindow.addEventListener) {
        printWindow.addEventListener('load', () => {
          setTimeout(() => {
            printWindow.print();
          }, 1000); // Delay to ensure PDF is fully loaded
        });
      }

      success(`${documentType} ready for printing.`);
    } catch (apiError) {
      console.error('API error:', apiError);
      throw apiError;
    }
  } catch (error) {
    console.error('Error generating professional PDF:', error);
    info('Failed to print professional document. Please try again.');
  }
};

// Add stock item to the form
const addStockItem = () => {
  billForm.value.stockItems.push({
    item: '',
    hsn: '',
    batch: null, // ✅ Changed from '' to null
    qty: 0,
    oem: '',
    pno: null, // ✅ Changed from '' to null
    uom: '',
    rate: 0,
    grate: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    disc: 0,
    project: '',
    total: 0,
    item_narration: '',
    mrp: null, // Optional - List Price/MRP
    expiryDate: null // Optional - Expiry Date
  });

  // Focus the first input of the new row after it's rendered
  nextTick(() => {
    const newRowIndex = billForm.value.stockItems.length - 1;
    const tableRows = document.querySelectorAll('#stockItemsTable tbody tr');
    if (tableRows[newRowIndex]) {
      const firstInput = tableRows[newRowIndex].querySelector('input');
      if (firstInput) {
        firstInput.focus();
        // Also update the table focus tracking
        trackTableFocus('stockItems', newRowIndex, 0);
      }
    }
  });
};

// Edit other charge
const editOtherCharge = (index) => {
  currentCharge.value = { ...billForm.value.oth_chg[index] };
  chargeIndex.value = index;
  showOtherChargesModal.value = true;
};

// Remove other charge
const removeOtherCharge = (index) => {
  billForm.value.oth_chg.splice(index, 1);
  calculateBillTotal();
};


// Handle stock item selection and auto-fill fields
const handleStockItemSelection = (index) => {
  const selectedItem = billForm.value.stockItems[index].item;
  const stockItem = inventoryData.value?.stocks?.find(stock => stock.item === selectedItem);

  if (stockItem) {
    billForm.value.stockItems[index] = {
      ...billForm.value.stockItems[index],
      hsn: stockItem.hsn || '',
      pno: stockItem.pno || null, // ✅ Changed from '' to null
      oem: stockItem.oem || '',
      batch: stockItem.batch || null, // ✅ Changed from '' to null
      uom: stockItem.uom || '',
      rate: stockItem.rate || 0,
      grate: stockItem.grate || 0,
      mrp: stockItem.mrp || null,
      expiryDate: stockItem.expiryDate || null
    };
    calculateItemTotal(index);
  }

  // Make sure party state is set correctly
  if (billForm.value.partyName) {
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
    if (selectedParty && selectedParty.state) {
      console.log('Setting party state from handleStockItemSelection:', selectedParty.state);
      billForm.value.partyState = selectedParty.state;
    }
  }
};

// Handle stock item selection from dropdown (receives stock object directly)
const handleStockItemDropdownSelection = (index, stockItem) => {
  if (stockItem) {
    billForm.value.stockItems[index] = {
      ...billForm.value.stockItems[index],
      item: stockItem.item || '',
      hsn: stockItem.hsn || '',
      pno: stockItem.pno || '',
      oem: stockItem.oem || '',
      batch: stockItem.batch || '',
      uom: stockItem.uom || '',
      rate: stockItem.rate || 0,
      grate: stockItem.grate || 0,
      mrp: stockItem.mrp || null,
      expiryDate: stockItem.expiryDate || null
    };
    calculateItemTotal(index);

    // Make sure party state is set correctly
    if (billForm.value.partyName) {
      const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
      if (selectedParty && selectedParty.state) {
        console.log('Setting party state from handleStockItemDropdownSelection:', selectedParty.state);
        billForm.value.partyState = selectedParty.state;
      }
    }
  }
};

// Add these refs
const showOtherChargesModal = ref(false);

// Update billForm data structure
billForm.value = {
  type: '',
  bno: '',
  bdate: new Date().toISOString().slice(0, 10),
  partyName: '',
  partyAddress: '',
  partyGstin: '',
  gtot: 0,
  cgst: 0,
  sgst: 0,
  igst: 0,
  rof: 0,
  ntot: 0,
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: '',
  vehicleNo: '',
  oth_chg: [],
  stockItems: []
};


const addOtherCharge = (chargeData) => {
  if (chargeIndex.value >= 0) {
    // Update existing charge at the specified index
    billForm.value.oth_chg[chargeIndex.value] = { ...chargeData };
    chargeIndex.value = -1;
    currentCharge.value = null;
  } else {
    // For new charges, check for duplicates
    const existingIndex = billForm.value.oth_chg.findIndex(charge =>
      charge.description === chargeData.description &&
      charge.oth_amt === chargeData.oth_amt
    );

    if (existingIndex === -1) {
      billForm.value.oth_chg.push({ ...chargeData });
    } else {
      billForm.value.oth_chg[existingIndex] = { ...chargeData };
    }
  }

  // Close modal and recalculate totals
  showOtherChargesModal.value = false;
  calculateBillTotal();
};

// Calculate bill totals from stock items
const calculateBillTotal = () => {
  // Reset totals
  billForm.value.gtot = 0;
  billForm.value.cgst = 0;
  billForm.value.sgst = 0;
  billForm.value.igst = 0;

  // Get states from selected GSTs
  const firmState = selectedFirmGST.value?.state?.toLowerCase()?.trim() || Firm_state.value?.toLowerCase()?.trim() || '';
  const partyState = selectedPartyGST.value?.state?.toLowerCase()?.trim() ||
    inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName)?.state?.toLowerCase()?.trim() || '';

  // Sum up stock items
  billForm.value.stockItems.forEach(item => {
    billForm.value.gtot += parseFloat((item.total || 0).toFixed(2));
    billForm.value.cgst += parseFloat((item.cgst || 0).toFixed(2));
    billForm.value.sgst += parseFloat((item.sgst || 0).toFixed(2));
    billForm.value.igst += parseFloat((item.igst || 0).toFixed(2));
  });

  // Format to 2 decimal places
  billForm.value.gtot = parseFloat(billForm.value.gtot.toFixed(2));
  billForm.value.cgst = parseFloat(billForm.value.cgst.toFixed(2));
  billForm.value.sgst = parseFloat(billForm.value.sgst.toFixed(2));
  billForm.value.igst = parseFloat(billForm.value.igst.toFixed(2));

  // Add other charges if present
  billForm.value.gtot += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_amt || 0), 0);
  billForm.value.cgst += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_cgst || 0), 0);
  billForm.value.sgst += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_sgst || 0), 0);
  billForm.value.igst += billForm.value.oth_chg.reduce((sum, charge) => sum + (charge.oth_igst || 0), 0);

  // Calculate net total
  let netTotal;
  if (firmState === partyState) {
    // Same state - use CGST and SGST for net total
    netTotal = parseFloat((billForm.value.gtot + billForm.value.cgst + billForm.value.sgst).toFixed(2));
  } else {
    // Different states - use IGST for net total
    netTotal = parseFloat((billForm.value.gtot + billForm.value.igst).toFixed(2));
  }

  // Calculate round-off amount to nearest whole number
  const roundedTotal = Math.round(netTotal);
  billForm.value.rof = parseFloat((roundedTotal - netTotal).toFixed(2));

  // Set final net total with round-off
  billForm.value.ntot = parseFloat(roundedTotal.toFixed(2));
};

// Remove stock item from the form
const removeStockItem = (index) => {
  billForm.value.stockItems.splice(index, 1);
  calculateBillTotal();
};

// Calculate total for a stock item
const calculateItemTotal = (index) => {
  const item = billForm.value.stockItems[index];
  const discamt = parseFloat(((item.rate * item.qty) * item.disc / 100).toFixed(2));

  // Get states from selected GSTs
  const firmState = selectedFirmGST.value?.state?.toLowerCase()?.trim() || Firm_state.value?.toLowerCase()?.trim() || '';
  const partyState = selectedPartyGST.value?.state?.toLowerCase()?.trim() ||
    inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName)?.state?.toLowerCase()?.trim() || '';

  // Compare states to determine GST calculation
  if (firmState === partyState) {
    // Same state - apply CGST/SGST, set IGST to 0
    item.cgst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100 / 2).toFixed(2));
    item.sgst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100 / 2).toFixed(2));
    item.igst = 0;
  } else {
    // Different states - apply IGST, set CGST/SGST to 0
    item.cgst = 0;
    item.sgst = 0;
    item.igst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100).toFixed(2));
  }

  // Ensure total is properly formatted to 2 decimal places
  item.total = parseFloat((item.rate * item.qty - discamt).toFixed(2));
  calculateBillTotal();
};

// Add loading state
const isSubmitting = ref(false);

// Submit the bill form
const submitBillForm = async () => {
  error.value = null;
  isSubmitting.value = true;

  // Validate form before submission
  if (!billForm.value.type) {
    info('Please select a bill type');
    return;
  }

  if (!billForm.value.bno) {
    info('Please enter a bill number');
    return;
  }

  if (!billForm.value.partyName) {
    info('Please select or add a party');
    return;
  }

  // Remove any blank rows in stock items before submission
  billForm.value.stockItems = billForm.value.stockItems.filter(item => {
    return item.item && item.item.trim() !== '';
  });

  // ✅ ADDED: Sanitize stock item data before submission
  billForm.value.stockItems = billForm.value.stockItems.map(item => ({
    ...item,
    pno: (item.pno && item.pno.trim() !== '') ? item.pno : null,
    batch: (item.batch && item.batch.trim() !== '') ? item.batch : null
  }));

  if (billForm.value.stockItems.length === 0) {
    info('Please add at least one stock item');
    return;
  }

  // Validate each stock item
  for (let i = 0; i < billForm.value.stockItems.length; i++) {
    const item = billForm.value.stockItems[i];
    if (!item.item) {
      info(`Stock item #${i + 1}: Please select an item`);
      return;
    }
    if (!item.qty || item.qty <= 0) {
      info(`Stock item #${i + 1}: Please enter a valid quantity`);
      return;
    }
    if (!item.rate || item.rate <= 0) {
      info(`Stock item #${i + 1}: Please enter a valid rate`);
      return;
    }
  }

  try {
    const $api = useApiWithAuth();
    let endpoint = '/api/inventory';

    // Determine the endpoint based on bill type
    switch (billForm.value.type) {
      case 'SALES':
        endpoint = isEditMode.value ? `/api/inventory/bills/${billId.value}` : '/api/inventory/bills';
        break;
      case 'PURCHASE':
        endpoint = isEditMode.value ? `/api/inventory/purchase/${billId.value}` : '/api/inventory/purchase';
        break;
      case 'DEBIT NOTE':
        endpoint = isEditMode.value ? `/api/inventory/debit-note/${billId.value}` : '/api/inventory/debit-note';
        break;
      case 'CREDIT NOTE':
        endpoint = isEditMode.value ? `/api/inventory/credit-note/${billId.value}` : '/api/inventory/credit-note';
        break;
    }

    // Make sure party state is set correctly before submission
    if (billForm.value.partyName) {
      const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
      if (selectedParty && selectedParty.state) {
        console.log('Setting party state before submission:', selectedParty.state);
        billForm.value.partyState = selectedParty.state;
      }
    }

    // Create request payload including newParty and GST selection data
    const payload = {
      ...billForm.value,
      newParty: newParty.value,
      gstPreferences: {
        selectedFirmGSTIndex: selectedFirmGSTIndex.value || 0,
        selectedPartyGSTIndex: selectedPartyGSTIndex.value || 0,
        firmGST: selectedFirmGST.value,
        partyGST: selectedPartyGST.value
      }
    };

    console.log('📤 Sending GST Preferences:', {
      selectedFirmGSTIndex: selectedFirmGSTIndex.value,
      selectedPartyGSTIndex: selectedPartyGSTIndex.value,
      firmGSTsAvailable: inventoryData.value?.firmGSTs?.length || 0,
      partyGSTsAvailable: selectedPartyGSTs.value?.length || 0
    });

    console.log('Submitting payload with party state:', payload.partyState);

    let response;
    const wasEditMode = isEditMode.value;

    if (wasEditMode) {
      // Update existing bill
      response = await $api.put(endpoint, payload);
      success('Bill updated successfully.');
    } else {
      // Create new bill
      response = await $api.post(endpoint, payload);
    }

    // Store the response data
    submittedInvoiceData.value = response;

    // Update the billId with the response ID for both new and edited invoices
    if (response.bill && response.bill._id) {
      billId.value = response.bill._id;

      // If this was a new bill creation, show success message and set edit mode
      if (!wasEditMode) {
        isEditMode.value = true;
        success('Bill created successfully. You can now edit, print, or create a new invoice.');
        // Don't set invoiceSubmitted to true so user can continue editing
      } else {
        // For updates, briefly show the success state
        invoiceSubmitted.value = true;
        // After a short delay, allow editing again
        setTimeout(() => {
          invoiceSubmitted.value = false;
        }, 1500);
      }

      // Handle file upload asynchronously after successful bill creation/update
      handleAsyncFileUpload(billId.value);
    } else {
      // If we don't have a bill ID, set submitted to true to show the success state
      invoiceSubmitted.value = true;
    }

  } catch (err) {
    info(`Failed to ${isEditMode.value ? 'update' : 'create'} bill. Please try again.`);
  } finally {
    isSubmitting.value = false;
  }
};



const editSelectedParty = (e) => {
  e.preventDefault();

  if (!billForm.value.partyName) {
    return;
  }

  const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
  if (selectedParty) {
    partyToEdit.value = { ...selectedParty };
    showPartyModal.value = true;
  }
};

// Handle Alt+C keypress on party name field
const handlePartyNameKeydown = (event) => {
  // Check if Alt+C or Alt+c was pressed
  if (event.altKey && (event.key === 'c' || event.key === 'C')) {
    event.preventDefault();
    showPartyModal.value = true;
  }
};

// Handle keyboard shortcuts on stock item field
const handleStockItemKeydown = (event) => {
  // Check if Alt+C or Alt+c was pressed
  if (event.altKey && (event.key === 'c' || event.key === 'C')) {
    event.preventDefault();
    // Clear the editStockItem to ensure we're creating a new item, not editing
    editStockItem.value = null;
    showStockItemModal.value = true;
  }

  // Check if Alt+V or Alt+v was pressed
  if (event.altKey && (event.key === 'v' || event.key === 'V')) {
    event.preventDefault();
    const index = event.target.closest('tr').rowIndex - 1; // Adjust for header row
    const currentItem = billForm.value.stockItems[index];

    if (currentItem && currentItem.item && billForm.value.partyName) {
      selectedStockItem.value = currentItem.item;
      showStockRegModal.value = true;
    } else {
      alert('Cannot show stock history: missing item name or party name');
    }
  }

  // Check if Ctrl+Enter was pressed
  if (event.ctrlKey && (event.key === 'Enter')) {
    event.preventDefault();
    const index = event.target.closest('tr').rowIndex - 1; // Adjust for header row
    const currentItem = billForm.value.stockItems[index];

    if (currentItem && currentItem.item) {
      // Set the current item from the bill form as the item to edit
      editStockItem.value = { ...currentItem };
      showStockItemModal.value = true;
    }
  }
};

// Submit the party form
const submitPartyForm = (partyData) => {
  // Check if this is a valid party submission (must have supply name)
  if (!partyData.supply) {
    return; // Don't process empty submissions
  }

  // Store the party data in the newParty ref
  newParty.value = {
    ...partyData,
    // Add a temporary ID for local use
    _id: 'temp_' + Date.now(),
  };
  // Add the new party to the local array and update the bill form
  handleAddParty(newParty.value);
  // Close modal if it's open
  if (showPartyModal.value) {
    showPartyModal.value = false;
  }
};

// Handle adding a new party from the modal
const handleAddParty = (newParty) => {
  // Ensure inventoryData.parties exists
  if (!inventoryData.value) {
    inventoryData.value = {};
  }

  if (!inventoryData.value.parties) {
    inventoryData.value.parties = [];
  }

  // Create allGSTs array for the new party (required for GST selection)
  const partyWithGSTs = {
    ...newParty,
    allGSTs: [{
      gstNumber: newParty.gstin || 'UNREGISTERED',
      state: newParty.state || '',
      stateCode: newParty.state_code || (newParty.gstin ? parseInt(newParty.gstin.substring(0, 2)) : 0),
      locationName: 'Primary Location',
      address: newParty.addr || '',
      city: '',
      pincode: newParty.pin ? newParty.pin.toString() : '',
      isActive: true,
      isDefault: true,
      isPrimary: true
    }],
    hasMultipleGSTs: false
  };

  // Add the new party to the local array
  inventoryData.value.parties.push(partyWithGSTs);

  // Set the party name in the form
  billForm.value.partyName = newParty.supply;

  // If the party has an address and GSTIN, set those too
  if (newParty.addr) {
    billForm.value.partyAddress = newParty.addr;
  }

  if (newParty.gstin) {
    billForm.value.partyGstin = newParty.gstin;
  }

  if (newParty.state) {
    billForm.value.partyState = newParty.state;
  }

  // Reset party GST selection to primary (index 0) for the new party
  selectedPartyGSTIndex.value = 0;

  console.log('✅ New party added with GST structure:', partyWithGSTs);
};

// Fetch party details by GST number
const fetchPartyByGST = async () => {
  const gstin = billForm.value.partyGstin?.trim();

  if (!gstin) {
    showError('Please enter a GSTIN number first');
    return;
  }

  // Validate GSTIN format (15 characters)
  if (gstin.length !== 15) {
    showError('GSTIN must be exactly 15 characters long');
    return;
  }

  // Load API settings from localStorage
  const apiSettings = localStorage.getItem('gstApiSettings', {
    rapidApiKey: '',
    enableLogging: false
  });

  if (!apiSettings.rapidApiKey) {
    showError('Please configure your RapidAPI key in the Party modal settings first');
    return;
  }

  gstFetchLoading.value = true;

  try {
    if (apiSettings.enableLogging) {
      console.log('Fetching party details for GSTIN:', gstin);
    }

    // RapidAPI endpoints
    const rapidApiEndpoints = [
      {
        url: `https://powerful-gstin-tool.p.rapidapi.com/v1/gstin/${gstin}/details`,
        headers: {
          'x-rapidapi-key': apiSettings.rapidApiKey,
          'x-rapidapi-host': 'powerful-gstin-tool.p.rapidapi.com'
        },
        parser: (data) => data
      }
    ];

    let apiSuccess = false;

    for (const endpoint of rapidApiEndpoints) {
      try {
        if (apiSettings.enableLogging) {
          console.log('Trying RapidAPI endpoint:', endpoint.url);
        }

        const fetchOptions = {
          method: endpoint.method || 'GET',
          headers: endpoint.headers
        };

        if (endpoint.body) {
          fetchOptions.body = endpoint.body;
        }

        const response = await fetch(endpoint.url, fetchOptions);

        if (response.ok) {
          const data = await response.json();
          if (apiSettings.enableLogging) {
            console.log('RapidAPI Response:', data);
          }

          // Parse the response based on the endpoint
          const parsedData = endpoint.parser(data);

          // Check for successful response in various formats
          const isSuccess = parsedData && (
            parsedData.status === 'success' ||
            parsedData.flag === true ||
            parsedData.success === true ||
            parsedData.data ||
            parsedData.result ||
            (parsedData.gstin && parsedData.legal_name) ||
            (parsedData.gstin && parsedData.trade_name) ||
            (parsedData.legal_name || parsedData.trade_name)
          );

          if (isSuccess) {
            // Extract party data from various possible locations
            const partyData = parsedData.data || parsedData.result || parsedData;
            populatePartyFromGSTData(partyData, gstin);
            success('Party details fetched successfully from GST database!');
            apiSuccess = true;
            break;
          } else {
            if (apiSettings.enableLogging) {
              console.log('API response not recognized as success:', parsedData);
            }
          }
        }
      } catch (endpointError) {
        if (apiSettings.enableLogging) {
          console.log('RapidAPI endpoint failed:', endpoint.url, endpointError.message);
        }
        continue;
      }
    }

    if (!apiSuccess) {
      showError('No valid data found for this GSTIN. Please check the number and try again.');
    }

  } catch (error) {
    console.error('Error fetching party details:', error);
    showError('Failed to fetch party details. Please try again.');
  } finally {
    gstFetchLoading.value = false;
  }
};

// Populate party form from GST API data
const populatePartyFromGSTData = (partyData, gstin) => {
  console.log('Processing GST API data:', partyData);

  // Extract data based on API response structure
  const tradeName = partyData.trade_name || '';
  const legalName = partyData.legal_name || '';

  // Use trade name first, then legal name
  const displayName = tradeName || legalName;

  if (displayName) {
    billForm.value.partyName = displayName;
  }

  // Extract and format address
  const address = formatGSTAddress(partyData);
  if (address) {
    billForm.value.partyAddress = address;
  }

  // Extract PIN code
  const pinCode = extractGSTPinCode(partyData);
  if (pinCode) {
    // Note: The edit-bill page doesn't have a PIN field in billForm,
    // but we can log it or add it if needed
    console.log('Extracted PIN code:', pinCode);
  }

  // GSTIN is already set, but ensure it's uppercase
  billForm.value.partyGstin = gstin.toUpperCase();
};

// Format address from GST API response
const formatGSTAddress = (partyData) => {
  if (!partyData) return '';

  // Try different possible address field names from Powerful GSTIN Tool API
  const addressFields = [
    partyData.pradr?.addr,  // Principal address from Powerful GSTIN Tool
    partyData.adadr?.addr,  // Additional address
    partyData.address,
    partyData.principalPlaceAddress,
    partyData.principal_place_address,
    partyData.pradr,
    partyData.businessAddress,
    partyData.business_address
  ];

  for (const addressField of addressFields) {
    if (addressField) {
      if (typeof addressField === 'string') {
        return addressField.trim();
      } else if (typeof addressField === 'object') {
        // Handle structured address from Powerful GSTIN Tool
        const parts = [
          addressField.bno,        // Building number
          addressField.bnm,        // Building name
          addressField.flno,       // Floor number
          addressField.st,         // Street
          addressField.loc,        // Location
          addressField.city,       // City
          addressField.dst,        // District
          addressField.stcd,       // State
          // Fallback fields
          addressField.building_name,
          addressField.building_no,
          addressField.floor_no,
          addressField.street,
          addressField.location,
          addressField.district,
          addressField.state
        ];
        const formattedAddress = parts.filter(p => p && p.toString().trim()).join(', ');
        if (formattedAddress) return formattedAddress;
      }
    }
  }

  // Try to construct address from individual fields if structured address not found
  if (partyData.pradr) {
    const addr = partyData.pradr;
    const parts = [
      addr.bno,
      addr.bnm,
      addr.flno,
      addr.st,
      addr.loc,
      addr.city,
      addr.dst
    ];
    const constructedAddress = parts.filter(p => p && p.toString().trim()).join(', ');
    if (constructedAddress) return constructedAddress;
  }

  return '';
};

// Extract PIN code from GST API response
const extractGSTPinCode = (partyData) => {
  if (!partyData) return '';

  // Try different possible PIN field names from Powerful GSTIN Tool API
  const pinFields = [
    partyData.pradr?.pncd,   // PIN code from principal address
    partyData.adadr?.pncd,   // PIN code from additional address
    partyData.pincode,
    partyData.pin,
    partyData.postalCode,
    partyData.postal_code,
    partyData.principalPlacePincode,
    partyData.principal_place_pincode
  ];

  for (const pinField of pinFields) {
    if (pinField) {
      const pinStr = String(pinField).trim();
      if (pinStr.length === 6 && /^\d{6}$/.test(pinStr)) {
        return pinStr;
      }
    }
  }

  // Also check if PIN is embedded in address structure
  if (partyData.pradr && partyData.pradr.pncd) {
    const pinStr = String(partyData.pradr.pncd).trim();
    if (pinStr.length === 6 && /^\d{6}$/.test(pinStr)) {
      return pinStr;
    }
  }

  return '';
};

// Show order and dispatch modal when party name field loses focus
const openOrderDispatchModal = () => {
  // Only show the modal if the feature is enabled and a party is selected
  if (!featureConfig.value.orderDispatch?.enabled) {
    return; // Feature is disabled, don't show modal
  }

  // Only show the modal if a party is selected and the party name is not empty
  if (billForm.value.partyName && billForm.value.partyName.trim() !== '') {
    // Find the selected party from inventoryData to get state and PIN
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);

    // Populate the orderDetails with current values from billForm
    orderDetails.value = {
      orderNo: billForm.value.orderNo || '',
      orderDate: billForm.value.orderDate || '',
      dispatchThrough: billForm.value.dispatchThrough || '',
      docketNo: billForm.value.docketNo || '',
      vehicleNo: billForm.value.vehicleNo || '',
      // Populate consignee details with party details by default
      consigneeName: billForm.value.partyName || '',
      consigneeGstin: billForm.value.partyGstin || '',
      consigneeAddress: billForm.value.partyAddress || '',
      consigneeState: selectedParty?.state || '',
      consigneePin: selectedParty?.pin || ''
    };
    // Show the modal
    showOrderDispatchModal.value = true;
  }
};

// Handle submission of order and dispatch details
const submitOrderDetails = (details) => {
  // Update the billForm with the submitted details
  billForm.value.orderNo = details.orderNo;
  billForm.value.orderDate = details.orderDate;
  billForm.value.dispatchThrough = details.dispatchThrough;
  billForm.value.docketNo = details.docketNo;
  billForm.value.vehicleNo = details.vehicleNo;
  // Update consignee details
  billForm.value.consigneeName = details.consigneeName;
  billForm.value.consigneeGstin = details.consigneeGstin;
  billForm.value.consigneeAddress = details.consigneeAddress;
  billForm.value.consigneeState = details.consigneeState;
  billForm.value.consigneePin = details.consigneePin;
};

// GST Selection handlers
const onFirmGSTChange = () => {
  console.log('🏢 Firm GST changed to index:', selectedFirmGSTIndex.value);

  // Save to localStorage
  localStorage.setItem('lastSelectedFirmGSTIndex', selectedFirmGSTIndex.value.toString());
  console.log('💾 Saved to localStorage:', localStorage.getItem('lastSelectedFirmGSTIndex'));

  // Recalculate GST for all items when firm GST changes
  billForm.value.stockItems.forEach((_, index) => {
    calculateItemTotal(index);
  });
  calculateBillTotal();
};

const onPartyGSTChange = () => {
  console.log('🏪 Party GST changed to index:', selectedPartyGSTIndex.value);

  // Save to localStorage
  localStorage.setItem('lastSelectedPartyGSTIndex', selectedPartyGSTIndex.value.toString());
  console.log('💾 Saved to localStorage:', localStorage.getItem('lastSelectedPartyGSTIndex'));

  const selectedGST = selectedPartyGSTs.value[selectedPartyGSTIndex.value];
  if (selectedGST) {
    billForm.value.partyGstin = selectedGST.gstNumber;
    billForm.value.partyState = selectedGST.state;

    // Update party address from the selected GST
    if (selectedGST.address) {
      billForm.value.partyAddress = selectedGST.address;
      console.log('📍 Updated party address to:', selectedGST.address);
    }

    // Update party PIN code from the selected GST
    if (selectedGST.pincode) {
      billForm.value.partyPin = selectedGST.pincode;
      console.log('📮 Updated party PIN to:', selectedGST.pincode);
    }

    // Recalculate GST for all items when party GST changes
    billForm.value.stockItems.forEach((_, index) => {
      calculateItemTotal(index);
    });
    calculateBillTotal();
  }
};

// GST Modal handlers
const submitFirmGST = async () => {
  if (isSubmittingGST.value) return;

  try {
    isSubmittingGST.value = true;

    const response = await $fetch('/api/inventory/firm-gst', {
      method: 'POST',
      body: firmGSTForm.value
    });

    if (response.success) {
      success('Firm GST registration added successfully!');
      showAddFirmGSTModal.value = false;

      // Reset form
      firmGSTForm.value = {
        gstNumber: '',
        locationName: '',
        address: '',
        city: '',
        pincode: '',
        registrationType: 'regular'
      };

      // Refresh inventory data to show new GST
      await fetchInventoryData();
    }
  } catch (error) {
    console.error('Error adding firm GST:', error);
    showError(error.data?.statusMessage || 'Failed to add firm GST registration');
  } finally {
    isSubmittingGST.value = false;
  }
};

const openAddPartyGSTModal = () => {
  if (!billForm.value.partyName) {
    showError('Please select a party first');
    return;
  }
  showAddPartyGSTModal.value = true;
};

const submitPartyGST = async () => {
  if (isSubmittingGST.value) return;

  try {
    isSubmittingGST.value = true;

    // Find the selected party
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
    if (!selectedParty) {
      throw new Error('Selected party not found');
    }

    const response = await $fetch('/api/inventory/party-gst', {
      method: 'POST',
      body: {
        partyId: selectedParty._id,
        ...partyGSTForm.value
      }
    });

    if (response.success) {
      success('Party GST registration added successfully!');
      showAddPartyGSTModal.value = false;

      // Reset form
      partyGSTForm.value = {
        gstNumber: '',
        locationName: '',
        address: '',
        city: '',
        pincode: '',
        contactPerson: '',
        contactNumber: '',
        registrationType: 'regular'
      };

      // Refresh inventory data to show new GST
      await fetchInventoryData();
    }
  } catch (error) {
    console.error('Error adding party GST:', error);
    showError(error.data?.statusMessage || 'Failed to add party GST registration');
  } finally {
    isSubmittingGST.value = false;
  }
};

// File upload functions
const handleFileUpload = (event) => {
  const file = event.target.files[0];

  if (!file) {
    selectedFile.value = null;
    return;
  }

  // Check file type (PDF only)
  if (file.type !== 'application/pdf') {
    showError('Only PDF files are allowed. Please select a PDF file.');
    event.target.value = '';
    selectedFile.value = null;
    return;
  }

  // Check file size (200KB = 204800 bytes)
  if (file.size > 204800) {
    showError('File size exceeds maximum limit of 200KB. Please select a smaller file.');
    event.target.value = '';
    selectedFile.value = null;
    return;
  }

  selectedFile.value = file;
};

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

const removeAttachment = async () => {
  if (!billId.value) {
    // If no bill ID, just clear the fields
    billForm.value.attachmentUrl = '';
    billForm.value.attachmentFileId = '';
    return;
  }

  try {
    const { post } = useApiWithAuth();

    // Use the dedicated remove attachment endpoint
    const response = await post('/api/inventory/bills/remove-attachment', {
      billId: billId.value
    });

    if (response.success) {
      // Clear the attachment fields
      billForm.value.attachmentUrl = '';
      billForm.value.attachmentFileId = '';

      success('Attachment removed successfully');
    }
  } catch (error) {
    console.error('Error removing attachment:', error);
    showError('Failed to remove attachment. Please try again.');
  }
};

const handleAsyncFileUpload = async (currentBillId) => {
  // Only upload if there's a selected file and it's a PURCHASE or DEBIT NOTE
  if (!selectedFile.value || !currentBillId) {
    return;
  }

  if (billForm.value.type !== 'PURCHASE' && billForm.value.type !== 'DEBIT NOTE') {
    return;
  }

  // Add a slight delay to ensure bill creation is fully complete
  setTimeout(async () => {
    try {
      isUploadingFile.value = true;
      console.log('Starting file upload for bill:', currentBillId);

      // Create FormData for file upload
      const formData = new FormData();
      formData.append('file', selectedFile.value);
      formData.append('billId', currentBillId);

      const { post } = useApiWithAuth();

      // Upload file using the new dedicated endpoint
      const uploadResponse = await post('/api/inventory/bills/upload-attachment', formData, {
        isFormData: true
      });

      if (uploadResponse.success) {
        // Update the form with the new attachment info
        billForm.value.attachmentUrl = uploadResponse.attachment.url;
        billForm.value.attachmentFileId = uploadResponse.attachment.fileId;

        // Clear the selected file
        selectedFile.value = null;
        if (fileInput.value) {
          fileInput.value.value = '';
        }

        success(`File "${uploadResponse.attachment.filename}" uploaded successfully!`);
        console.log('File upload completed successfully');
      } else {
        throw new Error('Upload response indicates failure');
      }
    } catch (error) {
      console.error('Error uploading file:', error);

      // Provide specific error messages based on error type
      let errorMessage = 'Failed to upload attachment. ';

      if (error.statusCode === 400) {
        errorMessage += 'Please check file format and size requirements.';
      } else if (error.statusCode === 404) {
        errorMessage += 'Bill not found. Please refresh and try again.';
      } else if (error.statusCode === 401) {
        errorMessage += 'Authentication required. Please log in again.';
      } else {
        errorMessage += 'You can try uploading again by editing the bill.';
      }

      showError(errorMessage);
    } finally {
      isUploadingFile.value = false;
    }
  }, 1500); // 1.5 second delay to ensure bill creation is complete
};

// Manual retry function for file upload
const retryFileUpload = () => {
  if (selectedFile.value && billId.value) {
    handleAsyncFileUpload(billId.value);
  }
};

// Add this function in the script section
const handlePartySelection = () => {
  if (billForm.value.partyName === 'Create New Party') {
    billForm.value.partyName = ''; // Clear the input
    showPartyModal.value = true; // Open the modal
  } else {
    // Find the selected party from inventoryData
    const selectedParty = inventoryData.value?.parties?.find(p => p.supply === billForm.value.partyName);
    console.log('Selected party:', selectedParty);

    // Update address, GSTIN and state if party found
    if (selectedParty) {
      billForm.value.partyAddress = selectedParty.addr || '';
      billForm.value.partyGstin = selectedParty.gstin || '';
      billForm.value.partyState = selectedParty.state || '';

      // Reset party GST selection to primary (index 0)
      selectedPartyGSTIndex.value = 0;

      console.log('Updated party state to:', billForm.value.partyState);
      console.log('Available party GSTs:', selectedParty.allGSTs?.length || 0);
    }
  }
};

// Reference to store the item being edited
const editStockItem = ref(null);

// Submit the stock item form
const submitStockItemForm = (stockItemData) => {
  // Check if this is a valid stock item submission (must have item name)
  if (!stockItemData.item) {
    return; // Don't process empty submissions
  }

  // Add user and firm information from the stored variables
  stockItemData.user = userId.value || 'unknown';
  stockItemData.firm = firmName.value || 'unknown';

  // Check if this is an edit operation
  if (stockItemData._isEdit && editStockItem.value) {
    // Find the index of the item being edited
    const editIndex = billForm.value.stockItems.findIndex(item =>
      item.item === editStockItem.value.item &&
      item.batch === editStockItem.value.batch);

    if (editIndex !== -1) {
      // Update the existing item with the new stock item data
      billForm.value.stockItems[editIndex] = {
        ...billForm.value.stockItems[editIndex],
        item: stockItemData.item,
        pno: stockItemData.pno,
        batch: stockItemData.batch,
        oem: stockItemData.oem,
        hsn: stockItemData.hsn,
        qty: stockItemData.qty,
        uom: stockItemData.uom,
        rate: stockItemData.rate,
        grate: stockItemData.grate,
        total: stockItemData.total
      };

      // Recalculate the bill total to ensure GST is calculated properly
      calculateItemTotal(editIndex);
      calculateBillTotal();
    }
  } else if (billForm.value.stockItems.length > 0) {
    // Alt+C should ALWAYS create a new item, never overwrite existing data
    // This preserves any partial data the user has typed
    billForm.value.stockItems.push({
      item: stockItemData.item,
      pno: stockItemData.pno,
      batch: stockItemData.batch,
      oem: stockItemData.oem,
      hsn: stockItemData.hsn,
      qty: stockItemData.qty,
      uom: stockItemData.uom,
      rate: stockItemData.rate,
      grate: stockItemData.grate,
      cgst: 0,
      sgst: 0,
      igst: 0,
      disc: 0,
      project: '',
      total: stockItemData.total,
      item_narration: '',
      mrp: null,
      expiryDate: null
    });

    // Calculate GST for the newly added item, then recalculate bill total
    const newItemIndex = billForm.value.stockItems.length - 1;
    calculateItemTotal(newItemIndex);
  } else {
    // If no stock items exist, add this as the first one
    billForm.value.stockItems.push({
      item: stockItemData.item,
      pno: stockItemData.pno,
      batch: stockItemData.batch,
      oem: stockItemData.oem,
      hsn: stockItemData.hsn,
      qty: stockItemData.qty,
      uom: stockItemData.uom,
      rate: stockItemData.rate,
      grate: stockItemData.grate,
      total: stockItemData.total
    });

    // Calculate GST for the newly added item, then recalculate bill total
    const newItemIndex = billForm.value.stockItems.length - 1;
    calculateItemTotal(newItemIndex);
  }

  // Close the modal and reset edit item
  showStockItemModal.value = false;
  editStockItem.value = null;
};
</script>

<style>
/* Custom styling for datalist options */
#stockItemList option {
  /* These styles will only be visible in some browsers that support styling datalist options */
  font-size: 14px;
  border-bottom: 1px solid #eaeaea;
}

/* Add color indicators based on stock quantity */
input[list="stockItemList"] {
  background-color: white;
  transition: background-color 0.3s ease;
}

/* Add colorful border to the datalist input when focused */
input[list="stockItemList"]:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Custom styling for the datalist dropdown (works in some browsers) */
input[list="stockItemList"]::-webkit-calendar-picker-indicator {
  color: #4f46e5;
}

/* Add a subtle background color change when hovering over the input */
input[list="stockItemList"]:hover {
  background-color: #f9fafb;
}
</style>
