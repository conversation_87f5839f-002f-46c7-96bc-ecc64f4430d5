// server/middleware/logger.ts
import { H3Event } from 'h3';
import { getFirestore } from 'firebase-admin/firestore';

export default defineEventHandler(async (event: H3Event) => {
  // Only log API requests
  const path = getRequestURL(event).pathname;
  if (!path.startsWith('/api/')) {
    return;
  }

  // 🛑 EXCLUDE ADMIN MONITORING ENDPOINTS TO PREVENT FLOODING
  const excludedPaths = [
    '/api/firestore/api-logs-status',      // API logs count check
    '/api/firestore/auto-backup-status',   // Auto backup status
    '/api/firestore/collections',          // Collection info
    '/api/admin/status',                    // Admin status checks
    '/api/health',                          // Health checks
    '/api/ping'                             // Ping checks
  ];

  // Skip logging for excluded paths
  if (excludedPaths.includes(path)) {
    return;
  }

  // Record start time
  const startTime = Date.now();
  
  // Store original response handlers
  const originalHandle = event.node.res.write;
  const originalEnd = event.node.res.end;
  
  let statusCode = 200;
  let responseBody = '';
  
  // Override response.end method to capture status code and response time
  // @ts-ignore - Overriding the method
  event.node.res.end = function(chunk: any, encoding: BufferEncoding, callback?: () => void) {
    // Calculate response time
    const responseTime = Date.now() - startTime;
    
    // Get status code
    statusCode = event.node.res.statusCode;
    
    // Capture response body if it exists
    if (chunk) {
      responseBody += chunk.toString();
    }
    
    // Log the request
    logRequest(event, statusCode, responseTime).catch(err => {
      console.error('Error logging API request:', err);
    });
    
    // Call the original end method
    return originalEnd.call(this, chunk, encoding, callback);
  };
  
  // Continue with the request
  return;
});

async function logRequest(event: H3Event, statusCode: number, responseTime: number) {
  try {
    const db = getFirestore();
    const requestUrl = getRequestURL(event);
    
    // Extract request details
    const method = event.node.req.method || 'UNKNOWN';
    const path = requestUrl.pathname;
    const query = Object.fromEntries(requestUrl.searchParams);
    const headers = event.node.req.headers;
    
    // Get user ID if available
    const userId = event.context.userId || null;
    
    // Create log entry
    const logEntry = {
      timestamp: new Date(),
      method,
      path,
      query: Object.keys(query).length > 0 ? query : null,
      headers: headers ? JSON.stringify(headers) : null,
      userId,
      statusCode,
      responseTime,
      userAgent: headers['user-agent'] || null,
      ip: headers['x-forwarded-for'] || event.node.req.socket.remoteAddress || null
    };
    
    // Add to Firestore
    await db.collection('api_logs').add(logEntry);
  } catch (error) {
    // Log error but don't interrupt the request flow
    console.error('Failed to log API request:', error);
  }
}