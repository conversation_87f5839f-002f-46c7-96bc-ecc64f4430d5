<template>
  <div class="space-y-6">
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-green-800 mb-4">Employment Bulk Update</h3>
      
      <div class="space-y-4">
        <!-- Status Field -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="fieldsToEdit.status" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2 text-sm font-medium">Status</span>
          </label>
          <div v-if="fieldsToEdit.status" class="flex-1">
            <select 
              v-model="editValues.status" 
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
            >
              <option value="Active">Active</option>
              <option value="Inactive">Inactive</option>
              <option value="On Leave">On Leave</option>
              <option value="Terminated">Terminated</option>
            </select>
          </div>
        </div>

        <!-- Category Field -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="fieldsToEdit.category" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2 text-sm font-medium">Category</span>
          </label>
          <div v-if="fieldsToEdit.category" class="flex-1">
            <select 
              v-model="editValues.category" 
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
            >
              <option value="">Select Category</option>
              <option v-for="category in uniqueCategories" :key="category" :value="category">
                {{ category }}
              </option>
            </select>
          </div>
        </div>

        <!-- Project Field -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="fieldsToEdit.project" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2 text-sm font-medium">Project</span>
          </label>
          <div v-if="fieldsToEdit.project" class="flex-1">
            <select 
              v-model="editValues.project" 
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
            >
              <option value="">Select Project</option>
              <option v-for="project in uniqueProjects" :key="project" :value="project">
                {{ project }}
              </option>
            </select>
          </div>
        </div>

        <!-- Site Field -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="fieldsToEdit.site" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2 text-sm font-medium">Site</span>
          </label>
          <div v-if="fieldsToEdit.site" class="flex-1">
            <select 
              v-model="editValues.site" 
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
            >
              <option value="">Select Site</option>
              <option v-for="site in uniqueSites" :key="site" :value="site">
                {{ site }}
              </option>
            </select>
          </div>
        </div>

        <!-- Daily Wage Field -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="fieldsToEdit.pDayWage" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2 text-sm font-medium">Daily Wage</span>
          </label>
          <div v-if="fieldsToEdit.pDayWage" class="flex-1">
            <input
              type="number"
              v-model="editValues.pDayWage"
              placeholder="Enter daily wage amount"
              min="0"
              step="0.01"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
          </div>
        </div>

        <!-- Date of Exit Field -->
        <div class="flex items-center space-x-4">
          <label class="flex items-center">
            <input 
              type="checkbox" 
              v-model="fieldsToEdit.dateOfExit" 
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2 text-sm font-medium">Date of Exit</span>
          </label>
          <div v-if="fieldsToEdit.dateOfExit" class="flex-1">
            <input
              type="date"
              v-model="editValues.dateOfExit"
              class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
          </div>
        </div>
      </div>

      <!-- Warning Notice -->
      <div class="mt-6 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">
              Bulk Update Warning
            </h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>
                These changes will be applied to <strong>{{ selectedEmployeesCount }}</strong> employees.
                Please ensure the values are correct before proceeding.
              </p>
            </div>
          </div>
        </div>
      </div>

      <!-- Summary -->
      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div class="text-sm font-medium text-green-800">
          Fields to Update: {{ selectedFieldsCount }}
          <span v-if="selectedFieldsCount === 0" class="text-red-600 ml-2">
            (Please select at least one field)
          </span>
        </div>
        <div v-if="selectedFieldsCount > 0" class="text-xs text-green-600 mt-1">
          Selected: {{ selectedFieldsList.join(', ') }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  selectedEmployeesCount: {
    type: Number,
    required: true
  },
  uniqueCategories: {
    type: Array,
    default: () => []
  },
  uniqueProjects: {
    type: Array,
    default: () => []
  },
  uniqueSites: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:changes'])

// Field configuration
const fieldsToEdit = ref({
  status: false,
  category: false,
  project: false,
  site: false,
  pDayWage: false,
  dateOfExit: false
})

const editValues = ref({
  status: 'Active',
  category: '',
  project: '',
  site: '',
  pDayWage: '',
  dateOfExit: ''
})

// Computed properties
const selectedFieldsCount = computed(() => {
  return Object.values(fieldsToEdit.value).filter(Boolean).length
})

const selectedFieldsList = computed(() => {
  const labels = {
    status: 'Status',
    category: 'Category',
    project: 'Project',
    site: 'Site',
    pDayWage: 'Daily Wage',
    dateOfExit: 'Date of Exit'
  }
  
  return Object.keys(fieldsToEdit.value)
    .filter(key => fieldsToEdit.value[key])
    .map(key => labels[key])
})

const selectedFieldsToEdit = computed(() => {
  const selected = {}
  Object.keys(fieldsToEdit.value).forEach(key => {
    if (fieldsToEdit.value[key]) {
      selected[key] = editValues.value[key]
    }
  })
  return selected
})

// Watch for changes and emit to parent
watch([fieldsToEdit, editValues], () => {
  emit('update:changes', {
    fieldsToEdit: fieldsToEdit.value,
    editValues: editValues.value,
    selectedFields: selectedFieldsToEdit.value
  })
}, { deep: true })

// Reset function
const resetForm = () => {
  Object.keys(fieldsToEdit.value).forEach(key => {
    fieldsToEdit.value[key] = false
  })
  
  editValues.value = {
    status: 'Active',
    category: '',
    project: '',
    site: '',
    pDayWage: '',
    dateOfExit: ''
  }
}

// Expose reset function to parent
defineExpose({
  resetForm
})
</script>
