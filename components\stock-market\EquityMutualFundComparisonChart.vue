<template>
  <div class="chart-container">
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, onBeforeUnmount } from 'vue';
import Chart from 'chart.js/auto';

// Props
const props = defineProps({
  chartData: {
    type: Object,
    required: true,
    default: () => ({
      labels: [],
      equityValue: 0,
      mutualFundValue: 0
    })
  },
  height: {
    type: String,
    default: '300px'
  }
});

// Refs
const chartCanvas = ref(null);
let chart = null;

// Function to initialize or update the chart
const initChart = () => {
  if (!chartCanvas.value) return;

  // If chart already exists, destroy it first
  if (chart) {
    chart.destroy();
  }

  // Create new chart
  const ctx = chartCanvas.value.getContext('2d');
  chart = new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: props.chartData.labels,
      datasets: [
        {
          data: [props.chartData.equityValue, props.chartData.mutualFundValue],
          backgroundColor: [
            'rgba(59, 130, 246, 0.7)', // blue-500 with opacity for equity
            'rgba(139, 92, 246, 0.7)'  // purple-500 with opacity for mutual funds
          ],
          borderColor: [
            '#3b82f6', // blue-500
            '#8b5cf6'  // purple-500
          ],
          borderWidth: 1,
          hoverOffset: 4
        }
      ]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          position: 'top',
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              let label = context.label || '';
              if (label) {
                label += ': ';
              }
              if (context.raw !== null) {
                label += new Intl.NumberFormat('en-IN', {
                  style: 'currency',
                  currency: 'INR',
                  maximumFractionDigits: 0
                }).format(context.raw);
                
                // Add percentage
                const total = props.chartData.equityValue + props.chartData.mutualFundValue;
                if (total > 0) {
                  const percentage = (context.raw / total * 100).toFixed(1);
                  label += ` (${percentage}%)`;
                }
              }
              return label;
            }
          }
        }
      }
    }
  });
};

// Initialize chart on mount
onMounted(() => {
  // Set the height of the chart container
  if (chartCanvas.value && chartCanvas.value.parentElement) {
    chartCanvas.value.parentElement.style.height = props.height;
  }
  
  initChart();
});

// Watch for changes in chart data
watch(() => props.chartData, () => {
  initChart();
}, { deep: true });

// Clean up on unmount
onBeforeUnmount(() => {
  if (chart) {
    chart.destroy();
    chart = null;
  }
});
</script>

<style scoped>
.chart-container {
  position: relative;
  width: 100%;
}
</style>