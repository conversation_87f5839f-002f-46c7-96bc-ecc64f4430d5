#!/bin/bash

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Node.js is not installed. Please install Node.js to run this script."
    exit 1
fi

# Check if the update script exists
if [ ! -f "scripts/update-user-roles-esm.js" ]; then
    echo "Error: update-user-roles-esm.js script not found."
    exit 1
fi

# Run the update script
echo "Updating user roles in the database..."
node scripts/update-user-roles-esm.js

# Check if the script executed successfully
if [ $? -eq 0 ]; then
    echo "User roles updated successfully!"
else
    echo "Error: Failed to update user roles."
    exit 1
fi

echo "Done!"
