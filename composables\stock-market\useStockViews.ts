import { ref, reactive, computed } from 'vue';

interface StockView {
  id: string;
  name: string;
  symbols: string[];
  createdAt: string;
  updatedAt: string;
}

export function useStockViews() {
  const views = ref<StockView[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const activeViewId = ref<string | null>(null);

  // Fetch all views
  const fetchViews = async () => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{ views: StockView[], error?: string }>('/api/stock-views');

      if (response.error) {
        throw new Error(response.error);
      }

      views.value = response.views || [];

      // Set active view to the first one if none is selected
      if (views.value.length > 0 && !activeViewId.value) {
        activeViewId.value = views.value[0].id;
      }

      return views.value;
    } catch (err: any) {
      console.error('Error fetching stock views:', err);
      error.value = err.message || 'Failed to fetch stock views';
      return [];
    } finally {
      isLoading.value = false;
    }
  };

  // Create a new view
  const createView = async (name: string, symbols: string[] = []) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{ view: StockView, success: boolean, error?: string }>('/api/stock-views/create', {
        method: 'POST',
        body: { name, symbols }
      });

      if (!response.success || response.error) {
        throw new Error(response.error || 'Failed to create view');
      }

      // Add the new view to the list
      views.value.push(response.view);

      // Set as active view if it's the first one
      if (views.value.length === 1) {
        activeViewId.value = response.view.id;
      }

      return response.view;
    } catch (err: any) {
      console.error('Error creating stock view:', err);
      error.value = err.message || 'Failed to create stock view';
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // Update a view
  const updateView = async (id: string, data: { name?: string, symbols?: string[] }) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{ view: StockView, success: boolean, error?: string }>('/api/stock-views/update', {
        method: 'POST',
        body: { id, ...data }
      });

      if (!response.success || response.error) {
        throw new Error(response.error || 'Failed to update view');
      }

      // Update the view in the list
      const index = views.value.findIndex(v => v.id === id);
      if (index !== -1) {
        views.value[index] = response.view;
      }

      return response.view;
    } catch (err: any) {
      console.error('Error updating stock view:', err);
      error.value = err.message || 'Failed to update stock view';
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // Delete a view
  const deleteView = async (id: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{ success: boolean, error?: string }>('/api/stock-views/delete', {
        method: 'POST',
        body: { id }
      });

      if (!response.success || response.error) {
        throw new Error(response.error || 'Failed to delete view');
      }

      // Remove the view from the list
      views.value = views.value.filter(v => v.id !== id);

      // If the active view was deleted, set a new active view
      if (activeViewId.value === id) {
        activeViewId.value = views.value.length > 0 ? views.value[0].id : null;
      }

      return true;
    } catch (err: any) {
      console.error('Error deleting stock view:', err);
      error.value = err.message || 'Failed to delete stock view';
      return false;
    } finally {
      isLoading.value = false;
    }
  };

  // Add a symbol to a view
  const addSymbolToView = async (viewId: string, symbol: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{ view: StockView, success: boolean, error?: string }>('/api/stock-views/update', {
        method: 'POST',
        body: { id: viewId, action: 'add', symbol }
      });

      if (!response.success || response.error) {
        throw new Error(response.error || 'Failed to add symbol to view');
      }

      // Update the view in the list
      console.log('Add symbol response:', {
        success: response.success,
        viewId,
        symbol,
        responseView: response.view,
        symbolsCount: response.view.symbols.length,
        symbols: response.view.symbols
      });

      const index = views.value.findIndex(v => v.id === viewId);
      if (index !== -1) {
        console.log('Before updating view in list:', {
          oldView: views.value[index],
          oldSymbolsCount: views.value[index].symbols.length,
          oldSymbols: views.value[index].symbols
        });

        views.value[index] = response.view;

        console.log('After updating view in list:', {
          newView: views.value[index],
          newSymbolsCount: views.value[index].symbols.length,
          newSymbols: views.value[index].symbols
        });
      } else {
        console.log('View not found in list:', viewId);
      }

      return response.view;
    } catch (err: any) {
      console.error('Error adding symbol to view:', err);
      error.value = err.message || 'Failed to add symbol to view';
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // Remove a symbol from a view
  const removeSymbolFromView = async (viewId: string, symbol: string) => {
    isLoading.value = true;
    error.value = null;

    try {
      const response = await $fetch<{ view: StockView, success: boolean, error?: string }>('/api/stock-views/update', {
        method: 'POST',
        body: { id: viewId, action: 'remove', symbol }
      });

      if (!response.success || response.error) {
        throw new Error(response.error || 'Failed to remove symbol from view');
      }

      // Update the view in the list
      const index = views.value.findIndex(v => v.id === viewId);
      if (index !== -1) {
        views.value[index] = response.view;
      }

      return response.view;
    } catch (err: any) {
      console.error('Error removing symbol from view:', err);
      error.value = err.message || 'Failed to remove symbol from view';
      return null;
    } finally {
      isLoading.value = false;
    }
  };

  // Get the active view
  const activeView = computed(() => {
    if (!activeViewId.value) return null;
    return views.value.find(v => v.id === activeViewId.value) || null;
  });

  // Set the active view
  const setActiveView = (id: string) => {
    activeViewId.value = id;
  };

  return {
    views,
    isLoading,
    error,
    activeViewId,
    activeView,
    fetchViews,
    createView,
    updateView,
    deleteView,
    addSymbolToView,
    removeSymbolFromView,
    setActiveView
  };
}
