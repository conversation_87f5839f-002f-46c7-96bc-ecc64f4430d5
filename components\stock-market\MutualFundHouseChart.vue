<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-4">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Fund House Allocation</h3>
    
    <div v-if="isLoading" class="flex justify-center items-center py-6">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
    </div>
    
    <div v-else-if="error" class="text-red-500 text-center py-4">
      {{ error }}
    </div>
    
    <div v-else-if="!fundHouseAllocation || fundHouseAllocation.length === 0" class="text-gray-500 dark:text-gray-400 text-center py-4">
      No fund house allocation data available
    </div>
    
    <div v-else class="flex flex-col md:flex-row">
      <!-- Chart Canvas -->
      <div class="w-full md:w-1/2 h-64">
        <canvas ref="chartCanvas"></canvas>
      </div>
      
      <!-- Legend -->
      <div class="w-full md:w-1/2 pl-0 md:pl-4 mt-4 md:mt-0">
        <div v-for="(item, index) in sortedFundHouseAllocation" :key="item.fundHouse" class="flex items-center mb-2">
          <div class="w-4 h-4 mr-2" :style="{ backgroundColor: getChartColors()[index % getChartColors().length] }"></div>
          <div class="flex-1 text-sm text-gray-700 dark:text-gray-300">{{ item.fundHouse }}</div>
          <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
            ₹{{ formatNumber(item.value) }}
            <span class="text-xs text-gray-500 dark:text-gray-400 ml-1">
              ({{ ((item.value / totalValue) * 100).toFixed(1) }}%)
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import Chart from 'chart.js/auto';

const props = defineProps({
  fundHouseAllocation: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
});

const chartCanvas = ref(null);
let chart = null;

// Sort fund house allocation by value (descending)
const sortedFundHouseAllocation = computed(() => {
  return [...props.fundHouseAllocation].sort((a, b) => b.value - a.value);
});

// Calculate total value
const totalValue = computed(() => {
  return props.fundHouseAllocation.reduce((sum, item) => sum + item.value, 0);
});

// Format number with commas for thousands
const formatNumber = (number) => {
  if (number === null || number === undefined) return '-';
  return number.toLocaleString('en-IN', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 0
  });
};

// Get chart colors
const getChartColors = () => {
  return [
    '#4F46E5', // indigo-600
    '#0891B2', // cyan-600
    '#0D9488', // teal-600
    '#16A34A', // green-600
    '#CA8A04', // yellow-600
    '#EA580C', // orange-600
    '#DC2626', // red-600
    '#DB2777', // pink-600
    '#9333EA', // purple-600
    '#2563EB', // blue-600
    '#059669', // emerald-600
    '#65A30D', // lime-600
    '#D97706', // amber-600
    '#F97316', // orange-500
    '#EF4444', // red-500
    '#EC4899', // pink-500
    '#A855F7', // purple-500
    '#3B82F6', // blue-500
    '#06B6D4', // cyan-500
    '#14B8A6', // teal-500
    '#22C55E', // green-500
    '#84CC16', // lime-500
    '#EAB308', // yellow-500
    '#F59E0B', // amber-500
  ];
};

// Render chart
const renderChart = () => {
  if (!chartCanvas.value || !sortedFundHouseAllocation.value.length) return;

  const ctx = chartCanvas.value.getContext('2d');
  
  if (chart) {
    chart.destroy();
  }

  const labels = sortedFundHouseAllocation.value.map(item => item.fundHouse);
  const data = sortedFundHouseAllocation.value.map(item => item.value);
  const colors = getChartColors().slice(0, sortedFundHouseAllocation.value.length);

  chart = new Chart(ctx, {
    type: 'pie',
    data: {
      labels: labels,
      datasets: [{
        data: data,
        backgroundColor: colors,
        borderColor: colors,
        borderWidth: 1
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: false
        },
        tooltip: {
          callbacks: {
            label: function(context) {
              const label = context.label || '';
              const value = context.raw;
              const percentage = (value / context.dataset.data.reduce((a, b) => a + b, 0)) * 100;
              return `${label}: ₹${formatNumber(value)} (${percentage.toFixed(1)}%)`;
            }
          }
        }
      }
    }
  });
};

// Watch for changes in fund house allocation
watch(() => props.fundHouseAllocation, () => {
  renderChart();
}, { deep: true });

// Render chart on mount
onMounted(() => {
  renderChart();
});
</script>
