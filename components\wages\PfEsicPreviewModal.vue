<template>
  <div v-if="show" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50" @click="$emit('close')">
    <div class="relative top-2 mx-auto p-3 border w-[98%] max-w-[98%] shadow-lg rounded-md bg-white max-h-[96vh] overflow-hidden flex flex-col" @click.stop>
      <!-- <PERSON><PERSON> Header - Compact -->
      <div class="flex items-center justify-between pb-2 border-b flex-shrink-0">
        <h3 class="text-base font-semibold text-gray-900">
          PF & ESIC Preview - {{ formatMonthYear(selectedMonth) }}
        </h3>
        <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Scrollable Content Area -->
      <div class="flex-1 overflow-y-auto mt-2">

        <!-- Loading State -->
        <div v-if="loading" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"></div>
          <span class="ml-3 text-gray-600">Loading PF & ESIC data...</span>
        </div>

        <!-- Content -->
        <div v-else-if="pfEsicData.length > 0">
          <!-- AI Configuration Warning (if not configured) -->
          <div v-if="!isConfigured" class="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <div class="flex items-start">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3 flex-1">
                <h3 class="text-sm font-medium text-yellow-800">AI Configuration Required</h3>
                <p class="mt-1 text-sm text-yellow-700">
                  Please configure your AI settings to use AI-powered EPF/ESIC rate updates. Choose your preferred AI provider and API key.
                </p>
                <div class="mt-3">
                  <button
                    @click="openAISettings"
                    class="bg-yellow-600 text-white px-4 py-2 rounded-lg hover:bg-yellow-700 transition-colors text-sm"
                  >
                    Configure AI Settings
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- AI-Powered Government Rules Information -->
          <div v-else class="mt-4 bg-gradient-to-r from-blue-50 to-green-50 p-4 rounded-lg border border-blue-200">
            <div class="flex items-center justify-between mb-2">
              <div class="flex items-center space-x-2">
                <h4 class="text-md font-medium text-blue-800">🤖 AI-Fetched Government Rules</h4>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                  Live Updates
                </span>
                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {{ getProviderName(aiConfig.provider) }} - {{ aiConfig.model }}
                </span>
              </div>
              <div class="flex items-center">
                <button
                  @click="openAISettings"
                  class="px-3 py-1 border border-transparent text-xs font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mr-2"
                  title="Configure AI settings"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Settings
                </button>
                <button
                  @click="showAIGuidanceDialog = true"
                  :disabled="refreshingAiRules"
                  class="px-3 py-1 border border-transparent text-xs font-medium rounded-md text-white bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center mr-2"
                  title="Refresh EPF/ESIC rates from AI system with custom guidance"
                >
                  <svg v-if="refreshingAiRules" class="animate-spin -ml-1 mr-1 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  {{ refreshingAiRules ? 'Updating...' : '🤖 Refresh' }}
                </button>
                <span class="text-xs text-blue-600">Updated via AI from official sources</span>
              </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <h5 class="font-medium text-blue-800 mb-1">EPF Rates (AI-Verified)</h5>
                <ul v-if="getCurrentRules.epf" class="text-blue-700 space-y-1">
                  <li>• Employee: {{ (getCurrentRules.epf.employeeRate * 100).toFixed(1) }}% (Max ₹{{ getCurrentRules.epf.maxEmployeeContribution?.toLocaleString('en-IN') }})</li>
                  <li>• Employer EPF: {{ (getCurrentRules.epf.employerEpfRate * 100).toFixed(2) }}%</li>
                  <li>• Employer EPS: {{ (getCurrentRules.epf.employerEpsRate * 100).toFixed(2) }}% (Max ₹{{ getCurrentRules.epf.maxEpsContribution?.toLocaleString('en-IN') }})</li>
                  <li>• EDLI: {{ (getCurrentRules.epf.edliRate * 100).toFixed(1) }}%</li>
                  <li>• Admin Charges: {{ (getCurrentRules.epf.adminChargesRate * 100).toFixed(2) }}% (Max ₹{{ getCurrentRules.epf.maxAdminCharges?.toLocaleString('en-IN') }})</li>
                  <li>• Wage Limit: ₹{{ getCurrentRules.epf.wageLimit?.toLocaleString('en-IN') }}</li>
                </ul>
                <div v-else class="text-red-600 text-sm">
                  <p>⚠️ No EPF data available. Please refresh to fetch current rates from AI system.</p>
                </div>
              </div>
              <div>
                <h5 class="font-medium text-blue-800 mb-1">ESIC Rates (AI-Verified)</h5>
                <ul v-if="getCurrentRules.esic" class="text-blue-700 space-y-1">
                  <li>• Employee: {{ (getCurrentRules.esic.employeeRate * 100).toFixed(2) }}%</li>
                  <li>• Employer: {{ (getCurrentRules.esic.employerRate * 100).toFixed(2) }}%</li>
                  <li>• Wage Limit: ₹{{ getCurrentRules.esic.wageLimit?.toLocaleString('en-IN') }}</li>
                </ul>
                <div v-else class="text-red-600 text-sm">
                  <p>⚠️ No ESIC data available. Please refresh to fetch current rates from AI system.</p>
                </div>
                <div v-if="getCurrentRules.epf || getCurrentRules.esic" class="mt-2 text-xs text-blue-600">
                  <div>Last Updated: {{ getCurrentRules.epf?.lastUpdated ? new Date(getCurrentRules.epf.lastUpdated).toLocaleDateString() : (getCurrentRules.esic?.lastUpdated ? new Date(getCurrentRules.esic.lastUpdated).toLocaleDateString() : 'N/A') }}</div>
                  <div>Source: {{ getCurrentRules.epf?.source || getCurrentRules.esic?.source || 'AI System' }}</div>
                </div>
              </div>
            </div>
            <div v-if="refreshingAiRules" class="mt-2 text-xs text-blue-600 flex items-center">
              <svg class="animate-spin -ml-1 mr-2 h-3 w-3 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Updating rules from AI system...
            </div>
          </div>
          <!-- Filters Section - Compact -->
          <div class="mt-2 bg-purple-50 p-2 rounded-lg">
            <div class="flex flex-wrap items-center gap-2">
              <h4 class="text-sm font-medium text-gray-900">Filters:</h4>
              <div class="flex items-center space-x-1">
                <label class="text-xs font-medium text-gray-700">Project:</label>
                <select v-model="filters.project" class="text-xs border border-gray-300 rounded px-1 py-1 focus:ring-purple-500 focus:border-purple-500">
                  <option value="">All Projects</option>
                  <option v-for="project in uniqueProjects" :key="project" :value="project">{{ project }}</option>
                </select>
              </div>
              <div class="flex items-center space-x-1">
                <label class="text-xs font-medium text-gray-700">Site:</label>
                <select v-model="filters.site" class="text-xs border border-gray-300 rounded px-1 py-1 focus:ring-purple-500 focus:border-purple-500">
                  <option value="">All Sites</option>
                  <option v-for="site in uniqueSites" :key="site" :value="site">{{ site }}</option>
                </select>
              </div>
              <div class="flex items-center space-x-1">
                <label class="text-xs font-medium text-gray-700">Status:</label>
                <select v-model="filters.status" class="text-xs border border-gray-300 rounded px-1 py-1 focus:ring-purple-500 focus:border-purple-500">
                  <option value="">All</option>
                  <option value="paid">Paid</option>
                  <option value="unpaid">Unpaid</option>
                </select>
              </div>
              <button
                @click="clearFilters"
                class="text-xs px-2 py-1 bg-gray-200 text-gray-700 rounded hover:bg-gray-300"
              >
                Clear
              </button>
            </div>
          </div>

          <!-- Summary Totals - Compact -->
          <div class="mt-2 bg-blue-50 p-2 rounded-lg">
            <h4 class="text-sm font-medium text-gray-900 mb-2">
              Summary ({{ filteredData.length }} of {{ pfEsicData.length }} employees)
              <span v-if="filteredData.length !== pfEsicData.length" class="text-xs text-blue-600 font-normal">- Filtered</span>
            </h4>

            <!-- Employee Count Summary - Compact -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm mb-2">
              <div class="bg-white p-1 rounded border">
                <p class="text-xs text-gray-500">Total</p>
                <p class="font-semibold text-xs">{{ filteredData.length }}</p>
              </div>
              <div class="bg-white p-1 rounded border">
                <p class="text-xs text-gray-500">Paid</p>
                <p class="font-semibold text-green-600 text-xs">{{ paidEmployeesCount }}</p>
              </div>
              <div class="bg-white p-1 rounded border">
                <p class="text-xs text-gray-500">Unpaid</p>
                <p class="font-semibold text-orange-600 text-xs">{{ unpaidEmployeesCount }}</p>
              </div>
              <div class="bg-white p-1 rounded border">
                <p class="text-xs text-gray-500">Gross Salary</p>
                <p class="font-semibold text-xs">₹{{ formatIndianCurrency(totalGrossSalary) }}</p>
              </div>
            </div>

            <!-- EPF Summary -->
            <div class="mb-4">
              <h5 class="text-sm font-medium text-gray-800 mb-2">EPF Summary</h5>
              <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-3 text-sm">
                <div class="bg-blue-50 p-2 rounded border border-blue-200">
                  <p class="text-xs text-blue-600">Employee EPF</p>
                  <p class="font-semibold text-blue-800">₹{{ formatIndianCurrency(totalEmployeeEpf) }}</p>
                </div>
                <div class="bg-green-50 p-2 rounded border border-green-200">
                  <p class="text-xs text-green-600">Employer EPF</p>
                  <p class="font-semibold text-green-800">₹{{ formatIndianCurrency(totalEmployerEpf) }}</p>
                </div>
                <div class="bg-yellow-50 p-2 rounded border border-yellow-200">
                  <p class="text-xs text-yellow-600">Employer EPS</p>
                  <p class="font-semibold text-yellow-800">₹{{ formatIndianCurrency(totalEmployerEps) }}</p>
                </div>
                <div class="bg-purple-50 p-2 rounded border border-purple-200">
                  <p class="text-xs text-purple-600">EDLI</p>
                  <p class="font-semibold text-purple-800">₹{{ formatIndianCurrency(totalEdli) }}</p>
                </div>
                <div class="bg-orange-50 p-2 rounded border border-orange-200">
                  <p class="text-xs text-orange-600">Admin Charges</p>
                  <p class="font-semibold text-orange-800">₹{{ formatIndianCurrency(totalAdminCharges) }}</p>
                </div>
                <div class="bg-indigo-50 p-2 rounded border border-indigo-200">
                  <p class="text-xs text-indigo-600">Total Employer EPF</p>
                  <p class="font-semibold text-indigo-800">₹{{ formatIndianCurrency(totalEmployerEpfContribution) }}</p>
                </div>
                <div class="bg-gray-50 p-2 rounded border border-gray-200">
                  <p class="text-xs text-gray-600">Total EPF</p>
                  <p class="font-bold text-gray-800">₹{{ formatIndianCurrency(totalEpfContribution) }}</p>
                </div>
              </div>
            </div>

            <!-- ESIC Summary -->
            <div>
              <h5 class="text-sm font-medium text-gray-800 mb-2">ESIC Summary</h5>
              <div class="grid grid-cols-2 md:grid-cols-3 gap-3 text-sm">
                <div class="bg-blue-50 p-2 rounded border border-blue-200">
                  <p class="text-xs text-blue-600">Employee ESIC (0.75%)</p>
                  <p class="font-semibold text-blue-800">₹{{ formatIndianCurrency(totalEmployeeEsic) }}</p>
                </div>
                <div class="bg-green-50 p-2 rounded border border-green-200">
                  <p class="text-xs text-green-600">Employer ESIC (3.25%)</p>
                  <p class="font-semibold text-green-800">₹{{ formatIndianCurrency(totalEmployerEsic) }}</p>
                </div>
                <div class="bg-gray-50 p-2 rounded border border-gray-200">
                  <p class="text-xs text-gray-600">Total ESIC</p>
                  <p class="font-bold text-gray-800">₹{{ formatIndianCurrency(totalEsicContribution) }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Employee Details Table -->
          <div class="mt-2 flex-1 min-h-0">
            <div class="flex justify-between items-center mb-2">
              <div>
                <h4 class="text-sm font-medium text-gray-900">Employee Details ({{ filteredData.length }})</h4>
                <p class="text-xs text-gray-500">Click ✏️ to edit wage days, per day wage, gross salary, and PF/ESIC values</p>
              </div>
              <button
                @click="exportToExcel"
                class="flex items-center px-2 py-1 text-xs font-medium text-white bg-green-600 border border-transparent rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Export
              </button>
            </div>
            <div class="flex-1 overflow-auto border rounded-lg" style="max-height: calc(100vh - 400px); min-height: 300px;">
              <table class="min-w-full divide-y divide-gray-200 table-fixed">
                <colgroup>
                  <col class="w-12"><!-- Sl. -->
                  <col class="w-44"><!-- Employee -->
                  <col class="w-36"><!-- Project/Site -->
                  <col class="w-28"><!-- UAN/ESIC -->
                  <col class="w-20"><!-- Wage Days -->
                  <col class="w-24"><!-- Per Day Wage -->
                  <col class="w-24"><!-- Gross Salary -->
                  <col class="w-24"><!-- Employee EPF -->
                  <col class="w-24"><!-- Employer EPF -->
                  <col class="w-20"><!-- EPS -->
                  <col class="w-20"><!-- Admin Charges -->
                  <col class="w-24"><!-- Employee ESIC -->
                  <col class="w-24"><!-- Employer ESIC -->
                  <col class="w-20"><!-- Status -->
                  <col class="w-24"><!-- Actions -->
                </colgroup>
                <thead class="bg-gradient-to-r from-purple-500 to-blue-600 sticky top-0">
                  <tr>
                    <th class="px-2 py-2 text-center text-xs font-medium text-white uppercase">Sl.</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-white uppercase">Employee</th>
                    <th class="px-3 py-2 text-left text-xs font-medium text-white uppercase">Project/Site</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-white uppercase">UAN/ESIC</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Wage Days</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Per Day Wage</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Gross Salary</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Employee EPF</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Employer EPF</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">EPS</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Admin Charges</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Employee ESIC</th>
                    <th class="px-2 py-2 text-right text-xs font-medium text-white uppercase">Employer ESIC</th>
                    <th class="px-2 py-2 text-center text-xs font-medium text-white uppercase">Status</th>
                    <th class="px-2 py-2 text-center text-xs font-medium text-white uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(employee, index) in filteredData" :key="employee.employeeId"
                      :class="[
                        'hover:bg-gray-50',
                        employee.paymentStatus === 'paid' ? 'bg-green-50' : 'bg-orange-50'
                      ]">
                    <td class="px-2 py-2 text-xs text-center font-medium">{{ index + 1 }}</td>
                    <td class="px-3 py-2 text-sm">
                      <div class="font-medium text-gray-900 truncate">{{ employee.employeeName }}</div>
                      <div class="text-xs text-gray-500 truncate">{{ employee.category }}</div>
                    </td>
                    <td class="px-3 py-2 text-sm">
                      <div class="text-gray-900 truncate">{{ employee.project }}</div>
                      <div class="text-xs text-gray-500 truncate">{{ employee.site }}</div>
                    </td>
                    <td class="px-2 py-2 text-xs">
                      <div class="text-gray-900 truncate">{{ employee.uan || 'N/A' }}</div>
                      <div class="text-gray-500 truncate">{{ employee.esicNo || 'N/A' }}</div>
                    </td>
                    <!-- Wage Days - Editable -->
                    <td class="px-2 py-2 text-sm text-right font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.wageDays"
                        type="number"
                        min="0"
                        max="31"
                        class="w-16 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>{{ employee.wageDays || 0 }}</span>
                    </td>
                    <!-- Per Day Wage - Editable -->
                    <td class="px-2 py-2 text-sm text-right font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.pDayWage"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-20 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.pDayWage) }}</span>
                    </td>
                    <!-- Gross Salary - Auto-calculated or Editable -->
                    <td class="px-2 py-2 text-sm text-right font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.grossSalary"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-24 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.grossSalary) }}</span>
                    </td>
                    <!-- Employee EPF - Editable -->
                    <td class="px-2 py-2 text-sm text-right text-blue-600 font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.employeeEpf"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-20 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.employeeEpf) }}</span>
                    </td>
                    <!-- Employer EPF - Editable -->
                    <td class="px-2 py-2 text-sm text-right text-green-600 font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.employerEpf"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-20 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.employerEpf) }}</span>
                    </td>
                    <!-- Employer EPS - Editable -->
                    <td class="px-2 py-2 text-sm text-right text-yellow-600 font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.employerEps"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-16 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.employerEps) }}</span>
                    </td>
                    <!-- Admin Charges - Editable -->
                    <td class="px-2 py-2 text-sm text-right text-orange-600 font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.adminCharges"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-16 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.adminCharges) }}</span>
                    </td>
                    <!-- Employee ESIC - Editable -->
                    <td class="px-2 py-2 text-sm text-right text-blue-600 font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.employeeEsic"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-20 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.employeeEsic) }}</span>
                    </td>
                    <!-- Employer ESIC - Editable -->
                    <td class="px-2 py-2 text-sm text-right text-green-600 font-medium">
                      <input
                        v-if="editingEmployeeId === employee.employeeId"
                        v-model="editingData.employerEsic"
                        type="number"
                        min="0"
                        step="0.01"
                        class="w-20 px-1 py-1 text-sm text-right border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                      <span v-else>₹{{ formatIndianCurrency(employee.employerEsic) }}</span>
                    </td>
                    <td class="px-2 py-2 text-xs text-center">
                      <span :class="[
                        'inline-flex px-2 py-1 text-xs font-semibold rounded-full',
                        employee.paymentStatus === 'paid'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-orange-100 text-orange-800'
                      ]">
                        {{ employee.paymentStatus }}
                      </span>
                    </td>
                    <!-- Actions Column -->
                    <td class="px-2 py-2 text-xs text-center">
                      <div v-if="editingEmployeeId === employee.employeeId" class="flex space-x-1">
                        <button
                          @click="saveEditing"
                          class="px-2 py-1 text-xs bg-green-600 text-white rounded hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
                          title="Save changes"
                        >
                          ✓
                        </button>
                        <button
                          @click="cancelEditing"
                          class="px-2 py-1 text-xs bg-gray-600 text-white rounded hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500"
                          title="Cancel editing"
                        >
                          ✕
                        </button>
                      </div>
                      <button
                        v-else
                        @click="startEditing(employee)"
                        class="px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        title="Edit wage days, per day wage, gross salary, and PF/ESIC values"
                      >
                        ✏️
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- No Data State -->
        <div v-else-if="!loading" class="text-center py-8">
          <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <h3 class="mt-2 text-sm font-medium text-gray-900">No PF/ESIC data found</h3>
          <p class="mt-1 text-sm text-gray-500">No active employees found for the selected month.</p>
          <div class="mt-6">
            <button
              @click="$emit('refresh')"
              class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
              Refresh Data
            </button>
          </div>
        </div>

      </div>

      <!-- Modal Actions - Fixed at bottom -->
      <div class="flex justify-end space-x-3 pt-2 border-t bg-white flex-shrink-0">
        <button
          @click="$emit('close')"
          class="px-3 py-1 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
        >
          Close
        </button>
      </div>
    </div>

    <!-- AI Guidance Dialog -->
    <div v-if="showAIGuidanceDialog" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-60" @click="showAIGuidanceDialog = false">
      <div class="relative top-20 mx-auto p-5 border w-full max-w-2xl shadow-lg rounded-md bg-white" @click.stop>
        <div class="mt-3">
          <!-- Dialog Header -->
          <div class="flex items-center justify-between pb-4 border-b">
            <h3 class="text-lg font-semibold text-gray-900">
              🤖 AI Guidance for EPF/ESIC Rate Analysis
            </h3>
            <button @click="showAIGuidanceDialog = false" class="text-gray-400 hover:text-gray-600">
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

          <!-- Dialog Content -->
          <div class="mt-4">
            <div class="mb-4">
              <p class="text-sm text-gray-600 mb-3">
                <strong>Guide the AI analysis:</strong> Provide specific instructions to help the AI focus on particular aspects of EPF/ESIC rate analysis.
                Be specific about what you want the AI to check, verify, or prioritize in its analysis.
              </p>

              <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-3">
                <h4 class="text-sm font-medium text-blue-800 mb-1">💡 Tips for Effective AI Guidance:</h4>
                <ul class="text-xs text-blue-700 space-y-1">
                  <li>• Be specific: "Check for 2024-25 notifications" instead of "check latest rates"</li>
                  <li>• Mention sources: "Verify from EPFO official website" or "Check ESIC circulars"</li>
                  <li>• Focus areas: "Pay attention to startup exemptions" or "Check wage ceiling changes"</li>
                  <li>• Time periods: "Look for changes after April 2024" or "Verify current financial year rates"</li>
                </ul>
              </div>

              <label class="block text-sm font-medium text-gray-700 mb-2">
                Additional Instructions for AI Analysis (Optional)
              </label>
              <textarea
                v-model="aiGuidanceText"
                class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                rows="4"
                placeholder="Example: Please check the EPFO official website for any notifications issued after April 2024 regarding EPF rate changes. Also verify if there are any recent amendments to the wage ceiling limits for both EPF and ESIC contributions."
              ></textarea>
            </div>

            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                Quick Guidance Options
              </label>
              <div class="space-y-2">
                <button
                  @click="aiGuidanceText = 'Please check for the latest EPF/ESIC rate notifications for 2024-25 financial year and verify if there are any recent changes or amendments.'"
                  class="w-full text-left px-3 py-2 text-xs bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100"
                >
                  📅 Check latest 2024-25 rate notifications
                </button>
                <button
                  @click="aiGuidanceText = 'Please verify if there are any special EPF/ESIC rate provisions for startups, small businesses, or specific industries.'"
                  class="w-full text-left px-3 py-2 text-xs bg-green-50 text-green-700 rounded-md hover:bg-green-100"
                >
                  🏢 Check special provisions for startups/small businesses
                </button>
                <button
                  @click="aiGuidanceText = 'Please check for any recent amendments or circulars from EPFO and ESIC that might affect contribution rates or wage limits.'"
                  class="w-full text-left px-3 py-2 text-xs bg-purple-50 text-purple-700 rounded-md hover:bg-purple-100"
                >
                  📋 Check recent amendments and circulars
                </button>
                <button
                  @click="aiGuidanceText = 'Please verify the current wage ceiling limits for EPF and ESIC contributions and check if there have been any recent revisions.'"
                  class="w-full text-left px-3 py-2 text-xs bg-orange-50 text-orange-700 rounded-md hover:bg-orange-100"
                >
                  💰 Verify wage ceiling limits
                </button>
              </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t">
              <button
                @click="showAIGuidanceDialog = false"
                class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                @click="refreshAiRulesWithGuidance"
                :disabled="refreshingAiRules"
                class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {{ refreshingAiRules ? 'Analyzing...' : '🤖 Start AI Analysis' }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'
import { useEpfEsicRules } from '~/composables/business/useEpfEsicRules'
import useToast from '~/composables/ui/useToast'
import { useAIApi } from '~/composables/ai/useAIApi'
import { useAIConfig } from '~/composables/ai/useAIConfig'
import { AI_PROVIDERS } from '~/types/ai'

// Import EPF/ESIC rules composable for AI-fetched rules
const { getCurrentRules, fetchLatestRules } = useEpfEsicRules()

// Import AI API composable for dynamic AI requests
const { makeAIRequest, isConfigured } = useAIApi()

// Import AI configuration composable
const { aiConfig, currentProvider, currentModel } = useAIConfig()

// Import toast system
const { showToast } = useToast()

// Helper function for notifications
const showFooterNotification = (message, type) => {
  showToast({ message, type })
}

// AI rules refresh state
const refreshingAiRules = ref(false)

// AI guidance dialog state
const showAIGuidanceDialog = ref(false)
const aiGuidanceText = ref('')

// Editing state
const editingMode = ref(false)
const editingEmployeeId = ref(null)
const editingData = ref({})
const originalData = ref({})

// Auto-calculate gross salary when wage days or per day wage changes
watch([() => editingData.value.wageDays, () => editingData.value.pDayWage], () => {
  if (editingData.value.wageDays && editingData.value.pDayWage) {
    editingData.value.grossSalary = Number(editingData.value.wageDays) * Number(editingData.value.pDayWage)
  }
}, { deep: true })

// Manual refresh of AI rules with user guidance
const refreshAiRulesWithGuidance = async () => {
  const guidance = aiGuidanceText.value.trim()
  console.log(`📝 User guidance provided: "${guidance}"`)

  showAIGuidanceDialog.value = false
  await refreshAiRules(guidance || null)
  aiGuidanceText.value = '' // Clear guidance after use
}

// Manual refresh of AI rules as a background job
const refreshAiRules = async (userGuidance = '') => {
  try {
    refreshingAiRules.value = true

    // Check if AI is configured
    if (!isConfigured.value) {
      showFooterNotification('❌ AI configuration required. Please configure your AI settings first.', 'error')
      refreshingAiRules.value = false
      return
    }

    // Show initial notification with current rates
    const currentEpfRate = getCurrentRules.epf?.employeeRate ? `${(getCurrentRules.epf.employeeRate * 100).toFixed(2)}%` : 'N/A'
    const currentEsicRate = getCurrentRules.esic?.employeeRate ? `${(getCurrentRules.esic.employeeRate * 100).toFixed(2)}%` : 'N/A'
    showFooterNotification(`🔄 Refreshing EPF/ESIC rules (Current: EPF ${currentEpfRate}, ESIC ${currentEsicRate})...`, 'info')

    // Log user guidance being sent
    if (userGuidance) {
      console.log(`🚀 Sending user guidance to AI: "${userGuidance}"`)
    } else {
      console.log(`🚀 No user guidance provided - using default AI analysis`)
    }

    // Start background verification job using dynamic AI API
    const response = await makeAIRequest('/api/ai/epf-esic-verify-background', {
      method: 'POST',
      body: {
        jobId: `epf_esic_verify_${Date.now()}`,
        userGuidance: userGuidance || null
      }
    })
    
    if (response.success) {
      // Poll for job status
      const jobId = response.jobId
      const startTime = Date.now()
      const maxWaitTime = 5 * 60 * 1000 // 5 minutes timeout

      const checkJobStatus = async () => {
        try {
          const statusResponse = await makeAIRequest(`/api/ai/epf-esic-verify-status?jobId=${jobId}`, {
            method: 'GET'
          })
          
          if (statusResponse.status === 'completed' && statusResponse.rules) {
            // Update rules directly from the status response instead of making another API call
            const { rules } = statusResponse
            
            // Update the epfEsicRules in the composable
            const { updateRulesDirectly } = useEpfEsicRules()
            updateRulesDirectly(rules)
            
            // Refresh the PF/ESIC data with new rules
            emit('refresh')
            
            // Show success notification with specific rates - NO FALLBACK DATA
            const epfRate = rules.epf?.employeeRate ? `${(rules.epf.employeeRate * 100).toFixed(2)}%` : 'N/A'
            const esicRate = rules.esic?.employeeRate ? `${(rules.esic.employeeRate * 100).toFixed(2)}%` : 'N/A'
            showFooterNotification(`✅ EPF/ESIC rules updated successfully! EPF: ${epfRate}, ESIC: ${esicRate}`, 'success')
            refreshingAiRules.value = false
          } else if (statusResponse.status === 'failed') {
            const errorMsg = statusResponse.error?.message || 'Unknown error occurred'
            const provider = statusResponse.error?.provider || 'AI system'
            console.error('❌ EPF/ESIC verification job failed:', statusResponse.error)
            showFooterNotification(`❌ EPF/ESIC verification failed: ${errorMsg} (Provider: ${provider})`, 'error')
            refreshingAiRules.value = false
          } else if (statusResponse.status === 'not_found') {
            console.error('❌ EPF/ESIC verification job not found')
            showFooterNotification('❌ EPF/ESIC verification job not found or expired. Please try again.', 'error')
            refreshingAiRules.value = false
          } else {
            // Check for timeout
            if (Date.now() - startTime > maxWaitTime) {
              console.error('❌ EPF/ESIC verification job timed out')
              showFooterNotification('❌ EPF/ESIC verification timed out. Please try again.', 'error')
              refreshingAiRules.value = false
              return
            }

            // Still processing, check again in 5 seconds
            setTimeout(checkJobStatus, 5000)
          }
        } catch (statusError) {
          console.error('Error checking job status:', statusError)
          showFooterNotification('❌ Failed to verify EPF/ESIC rules. Using cached values.', 'error')
          refreshingAiRules.value = false
        }
      }
      
      // Start polling
      setTimeout(checkJobStatus, 3000)
    } else {
      throw new Error(response.message || 'Failed to start verification job')
    }
  } catch (error) {
    console.error('Error refreshing AI rules:', error)
    const { getCurrentRules } = useEpfEsicRules()
    const cachedEpfRate = getCurrentRules.epf?.employeeRate ? `${(getCurrentRules.epf.employeeRate * 100).toFixed(2)}%` : 'N/A'
    const cachedEsicRate = getCurrentRules.esic?.employeeRate ? `${(getCurrentRules.esic.employeeRate * 100).toFixed(2)}%` : 'N/A'
    showFooterNotification(`❌ Failed to refresh EPF/ESIC rules. ${cachedEpfRate !== 'N/A' || cachedEsicRate !== 'N/A' ? `Using cached values: EPF ${cachedEpfRate}, ESIC ${cachedEsicRate}` : 'No cached data available - please try again or configure AI settings.'}`, 'error')
    refreshingAiRules.value = false
  }
}

// AI Configuration Methods
const getProviderName = (providerId) => {
  const provider = AI_PROVIDERS.find(p => p.id === providerId)
  return provider ? provider.name : providerId
}

const openAISettings = () => {
  // Emit event to open global settings with AI tab
  window.dispatchEvent(new CustomEvent('open-global-settings', {
    detail: { activeTab: 'ai' }
  }))
}

// Editing functions
const startEditing = (employee) => {
  editingEmployeeId.value = employee.employeeId
  editingMode.value = true

  // Store original data for cancellation
  originalData.value = { ...employee }

  // Create editable copy
  editingData.value = {
    wageDays: employee.wageDays,
    pDayWage: employee.pDayWage,
    grossSalary: employee.grossSalary,
    employeeEpf: employee.employeeEpf,
    employerEpf: employee.employerEpf,
    employerEps: employee.employerEps,
    adminCharges: employee.adminCharges,
    employeeEsic: employee.employeeEsic,
    employerEsic: employee.employerEsic
  }
}

const cancelEditing = () => {
  editingMode.value = false
  editingEmployeeId.value = null
  editingData.value = {}
  originalData.value = {}
}

const saveEditing = async () => {
  try {
    const employee = props.pfEsicData.find(emp => emp.employeeId === editingEmployeeId.value)
    if (!employee) return

    // Validate input values
    const wageDays = Number(editingData.value.wageDays)
    const pDayWage = Number(editingData.value.pDayWage)
    const grossSalary = Number(editingData.value.grossSalary)
    const employeeEpf = Number(editingData.value.employeeEpf)
    const employerEpf = Number(editingData.value.employerEpf)
    const employerEps = Number(editingData.value.employerEps)
    const adminCharges = Number(editingData.value.adminCharges)
    const employeeEsic = Number(editingData.value.employeeEsic)
    const employerEsic = Number(editingData.value.employerEsic)

    if (wageDays < 0 || wageDays > 31) {
      showFooterNotification('Wage days must be between 0 and 31', 'error')
      return
    }

    if (pDayWage < 0) {
      showFooterNotification('Per day wage cannot be negative', 'error')
      return
    }

    if (grossSalary < 0) {
      showFooterNotification('Gross salary cannot be negative', 'error')
      return
    }

    // Validate PF/ESIC values
    if (employeeEpf < 0 || employerEpf < 0 || employerEps < 0 || adminCharges < 0 || employeeEsic < 0 || employerEsic < 0) {
      showFooterNotification('PF/ESIC values must be positive', 'error')
      return
    }

    // Prepare updated employee data with manual PF/ESIC values
    const updatedEmployee = {
      ...employee,
      wageDays,
      pDayWage,
      grossSalary,
      employeeEpf,
      employerEpf,
      employerEps,
      adminCharges,
      employeeEsic,
      employerEsic
    }

    // Emit update event to parent component
    emit('update-employee', updatedEmployee)

    // Show success message
    showFooterNotification(`Updated data for ${employee.employeeName}`, 'success')

    // Exit editing mode
    cancelEditing()

  } catch (error) {
    console.error('Error saving edits:', error)
    showFooterNotification('Failed to save changes', 'error')
  }
}

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  selectedMonth: {
    type: String,
    default: ''
  },
  pfEsicData: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  filters: {
    type: Object,
    default: () => ({
      project: '',
      site: '',
      status: '',
      employeeStatus: 'active'
    })
  }
})

const emit = defineEmits(['close', 'refresh', 'update-employee'])

// Computed properties for filtering
const filteredData = computed(() => {
  let filtered = props.pfEsicData

  if (props.filters.project) {
    filtered = filtered.filter(emp => emp.project === props.filters.project)
  }

  if (props.filters.site) {
    filtered = filtered.filter(emp => emp.site === props.filters.site)
  }

  if (props.filters.status) {
    filtered = filtered.filter(emp => emp.paymentStatus === props.filters.status)
  }

  return filtered
})

// Unique values for filter dropdowns
const uniqueProjects = computed(() => {
  const projects = props.pfEsicData
    .map(emp => emp.project)
    .filter(project => project && project.trim() !== '' && project !== 'N/A')
  return [...new Set(projects)].sort()
})

const uniqueSites = computed(() => {
  const sites = props.pfEsicData
    .map(emp => emp.site)
    .filter(site => site && site.trim() !== '' && site !== 'N/A')
  return [...new Set(sites)].sort()
})

// Summary calculations
const paidEmployeesCount = computed(() => {
  return filteredData.value.filter(emp => emp.paymentStatus === 'paid').length
})

const unpaidEmployeesCount = computed(() => {
  return filteredData.value.filter(emp => emp.paymentStatus === 'unpaid').length
})

const totalGrossSalary = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.grossSalary || 0), 0)
    .toFixed(2)
})

// Complete EPF calculations
const totalEmployeeEpf = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.employeeEpf || 0), 0)
    .toFixed(2)
})

const totalEmployerEpf = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.employerEpf || 0), 0)
    .toFixed(2)
})

const totalEmployerEps = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.employerEps || 0), 0)
    .toFixed(2)
})

const totalEdli = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.edli || 0), 0)
    .toFixed(2)
})

const totalAdminCharges = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.adminCharges || 0), 0)
    .toFixed(2)
})

const totalEmployerEpfContribution = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.totalEmployerEpfContribution || 0), 0)
    .toFixed(2)
})

const totalEpfContribution = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.totalEpfContribution || 0), 0)
    .toFixed(2)
})

// Complete ESIC calculations
const totalEmployeeEsic = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.employeeEsic || 0), 0)
    .toFixed(2)
})

const totalEmployerEsic = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.employerEsic || 0), 0)
    .toFixed(2)
})

const totalEsicContribution = computed(() => {
  return filteredData.value
    .reduce((sum, emp) => sum + Number(emp.totalEsicContribution || 0), 0)
    .toFixed(2)
})

// Helper functions
const formatMonthYear = (monthString) => {
  if (!monthString) return 'Not selected'
  const [year, month] = monthString.split('-')
  const date = new Date(year, month - 1)
  return date.toLocaleDateString('en-US', { year: 'numeric', month: 'long' })
}

const formatIndianCurrency = (amount) => {
  const num = Number(amount) || 0
  return num.toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
}

const clearFilters = () => {
  props.filters.project = ''
  props.filters.site = ''
  props.filters.status = ''
}

const exportToExcel = async () => {
  try {
    // Use useApiWithAuth composable for authenticated requests
    const api = useApiWithAuth()

    // Prepare export data
    const exportData = {
      month: props.selectedMonth,
      employees: filteredData.value.map(emp => ({
        ...emp,
        wageDays: emp.wageDays || 0,
        perDayWage: emp.pDayWage || 0,
      })),
      summary: {
        totalEmployees: filteredData.value.length,
        paidEmployees: paidEmployeesCount.value,
        unpaidEmployees: unpaidEmployeesCount.value,
        totalGrossSalary: totalGrossSalary.value,
        epf: {
          employeeEpf: totalEmployeeEpf.value,
          employerEpf: totalEmployerEpf.value,
          employerEps: totalEmployerEps.value,
          edli: totalEdli.value,
          adminCharges: totalAdminCharges.value,
          totalEmployerContribution: totalEmployerEpfContribution.value,
          totalContribution: totalEpfContribution.value
        },
        esic: {
          employeeEsic: totalEmployeeEsic.value,
          employerEsic: totalEmployerEsic.value,
          totalContribution: totalEsicContribution.value
        }
      },
      filters: props.filters
    }

    // Call the export API using fetchWithAuth for blob response
    const response = await api.fetchWithAuth('/api/wages/pf-esic-export', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(exportData)
    })

    // Create download link
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    })

    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url

    // Generate filename with month and date
    const monthName = formatMonthYear(props.selectedMonth).replace(' ', '-')
    link.setAttribute('download', `PF-ESIC-Report-${monthName}-${new Date().toISOString().split('T')[0]}.xlsx`)

    document.body.appendChild(link)
    link.click()

    // Clean up
    document.body.removeChild(link)
    window.URL.revokeObjectURL(url)

  } catch (error) {
    console.error('Error exporting to Excel:', error)
    alert('Failed to export data to Excel. Please try again.')
  }
}
</script>
