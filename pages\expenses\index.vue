<template>
  <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8 mt-0">
    <h1 class="text-xl sm:text-2xl font-bold text-gray-900 mb-4 sm:mb-8">Financial Expenses Dashboard</h1>
    <!-- Toast notifications are handled globally -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading dashboard...</span>
    </div>

    <div v-else>
      <!-- Action Buttons -->
      <div class="flex justify-end mb-4 sm:mb-6">
        <!-- Add Expense Button -->
        <button
          @click="showAddExpenseModal = true"
          class="px-3 py-2 sm:px-4 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm sm:text-base flex items-center"
        >
          <svg class="h-5 w-5 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span class="hidden xs:inline">Add New Expense</span>
          <span class="xs:hidden">Add</span>
        </button>
      </div>

      <ExpensesDashboard ref="dashboard" :is-loading="isLoading" />

      <!-- Expense Modal -->
      <ExpenseModal
        :show="showAddExpenseModal"
        title="Add New Expense"
        @close="showAddExpenseModal = false"
        @saved="handleExpenseSaved"
      />
    </div>
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true
});

import { ref, onMounted } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useLedgers } from '~/composables/expenses/useLedgers';
import ExpenseModal from '~/components/expenses/ExpenseModal.vue';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import useToast from '~/composables/ui/useToast';
const toast = ref(null);

// Set page title
usePageTitle('Financial Expenses Dashboard', 'Manage and track your financial expenses and transactions');

// State
const isLoading = ref(true);
const showAddExpenseModal = ref(false);
// Refs
const dashboard = ref(null);

// Methods
const handleExpenseSaved = async (expense) => {

  // Refresh data
  await fetchExpenses();

  // Refresh dashboard
  if (dashboard.value && dashboard.value.refreshDashboard) {

    await dashboard.value.refreshDashboard();
  }
};

// Get composables
const { fetchExpenses } = useExpenses();
const { fetchLedgers, ensureDefaultCashBook } = useLedgers();

// Initialize
onMounted(async () => {
  try {
    // Ensure default cash book exists
    await ensureDefaultCashBook();

    // Fetch initial data
    await Promise.all([
      fetchExpenses(),
      fetchLedgers()
    ]);
  } catch (error) {

  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
