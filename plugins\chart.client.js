/**
 * Plugin to register Chart.js globally
 * This ensures Chart.js is only registered once across the application
 */
import { defineNuxtPlugin } from '#app';
import Chart from 'chart.js/auto';

export default defineNuxtPlugin((nuxtApp) => {
  // Only run on client side
  if (process.server) {
    return;
  }
  
  // Register Chart.js globally if not already registered
  if (!nuxtApp.hasOwnProperty('$chart')) {
    console.log('Registering Chart.js globally');
    nuxtApp.provide('chart', Chart);
  } else {
    console.log('Chart.js already registered globally');
  }
});
