<template>
  <div class="container mx-auto px-4 py-8">
    <!-- <PERSON> Header -->
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Inventory Dashboard</h1>
        <p class="text-gray-600 mt-1">Overview of your inventory system</p>
      </div>
      <div class="mt-4 md:mt-0 flex space-x-3">
        <NuxtLink to="/inventory/edit-bill" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium transition-colors duration-300 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          New Bill
        </NuxtLink>
        <NuxtLink to="/inventory/stock-report" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md shadow-sm text-sm font-medium transition-colors duration-300 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          Stock Report
        </NuxtLink>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
      <p>{{ error }}</p>
    </div>

    <div v-else>
      <!-- Summary Cards -->
      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Stock Items Card -->
        <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
          <div class="p-5 flex items-center">
            <div class="flex-shrink-0 rounded-full bg-white/20 p-3">
              <svg class="h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-blue-100 truncate">
                  Total Stock Items
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-white">
                    {{ summary.totalItems }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
          <div class="bg-blue-700/30 px-5 py-2">
            <div class="text-sm text-blue-100">
              <NuxtLink to="/inventory/stock-report" class="flex items-center hover:text-white">
                <span>View all items</span>
                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Low Stock Items Card -->
        <div class="bg-gradient-to-br from-red-500 to-red-600 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
          <div class="p-5 flex items-center">
            <div class="flex-shrink-0 rounded-full bg-white/20 p-3">
              <svg class="h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-red-100 truncate">
                  Low Stock Items
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-white">
                    {{ summary.lowStockItems }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
          <div class="bg-red-700/30 px-5 py-2">
            <div class="text-sm text-red-100">
              <button @click="filterLowStock" class="flex items-center hover:text-white">
                <span>View low stock items</span>
                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- Total Inventory Value Card -->
        <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
          <div class="p-5 flex items-center">
            <div class="flex-shrink-0 rounded-full bg-white/20 p-3">
              <svg class="h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-green-100 truncate">
                  Total Inventory Value
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-white">
                    ₹{{ formatCurrency(summary.totalValue) }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
          <div class="bg-green-700/30 px-5 py-2">
            <div class="text-sm text-green-100">
              <NuxtLink to="/inventory/stock-report" class="flex items-center hover:text-white">
                <span>View inventory details</span>
                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>

        <!-- Recent Bills Card -->
        <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg shadow-lg overflow-hidden transform transition-all duration-300 hover:scale-105">
          <div class="p-5 flex items-center">
            <div class="flex-shrink-0 rounded-full bg-white/20 p-3">
              <svg class="h-8 w-8 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-purple-100 truncate">
                  Recent Bills
                </dt>
                <dd class="flex items-baseline">
                  <div class="text-2xl font-semibold text-white">
                    {{ summary.recentBills }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
          <div class="bg-purple-700/30 px-5 py-2">
            <div class="text-sm text-purple-100">
              <NuxtLink to="/inventory/bills" class="flex items-center hover:text-white">
                <span>View all bills</span>
                <svg class="ml-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </NuxtLink>
            </div>
          </div>
        </div>
      </div>

      <!-- Main Content Grid -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Recent Activity -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="px-6 py-4 bg-gradient-to-r from-indigo-500 to-indigo-600">
              <h2 class="text-lg font-semibold text-white">Recent Activity</h2>
            </div>
            <div class="p-6">
              <div v-if="recentActivity.length === 0" class="text-center py-8 text-gray-500">
                No recent activity found
              </div>
              <div v-else class="space-y-4">
                <div v-for="(activity, index) in recentActivity" :key="index"
                     class="flex items-start p-4 rounded-lg transition-all duration-300 hover:bg-gray-50"
                     :class="{'border-b border-gray-100': index !== recentActivity.length - 1}">
                  <div class="flex-shrink-0">
                    <div class="w-10 h-10 rounded-full flex items-center justify-center"
                         :class="{
                           'bg-green-100 text-green-600': activity.type === 'PURCHASE' || activity.type === 'CREDIT NOTE',
                           'bg-red-100 text-red-600': activity.type === 'SALES' || activity.type === 'DEBIT NOTE'
                         }">
                      <svg v-if="activity.type === 'PURCHASE' || activity.type === 'CREDIT NOTE'" class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11l5-5m0 0l5 5m-5-5v12" />
                      </svg>
                      <svg v-else class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 13l-5 5m0 0l-5-5m5 5V6" />
                      </svg>
                    </div>
                  </div>
                  <div class="ml-4 flex-1">
                    <div class="flex justify-between items-center">
                      <div class="text-sm font-medium text-gray-900">{{ activity.item }}</div>
                      <div class="text-xs text-gray-500">{{ formatDate(activity.date) }}</div>
                    </div>
                    <div class="mt-1 text-sm text-gray-600">
                      {{ activity.type }} - Bill #{{ activity.bno }}
                    </div>
                    <div class="mt-1 flex justify-between">
                      <span class="text-sm text-gray-500">
                        {{ activity.supply }}
                      </span>
                      <span class="text-sm font-medium"
                            :class="{
                              'text-green-600': activity.type === 'PURCHASE' || activity.type === 'CREDIT NOTE',
                              'text-red-600': activity.type === 'SALES' || activity.type === 'DEBIT NOTE'
                            }">
                        {{ activity.type === 'PURCHASE' || activity.type === 'CREDIT NOTE' ? '+' : '-' }}{{ activity.qty }} {{ activity.uom }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Low Stock Items -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-md overflow-hidden h-full">
            <div class="px-6 py-4 bg-gradient-to-r from-red-500 to-red-600">
              <h2 class="text-lg font-semibold text-white">Low Stock Items</h2>
            </div>
            <div class="p-6">
              <div v-if="lowStockItems.length === 0" class="text-center py-8 text-gray-500">
                No low stock items found
              </div>
              <div v-else class="space-y-4">
                <div v-for="(item, index) in lowStockItems" :key="item._id"
                     class="p-4 rounded-lg transition-all duration-300 hover:bg-gray-50"
                     :class="{'border-b border-gray-100': index !== lowStockItems.length - 1}">
                  <div class="flex justify-between items-center">
                    <div class="font-medium text-gray-900">{{ item.item }}</div>
                    <div class="text-red-600 font-medium">{{ item.qty }} {{ item.uom }}</div>
                  </div>
                  <div class="mt-1 text-sm text-gray-600 flex justify-between">
                    <span>{{ item.pno || 'No Part #' }}</span>
                    <span>HSN: {{ item.hsn }}</span>
                  </div>
                  <div class="mt-2">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-red-600 h-2 rounded-full" :style="{ width: `${Math.min(item.qty * 20, 100)}%` }"></div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="mt-4 text-center" v-if="lowStockItems.length > 0">
                <NuxtLink to="/inventory/stock-report" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                  View all stock items
                </NuxtLink>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="mt-8">
        <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <NuxtLink to="/inventory/edit-bill" class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-indigo-100 rounded-full flex items-center justify-center text-indigo-600 mb-4">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="text-gray-900 font-medium">Create New Bill</h3>
            <p class="text-gray-500 text-sm mt-1">Add a new sales or purchase bill</p>
          </NuxtLink>

          <NuxtLink to="/inventory/stock-report" class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center text-green-600 mb-4">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="text-gray-900 font-medium">Stock Report</h3>
            <p class="text-gray-500 text-sm mt-1">View detailed stock report</p>
          </NuxtLink>

          <NuxtLink to="/inventory/bills" class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mb-4">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            </div>
            <h3 class="text-gray-900 font-medium">View Bills</h3>
            <p class="text-gray-500 text-sm mt-1">Manage existing bills</p>
          </NuxtLink>

          <NuxtLink to="/inventory/edit-bill" class="bg-white rounded-lg shadow-md p-6 flex flex-col items-center justify-center text-center hover:shadow-lg transition-shadow duration-300">
            <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center text-yellow-600 mb-4">
              <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 class="text-gray-900 font-medium">Credit/Debit Note</h3>
            <p class="text-gray-500 text-sm mt-1">Create credit or debit notes</p>
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { usePageTitle } from '~/composables/ui/usePageTitle';

// Set page title
usePageTitle('Inventory Dashboard', 'Overview of your inventory system');

// State
const isLoading = ref(true);
const error = ref(null);
const inventoryData = ref(null);
const recentActivity = ref([]);
const lowStockItems = ref([]);

// Summary data
const summary = ref({
  totalItems: 0,
  lowStockItems: 0,
  totalValue: 0,
  recentBills: 0
});

// Fetch inventory data
const fetchInventoryData = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const api = useApiWithAuth();
    const response = await api.get('/api/inventory');

    inventoryData.value = response;

    // Process data for dashboard
    processInventoryData();

    isLoading.value = false;
  } catch (err) {
    error.value = err.message || 'Failed to load inventory data';
    isLoading.value = false;
  }
};

// Process inventory data for dashboard
const processInventoryData = () => {
  if (!inventoryData.value) return;

  // Calculate summary data
  const stocks = inventoryData.value.stocks || [];
  const stockReg = inventoryData.value.stockReg || [];
  const bills = inventoryData.value.bills || [];

  // Total items
  summary.value.totalItems = stocks.length;

  // Low stock items (qty <= 5)
  const lowStockItemsArray = stocks.filter(item => item.qty <= 5);
  summary.value.lowStockItems = lowStockItemsArray.length;
  lowStockItems.value = lowStockItemsArray.slice(0, 5); // Show only top 5

  // Total inventory value
  summary.value.totalValue = stocks.reduce((total, item) => {
    return total + (item.qty * item.rate);
  }, 0);

  // Recent bills (last 30 days)
  const thirtyDaysAgo = new Date();
  thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

  const recentBills = bills.filter(bill => {
    const billDate = new Date(bill.bdate);
    return billDate >= thirtyDaysAgo;
  });

  summary.value.recentBills = recentBills.length;

  // Recent activity (from stockReg)
  recentActivity.value = stockReg
    .sort((a, b) => new Date(b.bdate) - new Date(a.bdate))
    .slice(0, 10)
    .map(item => ({
      item: item.item,
      type: item.type,
      qty: item.qty,
      uom: item.uom,
      bno: item.bno,
      date: item.bdate,
      supply: item.supply
    }));
};

// Format currency
const formatCurrency = (value) => {
  return new Intl.NumberFormat('en-IN').format(value);
};

// Format date
const formatDate = (dateString) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

// Filter low stock items
const filterLowStock = () => {
  const router = useRouter();
  router.push('/inventory/stock-report?lowStock=true');
};

// Fetch data on component mount
onMounted(() => {
  fetchInventoryData();
});
</script>

<style scoped>
/* Add any component-specific styles here */
.feature-card {
  transition: all 0.3s ease;
}

.feature-card:hover {
  transform: translateY(-5px);
}
</style>
