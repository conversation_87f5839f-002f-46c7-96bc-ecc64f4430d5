<template>
  <div class="relative w-full">
    <!-- Input field -->
    <input
      ref="inputRef"
      type="text"
      :value="modelValue"
      @input="onInput"
      @focus="onFocus"
      @blur="onBlur"
      @keydown="onKeyDown"
      class="w-full p-1 border rounded"
      placeholder="Type to search symbols"
    />

    <!-- Dropdown menu -->
    <Teleport to="body">
      <div
        v-if="showDropdown && filteredOptions.length > 0"
        class="fixed z-[9999] bg-white border border-gray-300 rounded-md shadow-xl max-h-60 overflow-y-auto"
        :style="dropdownStyle"
      >
        <div
          v-for="(option, index) in filteredOptions"
          :key="option"
          :class="[
            'p-2 cursor-pointer hover:bg-gray-100 flex items-center',
            index === selectedIndex ? 'bg-red-100 font-bold border-l-4 border-red-500' : ''
          ]"
          @mousedown.prevent="selectOption(option)"
          @mouseover="selectedIndex = index"
        >
          <div class="flex-1 flex justify-between items-center">
            <span class="font-medium">{{ option }}</span>
            <span v-if="symbolPrices[option] !== undefined" class="ml-2 text-sm font-semibold"
                  :class="{'text-green-600': symbolPrices[option] > 0, 'text-red-600': symbolPrices[option] < 0, 'text-gray-600': symbolPrices[option] === 0}">
              ₹{{ formatPrice(symbolPrices[option]) }}
            </span>
            <span v-else class="ml-2 text-xs text-gray-400 italic">
              Loading price...
            </span>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';

const props = defineProps<{
  modelValue: string;
  options: string[];
}>();

const emit = defineEmits(['update:modelValue', 'price-updated']);

const inputRef = ref<HTMLInputElement | null>(null);
const showDropdown = ref(false);
const searchQuery = ref('');
const selectedIndex = ref(-1);
const symbolPrices = ref<Record<string, number>>({});
const inputPosition = ref({ top: 0, left: 0, width: 0 });

// Filter options based on search query
const filteredOptions = computed(() => {
  if (!searchQuery.value) {
    return props.options.slice(0, 10); // Show first 10 options when empty
  }

  const query = searchQuery.value.toLowerCase();
  return props.options
    .filter(option => option.toLowerCase().includes(query))
    .slice(0, 10); // Limit to 10 results for performance
});

// Compute dropdown position and style
const dropdownStyle = computed(() => {
  return {
    top: `${inputPosition.value.top + 40}px`,
    left: `${inputPosition.value.left}px`,
    width: `${Math.max(inputPosition.value.width, 300)}px`,
    maxWidth: '500px',
    minWidth: '300px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
  };
});

// Format price with commas for thousands
function formatPrice(price: number): string {
  // Check if price is undefined, null, or NaN
  if (price === undefined || price === null || isNaN(price)) {
    return '0.00';
  }

  // Handle zero or actual price values
  return price.toLocaleString('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// Function to fetch prices from Yahoo Finance API
async function fetchPricesFromYahooFinance() {
  try {
    console.log('Fetching stock prices from Yahoo Finance API...');
    
    // Call the API to get current prices
    const response = await $fetch('/api/stock-market/prices', {
      params: {
        updateDatabase: 'false'
      }
    });

    if (!response) {
      console.error('Failed to fetch current prices');
      return;
    }

    console.log(`Received price updates for ${Object.keys(response.priceUpdates).length} symbols`);
    
    // Create a map of symbol prices
    const priceMap: Record<string, number> = {};
    
    // Process the price updates
    for (const [symbol, data] of Object.entries(response.priceUpdates)) {
      if (data && typeof data === 'object' && 'currentPrice' in data) {
        const price = Number(data.currentPrice);
        if (!isNaN(price)) {
          priceMap[symbol] = price;
        }
      }
    }
    
    // Update the prices
    if (Object.keys(priceMap).length > 0) {
      symbolPrices.value = priceMap;
      console.log('Prices loaded from Yahoo Finance API:', Object.keys(priceMap).length);
    }
  } catch (error) {
    console.error('Error fetching prices from Yahoo Finance API:', error);
  }
}

// Handle input changes
function onInput(event: Event) {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;
  emit('update:modelValue', target.value);

  // Reset selected index when input changes
  selectedIndex.value = -1;

  // Show dropdown when typing
  showDropdown.value = true;

  // Update position when typing
  if (inputRef.value) {
    const rect = inputRef.value.getBoundingClientRect();
    inputPosition.value = {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width
    };
  }
}

// Handle focus
function onFocus() {
  showDropdown.value = true;
  searchQuery.value = props.modelValue;

  // Calculate position of input for dropdown positioning
  nextTick(() => {
    if (inputRef.value) {
      const rect = inputRef.value.getBoundingClientRect();
      inputPosition.value = {
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      };
    }
    // This will trigger the computed property to update
    searchQuery.value = searchQuery.value;
  });
}

// Handle blur
function onBlur() {
  // Delay hiding dropdown to allow click events to complete
  setTimeout(() => {
    showDropdown.value = false;
  }, 300);
}

// Handle keyboard navigation
function onKeyDown(event: KeyboardEvent) {
  if (!showDropdown.value) {
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      showDropdown.value = true;
      event.preventDefault();
    }
    return;
  }

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      if (selectedIndex.value < filteredOptions.value.length - 1) {
        selectedIndex.value++;
        ensureSelectedVisible();
      }
      break;

    case 'ArrowUp':
      event.preventDefault();
      if (selectedIndex.value > 0) {
        selectedIndex.value--;
        ensureSelectedVisible();
      }
      break;

    case 'Enter':
      event.preventDefault();
      if (selectedIndex.value >= 0 && selectedIndex.value < filteredOptions.value.length) {
        selectOption(filteredOptions.value[selectedIndex.value]);
      }
      break;

    case 'Escape':
      event.preventDefault();
      showDropdown.value = false;
      break;
  }
}

// Ensure the selected item is visible in the dropdown
function ensureSelectedVisible() {
  nextTick(() => {
    // Use a more specific selector to find our dropdown
    const dropdown = document.querySelector('.fixed.z-\\[9999\\].overflow-y-auto');
    if (!dropdown) return;

    // Find all direct children that are div elements
    const dropdownItems = dropdown.querySelectorAll(':scope > div');
    if (!dropdownItems.length) return;

    // Get the selected item
    const selectedItem = dropdownItems[selectedIndex.value] as HTMLElement;
    if (!selectedItem) return;

    const dropdownRect = dropdown.getBoundingClientRect();
    const selectedRect = selectedItem.getBoundingClientRect();

    if (selectedRect.bottom > dropdownRect.bottom) {
      dropdown.scrollTop += selectedRect.bottom - dropdownRect.bottom;
    } else if (selectedRect.top < dropdownRect.top) {
      dropdown.scrollTop -= dropdownRect.top - selectedRect.top;
    }
  });
}

// Select an option from the dropdown
function selectOption(option: string) {
  emit('update:modelValue', option);
  searchQuery.value = option;
  showDropdown.value = false;

  // Emit the price if available
  if (symbolPrices.value[option] !== undefined) {
    emit('price-updated', symbolPrices.value[option]);
  }

  // Focus the input after selection
  nextTick(() => {
    inputRef.value?.focus();
  });
}

// Watch for changes in modelValue prop
watch(() => props.modelValue, (newValue) => {
  if (!showDropdown.value) {
    searchQuery.value = newValue;
  }
});

// Update dropdown position on window resize
function updatePosition() {
  if (showDropdown.value && inputRef.value) {
    const rect = inputRef.value.getBoundingClientRect();
    inputPosition.value = {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width
    };
  }
}

// Track interval for proper cleanup
const refreshInterval = ref<NodeJS.Timeout | null>(null);

// Fetch symbol prices on component mount and set up resize listener
onMounted(async () => {
  console.log('StockSymbolDropdown: Component mounted - starting price fetch and timer');

  // Fetch prices from Yahoo Finance API
  await fetchPricesFromYahooFinance();

  // Set up event listeners
  window.addEventListener('resize', updatePosition);
  window.addEventListener('scroll', updatePosition, true);

  // Start refresh interval only when component is mounted and visible
  startRefreshInterval();
});

// Function to start refresh interval
function startRefreshInterval() {
  console.log('StockSymbolDropdown: Starting refresh interval');

  // Clear any existing interval first
  stopRefreshInterval();

  // Refresh prices every 5 minutes (300000 ms)
  refreshInterval.value = setInterval(async () => {
    console.log('StockSymbolDropdown: Auto-refreshing prices from Yahoo Finance');
    await fetchPricesFromYahooFinance();
  }, 300000);
}

// Function to stop refresh interval
function stopRefreshInterval() {
  console.log('StockSymbolDropdown: Stopping refresh interval');

  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
}

// Clean up event listeners and interval
onUnmounted(() => {
  console.log('StockSymbolDropdown: Component unmounting - cleaning up');

  // Stop refresh interval
  stopRefreshInterval();

  // Remove event listeners
  window.removeEventListener('resize', updatePosition);
  window.removeEventListener('scroll', updatePosition, true);
});

// Expose symbolPrices to parent components
defineExpose({
  symbolPrices
});
</script>

<style scoped>
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}
</style>
