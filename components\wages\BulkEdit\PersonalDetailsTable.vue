<template>
  <div class="space-y-6">
    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
      <h3 class="text-lg font-semibold text-green-800 mb-4">Edit Personal Details</h3>
      
      <!-- Column Visibility Controls -->
      <div class="mb-4 p-3 bg-white border border-gray-200 rounded-lg">
        <h4 class="text-sm font-medium text-gray-700 mb-2">Show/Hide Columns</h4>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-2">
          <label v-for="(group, key) in columnGroups" :key="key" class="flex items-center text-sm">
            <input 
              type="checkbox" 
              v-model="visibleGroups" 
              :value="key"
              class="rounded border-gray-300 text-green-600 shadow-sm focus:border-green-500 focus:ring-green-500"
            />
            <span class="ml-2">{{ group.label }}</span>
          </label>
        </div>
      </div>

      <!-- Search and Filter -->
      <div class="mb-4">
        <input
          type="text"
          v-model="searchTerm"
          placeholder="Search employees..."
          class="w-full rounded-md border-gray-300 shadow-sm focus:border-green-500 focus:ring-green-500"
        />
      </div>

      <!-- Editable Table -->
      <div class="overflow-x-auto border border-gray-200 rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider sticky left-0 bg-gray-50 z-10">
                Employee Name
              </th>
              <th v-for="field in visibleFields" :key="field" class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                {{ getFieldLabel(field) }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="employee in filteredEmployees" :key="employee._id" :class="{ 'bg-yellow-50': isDirty(employee._id) }">
              <!-- Employee Name (Fixed Column) -->
              <td class="px-4 py-3 text-sm font-medium text-gray-900 sticky left-0 bg-white z-10">
                {{ employee.employeeName }}
              </td>
              
              <!-- Editable Fields -->
              <td v-for="field in visibleFields" :key="field" class="px-4 py-3 text-sm">
                <div class="relative">
                  <!-- Date Fields -->
                  <input
                    v-if="field === 'dateOfBirth' && editableData[employee._id]"
                    type="date"
                    v-model="editableData[employee._id][field]"
                    @input="markDirty(employee._id, field)"
                    :class="getInputClasses(employee._id, field)"
                  />
                  
                  <!-- Phone Number Field -->
                  <input
                    v-else-if="field === 'phoneNo' && editableData[employee._id]"
                    type="tel"
                    v-model="editableData[employee._id][field]"
                    @input="markDirty(employee._id, field)"
                    maxlength="10"
                    placeholder="10-digit number"
                    :class="getInputClasses(employee._id, field)"
                  />

                  <!-- IFSC Field with Auto-lookup -->
                  <input
                    v-else-if="field === 'ifsc' && editableData[employee._id]"
                    type="text"
                    v-model="editableData[employee._id][field]"
                    @input="handleIFSCChange(employee._id, $event.target.value)"
                    maxlength="11"
                    placeholder="ABCD0123456"
                    style="text-transform: uppercase"
                    :class="getInputClasses(employee._id, field)"
                  />

                  <!-- Account Number Field -->
                  <input
                    v-else-if="field === 'accountNo' && editableData[employee._id]"
                    type="text"
                    v-model="editableData[employee._id][field]"
                    @input="markDirty(employee._id, field)"
                    placeholder="Account number"
                    :class="getInputClasses(employee._id, field)"
                  />

                  <!-- UAN Field -->
                  <input
                    v-else-if="field === 'uan' && editableData[employee._id]"
                    type="text"
                    v-model="editableData[employee._id][field]"
                    @input="markDirty(employee._id, field)"
                    maxlength="12"
                    placeholder="12-digit UAN"
                    :class="getInputClasses(employee._id, field)"
                  />

                  <!-- ESIC Field -->
                  <input
                    v-else-if="field === 'esicNo' && editableData[employee._id]"
                    type="text"
                    v-model="editableData[employee._id][field]"
                    @input="markDirty(employee._id, field)"
                    maxlength="10"
                    placeholder="10-digit ESIC"
                    :class="getInputClasses(employee._id, field)"
                  />

                  <!-- Text Fields (Default) -->
                  <input
                    v-else-if="editableData[employee._id]"
                    type="text"
                    v-model="editableData[employee._id][field]"
                    @input="markDirty(employee._id, field)"
                    :class="getInputClasses(employee._id, field)"
                  />

                  <!-- Loading placeholder if data not ready -->
                  <div v-else class="text-gray-400 text-sm">Loading...</div>
                  
                  <!-- Validation Error -->
                  <div v-if="getValidationError(employee._id, field)" class="absolute top-full left-0 mt-1 text-xs text-red-600 bg-red-50 px-2 py-1 rounded shadow-sm z-20">
                    {{ getValidationError(employee._id, field) }}
                  </div>
                </div>
              </td>
              
              <!-- Actions -->
              <td class="px-4 py-3 text-sm">
                <div class="flex space-x-2">
                  <button
                    v-if="isDirty(employee._id)"
                    @click="saveEmployee(employee._id)"
                    :disabled="hasValidationErrors(employee._id)"
                    class="text-green-600 hover:text-green-800 disabled:text-gray-400 disabled:cursor-not-allowed"
                    title="Save changes"
                  >
                    ✓
                  </button>
                  <button
                    v-if="isDirty(employee._id)"
                    @click="resetEmployee(employee._id)"
                    class="text-red-600 hover:text-red-800"
                    title="Cancel changes"
                  >
                    ✗
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Summary -->
      <div class="mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
        <div class="text-sm font-medium text-green-800">
          Changes: {{ dirtyEmployeesCount }} employees modified
          <span v-if="totalValidationErrors > 0" class="text-red-600 ml-2">
            ({{ totalValidationErrors }} validation errors)
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'

const props = defineProps({
  employees: {
    type: Array,
    required: true
  }
})

const emit = defineEmits(['update:changes'])

// Column groups for visibility control
const columnGroups = {
  personal: { 
    label: 'Personal', 
    fields: ['dateOfBirth', 'phoneNo', 'fatherHusbandName', 'address'] 
  },
  identity: { 
    label: 'Identity', 
    fields: ['aadhar', 'pan'] 
  },
  banking: { 
    label: 'Banking', 
    fields: ['bank', 'branch', 'accountNo', 'ifsc'] 
  },
  government: { 
    label: 'Government', 
    fields: ['uan', 'esicNo', 'sKalyanNo'] 
  }
}

// Reactive state
const visibleGroups = ref(['personal', 'banking'])
const searchTerm = ref('')
const editableData = ref({})
const dirtyFields = ref({})
const validationErrors = ref({})

// Initialize editable data
const initializeEditableData = () => {
  const data = {}
  props.employees.forEach(emp => {
    // Ensure we have a valid employee with _id
    if (emp && emp._id) {
      data[emp._id] = {
        ...emp,
        // Ensure all required fields exist with default values
        dateOfBirth: emp.dateOfBirth || '',
        phoneNo: emp.phoneNo || '',
        fatherHusbandName: emp.fatherHusbandName || '',
        address: emp.address || '',
        aadhar: emp.aadhar || '',
        pan: emp.pan || '',
        bank: emp.bank || '',
        branch: emp.branch || '',
        accountNo: emp.accountNo || '',
        ifsc: emp.ifsc || '',
        uan: emp.uan || '',
        esicNo: emp.esicNo || '',
        sKalyanNo: emp.sKalyanNo || ''
      }
    }
  })
  editableData.value = data
}

// Computed properties
const visibleFields = computed(() => {
  return visibleGroups.value.flatMap(groupKey => columnGroups[groupKey].fields)
})

const filteredEmployees = computed(() => {
  if (!searchTerm.value) return props.employees
  
  const search = searchTerm.value.toLowerCase()
  return props.employees.filter(emp => 
    emp.employeeName.toLowerCase().includes(search)
  )
})

const dirtyEmployeesCount = computed(() => {
  return Object.keys(dirtyFields.value).length
})

const totalValidationErrors = computed(() => {
  return Object.values(validationErrors.value).reduce((total, empErrors) => {
    return total + Object.keys(empErrors).length
  }, 0)
})

// Methods
const getFieldLabel = (field) => {
  const labels = {
    dateOfBirth: 'Date of Birth',
    phoneNo: 'Phone Number',
    fatherHusbandName: 'Father/Husband Name',
    address: 'Address',
    aadhar: 'Aadhar Number',
    pan: 'PAN Number',
    bank: 'Bank Name',
    branch: 'Branch',
    accountNo: 'Account Number',
    ifsc: 'IFSC Code',
    uan: 'UAN',
    esicNo: 'ESIC Number',
    sKalyanNo: 'S.Kalyan Number'
  }
  return labels[field] || field
}

const markDirty = (employeeId, field) => {
  if (!dirtyFields.value[employeeId]) {
    dirtyFields.value[employeeId] = new Set()
  }
  dirtyFields.value[employeeId].add(field)
  
  // Validate field
  validateField(employeeId, field)
  
  // Emit changes to parent
  emitChanges()
}

const isDirty = (employeeId) => {
  return dirtyFields.value[employeeId] && dirtyFields.value[employeeId].size > 0
}

const validateField = (employeeId, field) => {
  const value = editableData.value[employeeId][field]
  let error = null
  
  // Field-specific validation
  switch (field) {
    case 'phoneNo':
      if (value && !/^[6-9]\d{9}$/.test(value)) {
        error = 'Invalid phone number format'
      }
      break
    case 'ifsc':
      if (value && !/^[A-Z]{4}0[A-Z0-9]{6}$/.test(value)) {
        error = 'Invalid IFSC format'
      }
      break
    case 'uan':
      if (value && !/^\d{12}$/.test(value)) {
        error = 'UAN must be 12 digits'
      }
      break
    case 'esicNo':
      if (value && !/^\d{10}$/.test(value)) {
        error = 'ESIC must be 10 digits'
      }
      break
  }
  
  // Set or clear validation error
  if (!validationErrors.value[employeeId]) {
    validationErrors.value[employeeId] = {}
  }
  
  if (error) {
    validationErrors.value[employeeId][field] = error
  } else {
    delete validationErrors.value[employeeId][field]
  }
}

const getValidationError = (employeeId, field) => {
  return validationErrors.value[employeeId]?.[field]
}

const hasValidationErrors = (employeeId) => {
  const empErrors = validationErrors.value[employeeId]
  return empErrors && Object.keys(empErrors).length > 0
}

const getInputClasses = (employeeId, field) => {
  const baseClasses = 'w-full rounded-md border shadow-sm focus:ring-1 text-sm'
  const hasError = getValidationError(employeeId, field)
  const isDirtyField = dirtyFields.value[employeeId]?.has(field)
  
  if (hasError) {
    return `${baseClasses} border-red-300 focus:border-red-500 focus:ring-red-500`
  } else if (isDirtyField) {
    return `${baseClasses} border-yellow-300 focus:border-yellow-500 focus:ring-yellow-500`
  } else {
    return `${baseClasses} border-gray-300 focus:border-green-500 focus:ring-green-500`
  }
}

const handleIFSCChange = async (employeeId, ifscCode) => {
  markDirty(employeeId, 'ifsc')
  
  if (ifscCode.length === 11) {
    try {
      // Auto-fetch bank details (simplified for now)
      // In real implementation, this would call an API
      console.log('IFSC validation for:', ifscCode)
    } catch (error) {
      console.error('IFSC validation error:', error)
    }
  }
}

const saveEmployee = (employeeId) => {
  if (hasValidationErrors(employeeId)) return
  
  // Clear dirty state for this employee
  delete dirtyFields.value[employeeId]
  
  // Emit changes
  emitChanges()
}

const resetEmployee = (employeeId) => {
  // Reset to original data
  const originalEmployee = props.employees.find(emp => emp._id === employeeId)
  if (originalEmployee) {
    editableData.value[employeeId] = { ...originalEmployee }
  }
  
  // Clear dirty state and validation errors
  delete dirtyFields.value[employeeId]
  delete validationErrors.value[employeeId]
  
  // Emit changes
  emitChanges()
}

const emitChanges = () => {
  const changes = []
  
  Object.keys(dirtyFields.value).forEach(employeeId => {
    const dirtyFieldsSet = dirtyFields.value[employeeId]
    dirtyFieldsSet.forEach(field => {
      changes.push({
        employeeId,
        field,
        value: editableData.value[employeeId][field]
      })
    })
  })
  
  emit('update:changes', changes)
}

// Initialize on mount
onMounted(() => {
  console.log('PersonalDetailsTable mounted with employees:', props.employees.length)
  initializeEditableData()
})

// Watch for employee changes
watch(() => props.employees, (newEmployees) => {
  console.log('Employees changed, reinitializing:', newEmployees.length)
  initializeEditableData()
}, { deep: true, immediate: true })
</script>
