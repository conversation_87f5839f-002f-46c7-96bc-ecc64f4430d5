<template>
  <div>
    <!-- Summary Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <!-- Total Expenses Card -->
      <div class="bg-white rounded-lg shadow overflow-hidden transition-transform duration-300 hover:transform hover:scale-105">
        <div class="p-5 bg-red-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 rounded-md bg-red-800 p-3">
              <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-red-100 truncate">
                  Total Expenses
                </dt>
                <dd>
                  <div class="text-lg font-bold text-white">
                    {{ isLoading ? 'Loading...' : formatCurrency(summary.totalExpenses) }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-red-50 px-5 py-3">
          <div class="text-sm">
            <a href="#" class="font-medium text-red-700 hover:text-red-900" @click.prevent="navigateTo('/expenses/reports?type=category')">
              View breakdown by category
            </a>
          </div>
        </div>
      </div>

      <!-- Total Receipts Card -->
      <div class="bg-white rounded-lg shadow overflow-hidden transition-transform duration-300 hover:transform hover:scale-105">
        <div class="p-5 bg-green-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 rounded-md bg-green-800 p-3">
              <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-green-100 truncate">
                  Total Receipts
                </dt>
                <dd>
                  <div class="text-lg font-bold text-white">
                    {{ isLoading ? 'Loading...' : formatCurrency(summary.totalReceipts) }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-green-50 px-5 py-3">
          <div class="text-sm">
            <a href="#" class="font-medium text-green-700 hover:text-green-900" @click.prevent="navigateTo('/expenses/list?category=RECEIPT')">
              View all receipts
            </a>
          </div>
        </div>
      </div>

      <!-- Cash Balance Card -->
      <div class="bg-white rounded-lg shadow overflow-hidden transition-transform duration-300 hover:transform hover:scale-105">
        <div class="p-5 bg-indigo-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 rounded-md bg-indigo-800 p-3">
              <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-indigo-100 truncate">
                  Cash Balance
                </dt>
                <dd>
                  <div class="text-lg font-bold text-white">
                    {{ isLoading ? 'Loading...' : formatCurrency(summary.cashBalance) }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-indigo-50 px-5 py-3">
          <div class="text-sm">
            <a href="#" class="font-medium text-indigo-700 hover:text-indigo-900" @click.prevent="navigateTo('/expenses/ledgers')">
              View cash ledger
            </a>
          </div>
        </div>
      </div>

      <!-- Bank Balance Card -->
      <div class="bg-white rounded-lg shadow overflow-hidden transition-transform duration-300 hover:transform hover:scale-105">
        <div class="p-5 bg-violet-600">
          <div class="flex items-center">
            <div class="flex-shrink-0 rounded-md bg-violet-800 p-3">
              <svg class="h-6 w-6 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 14v3m4-3v3m4-3v3M3 21h18M3 10h18M3 7l9-4 9 4M4 10h16v11H4V10z" />
              </svg>
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-violet-100 truncate">
                  Bank Balance
                </dt>
                <dd>
                  <div class="text-lg font-bold text-white">
                    {{ isLoading ? 'Loading...' : formatCurrency(summary.bankBalance) }}
                  </div>
                </dd>
              </dl>
            </div>
          </div>
        </div>
        <div class="bg-violet-50 px-5 py-3">
          <div class="text-sm">
            <a href="#" class="font-medium text-violet-700 hover:text-violet-900" @click.prevent="navigateTo('/expenses/ledgers')">
              View bank accounts
            </a>
          </div>
        </div>
      </div>
    </div>

    <!-- Charts and Tables Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Monthly Expenses Chart -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Monthly Expenses</h2>
        <div v-if="isLoading" class="flex items-center justify-center h-64">
          <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <div v-else-if="!monthlyChartData" class="flex items-center justify-center h-64 text-gray-500">
          No data available
        </div>
        <div v-else class="h-64">
          <canvas :key="'monthly-chart-' + Date.now()" ref="monthlyChart" width="400" height="200"></canvas>
        </div>
      </div>

      <!-- Category Distribution Chart -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Expense Categories</h2>
        <div v-if="isLoading" class="flex items-center justify-center h-64">
          <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <div v-else-if="!categoryChartData" class="flex items-center justify-center h-64 text-gray-500">
          No data available
        </div>
        <div v-else class="h-64">
          <canvas :key="'category-chart-' + Date.now()" ref="categoryChart" width="400" height="200"></canvas>
        </div>
      </div>
    </div>

    <!-- Recent Transactions and Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- Recent Transactions -->
      <div class="lg:col-span-2 bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Recent Transactions</h2>
        </div>
        <div v-if="isLoading" class="flex items-center justify-center h-64">
          <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        </div>
        <div v-else-if="recentTransactions.length === 0" class="flex items-center justify-center h-64 text-gray-500">
          No recent transactions
        </div>
        <div v-else class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Date
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Paid To/From
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Amount
                </th>
                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="transaction in recentTransactions" :key="transaction.id" class="hover:bg-gray-50">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {{ formatDate(transaction.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  <div>
                    {{ transaction.paidTo }}
                    <div v-if="transaction.hasDeductions" class="text-xs text-orange-600 mt-1">
                      <svg class="inline h-3 w-3 mr-1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 0l-3-3m3 3l-3 3M9 17h6m0 0l-3-3m3 3l-3 3" />
                      </svg>
                      {{ transaction.deductions?.length || 0 }} deduction(s)
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div :class="getAmountClass(getDisplayAmount(transaction))">
                    {{ formatCurrency(getDisplayAmount(transaction)) }}
                    <div v-if="transaction.hasDeductions" class="text-xs text-gray-500 mt-1">
                      Net of {{ formatCurrency(getTotalDeductions(transaction)) }} deductions
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="getCategoryClass(transaction.category)"
                  >
                    {{ transaction.category || 'PAYMENT' }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="px-6 py-4 border-t border-gray-200 bg-gray-50">
          <a href="#" class="text-sm font-medium text-indigo-600 hover:text-indigo-900" @click.prevent="navigateTo('/expenses/list')">
            View all transactions
          </a>
        </div>
      </div>

      <!-- Quick Actions -->
      <div class="bg-white rounded-lg shadow overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
          <h2 class="text-lg font-medium text-gray-900">Quick Actions</h2>
        </div>
        <div class="p-6 space-y-6">
          <button
            @click="navigateTo('/expenses/add')"
            class="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-transform duration-200 hover:scale-105"
          >
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add New Expense
          </button>

          <button
            @click="navigateTo('/expenses/transfer')"
            class="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 transition-transform duration-200 hover:scale-105"
          >
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
            Transfer Funds
          </button>

          <button
            @click="navigateTo('/expenses/reports')"
            class="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-violet-600 hover:bg-violet-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-violet-500 transition-transform duration-200 hover:scale-105"
          >
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Generate Reports
          </button>

          <button
            @click="navigateTo('/expenses/ledgers')"
            class="w-full flex items-center justify-center px-4 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-transform duration-200 hover:scale-105"
          >
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
            Manage Ledgers
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, onMounted, watch, nextTick } from 'vue';
import { useRouter } from '#app';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useLedgers } from '~/composables/expenses/useLedgers';
import { useReports } from '~/composables/expenses/useReports';
import useToast from '~/composables/ui/useToast';
import Chart from 'chart.js/auto';

export default {
  name: 'ExpensesDashboard',

  props: {
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  setup(props) {
    const router = useRouter();

    // Get composables
    const { expenses, fetchExpenses } = useExpenses();
    const { cashLedgers, bankLedgers, totalCashBalance, totalBankBalance, fetchLedgers } = useLedgers();
    const { generateReport } = useReports();
    const toast = useToast();

    // Chart references
    const monthlyChart = ref(null);
    const categoryChart = ref(null);

    // Chart instances
    let monthlyChartInstance = null;
    let categoryChartInstance = null;

    // Chart data
    const monthlyChartData = ref(null);
    const categoryChartData = ref(null);

    // Summary data
    const summary = ref({
      totalExpenses: 0,
      totalReceipts: 0,
      cashBalance: 0,
      bankBalance: 0
    });

    // Recent transactions
    const recentTransactions = ref([]);

    // Methods
    const formatCurrency = (amount, showNegative = false) => {
      // For display purposes, we show the absolute value of expenses (negative amounts)
      // unless showNegative is true
      const displayAmount = (!showNegative && amount < 0) ? Math.abs(amount) : amount;

      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(displayAmount);
    };

    const formatDate = (date) => {
      if (!date) return '-';
      return new Date(date).toLocaleDateString();
    };

    const getAmountClass = (amount) => {
      return amount < 0 ? 'text-red-600' : 'text-green-600';
    };

    const getCategoryClass = (category) => {
      switch (category) {
        case 'PAYMENT':
          return 'bg-red-100 text-red-800';
        case 'RECEIPT':
          return 'bg-green-100 text-green-800';
        case 'TRANSFER':
          return 'bg-blue-100 text-blue-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    // Helper functions for deduction handling
    const getDisplayAmount = (transaction) => {
      // Use net amount if available, otherwise use gross amount
      return transaction.netAmount !== undefined ? transaction.netAmount : transaction.amount;
    };

    const getTotalDeductions = (transaction) => {
      if (!transaction.deductions || !Array.isArray(transaction.deductions)) {
        return 0;
      }
      return transaction.deductions.reduce((sum, deduction) => sum + (deduction.amount || 0), 0);
    };

    const navigateTo = (path) => {
      router.push(path);
    };

    const loadDashboardData = async () => {
      try {
        // Fetch expenses and ledgers
        await Promise.all([
          fetchExpenses(),
          fetchLedgers()
        ]);

        // Generate monthly report
        const monthlyReport = await generateReport('monthly');

        // Generate category report
        const categoryReport = await generateReport('category');

        // Update chart data
        updateChartData(monthlyReport, categoryReport);

        // Update summary data
        updateSummaryData();

        // Update recent transactions
        updateRecentTransactions();
      } catch (error) {

        toast.error('Error loading dashboard data: ' + error.message, {
          position: 'top-center',
          duration: 5000
        });
      }
    };

    const updateChartData = (monthlyReport, categoryReport) => {
      // Monthly chart data
      if (monthlyReport && monthlyReport.data && monthlyReport.data.length > 0) {
        monthlyChartData.value = {
          labels: monthlyReport.data.map(item => {
            const [year, month] = item.month.split('-');
            return new Date(year, month - 1).toLocaleDateString('default', { month: 'short', year: 'numeric' });
          }),
          datasets: [
            {
              label: 'PAYMENT',
              data: monthlyReport.data.map(item => item.totalExpenses),
              backgroundColor: 'rgba(239, 68, 68, 0.7)', // Stronger red for PAYMENT
              borderColor: 'rgb(239, 68, 68)',
              borderWidth: 1
            },
            {
              label: 'RECEIPT',
              data: monthlyReport.data.map(item => item.totalReceipts),
              backgroundColor: 'rgba(34, 197, 94, 0.7)', // Stronger green for RECEIPT
              borderColor: 'rgb(34, 197, 94)',
              borderWidth: 1
            }
          ]
        };
      }

      // Category chart data
      if (categoryReport && categoryReport.data && categoryReport.data.length > 0) {
        // Define specific colors for PAYMENT and RECEIPT
        const paymentColor = 'rgba(239, 68, 68, 0.7)'; // Red for PAYMENT
        const receiptColor = 'rgba(34, 197, 94, 0.7)'; // Green for RECEIPT

        // Other colors for additional categories
        const otherColors = [
          'rgba(59, 130, 246, 0.7)',  // Blue
          'rgba(139, 92, 246, 0.7)',  // Purple
          'rgba(236, 72, 153, 0.7)',  // Pink
          'rgba(245, 158, 11, 0.7)',  // Orange
          'rgba(16, 185, 129, 0.7)',  // Teal
          'rgba(107, 114, 128, 0.7)'  // Gray
        ];

        categoryChartData.value = {
          labels: categoryReport.data.map(item => item.category),
          datasets: [
            {
              label: 'Amount',
              // For category chart, we want to show both expenses and receipts
              data: categoryReport.data.map(item => {
                // Use the appropriate value based on category
                if (item.category === 'RECEIPT') {
                  return item.totalReceipts;
                } else {
                  return item.totalExpenses;
                }
              }),
              backgroundColor: categoryReport.data.map(item => {
                // Use specific colors for PAYMENT and RECEIPT
                if (item.category === 'PAYMENT') {
                  return paymentColor;
                } else if (item.category === 'RECEIPT') {
                  return receiptColor;
                } else {
                  // For other categories, use the color array
                  const index = categoryReport.data.findIndex(d => d.category === item.category);
                  return otherColors[index % otherColors.length];
                }
              }),
              borderWidth: 1
            }
          ]
        };
      }
    };

    const updateSummaryData = () => {
      try {
        // Ensure we have valid expenses data
        if (!expenses.value || !Array.isArray(expenses.value)) {
          console.warn('Invalid expenses data for summary calculation');
          return;
        }

        // Calculate total expenses and receipts using net amounts (actual financial impact)
        const totalExpenses = expenses.value
          .filter(e => {
            // Filter out transfers and ensure we have valid expense data
            const netAmount = e.netAmount !== undefined ? e.netAmount : e.amount;
            return !e.isTransfer &&
                   netAmount !== undefined &&
                   netAmount !== null &&
                   netAmount < 0; // Payments (expenses) are negative
          })
          .reduce((sum, expense) => {
            // Use net amount if available, otherwise fall back to gross amount
            const netAmount = expense.netAmount !== undefined ? expense.netAmount : expense.amount;
            const amount = Math.abs(Number(netAmount) || 0);
            return sum + amount;
          }, 0);

        const totalReceipts = expenses.value
          .filter(e => {
            // Filter out transfers and ensure we have valid receipt data
            const netAmount = e.netAmount !== undefined ? e.netAmount : e.amount;
            return !e.isTransfer &&
                   netAmount !== undefined &&
                   netAmount !== null &&
                   netAmount > 0; // Receipts (income) are positive
          })
          .reduce((sum, expense) => {
            // Use net amount if available, otherwise fall back to gross amount
            const netAmount = expense.netAmount !== undefined ? expense.netAmount : expense.amount;
            const amount = Number(netAmount) || 0;
            return sum + amount;
          }, 0);

        // Ensure balance values are valid numbers
        const cashBalance = Number(totalCashBalance.value) || 0;
        const bankBalance = Number(totalBankBalance.value) || 0;

        // Update summary with validated data
        summary.value = {
          totalExpenses: Math.round(totalExpenses * 100) / 100, // Round to 2 decimal places
          totalReceipts: Math.round(totalReceipts * 100) / 100,
          cashBalance: Math.round(cashBalance * 100) / 100,
          bankBalance: Math.round(bankBalance * 100) / 100
        };

        console.log('Summary data updated:', {
          expenseCount: expenses.value.length,
          totalExpenses: summary.value.totalExpenses,
          totalReceipts: summary.value.totalReceipts,
          cashBalance: summary.value.cashBalance,
          bankBalance: summary.value.bankBalance
        });
      } catch (error) {
        console.error('Error updating summary data:', error);
        // Set default values on error
        summary.value = {
          totalExpenses: 0,
          totalReceipts: 0,
          cashBalance: 0,
          bankBalance: 0
        };
      }
    };

    const updateRecentTransactions = () => {
      // Get the 5 most recent transactions
      recentTransactions.value = [...expenses.value]
        .sort((a, b) => new Date(b.date) - new Date(a.date))
        .slice(0, 5);
    };

    // Separate functions for each chart to improve error isolation
    const renderMonthlyChart = () => {
      return new Promise((resolve) => {
        // Wait for next frame to ensure DOM is ready
        requestAnimationFrame(() => {
          if (!monthlyChart.value) {

            return resolve(false);
          }

          try {
            // Clean up existing chart
            if (monthlyChartInstance) {
              monthlyChartInstance.destroy();
              monthlyChartInstance = null;
            }

            // Check if we have data
            if (!monthlyChartData.value) {

              return resolve(false);
            }

            // Get context safely
            let ctx;
            try {
              ctx = monthlyChart.value.getContext('2d');
            } catch (err) {

              return resolve(false);
            }

            if (!ctx) {

              return resolve(false);
            }

            // Create new chart
            monthlyChartInstance = new Chart(ctx, {
              type: 'bar',
              data: monthlyChartData.value,
              options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                  y: {
                    beginAtZero: true
                  }
                }
              }
            });


            resolve(true);
          } catch (error) {

            resolve(false);
          }
        });
      });
    };

    const renderCategoryChart = () => {
      return new Promise((resolve) => {
        // Wait for next frame to ensure DOM is ready
        requestAnimationFrame(() => {
          if (!categoryChart.value) {

            return resolve(false);
          }

          try {
            // Clean up existing chart
            if (categoryChartInstance) {
              categoryChartInstance.destroy();
              categoryChartInstance = null;
            }

            // Check if we have data
            if (!categoryChartData.value) {

              return resolve(false);
            }

            // Get context safely
            let ctx;
            try {
              ctx = categoryChart.value.getContext('2d');
            } catch (err) {

              return resolve(false);
            }

            if (!ctx) {

              return resolve(false);
            }

            // Create new chart
            categoryChartInstance = new Chart(ctx, {
              type: 'doughnut',
              data: categoryChartData.value,
              options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: 'right'
                  }
                }
              }
            });


            resolve(true);
          } catch (error) {

            resolve(false);
          }
        });
      });
    };

    // Helper function to check if canvas is ready
    const isCanvasReady = (canvasRef) => {
      if (!canvasRef || !canvasRef.value) {
        return false;
      }

      try {
        // Try to get context as a test
        const ctx = canvasRef.value.getContext('2d');
        return !!ctx;
      } catch (error) {

        return false;
      }
    };

    // Main render function that coordinates both charts
    const renderCharts = async () => {


      // Use a small delay to ensure DOM is fully ready
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check if canvases are ready
      let attempts = 0;
      const maxAttempts = 5;

      while (attempts < maxAttempts) {
        if (isCanvasReady(monthlyChart) && isCanvasReady(categoryChart)) {

          break;
        }


        await new Promise(resolve => setTimeout(resolve, 200));
        attempts++;

        if (attempts >= maxAttempts) {

        }
      }

      // Render charts in sequence
      const monthlyResult = await renderMonthlyChart();


      const categoryResult = await renderCategoryChart();


      return monthlyResult && categoryResult;
    };

    // Initialize
    onMounted(async () => {

      await loadDashboardData();

      // Wait for DOM to be fully ready
      setTimeout(async () => {

        try {
          await renderCharts();
        } catch (error) {

        }
      }, 500); // Longer timeout to ensure everything is ready
    });

    // Watch for changes to chart data
    watch([monthlyChartData, categoryChartData], async (newValues, oldValues) => {


      // Wait for DOM to be fully ready
      setTimeout(async () => {
        try {
          await renderCharts();
        } catch (error) {

        }
      }, 200);
    });

    // Watch for changes to expenses data with debouncing
    let updateTimeout = null;
    watch(() => expenses.value, async (newExpenses, oldExpenses) => {
      // Clear any pending updates to prevent race conditions
      if (updateTimeout) {
        clearTimeout(updateTimeout);
      }

      // Debounce updates to prevent multiple rapid refreshes
      updateTimeout = setTimeout(async () => {
        try {
          // Check if this is an addition, update, or deletion
          if (oldExpenses && newExpenses) {
            if (newExpenses.length > oldExpenses.length) {
              // New transaction added
              toast.success('New transaction added successfully', {
                position: 'top-center',
                duration: 3000
              });
            } else if (newExpenses.length < oldExpenses.length) {
              // Transaction deleted
              toast.success('Transaction deleted successfully', {
                position: 'top-center',
                duration: 3000
              });
            } else {
              // Transaction updated (same length but data changed)
              toast.success('Transaction updated successfully', {
                position: 'top-center',
                duration: 3000
              });
            }
          }

          // Ensure all data is fresh before calculations
          await nextTick();

          // Generate reports in parallel for better performance
          const [monthlyReport, categoryReport] = await Promise.all([
            generateReport('monthly'),
            generateReport('category')
          ]);

          // Update all dashboard components atomically
          updateChartData(monthlyReport, categoryReport);
          updateSummaryData();
          updateRecentTransactions();

          console.log('Dashboard updated successfully after expense changes');
        } catch (error) {
          console.error('Dashboard update error:', error);
          toast.error('Error updating dashboard: ' + error.message, {
            position: 'top-center',
            duration: 5000
          });
        }
      }, 300); // 300ms debounce to prevent rapid successive updates
    }, { deep: true });

    // Watch for changes to ledger data (cash and bank balances)
    watch([cashLedgers, bankLedgers], async () => {


      try {
        // Update summary data with new balances
        updateSummaryData();

        // Show toast notification
        toast.info('Account balances updated', {
          position: 'top-center',
          duration: 2000
        });


      } catch (error) {

        toast.error('Error updating account balances: ' + error.message, {
          position: 'top-center',
          duration: 5000
        });
      }
    }, { deep: true });

    // Function to refresh all dashboard data
    const refreshDashboard = async () => {

      try {
        // Show loading toast
        const loadingToast = toast.loading('Refreshing dashboard...', {
          position: 'top-center'
        });

        await loadDashboardData();
        await renderCharts();

        // Close loading toast
        toast.dismiss(loadingToast);

        // Show success toast
        toast.success('Dashboard refreshed successfully', {
          position: 'top-center',
          duration: 2000
        });


        return true;
      } catch (error) {


        // Show error toast
        toast.error('Failed to refresh dashboard: ' + error.message, {
          position: 'top-center',
          duration: 5000
        });

        return false;
      }
    };

    return {
      monthlyChart,
      categoryChart,
      monthlyChartData,
      categoryChartData,
      summary,
      recentTransactions,
      formatCurrency,
      formatDate,
      getAmountClass,
      getCategoryClass,
      getDisplayAmount,
      getTotalDeductions,
      navigateTo,
      refreshDashboard // Expose refresh function to parent components
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
