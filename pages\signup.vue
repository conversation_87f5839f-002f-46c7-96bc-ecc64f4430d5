<template>
  <div class="h-screen relative flex items-center justify-center overflow-hidden bg-[url('https://images.unsplash.com/photo-*************-50d01698950b?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80')] bg-cover bg-center"
  style="height: calc(100vh - 80px); margin-top: -25px; margin-bottom: -60px;">
    <div v-if="mounted">
      <div class="relative z-10 w-full md:w-[800px] px-4">
        <div class="bg-white/15 backdrop-blur-xl rounded-2xl p-9 shadow-2xl border-2 border-white/30">
          <div class="mb-9 text-center">
            <h1 class="text-4xl font-bold text-white mb-2">Create Account</h1>
            <p class="text-white/90 text-lg">Join us to start your journey</p>
          </div>

          <form @submit.prevent="handleRegister" class="grid grid-cols-2 gap-x-4 gap-y-3">
            <div>
              <label class="block text-base font-medium text-white/90 mb-2">Username</label>
              <div class="relative">
                <input v-model="username" type="text"
                  class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
                  placeholder="Choose your username" required>
                <span class="absolute right-4 top-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-white/60">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 6a3.75 3.75 0 11-7.5 0 3.75 3.75 0 017.5 0zM4.501 20.118a7.5 7.5 0 0114.998 0A17.933 17.933 0 0112 21.75c-2.676 0-5.216-.584-7.499-1.632z" />
                  </svg>
                </span>
              </div>
            </div>

            <div>
              <label class="block text-base font-medium text-white/90 mb-2">Email</label>
              <div class="relative">
                <input v-model="email" type="email"
                  class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
                  placeholder="Enter your email" required>
                <span class="absolute right-4 top-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-white/60">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                  </svg>
                </span>
              </div>
            </div>

            <div>
              <label class="block text-base font-medium text-white/90 mb-2">Full Name</label>
              <div class="relative">
                <input v-model="fullname" type="text"
                  class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
                  placeholder="Enter your full name" required>
                <span class="absolute right-4 top-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-white/60">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 9h3.75M15 12h3.75M15 15h3.75M4.5 19.5h15a2.25 2.25 0 002.25-2.25V6.75A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25v10.5A2.25 2.25 0 004.5 19.5zm6-10.125a1.875 1.875 0 11-3.75 0 1.875 1.875 0 013.75 0zm1.294 6.336a6.721 6.721 0 01-3.17.789 6.721 6.721 0 01-3.168-.789 3.376 3.376 0 016.338 0z" />
                  </svg>
                </span>
              </div>
            </div>

            <div>
              <label class="block text-base font-medium text-white/90 mb-2">Password</label>
              <div class="relative">
                <input v-model="password" type="password"
                  class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
                  placeholder="Choose your password" required>
                <span class="absolute right-4 top-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-white/60">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 10.5V6.75a4.5 4.5 0 10-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 002.25-2.25v-6.75a2.25 2.25 0 00-2.25-2.25H6.75a2.25 2.25 0 00-2.25 2.25v6.75a2.25 2.25 0 002.25 2.25z" />
                  </svg>
                </span>
              </div>
            </div>

            <div>
              <label class="block text-base font-medium text-white/90 mb-2">Select Firm</label>
              <div class="relative">
                <select v-model="selectedFirmId" class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10" required>
                  <option value="" disabled>Select a firm</option>
                  <option v-for="firm in firms" :key="firm._id" :value="firm._id" class="text-black">{{ firm.name }}</option>
                  <option value="new" class="text-black font-bold">Create New</option>
                </select>
                <div v-if="!selectedFirmId && formSubmitted" class="text-red-400 text-sm mt-2">Please select a firm</div>
              </div>

              <!-- Create New Firm Button (shown when "Create New" is selected) -->
              <div v-if="selectedFirmId === 'new'" class="mt-3">
                <div v-if="selectedFirm" class="bg-green-500/30 p-3 rounded-lg mb-3">
                  <p class="text-white font-medium">New firm created: <span class="font-bold">{{ selectedFirm.name }}</span></p>
                  <p class="text-white/80 text-sm">Status: {{ selectedFirm.status }}</p>
                </div>
                <button
                  type="button"
                  @click="showFirmModal = true"
                  class="w-full py-2 bg-green-500/50 hover:bg-green-500/70 text-white rounded-xl font-semibold text-base transition-colors duration-300">
                  {{ selectedFirm ? 'Change Firm' : 'Create New Firm' }}
                </button>
              </div>
            </div>

            <!-- Firm Create Modal -->
            <FirmCreateModal
              :show="showFirmModal"
              @close="showFirmModal = false"
              @created="handleFirmCreated" />


            <div>
              <label class="block text-base font-medium text-white/90 mb-2">Manager Access Code (optional)</label>
              <div class="relative">
                <input v-model="managerCode" type="text"
                  class="w-full px-4 py-2 bg-white/25 backdrop-blur-md rounded-xl border-2 border-white/40 text-white placeholder-white/60 focus:ring-2 focus:ring-white/60 focus:outline-none text-base transition-all duration-300 h-10"
                  placeholder="Enter code if you have one">
                <span class="absolute right-4 top-2">
                  <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5 text-white/60">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M15.75 5.25a3 3 0 013 3m3 0a6 6 0 01-7.029 5.912c-.563-.097-1.159.026-1.563.43L10.5 17.25H8.25v2.25H6v2.25H2.25v-2.818c0-.597.237-1.17.659-1.591l6.499-6.499c.404-.404.527-1 .43-1.563A6 6 0 1121.75 8.25z" />
                  </svg>
                </span>
              </div>
              <div class="text-sm text-white/70 mt-2">Leave empty to register as a regular user</div>
            </div>

            <div class="col-span-2">
              <button type="submit"
                class="w-full py-3 bg-white/20 backdrop-blur-md text-white rounded-xl font-semibold text-base hover:bg-white/30 focus:outline-none focus:ring-2 focus:ring-white/60 transition-all duration-300 mt-4">
                Create Account
              </button>

              <div v-if="errorMsg" class="text-red-400 text-center mt-3 bg-white/10 backdrop-blur-md rounded-lg p-2">{{ errorMsg }}</div>
              <div v-if="successMsg" class="text-emerald-400 text-center mt-3 bg-white/10 backdrop-blur-md rounded-lg p-2">{{ successMsg }}</div>

              <!-- Login Link -->
              <div class="mt-4 text-center">
                <a href="/login" class="text-white hover:text-blue-200 transition-colors duration-300">
                  Already have an account? Login here
                </a>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import FirmCreateModal from '~/components/FirmCreateModal.vue';

// Set page title
useHead({
  title: 'Sign Up',
  meta: [
    { name: 'description', content: 'Create a new account' }
  ]
});

const mounted = ref(false);
onMounted(() => {
  mounted.value = true;
  loadFirms();
});

const username = ref('');
const email = ref('');
const fullname = ref('');
const password = ref('');
const managerCode = ref('');
const errorMsg = ref('');
const successMsg = ref('');
const selectedFirmId = ref('');
const formSubmitted = ref(false);
const firms = ref([]);
const showFirmModal = ref(false);
const selectedFirm = ref(null);


// Load firms for the dropdown
async function loadFirms() {
  try {
    const response = await fetch('/api/firms');
    const data = await response.json();
    firms.value = data;
  } catch (error) {
    console.error('Error loading firms:', error);
    errorMsg.value = 'Failed to load firms. Please try again later.';
  }
}

// Handle firm creation from modal
function handleFirmCreated(firm) {
  selectedFirm.value = firm;
  // Keep selectedFirmId as 'new' to indicate we're using a newly created firm
}



async function handleRegister() {
  errorMsg.value = '';
  successMsg.value = '';
  formSubmitted.value = true;


  // Basic validation for required fields
  if (!username.value.trim()) {
    errorMsg.value = 'Please enter a username';
    return;
  }

  if (!email.value.trim()) {
    errorMsg.value = 'Please enter an email address';
    return;
  }

  // Basic email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email.value)) {
    errorMsg.value = 'Please enter a valid email address';
    return;
  }

  if (!fullname.value.trim()) {
    errorMsg.value = 'Please enter your full name';
    return;
  }

  if (!password.value) {
    errorMsg.value = 'Please enter a password';
    return;
  }

  if (password.value.length < 6) {
    errorMsg.value = 'Password must be at least 6 characters long';
    return;
  }

  if (!selectedFirmId.value) {
    errorMsg.value = 'Please select a firm';
    return;
  }

  // Check if "Create New" is selected but no firm has been created yet
  if (selectedFirmId.value === 'new' && !selectedFirm.value) {
    errorMsg.value = 'Please create a new firm first';
    showFirmModal.value = true;
    return;
  }

  // Clear any previous error messages
  errorMsg.value = '';

  try {
    let firmIdToUse = selectedFirmId.value;

    // If using a newly created firm, use its ID
    if (selectedFirmId.value === 'new' && selectedFirm.value) {
      firmIdToUse = selectedFirm.value._id;
    }

    const response = await fetch('/api/auth/register', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        username: username.value,
        email: email.value,
        fullname: fullname.value,
        password: password.value,
        firmId: firmIdToUse,
        managerCode: managerCode.value
      })
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.statusMessage || 'Registration failed');
    }


    // Set a more detailed success message based on whether a new firm was created
    if (selectedFirmId.value === 'new' && selectedFirm.value) {
      successMsg.value = 'Account created successfully! Your firm is pending approval. You will be notified by email when it is approved.';
    } else {
      successMsg.value = 'Account created successfully! You can now log in.';
    }

    // Redirect to login after registration with a delay
    setTimeout(() => {
      window.location.href = '/login';
    }, 2000);
  } catch (error) {
    console.error('Registration error:', error);
    errorMsg.value = error.message || 'Registration failed';
  }
}
</script>

<style scoped>
.glass-input :deep(.border) {
  @apply bg-white/25 backdrop-blur-md border-white/40 text-white placeholder-white/60;
}

.glass-input :deep(input) {
  @apply bg-white/25 backdrop-blur-md text-white placeholder-white/60;
}

.glass-input :deep(.suggestions) {
  @apply bg-white/20 backdrop-blur-md border-white/40;
}

.glass-input :deep(.suggestion-item) {
  @apply text-white hover:bg-white/20;
}

/* Fix for dropdown styling */
select option {
  background-color: white;
  color: black;
  padding: 8px;
}

select option[value="new"] {
  font-weight: bold;
  background-color: #f0f9ff;
}
</style>
