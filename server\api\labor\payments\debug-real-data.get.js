import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'
import { LaborPaymentService } from '~/utils/laborPaymentService.js'

/**
 * Debug endpoint for real data issues
 * Helps diagnose why payments aren't showing in the unpaid amounts table
 */
export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { firmId, groupId } = query

    if (!firmId || !groupId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: firmId and groupId are required'
      })
    }

    // Get Supabase configuration
    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found for this firm'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Initialize the payment service
    const paymentService = new LaborPaymentService(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Get raw data step by step
    console.log('=== DEBUGGING REAL DATA ===')
    console.log('Group ID:', groupId)
    console.log('Firm ID:', firmId)

    // 1. Check if group exists
    const { data: groupData, error: groupError } = await supabase
      .from('labor_groups')
      .select('*')
      .eq('id', groupId)
      .eq('firm_id', firmId)

    console.log('Group data:', groupData)
    if (groupError) console.log('Group error:', groupError)

    // 2. Get labor profiles for this group
    const { data: profiles, error: profilesError } = await supabase
      .from('labor_profiles')
      .select('*')
      .eq('group_id', groupId)

    console.log('Labor profiles:', profiles)
    if (profilesError) console.log('Profiles error:', profilesError)

    // 3. Get all payments for this group
    const payments = await paymentService.getGroupPayments(groupId)
    console.log('Payments found:', payments.length)
    console.log('Payment details:', payments)

    // 4. Get attendance records for this group
    const laborIds = profiles?.map(p => p.id) || []
    console.log('Labor IDs:', laborIds)

    let attendanceRecords = []
    let attendanceError = null
    if (laborIds.length > 0) {
      const { data: attendance, error: attError } = await supabase
        .from('attendance_records')
        .select('*')
        .in('labor_id', laborIds)
        .order('attendance_date', { ascending: true })

      attendanceRecords = attendance || []
      attendanceError = attError
    }

    console.log('Attendance records found:', attendanceRecords.length)
    console.log('Attendance details:', attendanceRecords)
    if (attendanceError) console.log('Attendance error:', attendanceError)

    // 5. Get unique periods
    const periods = await paymentService.getAttendancePeriods(groupId)
    console.log('Unique periods found:', periods.length)
    console.log('Period details:', periods)

    // 6. Test the logic with current data
    let unpaidAmounts = []
    let calculationError = null
    try {
      unpaidAmounts = await paymentService.calculateUnpaidAmounts(groupId)
      console.log('Calculated unpaid amounts:', unpaidAmounts)
    } catch (error) {
      calculationError = error
      console.log('Calculation error:', error)
    }

    // 7. Manual analysis
    const analysis = {
      hasGroup: groupData && groupData.length > 0,
      hasProfiles: profiles && profiles.length > 0,
      hasPayments: payments && payments.length > 0,
      hasAttendance: attendanceRecords && attendanceRecords.length > 0,
      hasPeriods: periods && periods.length > 0,
      hasResults: unpaidAmounts && unpaidAmounts.length > 0
    }

    console.log('Analysis:', analysis)

    // 8. Identify the issue
    let issue = 'Unknown'
    let recommendation = 'Check logs for details'

    if (!analysis.hasGroup) {
      issue = 'Group not found'
      recommendation = 'Verify the group ID and firm ID are correct'
    } else if (!analysis.hasProfiles) {
      issue = 'No labor profiles in group'
      recommendation = 'Add labor profiles to this group first'
    } else if (!analysis.hasPayments) {
      issue = 'No payments found'
      recommendation = 'Add payments for this group'
    } else if (!analysis.hasAttendance) {
      issue = 'No attendance records found'
      recommendation = 'Create attendance records for the labor profiles in this group'
    } else if (!analysis.hasPeriods) {
      issue = 'No attendance periods found'
      recommendation = 'Ensure attendance records have valid period_start and period_end dates'
    } else if (!analysis.hasResults) {
      issue = 'Calculation failed or no matching periods'
      recommendation = 'Check if payment dates align with attendance periods'
    }

    return {
      success: true,
      debug: {
        groupId,
        firmId,
        rawData: {
          group: groupData,
          profiles,
          payments,
          attendanceRecords,
          periods
        },
        analysis,
        issue,
        recommendation,
        unpaidAmounts,
        errors: {
          groupError,
          profilesError,
          attendanceError,
          calculationError
        }
      }
    }

  } catch (error) {
    console.error('Debug error:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to debug real data'
    })
  }
})
