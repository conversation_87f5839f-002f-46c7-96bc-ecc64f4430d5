<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-md">
      <!-- Modal Header -->
      <div class="bg-gradient-to-r from-red-600 to-red-800 p-4 text-white flex justify-between items-center rounded-t-lg">
        <h2 class="text-xl font-bold">Cancel {{ getBillTypeLabel }}</h2>
        <button @click="close" class="text-white hover:text-gray-200">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6">
        <div class="mb-6">
          <div class="bg-yellow-50 border-l-4 border-yellow-400 p-4 mb-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <p class="text-sm text-yellow-700">
                  <strong>Warning:</strong> Cancelling this {{ billType.toLowerCase() }} will reverse all stock movements and financial transactions. This action cannot be undone.
                </p>
              </div>
            </div>
          </div>

          <div class="mb-4">
            <label for="cancellationReason" class="block text-sm font-medium text-gray-700 mb-1">Reason for Cancellation <span class="text-red-500">*</span></label>
            <textarea
              id="cancellationReason"
              v-model="cancellationReason"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-red-500 focus:border-red-500"
              placeholder="Please provide a reason for cancellation"
              required
            ></textarea>
            <p v-if="reasonError" class="mt-1 text-sm text-red-600">{{ reasonError }}</p>
          </div>

          <div class="bg-gray-100 p-4 rounded-md mb-4">
            <h3 class="font-medium text-gray-800 mb-2">{{ getBillTypeLabel }} Details</h3>
            <div class="grid grid-cols-2 gap-2 text-sm">
              <div>
                <span class="text-gray-600">Bill Number:</span>
                <span class="font-medium ml-1">{{ billData.bno }}</span>
              </div>
              <div>
                <span class="text-gray-600">Date:</span>
                <span class="font-medium ml-1">{{ formatDate(billData.bdate) }}</span>
              </div>
              <div>
                <span class="text-gray-600">Party:</span>
                <span class="font-medium ml-1">{{ billData.partyName }}</span>
              </div>
              <div>
                <span class="text-gray-600">Amount:</span>
                <span class="font-medium ml-1">₹{{ formatNumber(billData.ntot) }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="flex justify-end space-x-3">
          <button
            type="button"
            @click="close"
            class="px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="confirmCancellation"
            :disabled="isSubmitting"
            class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 flex items-center"
          >
            <template v-if="isSubmitting">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </template>
            <template v-else>
              Confirm Cancellation
            </template>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import useToast from '~/composables/ui/useToast';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  billData: {
    type: Object,
    default: () => ({})
  },
  billType: {
    type: String,
    default: 'SALES'
  }
});

const emit = defineEmits(['close', 'cancelled']);

const cancellationReason = ref('');
const reasonError = ref('');
const isSubmitting = ref(false);
const { success, error } = useToast();

const getBillTypeLabel = computed(() => {
  switch (props.billType) {
    case 'SALES':
      return 'Sales Bill';
    case 'PURCHASE':
      return 'Purchase Bill';
    case 'CREDIT NOTE':
      return 'Credit Note';
    case 'DEBIT NOTE':
      return 'Debit Note';
    default:
      return 'Bill';
  }
});

function formatDate(dateString) {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric'
  });
}

function formatNumber(num) {
  if (num === undefined || num === null) return '0.00';
  return parseFloat(num).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

function close() {
  cancellationReason.value = '';
  reasonError.value = '';
  emit('close');
}

async function confirmCancellation() {
  // Validate reason
  if (!cancellationReason.value.trim()) {
    reasonError.value = 'Please provide a reason for cancellation';
    return;
  }

  reasonError.value = '';
  isSubmitting.value = true;

  try {
    const $api = useApiWithAuth();
    const response = await $api.post(`/api/inventory/bills/cancel/${props.billData._id}`, {
      cancellationReason: cancellationReason.value
    });

    if (response && response.success) {
      success('Bill cancelled successfully');
      emit('cancelled', response.bill);
      close();
    } else {
      error(response?.message || 'Failed to cancel bill');
    }
  } catch (err) {
    console.error('Error cancelling bill:', err);
    let errorMessage = 'An error occurred while cancelling the bill';

    if (err.response && err.response.data) {
      errorMessage = err.response.data.statusMessage || err.response.data.message || errorMessage;
    } else if (err.message) {
      errorMessage = err.message;
    }

    error(errorMessage);
  } finally {
    isSubmitting.value = false;
  }
}
</script>
