<template>
  <div class="bg-gradient-to-r from-purple-50 to-blue-50 border border-purple-200 rounded-lg p-6">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-lg font-semibold text-gray-900">📰 AI Stock News Research-CLIENT</h4>
      <div class="flex items-center space-x-3">
        <!-- Analysis Button -->
        <button
          v-if="!clientNewsLoading && !clientStockNews"
          @click="startStockNewsAnalysis"
          :disabled="!isConfigured"
          class="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 disabled:opacity-50 transition-all duration-200 shadow-sm"
        >
          🤖 Fetch Stock News
        </button>

        <!-- Refresh Button -->
        <button
          v-if="clientStockNews && !clientNewsLoading"
          @click="startStockNewsAnalysis"
          class="px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-200 shadow-sm"
        >
          🔄 Refresh News
        </button>
      </div>
    </div>

    <!-- AI Configuration Warning -->
    <div v-if="!isConfigured" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <div class="flex items-center space-x-2">
        <span class="text-yellow-600 text-lg">⚠️</span>
        <div>
          <h5 class="font-medium text-yellow-800">AI Configuration Required</h5>
          <p class="text-yellow-700 text-sm">Please configure your AI settings in Global Settings to use stock news research.</p>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="clientNewsLoading" class="mb-4">
      <div class="bg-white rounded-lg p-4">
        <div class="flex items-center space-x-3 mb-3">
          <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
          <span class="text-gray-700">{{ clientNewsStatusMessage || 'AI searching for stock-specific news...' }}</span>
        </div>
        <div class="bg-gray-200 rounded-full h-2">
          <div class="bg-purple-500 h-2 rounded-full transition-all duration-500"
            :style="{ width: clientNewsProgress + '%' }"></div>
        </div>
        <div class="text-xs text-gray-500 mt-1">{{ clientNewsProgress }}% complete</div>
      </div>
    </div>

    <!-- Error State -->
    <div v-else-if="clientNewsError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div class="flex items-center space-x-2">
        <span class="text-red-600 text-lg">❌</span>
        <div>
          <h5 class="font-medium text-red-800">Stock News Analysis Failed</h5>
          <p class="text-red-700 text-sm">{{ clientNewsError }}</p>
          <button @click="startStockNewsAnalysis"
            class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors">
            🔄 Retry Analysis
          </button>
        </div>
      </div>
    </div>

    <!-- Stock News Results -->
    <div v-else-if="clientStockNews" class="space-y-4">
      <!-- Search Summary -->
      <div class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-2">📊 Stock News Summary</h5>
        <p class="text-gray-700 text-sm">{{ clientStockNews.searchSummary }}</p>
        <p class="text-gray-600 text-xs mt-1">Found: {{ clientStockNews.totalFound }}</p>
      </div>

      <!-- Recent Stock News -->
      <div v-if="clientStockNews.recentNews && clientStockNews.recentNews.length > 0" class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-3">📰 Recent Stock News</h5>
        <div class="space-y-3">
          <div v-for="(news, index) in clientStockNews.recentNews" :key="index"
            class="border-l-4 border-blue-400 pl-3">
            <h6 class="font-medium text-gray-900 text-sm">{{ news.headline }}</h6>
            <p class="text-gray-600 text-xs mt-1">{{ news.date }} | {{ news.source }}</p>
            <p class="text-gray-700 text-sm mt-1">{{ news.summary }}</p>
            <span class="inline-block px-2 py-1 text-xs rounded-full mt-2" :class="news.impact === 'Positive' ? 'bg-green-100 text-green-800' :
              news.impact === 'Negative' ? 'bg-red-100 text-red-800' :
                'bg-gray-100 text-gray-800'">
              {{ news.impact }} Impact
            </span>
          </div>
        </div>
      </div>

      <!-- Key Developments -->
      <div v-if="clientStockNews.keyDevelopments" class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-2">🔑 Key Developments</h5>
        <div class="text-gray-700 text-sm" v-html="formatAnalysisText(clientStockNews.keyDevelopments)"></div>
      </div>

      <!-- Market Impact -->
      <div v-if="clientStockNews.marketImpact" class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-2">📈 Market Impact</h5>
        <div class="text-gray-700 text-sm" v-html="formatAnalysisText(clientStockNews.marketImpact)"></div>
      </div>

      <!-- Analyst Views -->
      <div v-if="clientStockNews.analystViews" class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-2">👥 Analyst Views</h5>
        <div class="text-gray-700 text-sm" v-html="formatAnalysisText(clientStockNews.analystViews)"></div>
      </div>

      <!-- Analysis Timestamp -->
      <div class="text-xs text-gray-500 text-center">
        <span v-if="clientStockNews.searchTimestamp">
          Analysis completed: {{ formatTimestamp(clientStockNews.searchTimestamp) }}
        </span>
      </div>
    </div>

    <!-- Initial State -->
    <div v-else class="bg-white rounded-lg p-4 text-center">
      <p class="text-gray-600 mb-3">Click the button above to let AI search for the latest news about {{ stock.symbol }} (CLIENT-SIDE)</p>
      <p class="text-gray-500 text-sm">Client-side AI will search for earnings, announcements, analyst reports, and market developments</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useClientStockNews } from '~/composables/stock-market/client-stock-news/useClientStockNews'

// Props
const props = defineProps<{
  stock: any
}>()

// Client-side stock news composable
const {
  loading: clientNewsLoading,
  error: clientNewsError,
  analysis: clientStockNews,
  progress: clientNewsProgress,
  statusMessage: clientNewsStatusMessage,
  currentOperation,
  isConfigured,
  performStockNewsAnalysis,
  reset: clientNewsReset
} = useClientStockNews()

// Start stock news analysis
const startStockNewsAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings in Global Settings first.')
    return
  }

  try {
    await performStockNewsAnalysis({
      symbol: props.stock.symbol,
      companyName: props.stock.meta?.companyName || props.stock.symbol,
      currentPrice: props.stock.lastPrice,
      change: props.stock.change,
      pChange: props.stock.pChange
    })
  } catch (error) {
    console.error('Stock news analysis failed:', error)
  }
}

// Format analysis text (convert newlines to HTML)
const formatAnalysisText = (text: string) => {
  if (!text) return ''
  return text.replace(/\n/g, '<br>')
}

// Format timestamp
const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}
</script>
