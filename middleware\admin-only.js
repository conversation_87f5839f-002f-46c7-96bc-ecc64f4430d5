import { navigateTo } from '#app'
import useUserRole from '~/composables/auth/useUserRole';
import { showAccessDenied } from '~/utils/accessDenied';

/**
 * Admin-only middleware
 *
 * This middleware restricts access to admin routes to users with the admin role only.
 * If a non-admin user tries to access an admin route, they will be redirected to the dashboard
 * with an error message.
 */
export default defineNuxtRouteMiddleware((to) => {
  // Skip middleware on server-side
  if (process.server) return;

  // Get user role
  const { isAdmin } = useUserRole();

  // If user is an admin, allow access
  if (isAdmin()) return;

  // User is not an admin, show error and redirect to dashboard
  showAccessDenied('Access denied: Admin privileges required');
  console.error(`Unauthorized access attempt to admin page: ${to.path}`);

  // Redirect to dashboard
  return navigateTo('/dashboard');
});
