<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[60]">
    <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[85vh] overflow-hidden">
      <!-- Modal Header -->
      <div class="bg-gradient-to-r from-red-700 to-red-900 p-4 text-white flex justify-between items-center">
        <h2 class="text-xl font-bold">{{ isEdit ? 'Edit CN Note Details' : 'Create CN Note Details' }}</h2>
        <button @click="close" class="text-white hover:text-gray-200">
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[calc(85vh-8rem)]">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">CN Number</label>
              <input
                v-model="formData.cn_no"
                type="text"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
                :class="{ 'border-red-500': cnNoError }"
                @blur="validateCnNo"
              />
              <div v-if="cnNoError" class="text-red-500 text-xs mt-1">{{ cnNoError }}</div>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">CN Date</label>
              <input
                v-model="formData.cn_date"
                type="date"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
              />
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Broker</label>
              <input
                v-model="formData.broker"
                type="text"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
                list="broker-options"
              />
              <datalist id="broker-options">
                <optgroup v-for="(brokers, letter) in brokerOptions" :key="letter" :label="letter">
                  <option v-for="broker in brokers" :key="broker" :value="broker">{{ broker }}</option>
                </optgroup>
              </datalist>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Type</label>
              <select
                v-model="formData.type"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
              >
                <option value="">Select Type</option>
                <option value="BUY">BUY</option>
                <option value="SELL">SELL</option>
              </select>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Folio</label>
              <input
                v-model="formData.folio"
                type="text"
                required
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
                list="folio-options"
              />
              <datalist id="folio-options">
                <optgroup v-for="(folios, letter) in folioOptions" :key="letter" :label="letter">
                  <option v-for="folio in folios" :key="folio" :value="folio">{{ folio }}</option>
                </optgroup>
              </datalist>
            </div>
            <div class="space-y-2">
              <label class="block text-sm font-medium text-gray-700">Other Charges</label>
              <input
                v-model.number="formData.oth_chg"
                type="number"
                step="0.01"
                class="w-full p-2 border rounded focus:ring-red-500 focus:border-red-500"
              />
            </div>
          </div>

          <!-- Records Table -->
          <div class="mt-6">
            <h3 class="text-lg font-semibold mb-4">Folio Records</h3>
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Symbol</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Price</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Quantity</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Brokerage</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Net Amount</th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(record, index) in recordsData" :key="index">
                    <td class="px-6 py-4">
                      <SymbolDropdown
                        v-model="record.symbol"
                        :options="symbolOptions"
                        @update:modelValue="updatePriceFromSymbol($event, record)"
                        @price-updated="(price) => { record.price = price; calculateAmount(record); }"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.price"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        @input="calculateAmount(record)"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.qnty"
                        type="number"
                        class="w-full p-1 border rounded"
                        @input="calculateAmount(record)"
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.amt"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        readonly
                      />
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.brokeragePerUnit"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        @input="calculateNetAmount(record)"
                        placeholder="Per unit"
                        title="Enter brokerage per unit"
                      />
                      <div class="text-xs text-gray-500 mt-1">
                        Total: {{ record.brokerage || 0 }}
                      </div>
                    </td>
                    <td class="px-6 py-4">
                      <input
                        v-model.number="record.namt"
                        type="number"
                        step="0.01"
                        class="w-full p-1 border rounded"
                        readonly
                      />
                    </td>
                    <td class="px-6 py-4">
                      <button
                        type="button"
                        @click="removeRecord(index)"
                        class="text-red-600 hover:text-red-800"
                      >
                        <Icon name="heroicons:trash" class="w-5 h-5" />
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
            <button
              type="button"
              @click="addRecord"
              class="mt-4 bg-red-600 text-white px-4 py-2 rounded hover:bg-red-700"
            >
              Add Record
            </button>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end mt-6">
            <button
              type="submit"
              class="bg-red-600 text-white px-6 py-2 rounded hover:bg-red-700"
              :disabled="isSubmitting"
            >
              {{ isSubmitting ? 'Saving...' : (isEdit ? 'Update' : 'Save') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watchEffect, onMounted, inject } from 'vue'
import PouchDB from 'pouchdb'
import { useCookie } from '#app'
import SymbolDropdown from './SymbolDropdown.vue'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'

const props = defineProps<{
  show: boolean
  isEdit: boolean
  initialData?: any
}>()

const emit = defineEmits(['close', 'submit'])

// Define options for broker and folio datalists
const brokerOptions = ref<Record<string, string[]>>({})
const folioOptions = ref<Record<string, string[]>>({})

// Define interface for form data to ensure type safety
interface CNNoteFormData {
  id: string;
  cn_no: string;
  cn_date: string;
  broker: string;
  type: string;
  folio: string;
  oth_chg: number;
  famt: number;
  firmId?: string; // Optional firmId field
}

// Define interface for record data
interface RecordData {
  symbol: string;
  price: number;
  qnty: number;
  amt: number;
  brokerage: number; // This will store the total brokerage (per unit * quantity)
  brokeragePerUnit?: number; // This is for UI display only
  namt: number;
  sector: string;
  rid: string;
  pdate: string;
  [key: string]: any; // Allow for additional properties
}

const formData = ref<CNNoteFormData>({
  id: '',
  cn_no: '',
  cn_date: '',
  broker: '',
  type: '',
  folio: '',
  oth_chg: 0,
  famt: 0,
  firmId: '' // Initialize with empty string
})

const recordsData = ref<RecordData[]>([])
const isSubmitting = ref(false)
const cnNoError = ref<string>('')

// Validate CN Number
function validateCnNo() {
  cnNoError.value = ''

  if (!formData.value.cn_no) {
    cnNoError.value = 'CN Number is required'
    return false
  }

  formData.value.cn_no = formData.value.cn_no.trim()
  if (formData.value.cn_no === '') {
    cnNoError.value = 'CN Number cannot be empty'
    return false
  }

  return true
}

// For datalist
const symbolOptions = ref<string[]>([])
const DB_PREFIX = 'app_data_';

// Fetch symbols from GS Sheet data in PouchDB
const fetchSymbolOptions = async () => {
  try {
    const gsDb = new PouchDB(`${DB_PREFIX}gs_records`)

    const result = await gsDb.allDocs({ include_docs: true })

    if (result.rows.length > 0) {
      // Extract symbols from GS Sheet data
      const symbols = result.rows.map(row => {
        const doc = row.doc as any
        // Remove NSE: prefix if it exists
        return doc.symbol ? doc.symbol.replace('NSE:', '') : ''
      })

      // Filter out empty symbols and remove duplicates
      const uniqueSymbols = [...new Set(symbols.filter(symbol => symbol))]

      // Sort symbols alphabetically for better user experience
      symbolOptions.value = uniqueSymbols.sort()

      console.log(`Loaded ${symbolOptions.value.length} unique symbols for dropdown`)
    } else {
      console.warn('No GS Sheet data found in PouchDB')
      // If no data in PouchDB, try to fetch from API
      try {
        const api = useApiWithAuth()
        const data = await api.get('/api/nse/gs_record')

        if (data) {
          const symbols = data.map((item: any) => item.symbol.replace('NSE:', ''))
          symbolOptions.value = [...new Set(symbols.filter((s: string) => s))].sort()
          console.log(`Loaded ${symbolOptions.value.length} unique symbols from API`)
        }
      } catch (apiError) {
        console.error('Error fetching symbols from API:', apiError)
      }
    }
  } catch (error) {
    console.error('Error fetching symbol options:', error)
  }
}

// Function to fetch broker and folio options
const fetchOptions = async () => {
  try {
    const api = useApiWithAuth()
    const data = await api.get('/api/nse/options')

    if (data && data.brokers) {
      brokerOptions.value = data.brokers
    }

    if (data && data.folios) {
      folioOptions.value = data.folios
    }
  } catch (error) {
    console.error('Error fetching options:', error)

    // Provide some sample data for testing
    brokerOptions.value = {
      'S': ['SBI', 'SHAREKHAN'],
      'H': ['HDFC', 'HSBC'],
      'I': ['ICICI', 'IIFL']
    }

    folioOptions.value = {
      '1': ['123', '125', '127'],
      '2': ['234', '256'],
      '3': ['345', '378']
    }
  }
}

// Fetch symbol options when component is mounted
onMounted(async () => {
  try {
    // Fetch data in parallel for better performance
    await Promise.all([
      fetchSymbolOptions(),
      fetchOptions()
    ]);
    console.log('CNNoteFormModal: All data loaded successfully');
  } catch (error) {
    console.error('Error loading initial data:', error);
  }
})

// Initialize form data if editing
watchEffect(() => {
  if (props.initialData && props.isEdit) {
    // Ensure proper type conversion for numeric fields
    formData.value = {
      id: props.initialData.id || '',
      cn_no: props.initialData.cn_no || '',
      cn_date: props.initialData.cn_date || '',
      broker: props.initialData.broker || '',
      type: props.initialData.type || '',
      folio: props.initialData.folio || '',
      oth_chg: Number(props.initialData.oth_chg || 0),
      famt: Number(props.initialData.famt || 0),
      firmId: props.initialData.firmId || ''
    }

    // Ensure proper type conversion for records
    // Check for records in Folio_rec first (from database), then fall back to records property
    const recordsSource = props.initialData.Folio_rec && Array.isArray(props.initialData.Folio_rec)
      ? props.initialData.Folio_rec
      : (props.initialData.records && Array.isArray(props.initialData.records) ? props.initialData.records : []);


    if (recordsSource.length > 0) {
      recordsData.value = recordsSource.map((record: any) => {
        const brokerage = Number(record.brokerage || 0);
        const qnty = Number(record.qnty || 0);
        // Calculate per unit brokerage for display in UI
        const brokeragePerUnit = qnty > 0 ? Number((brokerage / qnty).toFixed(2)) : 0;

        // Ensure all required fields are present
        const currentDate = record.pdate || formData.value.cn_date || new Date().toISOString().split('T')[0];

        return {
          symbol: record.symbol || '',
          price: Number(record.price || 0),
          qnty: qnty,
          amt: Number(record.amt || 0),
          brokerage: brokerage, // Total brokerage from backend
          brokeragePerUnit: brokeragePerUnit, // Per unit brokerage for UI
          namt: Number(record.namt || 0),

          // Ensure required fields are present
          sector: record.sector || 'EQUITY',
          rid: record.rid || generateRid(),
          pdate: currentDate,

          // Preserve any other fields
          ...Object.fromEntries(
            Object.entries(record)
              .filter(([key]) => !['symbol', 'price', 'qnty', 'amt', 'brokerage', 'brokeragePerUnit', 'namt', 'sector', 'rid', 'pdate'].includes(key))
          )
        };
      });
    } else {
      recordsData.value = []
    }
  } else {
    resetForm()
  }
})

function resetForm() {
  formData.value = {
    id: '',
    cn_no: '',
    cn_date: '',
    broker: '',
    type: '',
    folio: '',
    oth_chg: 0,
    famt: 0,
    firmId: '' // Reset firmId too
  }
  recordsData.value = []
}

function calculateAmount(record: RecordData) {
  if (record.price && record.qnty) {
    // Ensure proper number conversion and precision
    record.amt = Number(parseFloat((record.price * record.qnty).toString()).toFixed(2))

    // Recalculate brokerage based on quantity
    if (record.brokerage) {
      // We store the per-unit brokerage in the brokerage field
      // But for calculations, we use brokerage * quantity
      calculateNetAmount(record)
    } else {
      calculateNetAmount(record)
    }
  }
}

function calculateNetAmount(record: RecordData) {
  if (record.amt !== undefined) {
    // Get the brokerage per unit from the UI input
    const brokeragePerUnit = Number(record.brokeragePerUnit || 0)

    // Calculate total brokerage (brokerage per unit * quantity)
    const totalBrokerage = brokeragePerUnit * (record.qnty || 0)

    // Store the total brokerage in the brokerage field (this is what gets sent to the backend)
    record.brokerage = Number(parseFloat(totalBrokerage.toString()).toFixed(2))

    // Calculate net amount (amount + total brokerage)
    record.namt = Number(parseFloat((record.amt + record.brokerage).toString()).toFixed(2))


    updateTotalAmount()
  }
}

function updateTotalAmount() {
  // Calculate total with proper number handling
  const total = recordsData.value.reduce(
    (sum, record) => sum + (Number(record.namt) || 0),
    0
  )
  formData.value.famt = Number(parseFloat(total.toString()).toFixed(2))
}

function addRecord() {
  const newRecord = {
    symbol: '',
    price: 0,
    qnty: 0,
    amt: 0,
    brokeragePerUnit: 0, // Per unit brokerage for UI
    brokerage: 0, // Total brokerage (per unit * quantity) for backend
    namt: 0,
    sector: 'EQUITY', // Default sector
    rid: generateRid(), // Generate a unique record ID
    pdate: new Date().toISOString().split('T')[0] // Current date in YYYY-MM-DD format
  }
  recordsData.value.push(newRecord)
}

// Generate a unique record ID
function generateRid() {
  return 'RID_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
}

function removeRecord(index: number) {
  recordsData.value.splice(index, 1)
  updateTotalAmount()
}

async function handleSubmit() {
  try {
    isSubmitting.value = true

    // Validate form data before submission
    if (!validateCnNo() || !formData.value.cn_date || !formData.value.broker || !formData.value.type || !formData.value.folio) {
      alert('Please fill in all required fields')
      isSubmitting.value = false
      return
    }

    // Validate records data
    if (recordsData.value.length === 0) {
      alert('Please add at least one record')
      isSubmitting.value = false
      return
    }

    // Ensure all records have required fields
    for (const record of recordsData.value) {
      if (!record.symbol || !record.qnty) {
        alert('Please fill in all required fields for each record')
        isSubmitting.value = false
        return
      }
    }

    // Create a clean payload with proper type conversions
    const payload = {
      formData: {
        ...formData.value,
        oth_chg: Number(formData.value.oth_chg),
        famt: Number(formData.value.famt)
      },
      recordsData: recordsData.value.map(record => {
        // Make sure all calculations are up to date
        calculateNetAmount(record);

        // Ensure all required fields are present
        const currentDate = formData.value.cn_date || new Date().toISOString().split('T')[0];

        return {
          ...record,
          // Basic fields with proper type conversion
          price: Number(record.price),
          qnty: Number(record.qnty),
          amt: Number(record.amt),
          brokerage: Number(record.brokerage), // This is already the total brokerage
          namt: Number(record.namt),

          // Required fields from the Folio model
          cn_no: formData.value.cn_no,
          broker: formData.value.broker,
          type: formData.value.type,
          folio: formData.value.folio,
          pdate: currentDate,
          sector: record.sector || 'EQUITY',
          rid: record.rid || generateRid()
        };
      })
    }

    emit('submit', payload)
  } catch (error) {
    console.error('Error submitting form:', error)
  } finally {
    isSubmitting.value = false
  }
}

// Function to update price when a symbol is selected
async function updatePriceFromSymbol(symbol: string, record: RecordData) {
  if (!symbol) return;

  try {
    // First try to get price from PouchDB
    const DB_PREFIX = 'app_data_';
    const gsDb = new PouchDB(`${DB_PREFIX}gs_records`);

    // Try to find the symbol in the database
    const result = await gsDb.allDocs({ include_docs: true });

    let price = 0;
    let priceFound = false;

    // Look for the symbol in the database
    for (const row of result.rows) {
      const doc = row.doc as any;
      if (doc.symbol && doc.symbol.replace('NSE:', '') === symbol) {
        // Try to get price from history first
        if (doc.history && doc.history.length > 0) {
          const lastEntry = doc.history[doc.history.length - 1];
          if (lastEntry && lastEntry.close !== undefined) {
            price = parseFloat(String(lastEntry.close));
            if (!isNaN(price)) {
              priceFound = true;
              break;
            }
          }
        }

        // If no history, try direct price property
        if (!priceFound && doc.price !== undefined) {
          price = parseFloat(String(doc.price));
          if (!isNaN(price)) {
            priceFound = true;
            break;
          }
        }
      }
    }

    // If price not found in PouchDB, try API
    if (!priceFound) {
      const api = useApiWithAuth()
      const data = await api.get('/api/nse/all_symbols')

      if (data) {
        const symbolData = data.find((item: any) => item.symbol === symbol);

        if (symbolData && symbolData.price !== undefined) {
          price = parseFloat(String(symbolData.price));
          priceFound = true;
        }
      }
    }

    // If price found, update the record
    if (priceFound && price > 0) {
      record.price = price;
      calculateAmount(record);
    }
  } catch (error) {
    console.error('Error fetching price for symbol:', error);
  }
}

function close() {
  emit('close')
  resetForm()
}
</script>