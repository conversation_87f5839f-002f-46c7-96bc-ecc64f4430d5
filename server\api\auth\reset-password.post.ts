// server/api/auth/reset-password.post.ts
import { defineEventHand<PERSON>, readBody, createError } from 'h3';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import User from '../../models/User';
import { passwordValidator } from '../../utils/passwordPolicy';
import { emailService } from '../../utils/emailService';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { token, password, confirmPassword } = body;

    // Validate required fields
    if (!token || !password || !confirmPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Token, password, and password confirmation are required'
      });
    }

    // Validate password confirmation
    if (password !== confirmPassword) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Passwords do not match'
      });
    }

    // Hash the token to match database
    const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

    // Find user with valid reset token
    const user = await User.findOne({
      passwordResetToken: hashedToken,
      passwordResetExpires: { $gt: new Date() }
    });

    if (!user) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid or expired password reset token'
      });
    }

    // Validate password against policy
    const validationResult = passwordValidator.validatePassword(
      password,
      {
        username: user.username,
        email: user.email,
        fullname: user.fullname
      },
      user.passwordHistory
    );

    if (!validationResult.isValid) {
      throw createError({
        statusCode: 400,
        statusMessage: validationResult.errors.join('. ')
      });
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(12);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Add current password to history before updating
    if (user.password) {
      user.addToPasswordHistory(user.password);
    }

    // Update user password and clear reset token
    user.password = hashedPassword;
    user.lastPasswordChange = new Date();
    user.passwordResetToken = undefined;
    user.passwordResetExpires = undefined;
    
    // Reset failed login attempts if any
    user.failedLoginAttempts = 0;
    user.accountLockedUntil = undefined;

    // Clear all active sessions (force re-login)
    user.activeSessions = [];

    await user.save();

    // Send password change notification email
    try {
      await emailService.sendPasswordChangeNotification(user.email, user.fullname);
    } catch (emailError) {
      console.error('Failed to send password change notification:', emailError);
      // Don't fail the password reset if email fails
    }

    console.log(`Password successfully reset for user: ${user.email}`);

    return {
      message: 'Password has been reset successfully. You can now log in with your new password.',
      passwordStrength: validationResult.strength
    };

  } catch (error: any) {
    console.error('Password reset error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'An error occurred while resetting your password. Please try again.'
    });
  }
});
