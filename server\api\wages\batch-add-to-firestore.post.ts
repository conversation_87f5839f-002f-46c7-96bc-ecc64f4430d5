import { defineEventHand<PERSON>, readBody, createError } from 'h3'
import { getFirestore, Timestamp } from 'firebase-admin/firestore'

export default defineEventHandler(async (event) => {
  const userId = event.context.userId
  const firmId = event.context.user?.firmId

  if (!userId || !firmId) {
    throw createError({
      statusCode: 401,
      message: 'Unauthorized'
    })
  }

  try {
    const { wages, chunkSize = 10, chunkIndex = 0 } = await readBody(event)

    if (!wages || !Array.isArray(wages)) {
      throw createError({
        statusCode: 400,
        message: 'Invalid wages data'
      })
    }

    // Validate required fields before processing
    const invalidWages = wages.filter(wage => !wage.ledgerId || !wage._id || !wage.employeeName)
    if (invalidWages.length > 0) {
      throw createError({
        statusCode: 400,
        message: `Missing required fields in ${invalidWages.length} wage records. Required: ledgerId, _id, employeeName`
      })
    }

    const db = getFirestore()
    const firmIdStr = firmId.toString()
    const userIdStr = userId.toString()
    
    // Process the current chunk
    const start = chunkIndex * chunkSize
    const chunk = wages.slice(start, start + chunkSize)
    
    // Group wages by ledgerId to handle ledger balance updates efficiently
    const wagesByLedger = {}
    for (const wage of chunk) {
      if (!wagesByLedger[wage.ledgerId]) {
        wagesByLedger[wage.ledgerId] = []
      }
      wagesByLedger[wage.ledgerId].push(wage)
    }
    
    // Process each ledger group separately
    const results = []
    
    for (const [ledgerId, ledgerWages] of Object.entries(wagesByLedger)) {
      try {
        // First, get the current ledger balance in a transaction
        let currentBalance = 0
        let newBalance = 0
        
        await db.runTransaction(async (transaction) => {
          // Get the current ledger to update its balance
          const ledgerRef = db.collection('ledgers').doc(ledgerId)
          const ledgerDoc = await transaction.get(ledgerRef)
          
          if (!ledgerDoc.exists) {
            throw new Error(`Ledger with ID ${ledgerId} not found`)
          }
          
          const ledgerData = ledgerDoc.data()
          currentBalance = ledgerData.currentBalance
          
          // Calculate total wage amount for this ledger
          const totalWageAmount = ledgerWages.reduce((sum, wage) => sum + wage.net_salary, 0)
          
          // Calculate new balance
          newBalance = currentBalance - totalWageAmount
          
          // Update ledger balance
          transaction.update(ledgerRef, {
            currentBalance: newBalance,
            updatedAt: Timestamp.now()
          })
        })
        
        // Now use a batch for the expense and ledger transaction records
        const batch = db.batch()
        
        // Process each wage in the batch
        for (const wage of ledgerWages) {
          // 1. Add expense record
          const expenseRef = db.collection('expenses').doc()
          batch.set(expenseRef, {
            amount: -wage.net_salary,
            category: "PAYMENT",
            createdAt: Timestamp.now(),
            date: new Date(wage.paid_date),
            description: `Salary payment to ${wage.employeeName} for ${new Date(wage.salary_month).toLocaleDateString()}`,
            expenseId: wage._id.toString(),
            firmId: firmIdStr,
            isTransfer: false,
            paidTo: wage.employeeName,
            paidToGroup: "SALARY",
            paymentMode: {
              bankId: wage.ledgerId,
              instrumentNo: wage.cheque_no || "",
              type: "bank"
            },
            project: wage.project || "KIR_NON_CORE",
            transferDetails: null,
            updatedAt: Timestamp.now(),
            userId: userIdStr
          })
          
          // 2. Add ledger transaction
          const ledgerTransactionRef = db.collection('ledgerTransactions').doc()
          batch.set(ledgerTransactionRef, {
            amount: wage.net_salary,
            balance: newBalance, // Use the final balance calculated in the transaction
            createdAt: Timestamp.now(),
            date: new Date(wage.paid_date),
            description: `Salary payment to ${wage.employeeName} for ${new Date(wage.salary_month).toLocaleDateString()}`,
            expenseId: wage._id.toString(),
            firmId: firmIdStr,
            ledgerId: wage.ledgerId,
            type: "debit",
            userId: userIdStr
          })
          
          // Add to results
          results.push({
            wageId: wage._id,
            success: true
          })
        }
        
        // Commit the batch
        await batch.commit()
        
      } catch (error) {
        console.error(`Error processing ledger ${ledgerId}:`, error)
        
        // Mark all wages in this ledger as failed
        for (const wage of ledgerWages) {
          results.push({
            wageId: wage._id,
            success: false,
            error: error.message
          })
        }
      }
    }
    
    const successCount = results.filter(r => r.success).length
    const failureCount = results.length - successCount
    const hasMoreChunks = (chunkIndex + 1) * chunkSize < wages.length
    
    return {
      success: true,
      processedCount: chunk.length,
      successCount,
      failureCount,
      chunkIndex,
      hasMoreChunks,
      totalChunks: Math.ceil(wages.length / chunkSize),
      results
    }
  } catch (error) {
    console.error('Error processing batch add to Firestore:', error)
    throw createError({
      statusCode: 500,
      message: error?.message || 'Error processing batch add to Firestore'
    })
  }
})
