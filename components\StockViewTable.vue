<template>
  <div>
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>

    <div v-else-if="isCustomView && (!symbols || symbols.length === 0)" class="text-center py-8">
      <p class="text-gray-500 mb-4">No stocks added to this view yet.</p>
      <p class="text-sm text-gray-400">Add stocks from the stock detail pages.</p>
    </div>

    <!-- Debug info -->
    <div v-if="showDebug" class="bg-gray-100 p-4 mb-4 rounded text-xs">
      <p>View ID: {{ props.viewId }}</p>
      <p>Is Custom View: {{ isCustomView }}</p>
      <p>Symbols: {{ symbols }}</p>
      <p>Market Data Available: {{ marketData.value && marketData.value.nifty50 && marketData.value.nifty50.length > 0 }}</p>
      <p>Displayed Stocks: {{ displayedStocks.length }}</p>
    </div>

    <div v-else>
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">LTP</th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">% Change</th>
                <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Volume</th>
                <th v-if="isCustomView" class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-if="displayedStocks.length === 0" class="hover:bg-gray-50">
                <td :colspan="isCustomView ? 7 : 6" class="px-4 py-4 text-center text-gray-500">
                  {{ searchQuery ? 'No stocks match your search' : 'No stock data available' }}
                </td>
              </tr>
              <tr v-for="stock in displayedStocks" :key="stock.symbol" class="hover:bg-gray-50">
                <td class="px-4 py-2 whitespace-nowrap text-sm font-medium">
                  <NuxtLink :to="`/stock-market/${stock.symbol}`" class="text-indigo-600 hover:text-indigo-800 hover:underline">
                    {{ stock.symbol }}
                  </NuxtLink>
                </td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ stock.meta?.companyName || 'N/A' }}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-900">{{ formatPrice(stock.lastPrice) }}</td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-right" :class="parseFloat(stock.change) >= 0 ? 'text-green-600' : 'text-red-600'">
                  {{ stock.change > 0 ? '+' : '' }}{{ formatPrice(stock.change) }}
                </td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-right" :class="parseFloat(stock.pChange) >= 0 ? 'text-green-600' : 'text-red-600'">
                  {{ stock.pChange > 0 ? '+' : '' }}{{ parseFloat(stock.pChange).toFixed(2) }}%
                </td>
                <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">{{ formatVolume(stock.totalTradedVolume) }}</td>
                <td v-if="isCustomView" class="px-4 py-2 whitespace-nowrap text-sm text-center">
                  <button
                    @click="removeStock(stock.symbol)"
                    class="text-red-500 hover:text-red-700"
                    title="Remove from view"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { useStockMarket } from '~/composables/stock-market/useStockMarket';
import { useStockViews } from '~/composables/stock-market/useStockViews';

const props = defineProps({
  viewId: {
    type: String,
    required: true
  },
  searchQuery: {
    type: String,
    default: ''
  }
});

console.log('StockViewTable: Initialized with viewId:', props.viewId);

const emit = defineEmits(['remove-stock']);

const { marketData, isLoading, error, formatPrice, formatVolume } = useStockMarket();
const { views, activeView, removeSymbolFromView } = useStockViews();

// For debugging
const showDebug = ref(true); // Set to true to show debug info

// Determine if this is a custom view or the default Nifty 50 view
const isCustomView = computed(() => props.viewId !== 'nifty50');

// Get the symbols for the current view
const symbols = computed(() => {
  if (props.viewId === 'nifty50') return null;

  const view = views.value.find(v => v.id === props.viewId);
  return view ? view.symbols : [];
});

// Create a local copy of market data to ensure reactivity
const localMarketData = ref(null);

// Update local market data when the original changes
watch(() => marketData.value, (newData) => {
  if (newData && newData.nifty50 && newData.nifty50.length > 0) {
    console.log('Updating local market data with', newData.nifty50.length, 'stocks');
    localMarketData.value = { ...newData };
  }
}, { immediate: true, deep: true });

// Get the stocks to display based on the view and search query
const displayedStocks = computed(() => {
  console.log('Computing displayedStocks with marketData:', {
    exists: !!localMarketData.value,
    hasNifty50: localMarketData.value && !!localMarketData.value.nifty50,
    nifty50Length: localMarketData.value && localMarketData.value.nifty50 ? localMarketData.value.nifty50.length : 0
  });

  // Check if market data is available
  if (!localMarketData.value || !localMarketData.value.nifty50 || localMarketData.value.nifty50.length === 0) {
    console.log('No market data available');
    return [];
  }

  let stocks = [];

  if (props.viewId === 'nifty50') {
    // For Nifty 50 view, use all stocks
    stocks = [...localMarketData.value.nifty50]; // Create a copy to ensure reactivity
    console.log('Nifty 50 stocks:', stocks.length);
  } else if (symbols.value) {
    // For custom views, filter by the symbols in the view
    stocks = localMarketData.value.nifty50.filter(stock =>
      symbols.value?.includes(stock.symbol)
    );
    console.log('Custom view stocks:', stocks.length, 'from symbols:', symbols.value);
  }

  // Apply search filter if provided
  if (props.searchQuery) {
    const query = props.searchQuery.toLowerCase();
    stocks = stocks.filter(stock =>
      stock.symbol.toLowerCase().includes(query) ||
      (stock.meta?.companyName && stock.meta.companyName.toLowerCase().includes(query))
    );
  }

  return stocks;
});

// Remove a stock from the view
const removeStock = async (symbol: string) => {
  if (isCustomView.value && props.viewId) {
    await removeSymbolFromView(props.viewId, symbol);
    emit('remove-stock', symbol);
  }
};

// Watch for changes in market data
watch(marketData, (newData) => {
  console.log('StockViewTable: Market data changed:', {
    hasData: !!newData,
    hasNifty50: newData && !!newData.nifty50,
    nifty50Length: newData && newData.nifty50 ? newData.nifty50.length : 0,
    displayedStocksLength: displayedStocks.value.length
  });

  // Force update of local market data
  if (newData && newData.nifty50 && newData.nifty50.length > 0) {
    console.log('StockViewTable: Forcing update of local market data');
    localMarketData.value = JSON.parse(JSON.stringify(newData));
  }
}, { deep: true, immediate: true });

// Watch for changes in displayed stocks
watch(displayedStocks, (newStocks) => {
  console.log('StockViewTable: Displayed stocks changed:', {
    length: newStocks.length,
    viewId: props.viewId,
    isCustomView: isCustomView.value
  });
});

// Watch for changes in viewId
watch(() => props.viewId, (newViewId) => {
  console.log('StockViewTable: View ID changed to:', newViewId);
}, { immediate: true });

// Force a re-evaluation of displayedStocks when the component mounts
onMounted(() => {
  console.log('StockViewTable: Component mounted, current market data:', {
    exists: !!marketData.value,
    hasNifty50: marketData.value && !!marketData.value.nifty50,
    nifty50Length: marketData.value && marketData.value.nifty50 ? marketData.value.nifty50.length : 0
  });
});
</script>
