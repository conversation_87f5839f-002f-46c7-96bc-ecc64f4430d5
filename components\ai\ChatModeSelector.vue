<template>
  <div class="chat-mode-selector bg-white border border-gray-200 rounded-lg p-4 mb-4">
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-lg font-semibold text-gray-800">AI Assistant Mode</h3>
      <div class="text-sm text-gray-500">Choose your interaction type</div>
    </div>
    
    <div class="grid md:grid-cols-2 gap-4">
      <!-- Normal Chat Mode -->
      <div class="mode-option">
        <label class="flex items-start p-4 border-2 rounded-lg cursor-pointer transition-all duration-200"
               :class="selectedMode === 'normal' ? 'border-blue-500 bg-blue-50' : 'border-gray-200 hover:border-gray-300'">
          <input type="radio" 
                 v-model="selectedMode" 
                 value="normal"
                 class="mt-1 mr-3 text-blue-600 focus:ring-blue-500">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-2xl mr-2">💬</span>
              <span class="font-semibold text-gray-800">Normal Chat</span>
            </div>
            <p class="text-sm text-gray-600 mb-2">
              General conversation, questions, explanations, and guidance
            </p>
            <div class="text-xs text-gray-500">
              <strong>Best for:</strong> Questions, learning, discussions, general help
            </div>
          </div>
        </label>
      </div>

      <!-- Document Generation Mode -->
      <div class="mode-option">
        <label class="flex items-start p-4 border-2 rounded-lg cursor-pointer transition-all duration-200"
               :class="selectedMode === 'document' ? 'border-green-500 bg-green-50' : 'border-gray-200 hover:border-gray-300'">
          <input type="radio" 
                 v-model="selectedMode" 
                 value="document"
                 class="mt-1 mr-3 text-green-600 focus:ring-green-500">
          <div class="flex-1">
            <div class="flex items-center mb-2">
              <span class="text-2xl mr-2">📄</span>
              <span class="font-semibold text-gray-800">Document Generation</span>
            </div>
            <p class="text-sm text-gray-600 mb-2">
              Create professional documents: quotations, invoices, reports, contracts
            </p>
            <div class="text-xs text-gray-500">
              <strong>Best for:</strong> Business documents, quotations, invoices, reports, templates
            </div>
          </div>
        </label>
      </div>
    </div>

    <!-- Mode Description -->
    <div class="mt-4 p-3 rounded-lg" 
         :class="selectedMode === 'normal' ? 'bg-blue-50 border border-blue-200' : 'bg-green-50 border border-green-200'">
      <div class="flex items-start">
        <span class="text-lg mr-2">
          {{ selectedMode === 'normal' ? '💡' : '🚀' }}
        </span>
        <div>
          <div class="font-medium text-gray-800 mb-1">
            {{ selectedMode === 'normal' ? 'Normal Chat Mode Active' : 'Document Generation Mode Active' }}
          </div>
          <div class="text-sm text-gray-600">
            <template v-if="selectedMode === 'normal'">
              Ask questions, get explanations, have conversations. Perfect for learning and general assistance.
            </template>
            <template v-else>
              Provide your requirements and get professional documents with download options. Supports Excel, PDF, and Word formats.
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- Examples -->
    <div class="mt-4">
      <div class="text-sm font-medium text-gray-700 mb-2">Example requests:</div>
      <div class="grid md:grid-cols-2 gap-3">
        <div v-if="selectedMode === 'normal'" class="text-xs text-gray-600 space-y-1">
          <div>• "Explain machine learning concepts"</div>
          <div>• "Help me understand blockchain"</div>
          <div>• "What's the best way to manage a team?"</div>
          <div>• "Guide me through project planning"</div>
        </div>
        <div v-else class="text-xs text-gray-600 space-y-1">
          <div>• "Create a quotation for 100 LED lights @ ₹1400 each"</div>
          <div>• "Generate an invoice for software services"</div>
          <div>• "Make a sales report for Q4 2024"</div>
          <div>• "Create a contract template for freelancers"</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: 'normal'
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'modeChanged']);

// Reactive data
const selectedMode = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
    emit('modeChanged', value);
  }
});

// Watch for mode changes
watch(selectedMode, (newMode) => {
  console.log('🔄 [CHAT MODE] Mode changed to:', newMode);
}, { immediate: true });
</script>

<style scoped>
.mode-option input[type="radio"]:checked + div {
  @apply text-gray-900;
}

.mode-option label:hover {
  @apply shadow-sm;
}

.chat-mode-selector {
  @apply shadow-sm;
}
</style>
