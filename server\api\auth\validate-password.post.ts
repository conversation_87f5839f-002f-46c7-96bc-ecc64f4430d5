// server/api/auth/validate-password.post.ts
import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody, createError } from 'h3';
import { passwordValidator } from '../../utils/passwordPolicy';
import { verifyToken } from '../../utils/auth';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { password, userInfo } = body;

    if (!password) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Password is required'
      });
    }

    let user = null;
    let passwordHistory: string[] = [];

    // If user is authenticated, get their info for better validation
    try {
      user = await verifyToken(event);
      passwordHistory = user.passwordHistory || [];
    } catch (error) {
      // Not authenticated - use provided userInfo if available
      user = null;
    }

    // Use authenticated user info or provided userInfo
    const validationUserInfo = user ? {
      username: user.username,
      email: user.email,
      fullname: user.fullname
    } : userInfo;

    // Validate password
    const result = passwordValidator.validatePassword(
      password,
      validationUserInfo,
      passwordHistory
    );

    // Get feedback
    const feedback = passwordValidator.getPasswordFeedback(result);

    return {
      isValid: result.isValid,
      errors: result.errors,
      strength: result.strength,
      score: result.score,
      feedback
    };

  } catch (error: any) {
    console.error('Password validation error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'Failed to validate password'
    });
  }
});
