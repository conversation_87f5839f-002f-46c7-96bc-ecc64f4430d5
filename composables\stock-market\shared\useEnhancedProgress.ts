import { ref, computed, readonly } from 'vue'

export interface ProgressStage {
  id: string
  name: string
  icon: string
  message: string
  weight: number
  estimatedDuration?: number
}

export interface ProviderTiming {
  min: number
  max: number
  avg: number
}

export const useEnhancedProgress = (analysisType: 'ai-analysis' | 'technical' | 'fundamental' | 'news') => {
  // State
  const currentStageIndex = ref(0)
  const stageProgress = ref(0)
  const startTime = ref<Date | null>(null)
  const elapsedTime = ref(0)
  const estimatedRemaining = ref<number | null>(null)
  const isActive = ref(false)
  
  // Timer
  let progressTimer: NodeJS.Timeout | null = null

  // Define stages for each analysis type
  const stageDefinitions: Record<string, ProgressStage[]> = {
    'ai-analysis': [
      { id: 'init', name: 'Initialize', icon: '🔄', message: 'Initializing comprehensive AI analysis...', weight: 5 },
      { id: 'prepare', name: 'Prepare', icon: '📋', message: 'Preparing stock data and parameters...', weight: 10 },
      { id: 'prompt', name: 'Generate', icon: '✍️', message: 'Creating comprehensive analysis prompt...', weight: 10 },
      { id: 'send', name: 'Send', icon: '📤', message: 'Sending request to AI provider...', weight: 15 },
      { id: 'process', name: 'Analyze', icon: '🤖', message: 'AI performing comprehensive analysis...', weight: 50 },
      { id: 'parse', name: 'Process', icon: '⚙️', message: 'Processing AI response and insights...', weight: 7 },
      { id: 'validate', name: 'Validate', icon: '✅', message: 'Validating analysis results...', weight: 2 },
      { id: 'finalize', name: 'Complete', icon: '🎯', message: 'Finalizing comprehensive analysis...', weight: 1 }
    ],
    'technical': [
      { id: 'init', name: 'Initialize', icon: '📊', message: 'Initializing technical analysis...', weight: 5 },
      { id: 'prepare', name: 'Prepare', icon: '📈', message: 'Preparing price data and indicators...', weight: 10 },
      { id: 'prompt', name: 'Generate', icon: '📝', message: 'Creating technical analysis prompt...', weight: 10 },
      { id: 'send', name: 'Send', icon: '📤', message: 'Sending to AI for technical analysis...', weight: 15 },
      { id: 'process', name: 'Analyze', icon: '🔍', message: 'AI analyzing charts and indicators...', weight: 50 },
      { id: 'parse', name: 'Process', icon: '⚙️', message: 'Extracting technical insights...', weight: 7 },
      { id: 'validate', name: 'Validate', icon: '✅', message: 'Validating technical analysis...', weight: 2 },
      { id: 'finalize', name: 'Complete', icon: '🎯', message: 'Preparing technical recommendations...', weight: 1 }
    ],
    'fundamental': [
      { id: 'init', name: 'Initialize', icon: '🏢', message: 'Initializing fundamental analysis...', weight: 5 },
      { id: 'prepare', name: 'Prepare', icon: '📊', message: 'Preparing financial data and metrics...', weight: 10 },
      { id: 'prompt', name: 'Generate', icon: '📝', message: 'Creating fundamental analysis prompt...', weight: 10 },
      { id: 'send', name: 'Send', icon: '📤', message: 'Sending to AI for fundamental analysis...', weight: 15 },
      { id: 'process', name: 'Analyze', icon: '💰', message: 'AI evaluating financial health...', weight: 50 },
      { id: 'parse', name: 'Process', icon: '⚙️', message: 'Processing fundamental insights...', weight: 7 },
      { id: 'validate', name: 'Validate', icon: '✅', message: 'Validating fundamental analysis...', weight: 2 },
      { id: 'finalize', name: 'Complete', icon: '🎯', message: 'Finalizing investment recommendations...', weight: 1 }
    ],
    'news': [
      { id: 'init', name: 'Initialize', icon: '📰', message: 'Initializing news analysis...', weight: 5 },
      { id: 'prepare', name: 'Prepare', icon: '🔍', message: 'Preparing news search parameters...', weight: 10 },
      { id: 'prompt', name: 'Generate', icon: '📝', message: 'Creating news analysis prompt...', weight: 10 },
      { id: 'send', name: 'Send', icon: '📤', message: 'Sending to AI for news research...', weight: 15 },
      { id: 'process', name: 'Research', icon: '🔎', message: 'AI researching and analyzing news...', weight: 50 },
      { id: 'parse', name: 'Process', icon: '⚙️', message: 'Organizing news insights...', weight: 7 },
      { id: 'validate', name: 'Validate', icon: '✅', message: 'Validating news analysis...', weight: 2 },
      { id: 'finalize', name: 'Complete', icon: '🎯', message: 'Preparing news summary...', weight: 1 }
    ]
  }

  // Provider timing estimates (in seconds)
  const providerTimings: Record<string, ProviderTiming> = {
    'openai': { min: 15, max: 45, avg: 25 },
    'google': { min: 8, max: 20, avg: 12 },
    'anthropic': { min: 20, max: 60, avg: 35 },
    'openrouter': { min: 10, max: 40, avg: 20 },
    'custom': { min: 15, max: 50, avg: 30 },
    // Special handling for reasoning models
    'deepseek-r1': { min: 60, max: 180, avg: 120 },
    'reasoning': { min: 90, max: 240, avg: 150 }
  }

  // Get stages for current analysis type
  const stages = computed(() => stageDefinitions[analysisType] || stageDefinitions['ai-analysis'])

  // Get current stage
  const currentStage = computed(() => stages.value[currentStageIndex.value] || null)

  // Calculate overall progress
  const overallProgress = computed(() => {
    if (!stages.value.length) return 0
    
    // Calculate completed stages weight
    let completedWeight = 0
    for (let i = 0; i < currentStageIndex.value; i++) {
      completedWeight += stages.value[i].weight
    }
    
    // Add current stage progress
    const currentStageWeight = currentStage.value?.weight || 0
    const currentStageContribution = (stageProgress.value / 100) * currentStageWeight
    
    // Calculate total weight
    const totalWeight = stages.value.reduce((sum, stage) => sum + stage.weight, 0)
    
    return Math.min(100, ((completedWeight + currentStageContribution) / totalWeight) * 100)
  })

  // Get current message
  const currentMessage = computed(() => {
    return currentStage.value?.message || 'Processing...'
  })

  // Methods
  const start = () => {
    isActive.value = true
    startTime.value = new Date()
    currentStageIndex.value = 0
    stageProgress.value = 0
    elapsedTime.value = 0
    
    // Start elapsed time timer
    progressTimer = setInterval(() => {
      if (startTime.value) {
        elapsedTime.value = Math.floor((Date.now() - startTime.value.getTime()) / 1000)
      }
    }, 1000)
  }

  const nextStage = () => {
    if (currentStageIndex.value < stages.value.length - 1) {
      currentStageIndex.value++
      stageProgress.value = 0
    }
  }

  const setStageProgress = (progress: number) => {
    stageProgress.value = Math.max(0, Math.min(100, progress))
  }

  const setStage = (stageId: string, progress: number = 0) => {
    const stageIndex = stages.value.findIndex(s => s.id === stageId)
    if (stageIndex !== -1) {
      currentStageIndex.value = stageIndex
      stageProgress.value = progress
    }
  }

  const updateEstimate = (provider: string, model: string) => {
    // Determine if it's a reasoning model
    const isReasoning = model.toLowerCase().includes('r1') || 
                       model.toLowerCase().includes('reasoning') ||
                       model.toLowerCase().includes('think')
    
    // Get timing estimate
    let timing: ProviderTiming
    if (isReasoning) {
      timing = providerTimings['reasoning']
    } else {
      timing = providerTimings[provider] || providerTimings['custom']
    }
    
    // Calculate estimated remaining time based on current progress
    const progressRatio = overallProgress.value / 100
    const estimatedTotal = timing.avg
    const remaining = Math.max(0, estimatedTotal - elapsedTime.value)
    
    estimatedRemaining.value = Math.round(remaining)
  }

  const complete = () => {
    currentStageIndex.value = stages.value.length - 1
    stageProgress.value = 100
    isActive.value = false
    
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
  }

  const reset = () => {
    isActive.value = false
    currentStageIndex.value = 0
    stageProgress.value = 0
    elapsedTime.value = 0
    estimatedRemaining.value = null
    startTime.value = null
    
    if (progressTimer) {
      clearInterval(progressTimer)
      progressTimer = null
    }
  }

  // Auto-update estimates every 5 seconds during processing
  const autoUpdateEstimates = (provider: string, model: string) => {
    const updateInterval = setInterval(() => {
      if (isActive.value) {
        updateEstimate(provider, model)
      } else {
        clearInterval(updateInterval)
      }
    }, 5000)
  }

  return {
    // State (readonly)
    stages: readonly(stages),
    currentStage: readonly(currentStage),
    currentStageIndex: readonly(currentStageIndex),
    stageProgress: readonly(stageProgress),
    overallProgress: readonly(overallProgress),
    currentMessage: readonly(currentMessage),
    elapsedTime: readonly(elapsedTime),
    estimatedRemaining: readonly(estimatedRemaining),
    isActive: readonly(isActive),
    
    // Methods
    start,
    nextStage,
    setStageProgress,
    setStage,
    updateEstimate,
    autoUpdateEstimates,
    complete,
    reset
  }
}
