import { initFirebase } from '../utils/firebase';

/**
 * Firebase middleware
 *
 * This middleware initializes the Firebase Admin SDK for each request
 * to ensure that Firebase is available for API handlers.
 */
export default defineEventHandler((event) => {
  try {
    // Initialize Firebase Admin SDK
    initFirebase();
  } catch (error) {
    console.error('Error in Firebase middleware:', error);
    throw createError({
      statusCode: 500,
      message: 'Failed to initialize Firebase'
    });
  }
});
