<template>
  <div class="container mx-auto px-4 py-8 mt-0">
    <h1 class="text-2xl font-bold text-gray-900 mb-8">Financial Reports & Analytics</h1>

    <div class="mb-8">
      <ReportFilters
        :is-loading="isLoading"
        @generate="generateReport"
        @reset="resetReport"
      />
    </div>

    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Generating report...</span>
    </div>

    <ReportResults
      v-else
      :report-data="reportData"
      :is-loading="isLoading"
    />

    <!-- Export Options -->
    <div v-if="reportData" class="mt-8 flex justify-end">
      <div class="flex space-x-4">
        <!-- PDF Export Button -->
        <a
          :href="`/api/expenses/export/pdf?type=${reportData?.reportType || 'monthly'}&timePeriod=${reportData?.timePeriod || ''}`"
          target="_blank"
          class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 inline-block"
          :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
          :tabindex="isLoading ? -1 : 0"
          @click="isLoading ? $event.preventDefault() : null"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Export to PDF
          </span>
        </a>

        <!-- Excel Export Button -->
        <a
          :href="`/api/expenses/export/excel?type=${reportData?.reportType || 'monthly'}&timePeriod=${reportData?.timePeriod || ''}`"
          target="_blank"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 inline-block"
          :class="{ 'opacity-50 cursor-not-allowed': isLoading }"
          :tabindex="isLoading ? -1 : 0"
          @click="isLoading ? $event.preventDefault() : null"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export to Excel
          </span>
        </a>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true
});

import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from '#app';
import { useReports } from '~/composables/expenses/useReports';
import ReportFilters from '~/components/expenses/ReportFilters.vue';
import ReportResults from '~/components/expenses/ReportResults.vue';
import { usePageTitle } from '~/composables/ui/usePageTitle';

// Set page title
usePageTitle('Financial Reports', 'Generate and analyze financial reports and analytics');

const route = useRoute();
const router = useRouter();

// State
const isLoading = ref(false);
const reportData = ref(null);

// Get composables
const { generateReport: generateReportAction } = useReports();

// Methods
const generateReport = async (filters) => {
  try {
    isLoading.value = true;
    const report = await generateReportAction(filters.type, filters);
    reportData.value = {
      ...report,
      reportType: filters.type,
      timePeriod: filters.timePeriod
    };

    // Update URL with report type and time period
    const queryParams = {
      ...route.query,
      type: filters.type
    };

    // Add time period to URL if present
    if (filters.timePeriod) {
      queryParams.timePeriod = filters.timePeriod;
    }

    router.replace({
      query: queryParams
    });
  } catch (error) {
    console.error('Error generating report:', error);
  } finally {
    isLoading.value = false;
  }
};

const resetReport = () => {
  reportData.value = null;

  // Remove report type and time period from URL
  const query = { ...route.query };
  delete query.type;
  delete query.timePeriod;
  router.replace({ query });
};

// No URL generation methods needed - using direct URLs in the template

// Initialize
onMounted(() => {
  // Check if there's a report type in the route query
  const reportType = route.query.type;
  if (reportType) {
    generateReport({ type: reportType });
  }
});
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
