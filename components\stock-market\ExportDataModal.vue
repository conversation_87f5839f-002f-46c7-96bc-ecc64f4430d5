<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <!-- Header -->
        <div class="bg-green-600 px-4 py-3 sm:px-6 flex justify-between items-center">
          <h3 class="text-lg leading-6 font-medium text-white">
            Export Data
          </h3>
          <button @click="$emit('close')" class="text-white hover:text-gray-200 focus:outline-none">
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Modal content -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <div class="mt-2">
                <!-- Data type selection -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Data to Export
                  </label>
                  <div class="mt-1">
                    <select
                      v-model="exportOptions.dataType"
                      class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-green-500 focus:border-green-500 sm:text-sm rounded-md"
                    >
                      <option value="investments">Investments (Equity)</option>
                      <option value="mutualFunds">Mutual Funds</option>
                      <option value="portfolio">Complete Portfolio</option>
                    </select>
                  </div>
                </div>

                <!-- Format selection -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Export Format
                  </label>
                  <div class="mt-1 grid grid-cols-3 gap-3">
                    <div
                      @click="exportOptions.format = 'excel'"
                      class="flex flex-col items-center justify-center p-3 border rounded-md cursor-pointer"
                      :class="exportOptions.format === 'excel' ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:bg-gray-50'"
                    >
                      <svg class="h-8 w-8 text-green-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                        <path fill="#4CAF50" d="M41,10H25v28h16c0.553,0,1-0.447,1-1V11C42,10.447,41.553,10,41,10z"/>
                        <path fill="#FFF" d="M32 15H39V18H32zM32 25H39V28H32zM32 30H39V33H32zM32 20H39V23H32zM25 15H30V18H25zM25 25H30V28H25zM25 30H30V33H25zM25 20H30V23H25z"/>
                        <path fill="#2E7D32" d="M27 42L6 38 6 10 27 6z"/>
                        <path fill="#FFF" d="M19.129,31l-2.411-4.561c-0.092-0.171-0.186-0.483-0.284-0.938h-0.037c-0.046,0.215-0.154,0.541-0.324,0.979L13.652,31H9.895l4.462-7.001L10.274,17h3.837l2.001,4.196c0.156,0.331,0.296,0.725,0.42,1.179h0.04c0.078-0.271,0.224-0.68,0.439-1.22L19.237,17h3.515l-4.199,6.939l4.316,7.059h-3.74V31z"/>
                      </svg>
                      <span class="mt-2 text-sm font-medium text-gray-900">Excel</span>
                    </div>
                    <div
                      @click="exportOptions.format = 'csv'"
                      class="flex flex-col items-center justify-center p-3 border rounded-md cursor-pointer"
                      :class="exportOptions.format === 'csv' ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:bg-gray-50'"
                    >
                      <svg class="h-8 w-8 text-blue-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                        <path fill="#90CAF9" d="M40 45L8 45 8 3 30 3 40 13z"/>
                        <path fill="#E1F5FE" d="M38.5 14L29 14 29 4.5z"/>
                        <path fill="#1976D2" d="M16 21H33V23H16zM16 25H29V27H16zM16 29H33V31H16zM16 33H29V35H16z"/>
                      </svg>
                      <span class="mt-2 text-sm font-medium text-gray-900">CSV</span>
                    </div>
                    <div
                      @click="exportOptions.format = 'pdf'"
                      class="flex flex-col items-center justify-center p-3 border rounded-md cursor-pointer"
                      :class="exportOptions.format === 'pdf' ? 'border-green-500 bg-green-50' : 'border-gray-300 hover:bg-gray-50'"
                    >
                      <svg class="h-8 w-8 text-red-600" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48">
                        <path fill="#FF5722" d="M40 45L8 45 8 3 30 3 40 13z"/>
                        <path fill="#FBE9E7" d="M38.5 14L29 14 29 4.5z"/>
                        <path fill="#FFEBEE" d="M16 21H33V23H16zM16 25H29V27H16zM16 29H33V31H16zM16 33H29V35H16z"/>
                      </svg>
                      <span class="mt-2 text-sm font-medium text-gray-900">PDF</span>
                    </div>
                  </div>
                </div>

                <!-- Additional options -->
                <div class="mb-4">
                  <label class="block text-sm font-medium text-gray-700 mb-2">
                    Options
                  </label>
                  <div class="space-y-2">
                    <div class="flex items-center">
                      <input
                        id="include-history"
                        v-model="exportOptions.includeHistory"
                        type="checkbox"
                        class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                      />
                      <label for="include-history" class="ml-2 block text-sm text-gray-700">
                        Include transaction history
                      </label>
                    </div>
                    <div class="flex items-center">
                      <input
                        id="include-charts"
                        v-model="exportOptions.includeCharts"
                        type="checkbox"
                        class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded"
                        :disabled="exportOptions.format !== 'pdf'"
                      />
                      <label for="include-charts" class="ml-2 block text-sm text-gray-700" :class="{ 'opacity-50': exportOptions.format !== 'pdf' }">
                        Include charts (PDF only)
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button
            @click="exportData"
            :disabled="isExporting"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <svg v-if="isExporting" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            {{ isExporting ? 'Exporting...' : 'Export' }}
          </button>
          <button
            @click="$emit('close')"
            type="button"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'export-complete']);

const isExporting = ref(false);
const exportOptions = ref({
  dataType: 'investments', // investments, mutualFunds, portfolio
  format: 'excel', // excel, csv, pdf
  includeHistory: false,
  includeCharts: false
});

const exportData = async () => {
  if (isExporting.value) return;
  
  try {
    isExporting.value = true;
    
    const api = useApiWithAuth();
    const params = {
      type: exportOptions.value.dataType,
      format: exportOptions.value.format,
      includeHistory: exportOptions.value.includeHistory,
      includeCharts: exportOptions.value.includeCharts
    };
    
    // Use fetchWithAuth for binary data download
    const response = await api.fetchWithAuth('/api/stock-market/export', {
      method: 'GET',
      params,
      responseType: 'blob'
    });
    
    // Create a blob from the response
    const contentType = getContentType(exportOptions.value.format);
    const blob = new Blob([response], { type: contentType });
    
    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);
    
    // Create a temporary link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', getFileName(exportOptions.value.dataType, exportOptions.value.format));
    document.body.appendChild(link);
    link.click();
    
    // Clean up
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    
    emit('export-complete', { success: true });
    
  } catch (error) {
    console.error('Error exporting data:', error);
    alert('Failed to export data. Please try again.');
    emit('export-complete', { success: false, error });
  } finally {
    isExporting.value = false;
  }
};

const getContentType = (format) => {
  switch (format) {
    case 'excel':
      return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
    case 'csv':
      return 'text/csv';
    case 'pdf':
      return 'application/pdf';
    default:
      return 'application/octet-stream';
  }
};

const getFileName = (dataType, format) => {
  const date = new Date().toISOString().split('T')[0];
  let name = '';
  
  switch (dataType) {
    case 'investments':
      name = 'Investment_Data';
      break;
    case 'mutualFunds':
      name = 'Mutual_Fund_Data';
      break;
    case 'portfolio':
      name = 'Portfolio_Data';
      break;
    default:
      name = 'Stock_Market_Data';
  }
  
  let extension = '';
  switch (format) {
    case 'excel':
      extension = 'xlsx';
      break;
    case 'csv':
      extension = 'csv';
      break;
    case 'pdf':
      extension = 'pdf';
      break;
    default:
      extension = 'txt';
  }
  
  return `${name}_${date}.${extension}`;
};
</script>
