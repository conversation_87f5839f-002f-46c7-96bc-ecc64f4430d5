<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="modal-panel inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
        <!-- Gradient Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-3 rounded-t-lg shadow-md">
          <h3 class="text-lg leading-6 font-medium text-white">{{ editMode ? 'Edit Stock Item' : 'Create New Stock Item' }}</h3>
        </div>

        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">

              <form @submit.prevent="submitForm">
                <div class="grid grid-cols-2 gap-4">
                  <!-- Item Name -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Item Name*</label>
                    <input ref="firstInput" v-model="form.item" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @keydown.esc="closeWithFocus" @keydown="handleKeydown" />
                  </div>

                  <!-- Part Number -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Part Number</label>
                    <input v-model="form.pno" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      @keydown="handleKeydown" />
                    <small class="text-gray-500">Optional, must be unique if provided</small>
                  </div>

                  <!-- Batch Number -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Batch Number</label>
                    <input v-model="form.batch" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      @keydown="handleKeydown" />
                    <small class="text-gray-500">Optional, must be unique if provided</small>
                  </div>

                  <!-- OEM -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">OEM</label>
                    <input v-model="form.oem" type="text" list="oemList"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      @keydown="handleKeydown" />
                    <datalist id="oemList">
                      <option v-for="oem in uniqueOems" :key="oem" :value="oem"></option>
                    </datalist>
                    <small class="text-gray-500">Optional</small>
                  </div>

                  <!-- HSN Code -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">HSN Code*</label>
                    <input v-model="form.hsn" type="text"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @keydown="handleKeydown" />
                  </div>

                  <!-- Quantity -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Quantity*</label>
                    <input v-model.number="form.qty" type="number" min="0" step="0.01"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @input="calculateTotal" @keydown="handleKeydown" />
                  </div>

                  <!-- UOM -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Unit of Measurement*</label>
                    <input v-model="form.uom" type="text" list="uomList"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @keydown="handleKeydown" />
                    <datalist id="uomList">
                      <option v-for="uom in uniqueUoms" :key="uom" :value="uom"></option>
                    </datalist>
                  </div>

                  <!-- Rate -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Rate*</label>
                    <input v-model.number="form.rate" type="number" min="0" step="0.01"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @input="calculateTotal" @keydown="handleKeydown" />
                  </div>

                  <!-- GST Rate -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">GST Rate (%)*</label>
                    <input v-model.number="form.grate" type="number" min="0" max="100" step="0.01"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      required @input="calculateTaxes" @keydown="handleKeydown" />
                  </div>

                  <!-- MRP/List Price -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">MRP/List Price</label>
                    <input v-model.number="form.mrp" type="number" min="0" step="0.01"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      @keydown="handleKeydown" />
                    <small class="text-gray-500">Optional</small>
                  </div>

                  <!-- Expiry Date -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                    <input v-model="form.expiryDate" type="date"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      @keydown="handleKeydown" />
                    <small class="text-gray-500">Optional</small>
                  </div>

                  <!-- Total -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Total*</label>
                    <input v-model.number="form.total" type="number" step="0.01"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 bg-gray-100"
                      readonly @keydown="handleKeydown" />
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                  <button type="submit"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm">
                    {{ editMode ? 'Update Stock Item' : 'Add Stock Item' }}
                  </button>
                  <button type="button" @click="close"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Gradient Footer -->
        <div class="bg-gradient-to-r from-purple-600 to-blue-500 px-4 py-3 rounded-b-lg shadow-md"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, watch, nextTick, onUnmounted } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  inventoryData: {
    type: Object,
    default: () => ({
      stocks: []
    })
  },
  editItem: {
    type: Object,
    default: null
  }
});

// Determine if we're in edit mode
const editMode = computed(() => !!props.editItem);

// Compute unique OEM values from inventory stocks
const uniqueOems = computed(() => {
  if (!props.inventoryData?.stocks) return [];
  const oems = props.inventoryData.stocks
    .filter(stock => stock.oem)
    .map(stock => stock.oem);
  return [...new Set(oems)];
});

// Compute unique UOM values from inventory stocks
const uniqueUoms = computed(() => {
  if (!props.inventoryData?.stocks) return [];
  const uoms = props.inventoryData.stocks
    .filter(stock => stock.uom)
    .map(stock => stock.uom);
  return [...new Set(uoms)];
});

const emit = defineEmits(['update:show', 'submit-stock-item']);

const firstInput = ref(null);

const form = ref({
  item: '',
  pno: null, // ✅ Changed from '' to null
  batch: null, // ✅ Changed from '' to null
  oem: '',
  hsn: '',
  qty: 0,
  uom: '',
  rate: 0,
  grate: 0,
  total: 0,
  mrp: null, // Optional - List Price/MRP
  expiryDate: null, // Optional - Expiry Date
  user: '', // Will be set by parent component
  firm: ''  // Will be set by parent component
});

const calculateTaxes = () => {
  // Just recalculate total when GST rate changes
  if (form.value.grate) {
    calculateTotal();
  }
};

const calculateTotal = () => {
  if (form.value.qty && form.value.rate) {
    // Calculate base amount
    let baseAmount = form.value.qty * form.value.rate;

    // Set the total (rounded to 2 decimal places)
    form.value.total = parseFloat(baseAmount.toFixed(2));
  } else {
    form.value.total = 0;
  }
};

const close = () => {
  // Emit event to close the modal
  emit('update:show', false);

  // Return focus to the appropriate element after modal closes
  nextTick(() => {
    // Try to focus the Add New Item button
    const addNewItemButton = document.querySelector('#addNewItemButton');
    if (addNewItemButton) {
      addNewItemButton.focus();
    }
  });
};

// Close modal and return focus to the item input in the stock items table
const closeWithFocus = () => {
  // Store the active element before closing the modal
  const activeElement = document.activeElement;

  // Close the modal
  close();

  // Return focus to the appropriate element in the table
  nextTick(() => {
    // Find the first empty item input in the stock items table
    const stockItemInputs = document.querySelectorAll('#stockItemsTable tbody tr input[list="stockItemList"]');
    let focusInput = null;

    // Try to find an empty input first
    for (const input of stockItemInputs) {
      if (!input.value) {
        focusInput = input;
        break;
      }
    }

    // If no empty input found, focus on the last one
    if (!focusInput && stockItemInputs.length > 0) {
      focusInput = stockItemInputs[stockItemInputs.length - 1];
    }

    // If we found an input to focus, focus it
    if (focusInput) {
      focusInput.focus();

      // If the input is part of a table row, update the table focus tracking
      const row = focusInput.closest('tr');
      if (row) {
        const rowIndex = Array.from(row.parentNode.children).indexOf(row);
        if (typeof window.trackTableFocus === 'function') {
          window.trackTableFocus('stockItems', rowIndex, 0);
        }
      }
    } else {
      // If no suitable input found, try to focus the Add New Item button
      const addNewItemButton = document.querySelector('#addNewItemButton');
      if (addNewItemButton) {
        addNewItemButton.focus();
      }
    }
  });
};

const resetForm = () => {
  form.value = {
    item: '',
    pno: null, // ✅ Changed from '' to null
    batch: null, // ✅ Changed from '' to null
    oem: '',
    hsn: '',
    qty: 0,
    uom: '',
    rate: 0,
    grate: 0,
    total: 0,
    mrp: null, // Optional - List Price/MRP
    expiryDate: null, // Optional - Expiry Date
    user: '',
    firm: ''
  };
};

// Watch for modal visibility changes to focus the first input and set up global event listeners
watch(() => props.show, (newVal) => {
  if (newVal) {
    // Focus the first input when modal opens
    nextTick(() => {
      if (firstInput.value) {
        firstInput.value.focus();
      }

      // Add global event listener for Enter key when modal is open
      if (process.client) {
        window.addEventListener('keydown', handleGlobalKeydown);
      }
    });
  } else {
    // Remove global event listener when modal is closed
    if (process.client) {
      window.removeEventListener('keydown', handleGlobalKeydown);
    }
  }
});

// Global keydown handler for the entire modal
const handleGlobalKeydown = (event) => {
  // If Enter is pressed with a modifier key (except Shift), let the browser handle it
  if (event.key === 'Enter' && (event.ctrlKey || event.altKey || event.metaKey)) {
    return;
  }

  // If Enter is pressed and we're not in a textarea, submit the form
  if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
    // Only handle if the active element is inside our modal
    const modalPanel = document.querySelector('.modal-panel');
    if (modalPanel && modalPanel.contains(document.activeElement)) {
      event.preventDefault();
      // Validate form before submission
      if (validateForm()) {
        submitForm();
      }
    }
  }
};

// Clean up event listeners when component is unmounted
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('keydown', handleGlobalKeydown);
  }
});

// Watch for changes to editItem and update form accordingly
watch(() => props.editItem, (newVal) => {
  if (newVal) {
    // Populate form with editItem data
    form.value = { ...newVal };
    // Calculate total in case it's not set
    calculateTotal();
  } else {
    // Reset form when not in edit mode
    resetForm();
  }
}, { immediate: true });


// Handle keyboard events
const handleKeydown = (event) => {
  // If Enter is pressed with a modifier key (except Shift), let the browser handle it
  if (event.key === 'Enter' && (event.ctrlKey || event.altKey || event.metaKey)) {
    return;
  }

  // If Enter is pressed in any input except textarea, submit the form
  if (event.key === 'Enter' && event.target.tagName !== 'TEXTAREA') {
    event.preventDefault();
    // Validate form before submission
    if (validateForm()) {
      submitForm();
    }
  }
};

// Validate form before submission
const validateForm = () => {
  // Check required fields
  if (!form.value.item || !form.value.item.trim()) {
    return false;
  }

  if (!form.value.hsn || !form.value.hsn.trim()) {
    return false;
  }

  if (!form.value.qty || form.value.qty <= 0) {
    return false;
  }

  if (!form.value.uom || !form.value.uom.trim()) {
    return false;
  }

  if (!form.value.rate || form.value.rate <= 0) {
    return false;
  }

  return true;
};

const submitForm = () => {
  // Validate form before submission
  if (!validateForm()) {
    return;
  }

  // ✅ ADDED: Sanitize form data before submission
  const sanitizedForm = {
    ...form.value,
    pno: (form.value.pno && form.value.pno.trim() !== '') ? form.value.pno : null,
    batch: (form.value.batch && form.value.batch.trim() !== '') ? form.value.batch : null,
    _isEdit: editMode.value
  };

  // Add user and firm from parent component if needed
  emit('submit-stock-item', sanitizedForm);
  close();
  // Reset form
  resetForm();
};
</script>