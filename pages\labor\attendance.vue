<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Attendance System</h1>
            <p class="mt-1 text-sm text-gray-500">
              Track daily attendance for your labor force.
            </p>
          </div>
          <div class="flex space-x-3">
            <button @click="saveAttendance" class="btn btn-primary" :disabled="!isDirty">
              <Icon name="heroicons:check-circle" class="w-5 h-5 mr-2" />
              Save Attendance
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Filters -->
      <div class="bg-white rounded-lg shadow p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div>
            <label for="group" class="block text-sm font-medium text-gray-700 mb-2">
              Filter by Group
            </label>
            <select
              id="group"
              v-model="selectedGroup"
              @change="fetchData"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">All Groups</option>
              <option v-for="group in groups" :key="group.id" :value="group.id">
                {{ group.name }}
              </option>
            </select>
          </div>
          <div>
            <label for="fromDate" class="block text-sm font-medium text-gray-700 mb-2">
              From Date
            </label>
            <input
              type="date"
              id="fromDate"
              v-model="fromDate"
              @change="fetchData"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          <div>
            <label for="toDate" class="block text-sm font-medium text-gray-700 mb-2">
              To Date
            </label>
            <input
              type="date"
              id="toDate"
              v-model="toDate"
              @change="fetchData"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        </div>
      </div>

      <!-- Attendance Summary -->
      <div class="mb-8">
        <AttendanceSummary :attendance-data="profiles" />
      </div>

      <!-- Attendance Table -->
      <AttendanceTable
        :firm-id="firmId"
        :profiles="profiles"
        :groups="groups"
        :from-date="fromDate"
        :to-date="toDate"
        @data-changed="isDirty = $event"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import AttendanceTable from '~/components/labor/AttendanceTable.vue'
import AttendanceSummary from '~/components/labor/AttendanceSummary.vue'

definePageMeta({
  title: 'Attendance System',
  layout: 'default'
})

// Mock data for now - replace with actual user/firm data
const firmId = ref('507f1f77bcf86cd799439011') // Replace with actual firm ID

const groups = ref([])
const profiles = ref([])
const selectedGroup = ref('')
const fromDate = ref(new Date().toISOString().split('T')[0])
const toDate = ref(new Date().toISOString().split('T')[0])
const isDirty = ref(false)

const loadGroups = async () => {
  try {
    const response = await $fetch('/api/labor/groups', {
      query: { firmId: firmId.value }
    })
    if (response.success) {
      groups.value = response.data
    }
  } catch (error) {
    console.error('Error loading groups:', error)
  }
}

const fetchData = async () => {
  try {
    const response = await $fetch('/api/labor/attendance', {
      query: {
        firmId: firmId.value,
        fromDate: fromDate.value,
        toDate: toDate.value,
        groupId: selectedGroup.value,
      },
    })
    if (response.success) {
      // This is a bit of a hack to get the profiles data to the table
      // In a real app, we'd probably have a more robust state management solution
      profiles.value = response.data.map(d => ({ ...d.profile, attendance: d.attendance }))
    }
  } catch (error) {
    console.error('Error fetching attendance data:', error)
  }
}

const saveAttendance = async () => {
  try {
    const response = await $fetch('/api/labor/attendance', {
      method: 'POST',
      body: {
        attendanceData: profiles.value.map(p => ({
            profile: p,
            attendance: p.attendance,
        })),
        firmId: firmId.value,
        fromDate: fromDate.value,
        toDate: toDate.value,
      },
    })
    if (response.success) {
        alert('Attendance saved successfully!')
        resetPage()
    }
  } catch (error) {
    console.error('Error saving attendance:', error)
    alert('Failed to save attendance. Please try again.')
  }
}

const resetPage = () => {
    isDirty.value = false
    profiles.value = []
    fromDate.value = new Date().toISOString().split('T')[0]
    toDate.value = new Date().toISOString().split('T')[0]
    selectedGroup.value = ''
}

onMounted(() => {
  loadGroups()
  fetchData()
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>