{"name": "nuxt-app", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "update-roles": "node scripts/update-user-roles-esm.js"}, "dependencies": {"@adobe/pdfservices-node-sdk": "^4.1.0", "@google/generative-ai": "^0.24.0", "@heroicons/vue": "^2.2.0", "@supabase/supabase-js": "^2.53.0", "@types/dompurify": "^3.0.5", "@types/jsdom": "^21.1.7", "@types/jsonwebtoken": "^9.0.9", "@types/pdfkit": "^0.13.9", "@types/uuid": "^9.0.8", "@upstash/redis": "^1.34.8", "archiver": "^7.0.1", "bcryptjs": "^3.0.2", "chartjs-adapter-luxon": "^1.3.1", "chartjs-chart-financial": "^0.2.1", "chartjs-plugin-zoom": "^2.2.0", "docx": "^9.4.1", "dompurify": "^3.2.5", "dotenv": "^16.5.0", "dropbox": "^10.34.0", "exceljs": "^4.4.0", "fast-xml-parser": "^5.2.1", "firebase": "^11.6.0", "firebase-admin": "^13.2.0", "form-data": "^4.0.4", "google-auth-library": "^9.15.1", "googleapis": "^148.0.0", "jsdom": "^26.1.0", "jsonwebtoken": "^9.0.2", "lodash": "^4.17.21", "luxon": "^3.6.1", "mime-types": "^3.0.1", "mongoose": "^8.12.1", "nodemailer": "^6.10.0", "nuxt": "^3.17.2", "nuxt-icon": "^1.0.0-beta.7", "pdfkit": "^0.16.0", "pouchdb": "^9.0.0", "pouchdb-browser": "^9.0.0", "uuid": "^9.0.1", "vue": "^3.5.13", "vue-chartjs": "^5.3.2", "vue-router": "^4.5.1", "yahoo-finance2": "^2.13.3"}, "devDependencies": {"@nuxt/icon": "^1.12.0", "@nuxtjs/tailwindcss": "^6.14.0", "@types/chart.js": "^2.9.41", "@types/pouchdb": "^6.4.2", "chart.js": "^4.4.9"}}