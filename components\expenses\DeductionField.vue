<template>
  <div class="border border-gray-200 rounded-md p-4 bg-gray-50">
    <div class="flex justify-between items-center mb-3">
      <h4 class="text-sm font-medium text-gray-700">
        Deduction #{{ index + 1 }}
      </h4>
      <button
        type="button"
        @click="$emit('remove')"
        class="text-red-600 hover:text-red-800 transition-colors duration-200"
        :disabled="disabled"
      >
        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
      </button>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Deduction Name -->
      <div>
        <label :for="`deduction-name-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
          Deduction Name *
        </label>
        <input
          type="text"
          :id="`deduction-name-${index}`"
          :value="deduction.name"
          @input="updateDeduction('name', $event.target.value)"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder="e.g., TDS, Service Charge"
          :disabled="disabled"
          required
        />
        <div v-if="errors.name" class="mt-1 text-sm text-red-600">
          {{ errors.name }}
        </div>
      </div>

      <!-- Deduction Amount -->
      <div>
        <label :for="`deduction-amount-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
          Amount *
        </label>
        <input
          type="number"
          :id="`deduction-amount-${index}`"
          :value="deduction.amount"
          @input="updateDeduction('amount', $event.target.value)"
          step="0.01"
          min="0"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder="0.00"
          :disabled="disabled"
          required
        />
        <div v-if="errors.amount" class="mt-1 text-sm text-red-600">
          {{ errors.amount }}
        </div>
      </div>

      <!-- Deduction Description -->
      <div class="md:col-span-2">
        <label :for="`deduction-description-${index}`" class="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <textarea
          :id="`deduction-description-${index}`"
          :value="deduction.description"
          @input="updateDeduction('description', $event.target.value)"
          rows="2"
          class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          placeholder="Optional description for this deduction"
          :disabled="disabled"
        ></textarea>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeductionField',
  
  props: {
    deduction: {
      type: Object,
      required: true,
      default: () => ({
        id: '',
        name: '',
        amount: '',
        description: ''
      })
    },
    index: {
      type: Number,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    errors: {
      type: Object,
      default: () => ({})
    }
  },

  emits: ['update', 'remove'],

  methods: {
    updateDeduction(field, value) {
      if (this.disabled) return;
      
      const updatedDeduction = {
        ...this.deduction,
        [field]: value
      };
      
      this.$emit('update', updatedDeduction);
    }
  }
};
</script>

<style scoped>
/* Component-specific styles */
.border-gray-200 {
  transition: border-color 0.2s ease;
}

.border-gray-200:hover {
  border-color: #d1d5db;
}

input:focus,
textarea:focus {
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style>
