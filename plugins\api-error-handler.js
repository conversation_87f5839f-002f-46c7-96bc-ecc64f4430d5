/**
 * Plugin for handling API errors consistently across the application
 * This ensures proper error handling and token-related issues are managed properly
 */
import { navigateTo } from '#app';

export default defineNuxtPlugin((nuxtApp) => {
  // Only run this error handler on the client side
  if (process.server) {
    return;
  }
  
  // Global error handler for API requests
  nuxtApp.hook('app:error', (error) => {
    console.error('Global error caught:', error);
    
    // Check if the error is an authentication error
    if (isAuthError(error)) {
      handleAuthError();
    }
  });
  
  // Check if an error is authentication-related
  function isAuthError(error) {
    // Handle API errors (typically have status codes)
    if (error.statusCode === 401 || error.statusCode === 403) {
      return true;
    }
    
    // Handle general errors with auth-related messages
    if (error.message && (
      error.message.includes('Unauthorized') || 
      error.message.includes('authentication') || 
      error.message.includes('token')
    )) {
      return true;
    }
    
    return false;
  }
  
  // Handle authentication errors
  function handleAuthError() {
    try {
      const accessCookie = useCookie('token');
      const refreshCookie = useCookie('refreshToken');
      
      // Clear auth cookies
      accessCookie.value = null;
      refreshCookie.value = null;
      
      // Redirect to login page using setTimeout to avoid navigation during render
      setTimeout(() => {
        navigateTo('/login');
      }, 0);
    } catch (error) {
      console.error('Error in handleAuthError:', error);
    }
  }
}); 