<template>
  <div class="bg-white rounded-lg shadow p-6">
    <form @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Date Field -->
        <div>
          <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
          <input
            type="date"
            id="date"
            v-model="formData.date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Amount Field -->
        <div>
          <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Amount *</label>
          <input
            type="number"
            id="amount"
            v-model="formData.amount"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- From Account Section -->
        <div class="md:col-span-2">
          <h3 class="text-lg font-medium text-gray-900 mb-2">From Account</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- From Account Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Account Type *</label>
              <div class="flex space-x-4">
                <div class="flex items-center">
                  <input
                    type="radio"
                    id="fromCash"
                    v-model="formData.fromMode"
                    value="cash"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                    required
                  />
                  <label for="fromCash" class="ml-2 text-sm text-gray-700">Cash</label>
                </div>
                <div class="flex items-center">
                  <input
                    type="radio"
                    id="fromBank"
                    v-model="formData.fromMode"
                    value="bank"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                    required
                  />
                  <label for="fromBank" class="ml-2 text-sm text-gray-700">Bank</label>
                </div>
              </div>
            </div>

            <!-- From Bank Selection (if bank is selected) -->
            <div v-if="formData.fromMode === 'bank'">
              <label for="fromBankId" class="block text-sm font-medium text-gray-700 mb-1">Bank Account *</label>
              <select
                id="fromBankId"
                v-model="formData.fromBankId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              >
                <option value="" disabled>Select Bank Account</option>
                <option v-for="bank in bankLedgers" :key="bank.id" :value="bank.id">
                  {{ bank.name }} (Balance: {{ formatCurrency(bank.currentBalance) }})
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- To Account Section -->
        <div class="md:col-span-2">
          <h3 class="text-lg font-medium text-gray-900 mb-2">To Account</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- To Account Type -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Account Type *</label>
              <div class="flex space-x-4">
                <div class="flex items-center">
                  <input
                    type="radio"
                    id="toCash"
                    v-model="formData.toMode"
                    value="cash"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                    required
                    :disabled="formData.fromMode === 'cash'"
                  />
                  <label for="toCash" class="ml-2 text-sm text-gray-700">Cash</label>
                </div>
                <div class="flex items-center">
                  <input
                    type="radio"
                    id="toBank"
                    v-model="formData.toMode"
                    value="bank"
                    class="h-4 w-4 text-indigo-600 focus:ring-indigo-500"
                    required
                    :disabled="formData.fromMode === 'bank' && bankLedgers.length <= 1"
                  />
                  <label for="toBank" class="ml-2 text-sm text-gray-700">Bank</label>
                </div>
              </div>
            </div>

            <!-- To Bank Selection (if bank is selected) -->
            <div v-if="formData.toMode === 'bank'">
              <label for="toBankId" class="block text-sm font-medium text-gray-700 mb-1">Bank Account *</label>
              <select
                id="toBankId"
                v-model="formData.toBankId"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                required
              >
                <option value="" disabled>Select Bank Account</option>
                <option
                  v-for="bank in availableToBanks"
                  :key="bank.id"
                  :value="bank.id"
                >
                  {{ bank.name }} (Balance: {{ formatCurrency(bank.currentBalance) }})
                </option>
              </select>
            </div>
          </div>
        </div>

        <!-- Instrument Number -->
        <div>
          <label for="instrumentNo" class="block text-sm font-medium text-gray-700 mb-1">Instrument No.</label>
          <input
            type="text"
            id="instrumentNo"
            v-model="formData.instrumentNo"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
        </div>

        <!-- Project Field with Datalist -->
        <div>
          <label for="project" class="block text-sm font-medium text-gray-700 mb-1">Project</label>
          <div class="relative">
            <input
              type="text"
              id="project"
              v-model="formData.project"
              list="projectList"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="projectList">
              <option v-for="project in uniqueProjects" :key="project" :value="project"></option>
            </datalist>
          </div>
        </div>

        <!-- Description Field -->
        <div class="md:col-span-2">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            id="description"
            v-model="formData.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          ></textarea>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="mt-8 flex justify-end space-x-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :disabled="isLoading || !isFormValid"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Processing...
          </span>
          <span v-else>Transfer Funds</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useLedgers } from '~/composables/expenses/useLedgers';

export default {
  name: 'TransferForm',

  props: {
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['submit', 'cancel'],

  setup(props, { emit }) {
    // Get composables
    const { getUniqueProjects } = useExpenses();
    const { bankLedgers, cashLedgers, fetchLedgers } = useLedgers();

    // Initialize form data
    const formData = ref({
      date: new Date().toISOString().split('T')[0],
      amount: '',
      fromMode: 'cash',
      fromBankId: '',
      toMode: 'bank',
      toBankId: '',
      instrumentNo: '',
      project: '',
      description: ''
    });

    // Computed properties
    const uniqueProjects = computed(() => getUniqueProjects.value);

    const availableToBanks = computed(() => {
      if (formData.value.fromMode === 'bank') {
        return bankLedgers.value.filter(bank => bank.id !== formData.value.fromBankId);
      }
      return bankLedgers.value;
    });

    const isFormValid = computed(() => {
      // Basic validation
      if (!formData.value.date || !formData.value.amount || formData.value.amount <= 0) {
        return false;
      }

      // Validate from account
      if (formData.value.fromMode === 'bank' && !formData.value.fromBankId) {
        return false;
      }

      // Validate to account
      if (formData.value.toMode === 'bank' && !formData.value.toBankId) {
        return false;
      }

      // Ensure from and to accounts are different
      if (formData.value.fromMode === formData.value.toMode) {
        if (formData.value.fromMode === 'cash') {
          return false; // Can't transfer from cash to cash
        }
        if (formData.value.fromBankId === formData.value.toBankId) {
          return false; // Can't transfer to the same bank account
        }
      }

      return true;
    });

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount);
    };

    const handleSubmit = () => {

      emit('submit', { ...formData.value });
    };

    // Initialize
    onMounted(async () => {
      try {
        await fetchLedgers();

        // Set default values based on available accounts
        if (bankLedgers.value.length > 0) {
          formData.value.toMode = 'bank';
          formData.value.toBankId = bankLedgers.value[0].id;
        } else if (cashLedgers.value.length > 0) {
          formData.value.toMode = 'cash';
        }
      } catch (error) {

      }
    });

    // Watch for changes to fromMode
    watch(() => formData.value.fromMode, (newValue) => {
      if (newValue === 'cash') {
        formData.value.toMode = 'bank';
        if (bankLedgers.value.length > 0) {
          formData.value.toBankId = bankLedgers.value[0].id;
        }
      } else if (newValue === 'bank') {
        formData.value.toMode = 'cash';
        formData.value.toBankId = '';
      }
    });

    // Watch for changes to fromBankId
    watch(() => formData.value.fromBankId, (newValue) => {
      if (newValue && formData.value.toBankId === newValue) {
        formData.value.toBankId = '';
      }
    });

    return {
      formData,
      uniqueProjects,
      bankLedgers,
      availableToBanks,
      isFormValid,
      formatCurrency,
      handleSubmit
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
