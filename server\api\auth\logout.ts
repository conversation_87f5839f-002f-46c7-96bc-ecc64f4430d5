// server/api/auth/logout.ts
import { defineEventHand<PERSON>, setCookie } from 'h3';

export default defineEventHandler(async (event) => {
  // Remove the token cookie (match the configuration from useAuthRefresh)
  setCookie(event, 'token', '', {
    maxAge: 0,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    httpOnly: false, // Match the client-side accessible configuration
    sameSite: 'lax'
  });

  // Remove the refresh token cookie (match the configuration from useAuthRefresh)
  setCookie(event, 'refreshToken', '', {
    maxAge: 0,
    path: '/',
    secure: process.env.NODE_ENV === 'production',
    httpOnly: false, // Match the client-side accessible configuration
    sameSite: 'lax'
  });

  return { message: 'Logged out successfully' };
});