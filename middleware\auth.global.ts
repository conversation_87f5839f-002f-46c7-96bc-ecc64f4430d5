import { navigateTo } from '#app'
import useAuthRefresh from '~/composables/auth/useAuthRefresh';

export default defineNuxtRouteMiddleware(async (to, from) => {
  // Define public routes that don't require authentication.
  const publicRoutes = [
    '/',
    '/login',
    '/register',
    '/signup',
    '/forgot-password',  // Password reset request page - must be public
    '/reset-password',   // Password reset confirmation page - must be public
    '/about',
    '/contact',
    // Add any other public routes here
    '/terms',
    '/privacy',
    '/faq',
    // Public API routes
    '/api/tools/languages',
    '/api/tools/translate'
  ];

  // Check if the current route is public by exact match or starts with a public route
  const isPublicRoute = publicRoutes.some(route =>
    to.path === route ||
    (route !== '/' && to.path.startsWith(`${route}/`))
  );

  if (isPublicRoute) {
    return;
  }

  // Use the new composable for token management
  const { ensureValidToken } = useAuthRefresh();

  // Ensure we have a valid token before allowing navigation
  const hasValidToken = await ensureValidToken();

  // If we don't have a valid token, redirect to login
  if (!hasValidToken) {
    return navigateTo('/login');
  }

  return;
});