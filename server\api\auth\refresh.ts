// server/api/auth/refresh.ts
import { defineEvent<PERSON>and<PERSON>, readBody, createError } from 'h3';
import jwt from 'jsonwebtoken';
import User from '../../models/User';
import { getRoleId, getRoleName } from '../../utils/roles';

export default defineEventHandler(async (event) => {
  const body = await readBody(event);
  const { refreshToken } = body;

  if (!refreshToken) {
    throw createError({ statusCode: 401, statusMessage: 'Refresh token missing' });
  }

  const secret = process.env.JWT_SECRET;
  if (!secret) {
    throw new Error('JWT_SECRET is not defined in environment variables');
  }

  try {
    // Verify the refresh token
    const decoded = jwt.verify(refreshToken, secret) as any;

    // Verify the user exists
    const user = await User.findById(decoded.id);
    if (!user) {
      throw createError({ statusCode: 401, statusMessage: 'Invalid refresh token' });
    }

    // Get or create roleId if it doesn't exist
    if (!user.roleId) {
      // Get the roleId for the user's role
      user.roleId = await getRoleId(user.role);
      await user.save();
    }

    // Get the role name for the status check
    const roleName = await getRoleName(user.roleId);

    // Check user status - only allow approved users and managers to refresh token
    // Admins are exempt from this restriction
    if (roleName !== 'admin') {
      // For users and managers, check if they are approved (status = 1)
      if (user.status !== 1) {
        if (user.status === 0) {
          throw createError({
            statusCode: 403,
            statusMessage: 'Your account is pending approval. Please contact your manager.'
          });
        } else if (user.status === -1) {
          throw createError({
            statusCode: 403,
            statusMessage: 'Your account has been rejected. Please contact your manager for assistance.'
          });
        } else {
          throw createError({
            statusCode: 403,
            statusMessage: 'Your account is not active. Please contact your manager.'
          });
        }
      }
    }

    // Reissue a new access token based on the user details
    const tokenPayload = {
      id: user._id,
      username: user.username,
      email: user.email,
      fullname: user.fullname,
      role: roleName, // Use the role name for backward compatibility
      roleId: user.roleId, // Include the role UUID
      status: user.status // Add user status to the payload
    };
    const newAccessToken = jwt.sign(tokenPayload, secret, { expiresIn: '15m' });

    return { accessToken: newAccessToken };
  } catch (error: any) {
    throw createError({ statusCode: 401, statusMessage: 'Invalid or expired refresh token' });
  }
});