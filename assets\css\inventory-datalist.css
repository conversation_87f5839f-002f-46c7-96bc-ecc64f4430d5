/* Custom styling for inventory datalist */

/* Base styling for datalist input */
input[list="stockItemList"] {
  background-color: white;
  transition: all 0.3s ease;
  border: 1px solid #e2e8f0;
}

/* Focus state with colorful border */
input[list="stockItemList"]:focus {
  border-color: #4f46e5;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.2);
}

/* Hover state */
input[list="stockItemList"]:hover {
  background-color: #f9fafb;
}

/* Custom dropdown indicator */
input[list="stockItemList"]::-webkit-calendar-picker-indicator {
  color: #4f46e5;
}

/* 
Note: Direct styling of datalist options is limited by browser support.
The following styles may work in some browsers but not all.
*/

/* Option styling (works in some browsers) */
#stockItemList option {
  padding: 8px;
  margin: 4px 0;
  font-size: 14px;
  border-radius: 4px;
  border-bottom: 1px solid #eaeaea;
  display: flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Color indicators based on stock levels */
#stockItemList option:nth-child(odd) {
  background-color: #f3f4f6;
}

#stockItemList option:nth-child(even) {
  background-color: #ffffff;
}

/* Hover effect for options (limited browser support) */
#stockItemList option:hover {
  background-color: #e0e7ff;
}

/* Styling for different parts of the option text */
#stockItemList option {
  position: relative;
}

/* Styling for item name */
#stockItemList option::before {
  content: attr(value);
  font-weight: bold;
  color: #1f2937;
  margin-right: 8px;
}

/* Styling for part number */
.pno-text {
  color: #4f46e5;
  font-weight: 500;
  margin: 0 4px;
}

/* Styling for batch number */
.batch-text {
  color: #7c3aed;
  font-style: italic;
  margin: 0 4px;
}

/* Styling for HSN code */
.hsn-text {
  color: #0891b2;
  font-family: monospace;
  margin: 0 4px;
}

/* Styling for quantity */
.qty-text {
  color: #059669;
  font-weight: 600;
  margin: 0 4px;
}

/* Low stock indicator */
.low-stock {
  color: #dc2626 !important;
}

/* Medium stock indicator */
.medium-stock {
  color: #d97706 !important;
}

/* High stock indicator */
.high-stock {
  color: #059669 !important;
}