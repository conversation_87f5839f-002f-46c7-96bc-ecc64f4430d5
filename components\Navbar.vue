<template>
  <nav class="w-full fixed top-0 bg-gradient-to-r from-teal-400 via-indigo-500 to-teal-400 shadow py-4 px-6 z-50">
    <div class="container mx-auto flex justify-between items-center">
      <!-- Brand / Logo -->
      <div class="font-bold text-xl text-white drop-shadow flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
        BusinessPro Suite
      </div>

      <!-- Add in template section after the brand/logo div -->
      <div v-if="isAuthenticated" class="hidden md:flex items-center space-x-4 text-sm text-white">
        <div class="flex flex-row items-center space-x-3">
          <span :class="{ 'text-red-500': isBlinking && isTokenExpiring }">Token: {{ tokenTimeLeft }}</span>
          <span>Session: {{ refreshTimeLeft }}</span>
        </div>
      </div>

      <!-- Mobile Timer Display (Always Visible) -->
      <div v-if="isAuthenticated" class="md:hidden flex items-center space-x-2 text-xs text-white">
        <div class="flex flex-row items-center space-x-1">
          <span :class="{ 'text-red-500': isBlinking && isTokenExpiring }">T:{{ tokenTimeLeft }}</span>
          <span>S:{{ refreshTimeLeft }}</span>
        </div>
      </div>

      <!-- Hamburger Menu Icon for Mobile -->
      <button class="text-white focus:outline-none md:hidden" @click="toggleMenu" aria-label="Toggle menu">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="w-6 h-6">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16m-7 6h7" />
        </svg>
      </button>

      <!-- Desktop Navigation Links -->
      <div class="hidden md:flex space-x-4">
        <NuxtLink to="/" class="text-white hover:text-teal-200 transition duration-300">Home</NuxtLink>
        <NuxtLink to="/about" class="text-white hover:text-teal-200 transition duration-300">About</NuxtLink>
        <NuxtLink to="/contact" class="text-white hover:text-teal-200 transition duration-300">Contact</NuxtLink>

        <template v-if="isAuthenticated">
          <NuxtLink to="/dashboard" class="text-white hover:text-teal-200 transition duration-300">Dashboard</NuxtLink>

          <!-- Documents - Not for sub-contractors -->
          <NuxtLink v-if="!isSubContractorUser" to="/documents" class="text-white hover:text-teal-200 transition duration-300">Docs</NuxtLink>

          <!-- Wages Dropdown - Not shown for sub-contractors -->
          <div v-if="!isSubContractorUser" class="relative inline-block text-left">
            <button @click="toggleWagesDropdown($event)"
              class="text-white hover:text-teal-200 transition duration-300 focus:outline-none inline-flex items-center">
              Wages
              <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div v-show="isWagesDropdownOpen"
              class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
              <div class="py-1">
                <NuxtLink to="/wages/dashboard"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isWagesDropdownOpen = false">
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                    Dashboard
                  </span>
                </NuxtLink>
                <NuxtLink to="/wages/master_roll"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isWagesDropdownOpen = false">
                  Master Roll
                </NuxtLink>
                <NuxtLink to="/wages"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isWagesDropdownOpen = false">
                  Wages Management
                </NuxtLink>
                <NuxtLink to="/wages/employee-advances"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isWagesDropdownOpen = false">
                  Employee Advances
                </NuxtLink>
                <NuxtLink to="/wages/edit"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isWagesDropdownOpen = false">
                  Edit Wages
                </NuxtLink>
                <NuxtLink to="/wages/report"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isWagesDropdownOpen = false">
                  Wages Report
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- Inventory Dropdown - Not shown for sub-contractors -->
          <div v-if="!isSubContractorUser" class="relative inline-block text-left">
            <button @click="toggleInventoryDropdown($event)"
              class="text-white hover:text-teal-200 transition duration-300 focus:outline-none inline-flex items-center">
              Inventory
              <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div v-show="isInventoryDropdownOpen"
              class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
              <div class="py-1">
                <NuxtLink to="/inventory/dashboard"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isInventoryDropdownOpen = false">
                  Dashboard
                </NuxtLink>
                <NuxtLink to="/inventory/edit-bill"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isInventoryDropdownOpen = false">
                  Inventory Management
                </NuxtLink>
                <NuxtLink to="/inventory/bills"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isInventoryDropdownOpen = false">
                  Bills
                </NuxtLink>
                <NuxtLink to="/inventory/stock-report"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isInventoryDropdownOpen = false">
                  Stock Report
                </NuxtLink>
              </div>
            </div>
          </div>

          <!-- Expenses Dropdown - Modified for sub-contractors -->
          <div class="relative inline-block text-left">
            <button v-if="!isSubContractorUser" @click="toggleExpensesDropdown($event)"
              class="text-white hover:text-teal-200 transition duration-300 focus:outline-none inline-flex items-center">
              Expenses
              <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <!-- Direct link to Subcontractor Accounts for sub-contractors -->
            <NuxtLink v-if="isSubContractorUser" to="/expenses/subs"
              class="text-white hover:text-teal-200 transition duration-300">
              My Account
            </NuxtLink>
            <div v-show="isExpensesDropdownOpen"
              class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
              <div class="py-1">
                <!-- Dashboard - Available to all users -->
                <NuxtLink to="/expenses"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isExpensesDropdownOpen = false">
                  Dashboard
                </NuxtLink>

                <!-- Transactions - Not for sub-contractors -->
                <NuxtLink v-if="!isSubContractorUser" to="/expenses/list"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isExpensesDropdownOpen = false">
                  Transactions
                </NuxtLink>

                <!-- Add Transaction - Not for sub-contractors -->
                <NuxtLink v-if="!isSubContractorUser" to="/expenses/add"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isExpensesDropdownOpen = false">
                  Add Transaction
                </NuxtLink>

                <!-- Ledgers - Not for sub-contractors -->
                <NuxtLink v-if="!isSubContractorUser" to="/expenses/ledgers"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isExpensesDropdownOpen = false">
                  Ledgers
                </NuxtLink>

                <!-- Reports - Not for sub-contractors -->
                <NuxtLink v-if="!isSubContractorUser" to="/expenses/reports"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isExpensesDropdownOpen = false">
                  Reports
                </NuxtLink>

                <!-- Subcontractor Accounts - Available to all users -->
                <NuxtLink to="/expenses/subs"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isExpensesDropdownOpen = false">
                  Subcontractor Accounts
                </NuxtLink>
              </div>
            </div>
          </div>

          <NuxtLink v-if="isAuthenticated" to="/stock-market" class="text-white hover:text-teal-200 transition duration-300">Stock Market</NuxtLink>

          <!-- Labor Dropdown -->
          <div v-if="!isSubContractorUser" class="relative inline-block text-left">
            <button @click="toggleLaborDropdown($event)"
              class="text-white hover:text-teal-200 transition duration-300 focus:outline-none inline-flex items-center">
              Labor
              <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </button>
            <div v-show="isLaborDropdownOpen"
              class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5">
              <div class="py-1">
                <NuxtLink to="/labor"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isLaborDropdownOpen = false">
                  Dashboard
                </NuxtLink>
                <NuxtLink to="/labor/attendance"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isLaborDropdownOpen = false">
                  Attendance
                </NuxtLink>
                <NuxtLink to="/labor/payments"
                  class="block px-4 py-2 text-sm text-gray-700 hover:bg-indigo-100 hover:text-indigo-900"
                  @click="isLaborDropdownOpen = false">
                  Payments
                </NuxtLink>
              </div>
            </div>
          </div>
          
          <!-- Tools Button (opens Settings & Tools modal) -->
          <button @click="openSettings"
            class="text-white hover:text-teal-200 transition duration-300 focus:outline-none inline-flex items-center">
            Tools
          </button>

          <NuxtLink v-if="isAdminUser" to="/admin" class="text-white hover:text-teal-200 transition duration-300">Admin
            Panel</NuxtLink>

          <button @click="handleLogout"
            class="text-white hover:text-teal-200 transition duration-300 focus:outline-none">
            Logout
          </button>
        </template>
        <template v-else>
          <NuxtLink to="/login" class="text-white hover:text-teal-200 transition duration-300">Login</NuxtLink>
          <NuxtLink to="/signup" class="text-white hover:text-teal-200 transition duration-300">Register</NuxtLink>
        </template>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div v-if="isMenuOpen" class="md:hidden bg-gradient-to-r from-teal-400 via-indigo-500 to-teal-400 py-4 px-6">


      <NuxtLink to="/" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
        @click="isMenuOpen = false">Home</NuxtLink>
      <NuxtLink to="/about" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
        @click="isMenuOpen = false">About</NuxtLink>
      <NuxtLink to="/contact" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
        @click="isMenuOpen = false">Contact
      </NuxtLink>

      <template v-if="isAuthenticated">
        <NuxtLink to="/dashboard" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">Dashboard
        </NuxtLink>

        <!-- Documents - Not for sub-contractors -->
        <NuxtLink v-if="!isSubContractorUser" to="/documents" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">Documents
        </NuxtLink>

        <!-- Wages dropdown for mobile view - Not for sub-contractors -->
        <div v-if="!isSubContractorUser" class="block text-white hover:text-teal-200 transition duration-300 mb-2">
          <button @click="toggleWagesDropdown($event)" class="flex items-center w-full text-left">
            Wages
            <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div v-show="isWagesDropdownOpen" class="mt-1 ml-4">
            <NuxtLink to="/wages/dashboard" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isWagesDropdownOpen = false">
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                Dashboard
              </span>
            </NuxtLink>
            <NuxtLink to="/wages/master_roll" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isWagesDropdownOpen = false">
              Master Roll
            </NuxtLink>
            <NuxtLink to="/wages" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isWagesDropdownOpen = false">
              Wages Management
            </NuxtLink>
            <NuxtLink to="/wages/employee-advances" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isWagesDropdownOpen = false">
              Employee Advances
            </NuxtLink>
            <NuxtLink to="/wages/edit" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isWagesDropdownOpen = false">
              Edit Wages
            </NuxtLink>
            <NuxtLink to="/wages/report" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isWagesDropdownOpen = false">
              Wages Report
            </NuxtLink>
          </div>
        </div>

        <!-- Inventory dropdown for mobile view - Not for sub-contractors -->
        <div v-if="!isSubContractorUser" class="block text-white hover:text-teal-200 transition duration-300 mb-2">
          <button @click="toggleInventoryDropdown($event)" class="flex items-center w-full text-left">
            Inventory
            <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div v-show="isInventoryDropdownOpen" class="mt-1 ml-4">
            <NuxtLink to="/inventory/dashboard" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isInventoryDropdownOpen = false">
              Dashboard
            </NuxtLink>
            <NuxtLink to="/inventory/edit-bill" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isInventoryDropdownOpen = false">
              Inventory Management
            </NuxtLink>
            <NuxtLink to="/inventory/bills" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isInventoryDropdownOpen = false">
              Bills
            </NuxtLink>
            <NuxtLink to="/inventory/stock-report" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isInventoryDropdownOpen = false">
              Stock Report
            </NuxtLink>
          </div>
        </div>

        <!-- Expenses dropdown for mobile view - Not for sub-contractors -->
        <div v-if="!isSubContractorUser" class="block text-white hover:text-teal-200 transition duration-300 mb-2">
          <button @click="toggleExpensesDropdown($event)" class="flex items-center w-full text-left">
            Expenses
            <svg class="ml-1 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
          <div v-show="isExpensesDropdownOpen" class="mt-1 ml-4">
            <NuxtLink to="/expenses" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isExpensesDropdownOpen = false">
              Dashboard
            </NuxtLink>
            <!-- Transactions - Not for sub-contractors -->
            <NuxtLink v-if="!isSubContractorUser" to="/expenses/list" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isExpensesDropdownOpen = false">
              Transactions
            </NuxtLink>

            <!-- Add Transaction - Not for sub-contractors -->
            <NuxtLink v-if="!isSubContractorUser" to="/expenses/add" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isExpensesDropdownOpen = false">
              Add Transaction
            </NuxtLink>

            <!-- Ledgers - Not for sub-contractors -->
            <NuxtLink v-if="!isSubContractorUser" to="/expenses/ledgers" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isExpensesDropdownOpen = false">
              Ledgers
            </NuxtLink>

            <!-- Reports - Not for sub-contractors -->
            <NuxtLink v-if="!isSubContractorUser" to="/expenses/reports" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isExpensesDropdownOpen = false">
              Reports
            </NuxtLink>
            <NuxtLink to="/expenses/subs" class="block text-white hover:text-teal-200 transition duration-300 mb-1"
              @click="isMenuOpen = false; isExpensesDropdownOpen = false">
              Subcontractor Accounts
            </NuxtLink>
          </div>
        </div>

        <!-- Direct link to Subcontractor Accounts for sub-contractors in mobile view -->
        <NuxtLink v-if="isSubContractorUser" to="/expenses/subs" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">
          My Account
        </NuxtLink>

        <!-- Stock Market Link -->
        <NuxtLink to="/stock-market" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">Stock Market
        </NuxtLink>

        <!-- Tools Button for mobile view (opens Settings & Tools modal) -->
        <button @click="isMenuOpen = false; openSettings()"
          class="block text-white hover:text-teal-200 transition duration-300 mb-2 text-left w-full">
          Tools
        </button>

        <NuxtLink v-if="isAdminUser" to="/admin" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">
          Admin Panel</NuxtLink>
        <button @click="handleLogout"
          class="block text-white hover:text-teal-200 transition duration-300 focus:outline-none">
          Logout
        </button>
      </template>
      <template v-else>
        <NuxtLink to="/login" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">Login</NuxtLink>
        <NuxtLink to="/signup" class="block text-white hover:text-teal-200 transition duration-300 mb-2"
          @click="isMenuOpen = false">Register
        </NuxtLink>
      </template>
    </div>
  </nav>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useCookie, useRouter } from '#app'
import useUserRole from '~/composables/auth/useUserRole'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'
import { useLogout } from '~/composables/auth/useLogout'


const router = useRouter()
const isMenuOpen = ref(false)
const isWagesDropdownOpen = ref(false)
const isInventoryDropdownOpen = ref(false)
const isExpensesDropdownOpen = ref(false)
const isLaborDropdownOpen = ref(false)

// Fetch the access token from cookies for both server and client.
const tokenCookie = useCookie('token')
const refreshCookie = useCookie('refreshToken')

// Token timer state
const tokenTimeLeft = ref('')
const refreshTimeLeft = ref('')
const tokenExpiringThreshold = 60 // seconds
const isTokenExpiring = ref(false)
const isBlinking = ref(false)

// Blink effect interval
let blinkInterval: number | null = null

// Function to decode JWT and get expiration time
const getTokenExpiration = (token: string | null | undefined): number | null => {
  if (!token) return null;
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload.exp * 1000; // Convert to milliseconds
  } catch {
    return null;
  }
};

// Update timer display
const updateTimers = () => {
  const now = Date.now()

  // Update access token timer
  const tokenExp = getTokenExpiration(tokenCookie.value)
  if (tokenExp) {
    const diff = tokenExp - now
    if (diff > 0) {
      const minutes = Math.floor(diff / 60000)
      const seconds = Math.floor((diff % 60000) / 1000)
      tokenTimeLeft.value = `${minutes}m ${seconds}s`

      // Check if token is about to expire (less than threshold seconds)
      const totalSeconds = minutes * 60 + seconds
      const wasExpiring = isTokenExpiring.value
      isTokenExpiring.value = totalSeconds <= tokenExpiringThreshold

      // Start or stop blinking based on expiration status
      if (isTokenExpiring.value && !wasExpiring) {
        startBlinking()
      } else if (!isTokenExpiring.value && wasExpiring) {
        stopBlinking()
      }
    } else {
      tokenTimeLeft.value = 'Expired'
      isTokenExpiring.value = false
      stopBlinking()
    }
  }

  // Update refresh token timer
  const refreshExp = getTokenExpiration(refreshCookie.value)
  if (refreshExp) {
    const diff = refreshExp - now
    if (diff > 0) {
      const days = Math.floor(diff / (24 * 60 * 60 * 1000))
      const hours = Math.floor((diff % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000))
      const minutes = Math.floor((diff % (60 * 60 * 1000)) / (60 * 1000))
      const seconds = Math.floor((diff % (60 * 1000)) / 1000)
      refreshTimeLeft.value = `${days}:${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    } else {
      refreshTimeLeft.value = 'Expired'
      handleLogout()
    }
  }
}

// Function to start blinking effect
const startBlinking = () => {
  if (blinkInterval !== null) return; // Already blinking

  // Toggle blinking state every 500ms
  blinkInterval = window.setInterval(() => {
    isBlinking.value = !isBlinking.value;
  }, 500);
};

// Function to stop blinking effect
const stopBlinking = () => {
  if (blinkInterval === null) return; // Not blinking

  window.clearInterval(blinkInterval);
  blinkInterval = null;
  isBlinking.value = false;
};

// Start timer when component is mounted
onMounted(() => {
  console.log('Navbar: Component mounted - setting up timers and event listeners');

  document.addEventListener('click', closeWagesDropdown)
  document.addEventListener('click', closeInventoryDropdown)
  document.addEventListener('click', closeExpensesDropdown)

  // Update timers every second
  const timerInterval = setInterval(updateTimers, 1000)
  // Initial update
  updateTimers()

  // Cleanup interval on unmount
  onUnmounted(() => {
    console.log('Navbar: Component unmounting - cleaning up timers and event listeners');

    document.removeEventListener('click', closeWagesDropdown)
    document.removeEventListener('click', closeInventoryDropdown)
    document.removeEventListener('click', closeExpensesDropdown)
    clearInterval(timerInterval)
    stopBlinking() // Make sure to clean up blinking interval

    console.log('Navbar: Cleanup completed');
  })
})

// isAuthenticated returns true if a token exists.
const isAuthenticated = computed(() => Boolean(tokenCookie.value))

// Get user role functions
const { isAdmin, isSubContractor, isManager, getUserRole } = useUserRole()

// Check if user is admin (computed property for template usage)
const isAdminUser = computed(() => isAdmin())

// Check if user is sub-contractor (computed property for template usage)
const isSubContractorUser = computed(() => isSubContractor())

// Check if user is manager (computed property for template usage)
const isManagerUser = computed(() => isManager())

// Get user role (computed property for template usage)
const userRole = computed(() => getUserRole())

// Toggle mobile menu
const toggleMenu = () => {
  isMenuOpen.value = !isMenuOpen.value
}

// Toggle wages dropdown
const toggleWagesDropdown = (event?: Event) => {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }
  isWagesDropdownOpen.value = !isWagesDropdownOpen.value
}

const toggleLaborDropdown = (event?: Event) => {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }
  isLaborDropdownOpen.value = !isLaborDropdownOpen.value
}



// Toggle inventory dropdown
const toggleInventoryDropdown = (event?: Event) => {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }
  isInventoryDropdownOpen.value = !isInventoryDropdownOpen.value
}

// Toggle expenses dropdown
const toggleExpensesDropdown = (event?: Event) => {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }
  isExpensesDropdownOpen.value = !isExpensesDropdownOpen.value
}

// Close dropdowns when clicking outside
const closeWagesDropdown = (event: any) => {
  // Don't close if clicking on the dropdown toggle button
  if (event.target.closest('button') && event.target.closest('button').textContent.trim() === 'Wages') {
    return;
  }
  // Close if clicking outside the dropdown
  if (!event.target.closest('.relative') && !event.target.closest('.block.text-white')) {
    isWagesDropdownOpen.value = false
  }
}

const closeLaborDropdown = (event: any) => {
  // Don't close if clicking on the dropdown toggle button
  if (event.target.closest('button') && event.target.closest('button').textContent.trim() === 'Labor') {
    return;
  }
  // Close if clicking outside the dropdown
  if (!event.target.closest('.relative') && !event.target.closest('.block.text-white')) {
    isLaborDropdownOpen.value = false
  }
}



const closeInventoryDropdown = (event: any) => {
  // Don't close if clicking on the dropdown toggle button
  if (event.target.closest('button') && event.target.closest('button').textContent.trim() === 'Inventory') {
    return;
  }
  // Close if clicking outside the dropdown
  if (!event.target.closest('.relative') && !event.target.closest('.block.text-white')) {
    isInventoryDropdownOpen.value = false
  }
}

const closeExpensesDropdown = (event: any) => {
  // Don't close if clicking on the dropdown toggle button
  if (event.target.closest('button') && event.target.closest('button').textContent.trim() === 'Expenses') {
    return;
  }
  // Close if clicking outside the dropdown
  if (!event.target.closest('.relative') && !event.target.closest('.block.text-white')) {
    isExpensesDropdownOpen.value = false
  }
}

// Handle logout using centralized logout composable
const { performLogout } = useLogout()

const handleLogout = async () => {
  await performLogout()
}

// Function to open calculator modal
const openCalculator = () => {
  // Dispatch custom event to open calculator
  window.dispatchEvent(new CustomEvent('open-calculator'));
};

// Function to open news reader modal - REMOVED
const openNewsReader = () => {
  console.log('📰 News Reader functionality has been removed');
  // News reader modal has been removed from the application
};

// Function to open settings & tools modal
const openSettings = () => {
  // Close mobile menu if open
  isMenuOpen.value = false;
  // Dispatch custom event to open settings
  window.dispatchEvent(new CustomEvent('open-settings'));
};



onMounted(() => {
  document.addEventListener('click', closeWagesDropdown)
  document.addEventListener('click', closeInventoryDropdown)
  document.addEventListener('click', closeExpensesDropdown)
  document.addEventListener('click', closeLaborDropdown)
})

onUnmounted(() => {
  document.removeEventListener('click', closeWagesDropdown)
  document.removeEventListener('click', closeInventoryDropdown)
  document.removeEventListener('click', closeExpensesDropdown)
  document.removeEventListener('click', closeLaborDropdown)
})
</script>
