<template>
  <div>
    <!-- Welcome Section -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-800 mb-2">Welcome to the Admin Dashboard</h1>
      <p class="text-gray-600">Here's an overview of your system's performance and statistics.</p>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Users Stat -->
        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-blue-500 transform transition-all duration-300 hover:scale-105">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-sm font-medium text-gray-500 mb-1">Total Users</p>
              <h3 class="text-2xl font-bold text-gray-800">
                <span v-if="loading.stats" class="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                <span v-else>{{ stats.users }}</span>
              </h3>
              <p class="text-xs text-green-600 mt-2 flex items-center" v-if="stats.userGrowth > 0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                {{ stats.userGrowth }}% from last month
              </p>
            </div>
            <div class="bg-blue-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Firms Stat -->
        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-purple-500 transform transition-all duration-300 hover:scale-105">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-sm font-medium text-gray-500 mb-1">Total Firms</p>
              <h3 class="text-2xl font-bold text-gray-800">
                <span v-if="loading.stats" class="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                <span v-else>{{ stats.firms }}</span>
              </h3>
              <p class="text-xs text-green-600 mt-2 flex items-center" v-if="stats.firmGrowth > 0">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18" />
                </svg>
                {{ stats.firmGrowth }}% from last month
              </p>
            </div>
            <div class="bg-purple-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
            </div>
          </div>
        </div>

        <!-- MongoDB Stat -->
        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-green-500 transform transition-all duration-300 hover:scale-105">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-sm font-medium text-gray-500 mb-1">MongoDB Models</p>
              <h3 class="text-2xl font-bold text-gray-800">
                <span v-if="loading.stats" class="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                <span v-else>{{ stats.mongoModels }}</span>
              </h3>
              <p class="text-xs text-gray-600 mt-2">
                <span v-if="loading.stats" class="inline-block w-24 h-3 bg-gray-200 rounded animate-pulse"></span>
                <span v-else>{{ stats.mongoRecords.toLocaleString() }} total records</span>
              </p>
            </div>
            <div class="bg-green-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Firestore Stat -->
        <div class="bg-white rounded-lg shadow-md p-6 border-l-4 border-orange-500 transform transition-all duration-300 hover:scale-105">
          <div class="flex justify-between items-start">
            <div>
              <p class="text-sm font-medium text-gray-500 mb-1">Firestore Collections</p>
              <h3 class="text-2xl font-bold text-gray-800">
                <span v-if="loading.stats" class="inline-block w-12 h-8 bg-gray-200 rounded animate-pulse"></span>
                <span v-else>{{ stats.firestoreCollections }}</span>
              </h3>
              <p class="text-xs text-gray-600 mt-2">
                <span v-if="loading.stats" class="inline-block w-24 h-3 bg-gray-200 rounded animate-pulse"></span>
                <span v-else>{{ stats.firestoreRecords.toLocaleString() }} total records</span>
              </p>
            </div>
            <div class="bg-orange-100 p-3 rounded-full">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-orange-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 14v6m-3-3h6M6 10h2a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2zm10 0h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2zM6 20h2a2 2 0 002-2v-2a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2z" />
              </svg>
            </div>
          </div>
        </div>
    </div>

    <!-- Activity and Pending Items Section -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
      <!-- Recent Activity -->
      <div class="bg-white rounded-lg shadow-md p-6 animate-fade-in">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          Recent Activity
        </h2>

        <!-- Activity Data -->
        <div v-if="recentActivity.length > 0" class="space-y-4">
          <div v-for="(activity, index) in recentActivity" :key="index"
               class="flex items-start p-3 rounded-lg transition-colors"
               :class="{'bg-gray-50': index % 2 === 0}">
            <div :class="`bg-${activity.color}-100 p-2 rounded-full mr-3`">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" :class="`text-${activity.color}-500`" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="activity.icon" />
              </svg>
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium text-gray-800">{{ activity.message }}</p>
              <p class="text-xs text-gray-500">{{ activity.time }}</p>
            </div>
          </div>
        </div>

        <!-- No Activity -->
        <div v-else class="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-gray-500">No recent activity</p>
        </div>

        <div class="mt-4 text-center">
          <button class="text-sm text-indigo-600 hover:text-indigo-800 font-medium">
            View All Activity
          </button>
        </div>
      </div>

      <!-- Pending Approvals -->
      <div class="bg-white rounded-lg shadow-md p-6 animate-fade-in" style="animation-delay: 0.2s;">
        <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          Pending Approvals
        </h2>

        <!-- Pending Items -->
        <div v-if="pendingItems.length > 0" class="space-y-3">
          <div v-for="(item, index) in pendingItems" :key="index"
               class="flex justify-between items-center p-3 rounded-lg border border-gray-100 hover:bg-yellow-50 transition-colors">
            <div class="flex items-center">
              <div :class="`bg-${item.color}-100 p-2 rounded-full mr-3`">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" :class="`text-${item.color}-500`" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="item.icon" />
                </svg>
              </div>
              <div>
                <p class="text-sm font-medium text-gray-800">{{ item.name }}</p>
                <p class="text-xs text-gray-500">{{ item.type }} • {{ item.time }}</p>
              </div>
            </div>
            <div class="flex space-x-2">
              <button
                @click="handleAction(item.approveAction, item.id)"
                class="p-1 rounded bg-green-500 text-white hover:bg-green-600 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                </svg>
              </button>
              <button
                @click="handleAction(item.rejectAction, item.id)"
                class="p-1 rounded bg-red-500 text-white hover:bg-red-600 transition-colors"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>
          </div>
        </div>

        <!-- No Pending Items -->
        <div v-else class="text-center py-8">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-300 mb-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <p class="text-gray-500">No pending approvals</p>
        </div>
      </div>
    </div>

    <!-- Quick Actions Section -->
    <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg shadow-lg p-6 text-white mb-8 transform transition-all duration-500 hover:shadow-xl">
      <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <NuxtLink to="/admin?tab=management&subtab=users" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-4 rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:scale-105">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          <span class="text-sm">Add User</span>
        </NuxtLink>
        <NuxtLink to="/admin?tab=management&subtab=firms" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-4 rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:scale-105">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
          </svg>
          <span class="text-sm">Add Firm</span>
        </NuxtLink>
        <NuxtLink to="/admin?tab=database" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-4 rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:scale-105">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
          </svg>
          <span class="text-sm">Database Actions</span>
        </NuxtLink>
        <NuxtLink to="/admin?tab=management&subtab=codes" class="bg-white bg-opacity-20 hover:bg-opacity-30 p-4 rounded-lg flex flex-col items-center justify-center transition-all duration-300 hover:scale-105">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
          </svg>
          <span class="text-sm">Manager Codes</span>
        </NuxtLink>
      </div>
    </div>

    <!-- System Health Section -->
    <div class="bg-white rounded-lg shadow-md p-6 mb-8 animate-fade-in" style="animation-delay: 0.4s;">
      <h2 class="text-xl font-semibold text-gray-800 mb-4 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
        </svg>
        System Health
      </h2>

      <!-- System Health Data -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- MongoDB Status -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-600">MongoDB Status</span>
            <span class="text-sm font-medium text-green-600">Connected</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-green-500 h-2.5 rounded-full" style="width: 100%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            <span v-if="loading.stats" class="inline-block w-32 h-3 bg-gray-200 rounded animate-pulse"></span>
            <span v-else>{{ stats.mongoModels }} models, {{ stats.mongoRecords.toLocaleString() }} records</span>
          </p>
        </div>

        <!-- Firestore Status -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-600">Firestore Status</span>
            <span class="text-sm font-medium text-green-600">Connected</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div class="bg-green-500 h-2.5 rounded-full" style="width: 100%"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            <span v-if="loading.stats" class="inline-block w-32 h-3 bg-gray-200 rounded animate-pulse"></span>
            <span v-else>{{ stats.firestoreCollections }} collections, {{ stats.firestoreRecords.toLocaleString() }} records</span>
          </p>
        </div>

        <!-- User Activity -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-600">User Activity</span>
            <span class="text-sm font-medium" :class="stats.userGrowth > 0 ? 'text-green-600' : 'text-yellow-600'">
              {{ stats.userGrowth > 0 ? 'Growing' : 'Stable' }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div :class="stats.userGrowth > 0 ? 'bg-green-500' : 'bg-yellow-500'" class="h-2.5 rounded-full" :style="`width: ${Math.min(stats.userGrowth * 5, 100)}%`"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            <span v-if="loading.stats" class="inline-block w-32 h-3 bg-gray-200 rounded animate-pulse"></span>
            <span v-else>{{ stats.userGrowth }}% growth in last 30 days</span>
          </p>
        </div>

        <!-- Firm Activity -->
        <div>
          <div class="flex justify-between items-center mb-2">
            <span class="text-sm font-medium text-gray-600">Firm Activity</span>
            <span class="text-sm font-medium" :class="stats.firmGrowth > 0 ? 'text-green-600' : 'text-yellow-600'">
              {{ stats.firmGrowth > 0 ? 'Growing' : 'Stable' }}
            </span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2.5">
            <div :class="stats.firmGrowth > 0 ? 'bg-green-500' : 'bg-yellow-500'" class="h-2.5 rounded-full" :style="`width: ${Math.min(stats.firmGrowth * 5, 100)}%`"></div>
          </div>
          <p class="text-xs text-gray-500 mt-1">
            <span v-if="loading.stats" class="inline-block w-32 h-3 bg-gray-200 rounded animate-pulse"></span>
            <span v-else>{{ stats.firmGrowth }}% growth in last 30 days</span>
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import useToast from '~/composables/ui/useToast';
import { useRealTimeStatus } from '~/composables/utils/useRealTimeStatus';

const { error } = useToast();

// Stats data
const stats = ref({
  users: 0,
  userGrowth: 0,
  firms: 0,
  firmGrowth: 0,
  mongoModels: 0,
  mongoRecords: 0,
  firestoreCollections: 0,
  firestoreRecords: 0
});

// Data loading states
const loading = ref({
  stats: true,
  activity: true,
  pending: true
});

// Users and firms data
const users = ref([]);
const firms = ref([]);
const pendingUsers = ref([]);
const pendingFirms = ref([]);
const models = ref([]);
const firestoreCollections = ref([]);
const systemLogs = ref([]);

// Recent activity data
const recentActivity = computed(() => {
  const activities = [];

  // Add recent user registrations
  users.value
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 3)
    .forEach(user => {
      activities.push({
        message: `New user registered: ${user.fullname || user.username}`,
        time: formatTimeAgo(user.createdAt),
        color: 'blue',
        icon: 'M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z'
      });
    });

  // Add recent firm registrations
  firms.value
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 3)
    .forEach(firm => {
      activities.push({
        message: `New firm registered: ${firm.name}`,
        time: formatTimeAgo(firm.createdAt),
        color: 'purple',
        icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4'
      });
    });

  // Add system logs if available
  if (systemLogs.value.length > 0) {
    systemLogs.value
      .sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp))
      .slice(0, 3)
      .forEach(log => {
        let color = 'gray';
        let icon = 'M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z';

        if (log.type === 'backup') {
          color = 'green';
          icon = 'M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4';
        } else if (log.type === 'error') {
          color = 'red';
          icon = 'M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z';
        }

        activities.push({
          message: log.message || 'System event occurred',
          time: formatTimeAgo(log.timestamp),
          color,
          icon
        });
      });
  }

  // Sort all activities by time
  return activities
    .sort((a, b) => {
      const timeA = parseTimeAgo(a.time);
      const timeB = parseTimeAgo(b.time);
      return timeA - timeB;
    })
    .slice(0, 5);
});

// Pending items data
const pendingItems = computed(() => {
  const items = [];

  // Add pending firms
  pendingFirms.value.forEach(firm => {
    items.push({
      id: firm._id,
      name: firm.name,
      type: 'Firm Registration',
      time: formatTimeAgo(firm.createdAt),
      color: 'purple',
      icon: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4',
      approveAction: 'approveFirm',
      rejectAction: 'rejectFirm'
    });
  });

  // Add pending users (managers awaiting approval)
  pendingUsers.value.forEach(user => {
    items.push({
      id: user._id,
      name: user.fullname || user.username,
      type: 'Manager Approval',
      time: formatTimeAgo(user.createdAt),
      color: 'blue',
      icon: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      approveAction: 'approveUser',
      rejectAction: 'rejectUser'
    });
  });

  // Sort by time
  return items.sort((a, b) => {
    const timeA = parseTimeAgo(a.time);
    const timeB = parseTimeAgo(b.time);
    return timeA - timeB;
  });
});

// Helper function to format time ago
function formatTimeAgo(dateString) {
  const date = new Date(dateString);
  const now = new Date();
  const diffMs = now - date;
  const diffSec = Math.floor(diffMs / 1000);
  const diffMin = Math.floor(diffSec / 60);
  const diffHour = Math.floor(diffMin / 60);
  const diffDay = Math.floor(diffHour / 24);

  if (diffSec < 60) {
    return 'just now';
  } else if (diffMin < 60) {
    return `${diffMin} minute${diffMin > 1 ? 's' : ''} ago`;
  } else if (diffHour < 24) {
    return `${diffHour} hour${diffHour > 1 ? 's' : ''} ago`;
  } else if (diffDay < 30) {
    return `${diffDay} day${diffDay > 1 ? 's' : ''} ago`;
  } else {
    return date.toLocaleDateString();
  }
}

// Helper function to parse time ago for sorting
function parseTimeAgo(timeAgoString) {
  const now = new Date();

  if (timeAgoString === 'just now') {
    return now.getTime();
  }

  const match = timeAgoString.match(/(\d+)\s+(minute|hour|day)s?\s+ago/);
  if (match) {
    const value = parseInt(match[1]);
    const unit = match[2];

    if (unit === 'minute') {
      return now.getTime() - (value * 60 * 1000);
    } else if (unit === 'hour') {
      return now.getTime() - (value * 60 * 60 * 1000);
    } else if (unit === 'day') {
      return now.getTime() - (value * 24 * 60 * 60 * 1000);
    }
  }

  // If we can't parse it, assume it's a date string
  return new Date(timeAgoString).getTime();
}

// Fetch users data
async function fetchUsers() {
  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/users');
    users.value = response.users || [];

    // Filter pending manager users
    pendingUsers.value = users.value.filter(user =>
      user.role === 'manager' && user.status === 'Pending'
    );

    // Calculate user growth (simplified - in a real app you'd compare with previous period)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentUsers = users.value.filter(user =>
      new Date(user.createdAt) > thirtyDaysAgo
    );

    if (users.value.length > 0) {
      stats.value.userGrowth = Math.round((recentUsers.length / users.value.length) * 100);
    }
  } catch (err) {
    console.error('Error fetching users:', err);
    error('Failed to fetch users data');
  }
}

// Fetch firms data
async function fetchFirms() {
  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/firms');
    firms.value = response || [];

    // Filter pending firms
    pendingFirms.value = firms.value.filter(firm => firm.status === 'pending');

    // Calculate firm growth (simplified)
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentFirms = firms.value.filter(firm =>
      new Date(firm.createdAt) > thirtyDaysAgo
    );

    if (firms.value.length > 0) {
      stats.value.firmGrowth = Math.round((recentFirms.length / firms.value.length) * 100);
    }
  } catch (err) {
    console.error('Error fetching firms:', err);
    error('Failed to fetch firms data');
  }
}

// Fetch MongoDB models
async function fetchModels() {
  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/models');
    models.value = response.models || [];

    // Count total records across all models
    let totalRecords = 0;
    for (const model of models.value) {
      totalRecords += model.count || 0;
    }

    stats.value.mongoModels = models.value.length;
    stats.value.mongoRecords = totalRecords;
  } catch (err) {
    console.error('Error fetching MongoDB models:', err);
    error('Failed to fetch MongoDB data');
  }
}

// Fetch Firestore collections
async function fetchFirestoreCollections() {
  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/firestore/collections');
    firestoreCollections.value = response.collections || [];

    // Count total records across all collections
    let totalRecords = 0;
    for (const collection of firestoreCollections.value) {
      totalRecords += collection.count || 0;
    }

    stats.value.firestoreCollections = firestoreCollections.value.length;
    stats.value.firestoreRecords = totalRecords;
  } catch (err) {
    console.error('Error fetching Firestore collections:', err);
    error('Failed to fetch Firestore data');
  }
}

// Fetch system logs (if available)
async function fetchSystemLogs() {
  try {
    // Check if the API endpoint exists by making a HEAD request first
    try {
      const api = useApiWithAuth();
      // Try to get logs from a more likely endpoint
      const response = await api.get('/api/logs');
      systemLogs.value = response.logs || [];
    } catch (innerErr) {
      // If that fails, try to get logs from Firestore api_logs collection
      try {
        const api = useApiWithAuth();
        const response = await api.get('/api/firestore/collections/api_logs');
        if (response && response.data) {
          // Transform Firestore logs to match expected format
          systemLogs.value = response.data.map(log => ({
            message: log.message || log.action || 'System event',
            timestamp: log.timestamp || log.createdAt || new Date().toISOString(),
            type: log.type || (log.error ? 'error' : 'info')
          }));
        } else {
          systemLogs.value = [];
        }
      } catch (firestoreErr) {
        // If both fail, use empty array
        console.log('Could not fetch logs from Firestore either');
        systemLogs.value = [];
      }
    }
  } catch (err) {
    console.log('System logs not available');
    // Don't show error to user as this is optional
    systemLogs.value = [];
  }
}

// Fetch all dashboard data
async function fetchDashboardData() {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `dashboard-load-${Date.now()}`;

  startOperation(operationId, 'Loading Dashboard', 'Fetching dashboard data from all sources...');

  // Set loading states
  loading.value.stats = true;
  loading.value.activity = true;
  loading.value.pending = true;

  try {
    updateProgress(operationId, 1, 'Loading users data...', 20);
    // Fetch users and update stats
    await fetchUsers().then(() => {
      stats.value.users = users.value.length;
    }).catch(err => {
      console.error('Error fetching users:', err);
      stats.value.users = 0;
      stats.value.userGrowth = 0;
    });

    updateProgress(operationId, 2, 'Loading firms data...', 40);
    // Fetch firms and update stats
    await fetchFirms().then(() => {
      stats.value.firms = firms.value.length;
    }).catch(err => {
      console.error('Error fetching firms:', err);
      stats.value.firms = 0;
      stats.value.firmGrowth = 0;
    });

    updateProgress(operationId, 3, 'Loading MongoDB models...', 60);
    // Fetch MongoDB models
    await fetchModels().catch(err => {
      console.error('Error fetching MongoDB models:', err);
      stats.value.mongoModels = 0;
      stats.value.mongoRecords = 0;
    });

    updateProgress(operationId, 4, 'Loading Firestore collections...', 80);
    // Fetch Firestore collections
    await fetchFirestoreCollections().catch(err => {
      console.error('Error fetching Firestore collections:', err);
      stats.value.firestoreCollections = 0;
      stats.value.firestoreRecords = 0;
    });

    updateProgress(operationId, 5, 'Loading system logs...', 90);
    // Fetch system logs (optional)
    await fetchSystemLogs().catch(err => {
      console.log('System logs not available');
      systemLogs.value = [];
    });

    updateProgress(operationId, 6, 'Dashboard loaded successfully', 100);
    completeOperation(operationId, 'success', 'Dashboard data loaded from all sources');

  } catch (err) {
    completeOperation(operationId, 'error', `Dashboard loading failed: ${err.message}`);
  } finally {
    // Set a timeout to ensure loading states are cleared
    setTimeout(() => {
      loading.value.stats = false;
      loading.value.activity = false;
      loading.value.pending = false;
    }, 500);
  }
}

// Handle approval/rejection actions
async function handleAction(action, id) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `dashboard-action-${action}-${id}-${Date.now()}`;

  const actionNames = {
    'approveFirm': 'Approving Firm',
    'rejectFirm': 'Rejecting Firm',
    'approveUser': 'Approving User',
    'rejectUser': 'Rejecting User'
  };

  startOperation(operationId, actionNames[action] || 'Processing Action', `Processing ${action} request...`);

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Sending request...', 50);
    if (action === 'approveFirm') {
      await api.post(`/api/firms/${id}/approve`);
    } else if (action === 'rejectFirm') {
      await api.post(`/api/firms/${id}/reject`);
    } else if (action === 'approveUser') {
      await api.put('/api/users', { id, status: 1 });
    } else if (action === 'rejectUser') {
      await api.put('/api/users', { id, status: -1 });
    }

    updateProgress(operationId, 3, 'Refreshing dashboard data...', 75);
    await fetchDashboardData();

    updateProgress(operationId, 4, 'Action completed successfully', 100);
    completeOperation(operationId, 'success', `${actionNames[action]} completed and dashboard refreshed`);
  } catch (err) {
    console.error('Error handling action:', err);
    completeOperation(operationId, 'error', `Failed to process ${action}: ${err.message}`);
    error('Failed to process the action');
  }
}

onMounted(() => {
  fetchDashboardData();
});
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
