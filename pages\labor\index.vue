<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Labor Management System</h1>
            <p class="mt-1 text-sm text-gray-500">
              Manage labor profiles, attendance, and payments
            </p>
          </div>
          <div class="flex space-x-3">
            <button
              @click="openConfigModal"
              class="btn btn-secondary"
            >
              <Icon name="heroicons:cog-6-tooth" class="w-5 h-5 mr-2" />
              Configure Supabase
            </button>
            <button
              v-if="isConfigured"
              @click="refreshData"
              :disabled="loading"
              class="btn btn-primary"
            >
              <Icon 
                name="heroicons:arrow-path" 
                :class="loading ? 'animate-spin' : ''"
                class="w-5 h-5 mr-2" 
              />
              Refresh
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Configuration Status -->
      <div class="mb-8">
        <div v-if="!isConfigured" class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
          <div class="flex items-center">
            <Icon name="heroicons:exclamation-triangle" class="w-6 h-6 text-yellow-600 mr-3" />
            <div>
              <h3 class="text-lg font-medium text-yellow-800">Supabase Configuration Required</h3>
              <p class="mt-1 text-sm text-yellow-700">
                Please configure your Supabase connection to start using the labor management system.
              </p>
              <button
                @click="openConfigModal"
                class="mt-3 btn btn-primary"
              >
                Configure Now
              </button>
            </div>
          </div>
        </div>

        <div v-else class="bg-green-50 border border-green-200 rounded-lg p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <Icon name="heroicons:check-circle" class="w-6 h-6 text-green-600 mr-3" />
              <div>
                <h3 class="text-lg font-medium text-green-800">Supabase Connected</h3>
                <p class="mt-1 text-sm text-green-700">
                  Configuration: {{ currentConfig?.configName }}
                </p>
                <p class="text-sm text-green-700">
                  URL: {{ currentConfig?.supabaseUrl }}
                </p>
              </div>
            </div>
            <button
              @click="openConfigModal"
              class="btn btn-secondary text-sm"
            >
              <Icon name="heroicons:pencil" class="w-4 h-4 mr-2" />
              Edit Config
            </button>
          </div>
        </div>
      </div>

      <!-- Feature Cards -->
      <div v-if="isConfigured" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Labor Profiles -->
        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="heroicons:users" class="w-8 h-8 text-blue-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Labor Profiles</h3>
              <p class="text-sm text-gray-500">Manage worker information</p>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-2xl font-bold text-blue-600">{{ stats.totalLabor || 0 }}</div>
            <div class="text-sm text-gray-500">Total Workers</div>
          </div>
        </div>

        <!-- Attendance -->
        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="heroicons:calendar-days" class="w-8 h-8 text-green-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Attendance</h3>
              <p class="text-sm text-gray-500">Track daily attendance</p>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-2xl font-bold text-green-600">{{ stats.todayAttendance || 0 }}</div>
            <div class="text-sm text-gray-500">Present Today</div>
          </div>
        </div>

        <!-- Payments -->
        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="heroicons:banknotes" class="w-8 h-8 text-amber-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Payments</h3>
              <p class="text-sm text-gray-500">Manage payments</p>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-2xl font-bold text-amber-600">₹{{ formatCurrency(stats.totalPayments || 0) }}</div>
            <div class="text-sm text-gray-500">This Month</div>
          </div>
        </div>

        <!-- Groups -->
        <div class="bg-white rounded-lg shadow p-6 hover:shadow-lg transition-shadow cursor-pointer">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <Icon name="heroicons:user-group" class="w-8 h-8 text-purple-600" />
            </div>
            <div class="ml-4">
              <h3 class="text-lg font-medium text-gray-900">Groups</h3>
              <p class="text-sm text-gray-500">Organize workers</p>
            </div>
          </div>
          <div class="mt-4">
            <div class="text-2xl font-bold text-purple-600">{{ stats.totalGroups || 0 }}</div>
            <div class="text-sm text-gray-500">Active Groups</div>
          </div>
        </div>
      </div>

      <!-- Quick Actions -->
      <div v-if="isConfigured" class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <button @click="openLaborProfileModal()" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Icon name="heroicons:plus-circle" class="w-6 h-6 text-blue-600 mr-3" />
              <div class="text-left">
                <div class="font-medium text-gray-900">Add Labor Profile</div>
                <div class="text-sm text-gray-500">Register new worker</div>
              </div>
            </button>
            
            <button @click="openGroupManagementModal()" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Icon name="heroicons:user-group" class="w-6 h-6 text-purple-600 mr-3" />
              <div class="text-left">
                <div class="font-medium text-gray-900">Manage Groups</div>
                <div class="text-sm text-gray-500">Create and edit groups</div>
              </div>
            </button>
            
            <NuxtLink to="/labor/attendance" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Icon name="heroicons:clipboard-document-check" class="w-6 h-6 text-green-600 mr-3" />
              <div class="text-left">
                <div class="font-medium text-gray-900">Mark Attendance</div>
                <div class="text-sm text-gray-500">Record daily attendance</div>
              </div>
            </NuxtLink>

            <NuxtLink to="/labor/payments" class="flex items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Icon name="heroicons:clipboard-document-check" class="w-6 h-6 text-green-600 mr-3" />
              <div class="text-left">
                <div class="font-medium text-gray-900">Payments</div>
                <div class="text-sm text-gray-500">Make Payments</div>
              </div>
            </NuxtLink>
          </div>
        </div>
      </div>
      
      <!-- Labor Profile Table -->
      <div v-if="isConfigured" class="mt-8">
        <LaborProfileTable
          :firm-id="firmId"
          :groups="groups"
          @add-profile="openLaborProfileModal()"
          @edit-profile="openLaborProfileModal"
          @edit-group="openGroupManagementModal"
          :key="componentKey"
        />
      </div>
    </div>

    <!-- Supabase Configuration Modal -->
    <SupabaseConfigModal
      :is-open="showConfigModal"
      :firm-id="firmId"
      :user-id="userId"
      @close="closeConfigModal"
      @saved="onConfigSaved"
    />

    <!-- Labor Profile Modal -->
    <LaborProfileModal
      :is-open="showLaborProfileModal"
      :firm-id="firmId"
      :user-id="userId"
      :groups="groups"
      :profile="selectedProfile"
      @close="closeLaborProfileModal"
      @saved="onProfileSaved"
      @group-created="onGroupCreated"
    />

    <!-- Group Management Modal -->
    <GroupManagementModal
      :is-open="showGroupManagementModal"
      :firm-id="firmId"
      :user-id="userId"
      :group="selectedGroupForEdit"
      @close="closeGroupManagementModal"
      @saved="onGroupSaved"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import SupabaseConfigModal from '~/components/labor/SupabaseConfigModal.vue'
import LaborProfileModal from '~/components/labor/LaborProfileModal.vue'
import GroupManagementModal from '~/components/labor/GroupManagementModal.vue'
import LaborProfileTable from '~/components/labor/LaborProfileTable.vue'

// Page metadata
definePageMeta({
  title: 'Labor Management System',
  layout: 'default'
})

// State
const showConfigModal = ref(false)
const showLaborProfileModal = ref(false)
const showGroupManagementModal = ref(false)
const currentConfig = ref(null)
const loading = ref(false)
const stats = ref({
  totalLabor: 0,
  todayAttendance: 0,
  totalPayments: 0,
  totalGroups: 0
})
const groups = ref([])
const profiles = ref([])
const selectedProfile = ref(null)
const selectedGroupForEdit = ref(null)
const componentKey = ref(0)

// Mock data for now - replace with actual user/firm data
const firmId = ref('507f1f77bcf86cd799439011') // Replace with actual firm ID
const userId = ref('507f1f77bcf86cd799439012') // Replace with actual user ID

// Computed
const isConfigured = computed(() => {
  return currentConfig.value && currentConfig.value.testConnection?.status === 'success'
})

// Methods
const openConfigModal = () => {
  showConfigModal.value = true
}

const closeConfigModal = () => {
  showConfigModal.value = false
}

const onConfigSaved = (config) => {
  currentConfig.value = config
  loadStats()
  loadGroups()
}

const openLaborProfileModal = (profile = null) => {
  selectedProfile.value = profile
  showLaborProfileModal.value = true
}

const closeLaborProfileModal = () => {
  showLaborProfileModal.value = false
  selectedProfile.value = null
}

const onProfileSaved = () => {
  refreshData()
}

const onGroupSaved = () => {
  loadGroups()
}

const openGroupManagementModal = (group = null) => {
    selectedGroupForEdit.value = group
    showGroupManagementModal.value = true
}

const closeGroupManagementModal = () => {
    showGroupManagementModal.value = false
    selectedGroupForEdit.value = null
}

const onGroupCreated = (group) => {
  groups.value.push(group)
}

const loadGroups = async () => {
  console.log('loadGroups called')
  if (!isConfigured.value) {
    console.log('isConfigured is false, not loading groups')
    return
  }
  try {
    console.log('Fetching groups from API...')
    const response = await $fetch('/api/labor/groups', {
      query: { firmId: firmId.value }
    })
    console.log('API response for groups:', response)
    if (response.success) {
      console.log('Groups loaded from API:', response.data)
      groups.value = response.data
      forceRerender()
    }
  } catch (error) {
    console.error('Error loading groups:', error)
  }
}

const forceRerender = () => {
  componentKey.value += 1
}

const loadCurrentConfig = async () => {
  try {
    loading.value = true
    const response = await $fetch('/api/labor/config/supabase', {
      query: { firmId: firmId.value, userId: userId.value }
    })

    if (response.success && response.data) {
      currentConfig.value = response.data
    }
  } catch (error) {
    console.error('Error loading config:', error)
  } finally {
    loading.value = false
  }
}

const loadStats = async () => {
  if (!isConfigured.value) return
  try {
    const response = await $fetch('/api/labor/stats', {
        query: { firmId: firmId.value }
    })
    if (response.success) {
        stats.value = response.data
    }
  } catch (error) {
    console.error('Error loading stats:', error)
  }
}

const refreshData = async () => {
  loading.value = true
  await Promise.all([
    loadCurrentConfig(),
    loadStats(),
    loadGroups()
  ])
  loading.value = false
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN').format(amount)
}

// Lifecycle
onMounted(() => {
  loadCurrentConfig()
})

watch(isConfigured, (newValue) => {
  if (newValue) {
    loadGroups()
    loadStats()
  }
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>