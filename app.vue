<template>
  <div class="min-h-screen flex flex-col">
    <!-- Fixed Navbar at the top -->
    <Navbar class="fixed top-0 left-0 w-full z-50" />

    <!-- Global Calculator Modal -->
    <CalculatorModal :is-open="isCalculatorOpen" @close="isCalculatorOpen = false" />

    <!-- Global PDF Tools Modal -->
    <PdfToolsModal :is-open="isPdfToolsOpen" @close="isPdfToolsOpen = false" />

    <!-- Global Task Manager Modal -->
    <TaskManagerModal :is-open="isTaskManagerOpen" @close="isTaskManagerOpen = false" />

    <!-- Global Todo List Modal -->
    <TodoListModal :is-open="isTodoListOpen" @close="isTodoListOpen = false" />

    <!-- Global Weather Modal -->
    <WeatherModal :is-open="isWeatherOpen" @close="isWeatherOpen = false" />

    <!-- Global Translator Modal -->
    <TranslatorModal :is-open="isTranslatorOpen" @close="isTranslatorOpen = false" />

    <!-- Global Text to Image Modal -->
    <TextToImageModal :is-open="isTextToImageOpen" @close="isTextToImageOpen = false" />

    <!-- Global Settings Popup -->
    <GlobalSettingsPopup ref="settingsPopup" />

    <!-- Global Toast Notifications -->
    <Toast />

    <!-- Global Help Modal -->
    <HelpModal :is-open="isHelpModalOpen" :help-topic="currentHelpTopic" @close="closeHelp" />

    <div class="flex flex-1">
      <!-- Sidebar -->
      <aside :class="[
        'fixed top-0 bottom-0 left-0 z-40 bg-gradient-to-b from-green-400 via-blue-500 to-purple-500 shadow-lg transition-all duration-300',
        isSidebarCollapsed ? 'w-16' : 'w-60'
      ]" aria-label="Sidebar">
        <!-- Sidebar Content: Adding padding to adjust links below the navbar -->
        <div class="pt-16 flex flex-col items-center md:items-start space-y-6 px-3">
          <!-- Loop through navigation links -->
          <template v-for="nav in visibleNavLinks" :key="nav.label">
            <!-- Regular links without children -->
            <NuxtLink v-if="!nav.children" :to="nav.to"
              class="flex items-center text-white hover:text-blue-200 transition duration-300 group w-full"
              :title="isSidebarCollapsed ? nav.label : ''">
              <!-- Icon -->
              <div class="w-8 h-8 flex items-center justify-center text-xl">
                <component :is="nav.icon" class="w-6 h-6 text-white" />
              </div>
              <!-- Label -->
              <span v-if="!isSidebarCollapsed" class="ml-3 text-sm font-medium group-hover:underline">
                {{ nav.label }}
              </span>
            </NuxtLink>
            <!-- Links with children (nested navigation) -->
            <div v-else class="w-full">
              <!-- Parent item -->
              <div class="flex items-center text-white hover:text-blue-200 transition duration-300 group w-full cursor-pointer"
                @click="toggleNestedMenu(nav.label)"
                :title="isSidebarCollapsed ? nav.label : ''">
                <div class="w-8 h-8 flex items-center justify-center text-xl">
                  <component :is="nav.icon" class="w-6 h-6 text-white" />
                </div>
                <span v-if="!isSidebarCollapsed" class="ml-3 text-sm font-medium group-hover:underline flex-grow">
                  {{ nav.label }}
                </span>
                <ChevronDownIcon v-if="!isSidebarCollapsed" class="h-4 w-4 transition-transform" :class="{ 'rotate-180': openNestedMenus.includes(nav.label) }" />
              </div>
              <!-- Nested items -->
              <div v-if="openNestedMenus.includes(nav.label)"
                :class="[isSidebarCollapsed ? 'ml-2' : 'ml-8', 'space-y-2 mt-2', !isSidebarCollapsed && 'border-l-2 border-white/20 pl-2']">
                <NuxtLink v-for="child in nav.children" :key="child.label" :to="child.to"
                  class="flex items-center text-white/80 hover:text-blue-200 transition duration-300 group"
                  :title="isSidebarCollapsed ? child.label : ''">
                  <div class="w-6 h-6 flex items-center justify-center">
                    <component :is="child.icon" class="w-4 h-4 text-white" />
                  </div>
                  <span v-if="!isSidebarCollapsed" class="text-sm font-medium group-hover:underline ml-2">{{ child.label }}</span>
                </NuxtLink>
              </div>
            </div>
          </template>
        </div>

        <!-- Collapse/Expand Button -->
        <div
          class="absolute top-1/2 -right-4 transform -translate-y-1/2 bg-white p-1 rounded-full cursor-pointer shadow-lg transition-transform duration-300"
          :class="{ 'rotate-180': !isSidebarCollapsed }" @click="toggleSidebar">
          <ChevronRightIcon class="w-6 h-6" />
        </div>
      </aside>

      <!-- Main content area -->
      <main :class="[isSidebarCollapsed ? 'ml-16' : 'ml-60']" class="flex-1 pt-20 pb-20">
        <!-- Nuxt will render your page component here -->
        <NuxtPage />
      </main>
    </div>

    <!-- Fixed Footer at the bottom -->
    <Footer class="fixed bottom-0 left-0 w-full z-50" />
  </div>
</template>

<script setup>
import Navbar from '~/components/Navbar.vue'
import Footer from '~/components/Footer.vue'
import CalculatorModal from '~/components/CalculatorModal.vue'
import PdfToolsModal from '~/components/PdfToolsModal.vue'
import TaskManagerModal from '~/components/TaskManagerModal.vue'
import TodoListModal from '~/components/TodoListModal.vue'
import WeatherModal from '~/components/WeatherModal.vue'
import TranslatorModal from '~/components/TranslatorModal.vue'
import TextToImageModal from '~/components/TextToImageModal.vue'
import Toast from '~/components/ui/Toast.vue'
import GlobalSettingsPopup from '~/components/GlobalSettingsPopup.vue'
import HelpModal from '~/components/HelpModal.vue'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from '#app'
import { useHelpModal } from '~/composables/ui/useHelpModal'
import { HomeIcon, InformationCircleIcon, PhoneIcon, UserIcon, DocumentTextIcon, ChevronRightIcon, ChevronDownIcon, BanknotesIcon, ChartBarIcon, PencilSquareIcon, ClipboardDocumentListIcon, CurrencyRupeeIcon, UserGroupIcon, DocumentChartBarIcon } from '@heroicons/vue/24/outline'
import useUserRole from '~/composables/auth/useUserRole'
import { showAccessDenied } from '~/utils/accessDenied'

// State for modals
const isCalculatorOpen = ref(false)
const isPdfToolsOpen = ref(false)
const isTaskManagerOpen = ref(false)
const isTodoListOpen = ref(false)
const isWeatherOpen = ref(false)
const isTranslatorOpen = ref(false)
const isTextToImageOpen = ref(false)

// Reference to the settings popup
const settingsPopup = ref(null)

// Help modal state
const { isHelpModalOpen, currentHelpTopic, closeHelp } = useHelpModal()

// Listen for calculator open event and keyboard shortcuts
onMounted(() => {
  // Calculator event listener
  window.addEventListener('open-calculator', () => {
    isCalculatorOpen.value = true
  })

  // PDF Tools event listener
  window.addEventListener('open-pdf-tools', () => {
    isPdfToolsOpen.value = true
  })

  // Task Manager event listener
  window.addEventListener('open-task-manager', () => {
    isTaskManagerOpen.value = true
  })

  // Todo List event listener
  window.addEventListener('open-todo-list', () => {
    isTodoListOpen.value = true
  })

  // Weather event listener
  window.addEventListener('open-weather', () => {
    isWeatherOpen.value = true
  })

  // Translator event listener
  window.addEventListener('open-translator', () => {
    isTranslatorOpen.value = true
  })

  // Text to Image event listener
  window.addEventListener('open-text-to-image', () => {
    isTextToImageOpen.value = true
  })

  // Settings event listener
  window.addEventListener('open-settings', () => {
    // Close calculator if it's open
    if (isCalculatorOpen.value) {
      isCalculatorOpen.value = false
    }

    // Open settings popup
    if (settingsPopup.value) {
      settingsPopup.value.openPopup()
    }
  })

  // Settings with Notes tab event listener
  window.addEventListener('open-settings-notes', () => {
    // Close calculator if it's open
    if (isCalculatorOpen.value) {
      isCalculatorOpen.value = false
    }

    // Open settings popup with Notes tab
    if (settingsPopup.value) {
      settingsPopup.value.openPopup()
      // Set the active tab to notes (this will be handled in the GlobalSettingsPopup component)
      setTimeout(() => {
        window.dispatchEvent(new CustomEvent('set-settings-tab', { detail: 'notes' }))
      }, 100)
    }
  })

  // Global keyboard shortcut handler
  window.addEventListener('keydown', handleGlobalKeyDown)
})

onUnmounted(() => {
  // Remove calculator event listener
  window.removeEventListener('open-calculator', () => {
    isCalculatorOpen.value = true
  })

  // Remove PDF Tools event listener
  window.removeEventListener('open-pdf-tools', () => {
    isPdfToolsOpen.value = true
  })

  // Remove Task Manager event listener
  window.removeEventListener('open-task-manager', () => {
    isTaskManagerOpen.value = true
  })

  // Remove Todo List event listener
  window.removeEventListener('open-todo-list', () => {
    isTodoListOpen.value = true
  })

  // Remove Weather event listener
  window.removeEventListener('open-weather', () => {
    isWeatherOpen.value = true
  })

  // Remove Translator event listener
  window.removeEventListener('open-translator', () => {
    isTranslatorOpen.value = true
  })

  // Remove Text to Image event listener
  window.removeEventListener('open-text-to-image', () => {
    isTextToImageOpen.value = true
  })

  // Remove settings event listener
  window.removeEventListener('open-settings', () => {
    if (settingsPopup.value) {
      settingsPopup.value.openPopup()
    }
  })

  // Remove settings with notes tab event listener
  window.removeEventListener('open-settings-notes', () => {
    if (settingsPopup.value) {
      settingsPopup.value.openPopup()
    }
  })

  // Remove keyboard shortcut handler
  window.removeEventListener('keydown', handleGlobalKeyDown)
})

// Global keyboard shortcut handler
function handleGlobalKeyDown(event) {
  // Ctrl + , (comma) to open settings (available to all users)
  if (event.ctrlKey && event.key === ',') {
    event.preventDefault()

    // Close calculator if it's open
    if (isCalculatorOpen.value) {
      isCalculatorOpen.value = false
    }

    // Open settings popup
    if (settingsPopup.value) {
      settingsPopup.value.openPopup()
    }
  }



  // Only process navigation shortcuts if user is authenticated
  if (!isAuthenticated.value) return;

  // Ctrl + D to navigate to Dashboard (available to all authenticated users)
  if (event.ctrlKey && !event.shiftKey && event.key === 'd') {
    event.preventDefault()
    router.push('/')
  }

  // Ctrl + A to navigate to AI Assistant (available to all authenticated users)
  if (event.ctrlKey && !event.shiftKey && event.key === 'a') {
    event.preventDefault()
    router.push('/ai')
  }

  // Ctrl + Shift + A to navigate to Admin panel (ONLY for admin users)
  if (event.ctrlKey && event.shiftKey && event.key === 'A') {
    event.preventDefault()
    // Check if user has admin privileges before navigating
    if (canAccessAdmin.value) {
      router.push('/admin')
    } else {
      // Show an error message using the standardized access denied utility
      showAccessDenied('Access denied: Admin privileges required')
    }
  }
}

// Sidebar collapse state (default to collapsed)
const isSidebarCollapsed = ref(true)

// Toggle the sidebar between expanded and collapsed
function toggleSidebar() {
  isSidebarCollapsed.value = !isSidebarCollapsed.value
}

// Authentication state
const tokenCookie = useCookie('token')

// isAuthenticated returns true if a token exists.
const isAuthenticated = computed(() => Boolean(tokenCookie.value))

// Get router instance
const router = useRouter()

// Get user role information
const { isSubContractor, isAdmin, hasRolePrivilege, ROLES } = useUserRole()
const canAccessAdmin = computed(() => hasRolePrivilege(ROLES.ADMIN))
const isSubContractorUser = computed(() => isSubContractor())

// Navigation state
const openNestedMenus = ref([])

// Toggle nested menu
function toggleNestedMenu(label) {
  const index = openNestedMenus.value.indexOf(label)
  if (index === -1) {
    openNestedMenus.value.push(label)
  } else {
    openNestedMenus.value.splice(index, 1)
  }
}



// Navigation links with SVG icons
const navLinks = [
  {
    label: 'Home',
    to: '/',
    icon: HomeIcon,
    restricted: false
  },
  {
    label: 'About',
    to: '/about',
    icon: InformationCircleIcon,
    restricted: false
  },
  {
    label: 'Contact',
    to: '/contact',
    icon: PhoneIcon,
    restricted: false
  },
  {
    label: 'Profile',
    to: '/dashboard',
    icon: UserIcon,
    restricted: true
  },


  {
    label: 'Docs',
    to: '/documents',
    icon: DocumentTextIcon,
    restricted: true
  },
  {
    label: 'Financial Management',
    icon: BanknotesIcon,
    restricted: true,
    children: [
      {
        label: 'Financial Dashboard',
        to: '/expenses',
        icon: ChartBarIcon
      },
      {
        label: 'Transactions',
        to: '/expenses/list',
        icon: ClipboardDocumentListIcon
      },
      {
        label: 'Cash & Bank Accounts',
        to: '/expenses/ledgers',
        icon: BanknotesIcon
      },
      {
        label: 'Subcontractor Accounts',
        to: '/expenses/subs',
        icon: UserIcon
      },
      {
        label: 'Reports & Analytics',
        to: '/expenses/reports',
        icon: DocumentTextIcon
      }
    ]
  },
  {
    label: 'Employee Wages',
    icon: CurrencyRupeeIcon,
    restricted: true,
    children: [
      {
        label: 'Dashboard',
        to: '/wages/dashboard',
        icon: ChartBarIcon
      },
      {
        label: 'Employee Wages',
        to: '/wages',
        icon: CurrencyRupeeIcon
      },
      {
        label: 'Master Roll',
        to: '/wages/master_roll',
        icon: UserGroupIcon
      },
      {
        label: 'Employee Advances',
        to: '/wages/employee-advances',
        icon: BanknotesIcon
      },
      {
        label: 'Edit Wages',
        to: '/wages/edit',
        icon: PencilSquareIcon
      },
      {
        label: 'Reports & Analytics',
        to: '/wages/report',
        icon: DocumentChartBarIcon
      }
    ]
  },
  {
    label: 'Inventory',
    icon: ClipboardDocumentListIcon,
    restricted: true,
    children: [
      {
        label: 'Dashboard',
        to: '/inventory/dashboard',
        icon: HomeIcon
      },
      {
        label: 'Inventory Management',
        to: '/inventory/edit-bill',
        icon: ClipboardDocumentListIcon
      },
      {
        label: 'Bills',
        to: '/inventory/bills',
        icon: DocumentTextIcon
      },
      {
        label: 'Stock Report',
        to: '/inventory/stock-report',
        icon: ChartBarIcon
      }
    ]
  }
]

// Create a special My Account link for sub-contractors
const myAccountLink = {
  label: 'My Account',
  to: '/expenses/subs',
  icon: UserIcon,
  restricted: true,
  forSubContractorOnly: true
}

// Add property to mark links that should be hidden for sub-contractors
navLinks.forEach(nav => {
  // Hide the entire Financial Management section for sub-contractors
  // They'll access Subcontractor Accounts through the dedicated My Account link
  if (nav.label === 'Financial Management') {
    nav.hideForSubContractor = true
    nav.children.forEach(child => {
      child.hideForSubContractor = true
    })
  }

  // Hide Employee Wages, Inventory, and Docs for sub-contractors
  if (['Employee Wages', 'Inventory', 'Docs'].includes(nav.label)) {
    nav.hideForSubContractor = true
    if (nav.children) {
      nav.children.forEach(child => {
        child.hideForSubContractor = true
      })
    }
  }
})

// Filter navigation links based on authentication state and user role
const visibleNavLinks = computed(() => {

  // First filter by authentication
  const authFiltered = navLinks.filter(nav => (nav.restricted ? isAuthenticated.value : true))

  // Then filter by sub-contractor role if applicable
  if (isSubContractorUser.value) {
    // For sub-contractors, add the special My Account link
    const subContractorLinks = [...authFiltered, myAccountLink]

    // For sub-contractors, filter out links marked as hidden for them
    return subContractorLinks.filter(nav => {
      // Always include links specifically for sub-contractors
      if (nav.forSubContractorOnly) return true
      // If the nav item itself is hidden for sub-contractors, filter it out
      if (nav.hideForSubContractor) return false

      // If it has children, we need to check if any children are visible
      if (nav.children) {
        // Filter the children
        const visibleChildren = nav.children.filter(child => !child.hideForSubContractor)

        // If there are no visible children, hide the parent
        if (visibleChildren.length === 0) return false

        // Otherwise, replace the children array with only the visible ones
        nav.children = visibleChildren
      }

      return true
    })
  }

  // For non-sub-contractors, return all authenticated links
  const result = authFiltered



  return result
})
</script>

<style>
/* Additional styles for better sidebar and content alignment */
body {
  margin: 0;
  /* Remove default margin for better alignment */
}

main {
  transition: margin-left 0.3s ease;
  /* Smooth transition for content area */
}

/* Card styling improvements */
.float {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(209, 213, 219, 0.5);
}

.float:hover {
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.15);
  transform: translateY(-5px);
}

/* Add a subtle shine effect to cards */
.bg-gradient-animate {
  background-size: 200% 200%;
  animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
</style>

