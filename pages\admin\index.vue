<template>
  <div>
    <!-- Main Tabs -->
    <div class="mb-6 border-b">
      <div class="flex">
        <button
          @click="setActiveTab('dashboard')"
          :class="[activeMainTab === 'dashboard' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
        >
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard
          </div>
        </button>
        <button
          @click="setActiveTab('management')"
          :class="[activeMainTab === 'management' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
        >
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
            Management
          </div>
        </button>
        <button
          @click="setActiveTab('database')"
          :class="[activeMainTab === 'database' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
        >
          <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4" />
            </svg>
            Database
          </div>
        </button>
      </div>
    </div>

    <!-- Dashboard Tab Content -->
    <div v-if="activeMainTab === 'dashboard'" class="fade-in">
      <!-- Real-Time Status Monitor -->
      <RealTimeStatus />

      <DashboardOverview />
    </div>

    <!-- Management Tab Content -->
    <div v-if="activeMainTab === 'management'" class="fade-in">
      <!-- Real-Time Status Monitor -->
      <RealTimeStatus />

      <!-- Sub Tabs for Management -->
      <div class="mb-6 border-b pl-4">
        <div class="flex">
          <button
            @click="setActiveSubTab('management', 'firms')"
            :class="[activeManagementTab === 'firms' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
          >
            Manage Firms
          </button>
          <button
            @click="setActiveSubTab('management', 'users')"
            :class="[activeManagementTab === 'users' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
          >
            Manage Users
          </button>
          <button
            @click="setActiveSubTab('management', 'codes')"
            :class="[activeManagementTab === 'codes' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
          >
            Manager Codes
          </button>
        </div>
      </div>

    <!-- Firms Management Tab -->
    <div v-if="activeManagementTab === 'firms'" class="space-y-6 fade-in">
      <div class="flex justify-between items-center">
        <h2 class="text-2xl font-semibold">Firms</h2>
      </div>

      <!-- Pending Firms Section -->
      <div v-if="pendingFirms.length > 0" class="bg-yellow-50 p-4 rounded-lg border border-yellow-200 mb-6">
        <h3 class="text-lg font-semibold text-yellow-800 mb-2">Pending Approval ({{ pendingFirms.length }})</h3>
        <div class="overflow-x-auto">
          <table class="min-w-full bg-white">
            <thead class="bg-gray-100">
              <tr>
                <th class="py-2 px-4 text-left">Name</th>
                <th class="py-2 px-4 text-left">Code</th>
                <th class="py-2 px-4 text-left">Description</th>
                <th class="py-2 px-4 text-left">Created</th>
                <th class="py-2 px-4 text-left">Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="firm in pendingFirms" :key="firm._id" class="border-t">
                <td class="py-2 px-4">{{ firm.name }}</td>
                <td class="py-2 px-4">{{ firm.code }}</td>
                <td class="py-2 px-4">{{ firm.description || '-' }}</td>
                <td class="py-2 px-4">{{ formatDate(firm.createdAt) }}</td>
                <td class="py-2 px-4 flex space-x-2">
                  <button @click="approveFirm(firm._id)" class="bg-green-500 text-white px-2 py-1 rounded text-sm hover:bg-green-600">
                    Approve
                  </button>
                  <button @click="rejectFirm(firm._id)" class="bg-red-500 text-white px-2 py-1 rounded text-sm hover:bg-red-600">
                    Reject
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- All Firms List -->
      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead class="bg-gray-100">
            <tr>
              <th class="py-2 px-4 text-left">Name</th>
              <th class="py-2 px-4 text-left">Code</th>
              <th class="py-2 px-4 text-left">Status</th>
              <th class="py-2 px-4 text-left">Description</th>
              <th class="py-2 px-4 text-left">Created</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="firm in firms" :key="firm._id" class="border-t">
              <td class="py-2 px-4">{{ firm.name }}</td>
              <td class="py-2 px-4">{{ firm.code }}</td>
              <td class="py-2 px-4">
                <span
                  :class="{
                    'bg-green-100 text-green-800': firm.status === 'approved',
                    'bg-yellow-100 text-yellow-800': firm.status === 'pending',
                    'bg-red-100 text-red-800': firm.status === 'rejected'
                  }"
                  class="px-2 py-1 rounded-full text-xs font-medium"
                >
                  {{ firm.status }}
                </span>
              </td>
              <td class="py-2 px-4">{{ firm.description || '-' }}</td>
              <td class="py-2 px-4">{{ formatDate(firm.createdAt) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Users Management Tab -->
    <div v-if="activeManagementTab === 'users'" class="fade-in">
      <!-- ... existing users management code ... -->
      <div class="flex justify-between items-center">
        <h2 class="text-2xl font-semibold">Users</h2>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead class="bg-gray-100">
            <tr>
              <th class="py-2 px-4 text-left">Username</th>
              <th class="py-2 px-4 text-left">Full Name</th>
              <th class="py-2 px-4 text-left">Email</th>
              <th class="py-2 px-4 text-left">Role</th>
              <th class="py-2 px-4 text-left">Firm</th>
              <th class="py-2 px-4 text-left">Status</th>
              <th class="py-2 px-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="user in users" :key="user._id" class="border-t">
              <td class="py-2 px-4">{{ user.username }}</td>
              <td class="py-2 px-4">{{ user.fullname }}</td>
              <td class="py-2 px-4">{{ user.email }}</td>
              <td class="py-2 px-4">
                <span
                  :class="{
                    'bg-blue-100 text-blue-800': user.role === 'admin',
                    'bg-green-100 text-green-800': user.role === 'manager',
                    'bg-gray-100 text-gray-800': user.role === 'user'
                  }"
                  class="px-2 py-1 rounded-full text-xs font-medium"
                >
                  {{ user.role }}
                </span>
              </td>
              <td class="py-2 px-4">{{ getUserFirmName(user.firmId) }}</td>
              <td class="py-2 px-4">
                <span
                  :class="{
                    'bg-green-100 text-green-800': user.status === 'Approved',
                    'bg-yellow-100 text-yellow-800': user.status === 'Pending',
                    'bg-red-100 text-red-800': user.status === 'Rejected'
                  }"
                  class="px-2 py-1 rounded-full text-xs font-medium"
                >
                  {{ user.status }}
                </span>
              </td>
              <td class="py-2 px-4 flex space-x-2">
                <button
                  @click="editUser(user)"
                  class="bg-blue-500 text-white px-2 py-1 rounded text-sm hover:bg-blue-600"
                >
                  Edit
                </button>
                <button
                  @click="deleteUser(user._id)"
                  class="bg-red-500 text-white px-2 py-1 rounded text-sm hover:bg-red-600"
                >
                  Delete
                </button>
                <!-- Only show approve/reject buttons for managers -->
                <template v-if="user.role === 'manager' && user.status === 'Pending'">
                  <button
                    @click="approveUser(user._id)"
                    class="bg-green-500 text-white px-2 py-1 rounded text-sm hover:bg-green-600"
                  >
                    Approve
                  </button>
                  <button
                    @click="rejectUser(user._id)"
                    class="bg-red-500 text-white px-2 py-1 rounded text-sm hover:bg-red-600"
                  >
                    Reject
                  </button>
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Manager Codes Tab -->
    <div v-if="activeManagementTab === 'codes'" class="fade-in">
      <div class="space-y-6">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold">Manager Codes</h2>
        <button
          @click="generateManagerCodes(5)"
          class="bg-indigo-600 text-white px-4 py-2 rounded-lg hover:bg-indigo-700 transition-colors"
        >
          Generate 5 New Codes
        </button>
      </div>

      <div class="overflow-x-auto">
        <table class="min-w-full bg-white">
          <thead class="bg-gray-100">
            <tr>
              <th class="py-2 px-4 text-left">Code</th>
              <th class="py-2 px-4 text-left">Status</th>
              <th class="py-2 px-4 text-left">Used By</th>
              <th class="py-2 px-4 text-left">Used At</th>
              <th class="py-2 px-4 text-left">Created At</th>
              <th class="py-2 px-4 text-left">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="code in managerCodes" :key="code._id" class="border-t">
              <td class="py-2 px-4 font-mono">{{ code.code }}</td>
              <td class="py-2 px-4">
                <span
                  :class="{
                    'bg-green-100 text-green-800': !code.used,
                    'bg-gray-100 text-gray-800': code.used
                  }"
                  class="px-2 py-1 rounded-full text-xs font-medium"
                >
                  {{ code.used ? 'Used' : 'Available' }}
                </span>
              </td>
              <td class="py-2 px-4">{{ code.usedBy ? getUserName(code.usedBy) : '-' }}</td>
              <td class="py-2 px-4">{{ code.usedAt ? formatDate(code.usedAt) : '-' }}</td>
              <td class="py-2 px-4">{{ formatDate(code.createdAt) }}</td>
              <td class="py-2 px-4">
                <button
                  @click="deleteManagerCodes([code._id])"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
    </div>
    </div>

    <!-- Database Tab Content -->
    <div v-if="activeMainTab === 'database'" class="fade-in">
      <!-- Database Sub Tabs -->
      <div class="mb-6 border-b pl-4">
        <div class="flex">
          <button
            @click="setActiveSubTab('database', 'mongodb')"
            :class="[activeDatabaseTab === 'mongodb' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
          >
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
              </svg>
              MongoDB
            </div>
          </button>
          <button
            @click="setActiveSubTab('database', 'firestore')"
            :class="[activeDatabaseTab === 'firestore' ? 'border-b-2 border-indigo-600 text-indigo-600' : 'text-gray-500', 'px-4 py-2 font-medium transition-all duration-300 hover:text-indigo-500']"
          >
            <div class="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 14v6m-3-3h6M6 10h2a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2zm10 0h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2zM6 20h2a2 2 0 002-2v-2a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2z" />
              </svg>
              Firestore
            </div>
          </button>
        </div>
      </div>

      <!-- MongoDB Tab -->
      <div v-if="activeDatabaseTab === 'mongodb'" class="mb-6 fade-in">
        <h2 class="text-2xl font-semibold mb-4">MongoDB Models</h2>
        <p class="text-gray-600 mb-6">View and manage your MongoDB database models and their data.</p>

        <!-- Database Actions -->
        <div class="mb-6">
          <DatabaseActions
            :models="models"
            :firestore-collections="firestoreCollections"
          />
        </div>

        <!-- API Logs Auto Backup Status -->
        <div class="mb-6">
          <ApiLogsAutoBackup />
        </div>

        <!-- Real-Time Status Monitor -->
        <RealTimeStatus />

        <!-- Loading state -->
        <div v-if="loadingModels" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>

        <!-- Models Grid -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <DatabaseCard
            v-for="(model, index) in models"
            :key="model.name"
            :model-name="model.name"
            :count="model.count"
            :loading="false"
            @view-details="viewModelDetails(model.name)"
            class="transform transition-all duration-700 hover:scale-105 stagger-item"
            :style="{ animationDelay: `${index * 0.2}s` }"
          />
        </div>
      </div>

      <!-- Firestore Tab -->
      <div v-if="activeDatabaseTab === 'firestore'" class="mb-6 fade-in">
        <h2 class="text-2xl font-semibold mb-4">Firestore Collections</h2>
        <p class="text-gray-600 mb-6">View and manage your Firestore database collections and their data.</p>

        <!-- Database Actions -->
        <div class="mb-6">
          <DatabaseActions
            :models="models"
            :firestore-collections="firestoreCollections"
          />
        </div>

        <!-- Real-Time Status Monitor -->
        <RealTimeStatus />

        <!-- Loading State -->
        <div v-if="loadingFirestoreCollections" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>

        <!-- Collections Grid -->
        <div v-else-if="firestoreCollections.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <DatabaseCard
            v-for="(collection, index) in firestoreCollections"
            :key="collection.name"
            :model-name="collection.name"
            :count="collection.count"
            :loading="false"
            :is-firestore="true"
            @view-details="viewFirestoreDetails(collection.name)"
            class="transform transition-all duration-700 hover:scale-105 stagger-item"
            :style="{ animationDelay: `${index * 0.2}s` }"
          />
        </div>

        <!-- No Collections Message -->
        <div v-else class="bg-white p-6 rounded-lg shadow-md text-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <h3 class="text-lg font-medium text-gray-900 mb-2">No Firestore Collections Found</h3>
          <p class="text-gray-600">There are no Firestore collections available or you may not have access to view them.</p>
        </div>
      </div>
    </div>

    <!-- Model Details Modal -->
    <ModelDetailsModal
      :show="showModelDetailsModal"
      :model-name="selectedModel"
      :data="modelData"
      :schema="modelSchema"
      :loading="loadingModelDetails"
      :all-models="models"
      :is-firestore="isFirestore"
      @close="showModelDetailsModal = false"
    />

    <!-- Edit User Modal -->
    <div v-if="showEditUserModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4">
      <div class="bg-white rounded-lg w-full max-w-2xl p-6">
        <div class="flex justify-between items-center mb-4">
          <h3 class="text-xl font-semibold">Edit User</h3>
          <button @click="showEditUserModal = false" class="text-gray-500 hover:text-gray-700">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="updateUser" class="space-y-4">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Username</label>
              <input v-model="userForm.username" type="text" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
              <input v-model="userForm.fullname" type="text" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Email</label>
              <input v-model="userForm.email" type="email" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Role</label>
              <select v-model="userForm.role" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option value="user">User</option>
                <option value="manager">Manager</option>
                <option value="admin">Admin</option>
              </select>
            </div>
            <div class="md:col-span-2">
              <label class="block text-sm font-medium text-gray-700 mb-1">Firm</label>
              <select v-model="userForm.firmId" required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                <option value="" disabled>Select a firm</option>
                <option v-for="firm in firms" :key="firm._id" :value="firm._id">{{ firm.name }}</option>
              </select>
            </div>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" @click="showEditUserModal = false"
              class="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              Cancel
            </button>
            <button type="submit"
              class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
              Save Changes
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from '#app';
import useToast from '~/composables/ui/useToast';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { useRealTimeStatus } from '~/composables/utils/useRealTimeStatus';
import DatabaseCard from '~/components/admin/DatabaseCard.vue';
import ModelDetailsModal from '~/components/admin/ModelDetailsModal.vue';
import DatabaseActions from '~/components/admin/DatabaseActions.vue';
import DashboardOverview from '~/components/admin/DashboardOverview.vue';
import ApiLogsAutoBackup from '~/components/admin/ApiLogsAutoBackup.vue';
import RealTimeStatus from '~/components/admin/RealTimeStatus.vue';

definePageMeta({
  title: 'Admin Dashboard',
  description: 'Administrative dashboard for managing database, users, and system settings',
  layout: 'admin',
  middleware: ['admin-only']
});

// Get toast functions
const { success, error } = useToast();

// Get route for navigation and query parameters
const route = useRoute();

interface Firm {
  _id: string;
  name: string;
  code: string;
  description?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: string;
}

interface User {
  _id: string;
  username: string;
  fullname: string;
  email: string;
  role: 'user' | 'manager' | 'admin';
  firmId: string;
}

interface ManagerCode {
  _id: string;
  code: string;
  used: boolean;
  usedBy?: string;
  usedAt?: string;
  createdAt: string;
}

// Main tab navigation
const activeMainTab = ref('dashboard');

// Management sub-tabs
const activeManagementTab = ref('firms');

// Database sub-tabs
const activeDatabaseTab = ref('mongodb');

// MongoDB management
const models = ref([]);
const loadingModels = ref(false);

// Firestore management
const firestoreCollections = ref([]);
const loadingFirestoreCollections = ref(false);

// Model details modal state
const showModelDetailsModal = ref(false);
const selectedModel = ref('');
const modelData = ref([]);
const modelSchema = ref({});
const loadingModelDetails = ref(false);
const isFirestore = ref(false);

// Firms Management
const firms:any = ref<Firm[]>([]);
const pendingFirms = ref<Firm[]>([]);
const showCreateFirmModal = ref(false);
const showEditFirmModal = ref(false);
const firmForm = ref<Omit<Firm, '_id' | 'createdAt'> & { _id?: string }>({
  name: '',
  code: '',
  description: '',
  status: 'pending'
});

// Users Management
const users = ref<User[]>([]);
const showEditUserModal = ref(false);
const userForm = ref<Omit<User, '_id'> & { _id?: string }>({
  username: '',
  fullname: '',
  email: '',
  role: 'user',
  firmId: ''
});

// Manager Codes Management
const managerCodes = ref<ManagerCode[]>([]);

// Fetch Data Functions
async function fetchFirms() {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `fetch-firms-${Date.now()}`;

  startOperation(operationId, 'Loading Firms', 'Fetching firms data from database...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Requesting firms data...', 50);
    const data = await api.get('/api/firms');

    updateProgress(operationId, 3, 'Processing firms data...', 75);
    firms.value = data;
    pendingFirms.value = data.filter((firm:any) => firm.status === 'pending');

    updateProgress(operationId, 4, `Loaded ${data.length} firms (${pendingFirms.value.length} pending)`, 100);
    completeOperation(operationId, 'success', `Successfully loaded ${data.length} firms`);
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to fetch firms: ${err.message}`);
    error('Failed to fetch firms');
  }
}

async function fetchUsers() {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `fetch-users-${Date.now()}`;

  startOperation(operationId, 'Loading Users', 'Fetching users data from database...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Requesting users data...', 50);
    const data = await api.get('/api/users');

    updateProgress(operationId, 3, 'Processing users data...', 75);
    users.value = data.users;

    updateProgress(operationId, 4, `Loaded ${data.users.length} users`, 100);
    completeOperation(operationId, 'success', `Successfully loaded ${data.users.length} users`);
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to fetch users: ${err.message}`);
    error('Failed to fetch users');
  }
}

async function fetchManagerCodes() {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `fetch-manager-codes-${Date.now()}`;

  startOperation(operationId, 'Loading Manager Codes', 'Fetching manager codes from database...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Requesting manager codes...', 50);
    const data = await api.get('/api/manager-codes');

    updateProgress(operationId, 3, 'Processing codes data...', 75);
    managerCodes.value = data.codes;

    const usedCodes = data.codes.filter(code => code.used).length;
    const availableCodes = data.codes.length - usedCodes;

    updateProgress(operationId, 4, `Loaded ${data.codes.length} codes (${availableCodes} available)`, 100);
    completeOperation(operationId, 'success', `Successfully loaded ${data.codes.length} manager codes`);
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to fetch manager codes: ${err.message}`);
    error('Failed to fetch manager codes');
  }
}

// Handle URL query parameters
function handleQueryParams() {
  // Use the route reference defined above
  // Set active tabs based on URL query parameters
  if (route.query.tab) {
    activeMainTab.value = route.query.tab as string;
  }

  if (route.query.subtab) {
    if (activeMainTab.value === 'management') {
      activeManagementTab.value = route.query.subtab as string;
    } else if (activeMainTab.value === 'database') {
      activeDatabaseTab.value = route.query.subtab as string;
    }
  }
}

// Update active tab and URL
function setActiveTab(tab) {
  activeMainTab.value = tab;
  updateUrl(tab);
}

// Update active subtab and URL
function setActiveSubTab(mainTab, subTab) {
  if (mainTab === 'management') {
    activeManagementTab.value = subTab;
  } else if (mainTab === 'database') {
    activeDatabaseTab.value = subTab;
  }
  updateUrl(mainTab, subTab);
}

// Update URL with query parameters
function updateUrl(tab, subtab = null) {
  const router = useRouter();
  const query = { ...route.query, tab };

  if (subtab) {
    query.subtab = subtab;
  } else {
    // If no subtab is provided, remove it from the URL
    delete query.subtab;
  }

  // Update URL without reloading the page
  router.push({ query });
}

// Watch for route changes to update tabs
watch(
  () => route.query,
  () => {
    handleQueryParams();
  }
);

// Load initial data
onMounted(async () => {
  // Handle URL query parameters first
  handleQueryParams();

  // Then fetch data
  await Promise.all([
    fetchFirms(),
    fetchUsers(),
    fetchManagerCodes(),
    fetchModels(),
    fetchFirestoreCollections()
  ]);
});

// Firm Management Functions
async function createFirm() {
  try {
    const api = useApiWithAuth();
    await api.post('/api/firms', firmForm.value);
    success('Firm created successfully');
    showCreateFirmModal.value = false;
    await fetchFirms();
    firmForm.value = { _id: '', name: '', code: '', description: '', status: 'pending' };
  } catch (err) {
    error('Failed to create firm');
  }
}

async function updateFirm() {
  try {
    const api = useApiWithAuth();
    await api.put(`/api/firms/${firmForm.value._id}`, firmForm.value);
    success('Firm updated successfully');
    showEditFirmModal.value = false;
    await fetchFirms();
  } catch (err) {
    error('Failed to update firm');
  }
}

async function deleteFirm(id: string) {
  if (confirm('Are you sure you want to delete this firm?')) {
    try {
      const api = useApiWithAuth();
      await api.delete(`/api/firms/${id}`);
      success('Firm deleted successfully');
      await fetchFirms();
    } catch (err) {
      error('Failed to delete firm');
    }
  }
}

async function approveFirm(id: string) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `approve-firm-${id}-${Date.now()}`;

  startOperation(operationId, 'Approving Firm', 'Processing firm approval request...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Sending approval request...', 50);
    await api.post(`/api/firms/${id}/approve`);

    updateProgress(operationId, 3, 'Refreshing firms data...', 75);
    await fetchFirms();

    updateProgress(operationId, 4, 'Firm approved successfully', 100);
    completeOperation(operationId, 'success', 'Firm approved and data refreshed');
    success('Firm approved successfully');
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to approve firm: ${err.message}`);
    error('Failed to approve firm');
  }
}

async function rejectFirm(id: string) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `reject-firm-${id}-${Date.now()}`;

  startOperation(operationId, 'Rejecting Firm', 'Processing firm rejection request...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Sending rejection request...', 50);
    await api.post(`/api/firms/${id}/reject`);

    updateProgress(operationId, 3, 'Refreshing firms data...', 75);
    await fetchFirms();

    updateProgress(operationId, 4, 'Firm rejected successfully', 100);
    completeOperation(operationId, 'success', 'Firm rejected and data refreshed');
    success('Firm rejected successfully');
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to reject firm: ${err.message}`);
    error('Failed to reject firm');
  }
}

function editFirm(firm: Firm) {
  firmForm.value = { ...firm };
  showEditFirmModal.value = true;
}

// User Management Functions
async function updateUser() {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `update-user-${userForm.value._id}-${Date.now()}`;

  startOperation(operationId, 'Updating User', `Updating user: ${userForm.value.username}...`);

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Sending update request...', 50);
    await api.put(`/api/users/${userForm.value._id}`, userForm.value);

    updateProgress(operationId, 3, 'Refreshing users data...', 75);
    showEditUserModal.value = false;
    await fetchUsers();

    updateProgress(operationId, 4, 'User updated successfully', 100);
    completeOperation(operationId, 'success', `User ${userForm.value.username} updated successfully`);
    success('User updated successfully');
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to update user: ${err.message}`);
    error('Failed to update user');
  }
}

async function deleteUser(id: string) {
  if (confirm('Are you sure you want to delete this user?')) {
    const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
    const operationId = `delete-user-${id}-${Date.now()}`;

    startOperation(operationId, 'Deleting User', 'Processing user deletion request...');

    try {
      updateProgress(operationId, 1, 'Connecting to API...', 25);
      const api = useApiWithAuth();

      updateProgress(operationId, 2, 'Sending deletion request...', 50);
      await api.delete(`/api/users/${id}`);

      updateProgress(operationId, 3, 'Refreshing users data...', 75);
      await fetchUsers();

      updateProgress(operationId, 4, 'User deleted successfully', 100);
      completeOperation(operationId, 'success', 'User deleted and data refreshed');
      success('User deleted successfully');
    } catch (err) {
      completeOperation(operationId, 'error', `Failed to delete user: ${err.message}`);
      error('Failed to delete user');
    }
  }
}

async function approveUser(id: string) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `approve-user-${id}-${Date.now()}`;

  startOperation(operationId, 'Approving User', 'Processing user approval request...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Sending approval request...', 50);
    await api.put('/api/users', { id, status: 1 });

    updateProgress(operationId, 3, 'Refreshing users data...', 75);
    await fetchUsers();

    updateProgress(operationId, 4, 'User approved successfully', 100);
    completeOperation(operationId, 'success', 'User approved and data refreshed');
    success('User approved successfully');
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to approve user: ${err.message}`);
    error('Failed to approve user');
  }
}

async function rejectUser(id: string) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `reject-user-${id}-${Date.now()}`;

  startOperation(operationId, 'Rejecting User', 'Processing user rejection request...');

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Sending rejection request...', 50);
    await api.put('/api/users', { id, status: -1 });

    updateProgress(operationId, 3, 'Refreshing users data...', 75);
    await fetchUsers();

    updateProgress(operationId, 4, 'User rejected successfully', 100);
    completeOperation(operationId, 'success', 'User rejected and data refreshed');
    success('User rejected successfully');
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to reject user: ${err.message}`);
    error('Failed to reject user');
  }
}

async function editUser(user: User) {
  userForm.value = { ...user };
  showEditUserModal.value = true;
}

// Manager Code Functions
async function generateManagerCodes(count: number) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `generate-codes-${count}-${Date.now()}`;

  startOperation(operationId, 'Generating Manager Codes', `Creating ${count} new manager codes...`);

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 20);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, `Generating ${count} unique codes...`, 40);
    await api.post('/api/manager-codes', { count });

    updateProgress(operationId, 3, 'Codes generated, refreshing data...', 70);
    await fetchManagerCodes();

    updateProgress(operationId, 4, `Successfully generated ${count} manager codes`, 100);
    completeOperation(operationId, 'success', `${count} manager codes generated and data refreshed`);
    success(`Generated ${count} manager code(s)`);
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to generate codes: ${err.message}`);
    error('Failed to generate manager codes');
  }
}

async function deleteManagerCodes(codes: string[]) {
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `delete-codes-${codes.length}-${Date.now()}`;

  startOperation(operationId, 'Deleting Manager Codes', `Deleting ${codes.length} manager codes...`);

  try {
    updateProgress(operationId, 1, 'Connecting to API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, `Deleting ${codes.length} codes...`, 50);
    await api.fetchWithAuth('/api/manager-codes', {
      method: 'DELETE',
      body: { codes }
    });

    updateProgress(operationId, 3, 'Refreshing codes data...', 75);
    await fetchManagerCodes();

    updateProgress(operationId, 4, `Successfully deleted ${codes.length} codes`, 100);
    completeOperation(operationId, 'success', `${codes.length} manager codes deleted and data refreshed`);
    success('Manager codes deleted successfully');
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to delete codes: ${err.message}`);
    error('Failed to delete manager codes');
  }
}

function getUserFirmName(firmId: string) {
  const firm = firms.value.find((f:any) => f._id === firmId);
  return firm ? firm.name : '-';
}

function getUserName(userId: string) {
  const user = users.value.find(u => u._id === userId);
  return user ? user.fullname : '-';
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString();
}

// Database Management Functions
async function fetchModels() {
  loadingModels.value = true;

  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `fetch-models-${Date.now()}`;

  startOperation(operationId, 'Loading MongoDB Models', 'Fetching MongoDB models and schemas...');

  try {
    updateProgress(operationId, 1, 'Connecting to MongoDB API...', 20);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Requesting models list...', 40);
    const response = await api.get('/api/models');
    models.value = response.models;

    updateProgress(operationId, 3, 'Processing models data...', 60);

    // If there's a User model, fetch its data for user name lookups
    const userModel = models.value.find(model =>
      model.name.toLowerCase() === 'user' ||
      model.name.toLowerCase() === 'users'
    );

    if (userModel) {
      try {
        updateProgress(operationId, 4, 'Loading user data for lookups...', 80);
        const userResponse = await api.get(`/api/models/${userModel.name}?limit=10000`);
        // Add the data to the model object
        userModel.data = userResponse.data;
      } catch (e) {
        console.warn('Failed to fetch user data for name lookups');
      }
    }

    updateProgress(operationId, 5, `Loaded ${models.value.length} MongoDB models`, 100);
    completeOperation(operationId, 'success', `Successfully loaded ${models.value.length} MongoDB models`);
  } catch (err) {
    completeOperation(operationId, 'error', `Failed to fetch models: ${err.message}`);
    error('Failed to fetch database models');
  } finally {
    loadingModels.value = false;
  }
}

// Firestore Management Functions
async function fetchFirestoreCollections() {
  loadingFirestoreCollections.value = true;

  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `fetch-firestore-${Date.now()}`;

  startOperation(operationId, 'Loading Firestore Collections', 'Fetching Firestore collections and counts...');

  try {
    updateProgress(operationId, 1, 'Connecting to Firestore API...', 25);
    const api = useApiWithAuth();

    updateProgress(operationId, 2, 'Requesting collections list...', 50);
    const response = await api.get('/api/firestore/collections');

    updateProgress(operationId, 3, 'Processing collections data...', 75);
    firestoreCollections.value = response.collections || [];

    updateProgress(operationId, 4, `Loaded ${firestoreCollections.value.length} Firestore collections`, 100);
    completeOperation(operationId, 'success', `Successfully loaded ${firestoreCollections.value.length} Firestore collections`);
  } catch (err) {
    console.error('Error fetching Firestore collections:', err);
    completeOperation(operationId, 'error', `Failed to fetch Firestore collections: ${err.message}`);
    error('Failed to fetch Firestore collections');
    firestoreCollections.value = [];
  } finally {
    loadingFirestoreCollections.value = false;
  }
}

async function viewModelDetails(modelName) {
  selectedModel.value = modelName;
  showModelDetailsModal.value = true;
  loadingModelDetails.value = true;
  isFirestore.value = false;

  // Start real-time operation tracking
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `view-mongodb-${modelName}-${Date.now()}`;

  const operation = startOperation(
    operationId,
    `Loading MongoDB: ${modelName}`,
    `Fetching records from ${modelName} collection...`
  );

  try {
    updateProgress(operationId, 1, `Connecting to MongoDB API...`, 25);

    const api = useApiWithAuth();
    updateProgress(operationId, 2, `Requesting records from ${modelName}...`, 50);

    // Request all records by setting a high limit
    const response = await api.get(`/api/models/${modelName}?limit=10000`);

    updateProgress(operationId, 3, `Processing ${response.data.length} records...`, 75);

    modelData.value = response.data;
    modelSchema.value = response.schema;

    updateProgress(operationId, 4, `Successfully loaded ${response.data.length} records`, 100);
    completeOperation(operationId, 'success', `MongoDB ${modelName} loaded successfully`);

  } catch (err) {
    completeOperation(operationId, 'error', `Failed to load ${modelName}: ${err.message}`);
    error(`Failed to fetch details for model: ${modelName}`);
  } finally {
    loadingModelDetails.value = false;
  }
}

async function viewFirestoreDetails(collectionName) {
  selectedModel.value = collectionName;
  showModelDetailsModal.value = true;
  loadingModelDetails.value = true;
  isFirestore.value = true;

  // Start real-time operation tracking
  const { startOperation, updateProgress, completeOperation } = useRealTimeStatus();
  const operationId = `view-firestore-${collectionName}-${Date.now()}`;

  const operation = startOperation(
    operationId,
    `Loading Firestore: ${collectionName}`,
    `Fetching documents from ${collectionName} collection...`
  );

  try {
    updateProgress(operationId, 1, `Connecting to Firestore API...`, 25);

    const api = useApiWithAuth();
    updateProgress(operationId, 2, `Requesting documents from ${collectionName}...`, 50);

    // Request all records by setting a high limit
    const response = await api.get(`/api/firestore/collections/${collectionName}?limit=10000`);

    updateProgress(operationId, 3, `Processing ${response.data.length} documents...`, 75);

    modelData.value = response.data;
    modelSchema.value = response.schema;

    updateProgress(operationId, 4, `Successfully loaded ${response.data.length} documents`, 100);
    completeOperation(operationId, 'success', `Firestore ${collectionName} loaded successfully`);

  } catch (err) {
    completeOperation(operationId, 'error', `Failed to load ${collectionName}: ${err.message}`);
    error(`Failed to fetch details for Firestore collection: ${collectionName}`);
  } finally {
    loadingModelDetails.value = false;
  }
}

// Initial Data Load
onMounted(async () => {
  await Promise.all([
    fetchFirms(),
    fetchUsers(),
    fetchManagerCodes(),
    fetchModels(),
    fetchFirestoreCollections()
  ]);
});
</script>