import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'
import { LaborPaymentService } from '~/utils/laborPaymentService.js'

/**
 * Enhanced unpaid amounts calculation API
 * Implements the new logic for handling settled periods, ongoing periods, and post-final payments
 */
export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { firmId, groupId, fromDate, toDate } = query

    if (!firmId || !groupId) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields: firmId and groupId are required'
      })
    }

    // Get Supabase configuration
    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found for this firm'
      })
    }

    // Initialize the payment service
    const paymentService = new LaborPaymentService(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    // Set date filter if provided
    let startDate = null
    if (fromDate) {
      startDate = new Date(fromDate)
      if (isNaN(startDate.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid fromDate format'
        })
      }
    } else {
      // Default to one year ago if no date provided
      startDate = new Date()
      startDate.setFullYear(startDate.getFullYear() - 1)
    }

    // Calculate unpaid amounts using the new logic
    const unpaidAmounts = await paymentService.calculateUnpaidAmounts(groupId, startDate)

    // Validate the payment data and include warnings if any
    const validation = await paymentService.validatePaymentData(groupId)

    // Filter by toDate if provided
    let filteredResults = unpaidAmounts
    if (toDate) {
      const endDate = new Date(toDate)
      if (isNaN(endDate.getTime())) {
        throw createError({
          statusCode: 400,
          statusMessage: 'Invalid toDate format'
        })
      }
      
      filteredResults = unpaidAmounts.filter(item => 
        new Date(item.period.period_start) <= endDate
      )
    }

    return {
      success: true,
      data: filteredResults,
      validation: validation,
      metadata: {
        totalRecords: filteredResults.length,
        settledPeriods: filteredResults.filter(r => r.type === 'settled').length,
        ongoingPeriods: filteredResults.filter(r => r.type === 'ongoing').length,
        postFinalPeriods: filteredResults.filter(r => r.type === 'post-final').length,
        dateRange: {
          from: fromDate || startDate.toISOString().split('T')[0],
          to: toDate || 'current'
        }
      }
    }

  } catch (error) {
    console.error('Error in unpaid-v2 API:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to calculate unpaid amounts'
    })
  }
})
