<template>
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl w-full mx-4 max-h-[90vh] overflow-y-auto"
      style="max-width: 75vw; min-width: 800px;">
      <!-- Header -->
      <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-blue-600 to-indigo-600">
        <div class="flex items-center justify-between">
          <div>
            <h3 class="text-xl font-semibold text-white">🤖 AI Stock Analysis</h3>
            <p class="text-blue-100 text-sm mt-1">{{ stock.symbol }} - {{ stock.meta?.companyName || 'N/A' }}</p>
          </div>
          <button @click="$emit('close')" class="text-white hover:text-gray-200 transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Content -->
      <div class="px-6 py-4">
        <!-- Stock Info Summary -->
        <div class="bg-gray-50 rounded-lg p-4 mb-6">
          <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span class="text-gray-500">Current Price:</span>
              <span class="font-medium ml-2">₹{{ formatPrice(stock.lastPrice) }}</span>
            </div>
            <div>
              <span class="text-gray-500">Change:</span>
              <span class="font-medium ml-2" :class="stock.pChange >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ stock.pChange >= 0 ? '+' : '' }}{{ stock.pChange?.toFixed(2) }}%
              </span>
            </div>
            <div>
              <span class="text-gray-500">Volume:</span>
              <span class="font-medium ml-2">{{ formatVolume(stock.totalTradedVolume) }}</span>
            </div>
            <div>
              <span class="text-gray-500">Day Range:</span>
              <span class="font-medium ml-2">₹{{ formatPrice(stock.dayLow) }} - ₹{{ formatPrice(stock.dayHigh) }}</span>
            </div>
          </div>
        </div>

        <!-- Tab Navigation -->
        <div class="border-b border-gray-200 mb-6">
          <nav class="flex space-x-8" aria-label="Tabs">
            <button @click="activeTab = 'analysis'" :class="[
              activeTab === 'analysis'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
            ]">
              📊 AI Analysis-CLIENT
            </button>
            <button @click="activeTab = 'news'" :class="[
              activeTab === 'news'
                ? 'border-purple-500 text-purple-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
            ]">
              📰 Stock News-CLIENT
            </button>
            <!-- OLD TECHNICAL ANALYSIS TAB - HIDDEN -->
            <!--
            <button
              @click="activeTab = 'technical'"
              :class="[
                activeTab === 'technical'
                  ? 'border-green-500 text-green-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
              ]"
            >
              📈 Technical Analysis
            </button>
            -->
            <button @click="activeTab = 'technical-client'" :class="[
              activeTab === 'technical-client'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
            ]">
              📊 Technical Analysis-CLIENT
            </button>
            <!-- OLD FUNDAMENTAL ANALYSIS TAB - HIDDEN -->
            <!--
            <button
              @click="activeTab = 'fundamental'"
              :class="[
                activeTab === 'fundamental'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
              ]"
            >
              💰 Fundamental Analysis
            </button>
            -->
            <button @click="activeTab = 'fundamental-client'" :class="[
              activeTab === 'fundamental-client'
                ? 'border-orange-500 text-orange-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
              'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
            ]">
              💰 Fundamental Analysis-CLIENT
            </button>
          </nav>
        </div>

        <!-- Tab Content -->
        <div class="tab-content">
          <!-- Tab 1: AI Analysis-CLIENT -->
          <div v-show="activeTab === 'analysis'" class="tab-panel">
            <ClientAIAnalysisTab :stock="stock" />
          </div>


        </div>

        <!-- Tab 2: Stock News-CLIENT -->
        <div v-show="activeTab === 'news'" class="tab-panel">
          <ClientStockNewsTab :stock="stock" />
        </div>

        <!-- OLD TECHNICAL ANALYSIS TAB CONTENT - HIDDEN -->

        <div v-show="activeTab === 'technical'" class="tab-panel">
          <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-6">
            <div class="flex items-center justify-between mb-4">
              <h4 class="text-lg font-semibold text-gray-900">📊 200-Day Technical Analysis</h4>
              <button @click="fetchTechnicalAnalysis" :disabled="technicalLoading"
                class="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-lg hover:from-green-700 hover:to-emerald-700 disabled:opacity-50 transition-all duration-200 shadow-sm">
                <span v-if="technicalLoading">📈 Analyzing...</span>
                <span v-else>📊 Technical Analysis</span>
              </button>
            </div>

            <!-- Technical Analysis Loading State -->
            <div v-if="technicalLoading" class="mb-4">
              <div class="bg-white rounded-lg p-4">
                <div class="flex items-center space-x-3 mb-3">
                  <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-green-500"></div>
                  <span class="text-gray-700">{{ technicalStatusMessage || 'Processing technical analysis...'
                  }}</span>
                </div>
                <div class="bg-gray-200 rounded-full h-2">
                  <div class="bg-green-500 h-2 rounded-full transition-all duration-500"
                    :style="{ width: technicalProgress + '%' }"></div>
                </div>
                <div class="text-xs text-gray-500 mt-1">{{ technicalProgress }}% complete</div>
              </div>
            </div>

            <!-- Technical Analysis Error State -->
            <div v-else-if="technicalError" class="bg-red-50 border border-red-200 rounded-lg p-4">
              <div class="text-red-600 mb-2">⚠️ Technical Analysis Failed</div>
              <p class="text-red-700 mb-3">{{ technicalError }}</p>
              <button @click="fetchTechnicalAnalysis"
                class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors">
                🔄 Retry Analysis
              </button>
            </div>

            <!-- Technical Analysis Results -->
            <div v-else-if="technicalAnalysis" class="space-y-4">
              <!-- Technical Recommendation -->
              <div class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-2">📈 Technical Recommendation</h5>
                <div class="flex items-center space-x-3">
                  <span v-if="technicalAnalysis.aiAnalysis?.technicalRecommendation"
                    class="px-3 py-1 rounded-full text-sm font-medium"
                    :class="getTechnicalRecommendationClass(technicalAnalysis.aiAnalysis?.technicalRecommendation)">
                    {{ technicalAnalysis.aiAnalysis?.technicalRecommendation }}
                  </span>
                  <span v-else class="text-gray-500 text-sm">No recommendation available</span>
                  <span v-if="technicalAnalysis.aiAnalysis?.confidence" class="text-gray-600 text-sm">
                    Confidence: {{ technicalAnalysis.aiAnalysis?.confidence }}
                  </span>
                </div>
              </div>

              <!-- Technical Indicators -->
              <div class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-3">🔢 Key Technical Indicators</h5>
                <div class="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span class="text-gray-600">200-day SMA:</span>
                    <span class="font-medium ml-1">
                      <span v-if="technicalAnalysis.technicalIndicators?.sma200">₹{{
                        technicalAnalysis.technicalIndicators.sma200.toFixed(2) }}</span>
                      <span v-else class="text-gray-400">Not available</span>
                    </span>
                  </div>
                  <div>
                    <span class="text-gray-600">50-day SMA:</span>
                    <span class="font-medium ml-1">
                      <span v-if="technicalAnalysis.technicalIndicators?.sma50">₹{{
                        technicalAnalysis.technicalIndicators.sma50.toFixed(2) }}</span>
                      <span v-else class="text-gray-400">Not available</span>
                    </span>
                  </div>
                  <div>
                    <span class="text-gray-600">RSI (14):</span>
                    <span class="font-medium ml-1" :class="getRSIClass(technicalAnalysis.technicalIndicators?.rsi)">
                      <span v-if="technicalAnalysis.technicalIndicators?.rsi">{{
                        technicalAnalysis.technicalIndicators.rsi.toFixed(2) }}</span>
                      <span v-else class="text-gray-400">Not available</span>
                    </span>
                  </div>
                  <div>
                    <span class="text-gray-600">MACD:</span>
                    <span class="font-medium ml-1">
                      <span v-if="technicalAnalysis.technicalIndicators?.macd?.line">{{
                        technicalAnalysis.technicalIndicators.macd.line.toFixed(4) }}</span>
                      <span v-else class="text-gray-400">Not available</span>
                    </span>
                  </div>
                  <div>
                    <span class="text-gray-600">ATR:</span>
                    <span class="font-medium ml-1">
                      <span v-if="technicalAnalysis.technicalIndicators?.atr">{{
                        technicalAnalysis.technicalIndicators.atr.toFixed(2) }}</span>
                      <span v-else class="text-gray-400">Not available</span>
                    </span>
                  </div>
                  <div>
                    <span class="text-gray-600">Data Points:</span>
                    <span class="font-medium ml-1">{{ technicalAnalysis.historicalDataPoints || 0 }}</span>
                  </div>
                </div>
              </div>

              <!-- Trend Analysis -->
              <div v-if="technicalAnalysis.aiAnalysis?.trendAnalysis" class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-2">📈 200-Day Trend Analysis</h5>
                <div class="text-gray-700 text-sm"
                  v-html="formatAnalysisText(technicalAnalysis.aiAnalysis.trendAnalysis)"></div>
              </div>

              <!-- Moving Average Analysis -->
              <div v-if="technicalAnalysis.aiAnalysis?.movingAverageAnalysis" class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-2">📊 Moving Average Analysis</h5>
                <div class="text-gray-700 text-sm"
                  v-html="formatAnalysisText(technicalAnalysis.aiAnalysis.movingAverageAnalysis)"></div>
              </div>

              <!-- Support & Resistance -->
              <div v-if="technicalAnalysis.technicalIndicators?.supportResistance" class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-2">🎯 Support & Resistance Levels</h5>
                <div class="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span class="text-red-600 font-medium">Resistance:</span>
                    <div class="mt-1">
                      <span v-for="(level, index) in technicalAnalysis.technicalIndicators.supportResistance.resistance"
                        :key="index" class="inline-block bg-red-100 text-red-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                        ₹{{ level.toFixed(2) }}
                      </span>
                    </div>
                  </div>
                  <div>
                    <span class="text-green-600 font-medium">Support:</span>
                    <div class="mt-1">
                      <span v-for="(level, index) in technicalAnalysis.technicalIndicators.supportResistance.support"
                        :key="index"
                        class="inline-block bg-green-100 text-green-800 px-2 py-1 rounded text-xs mr-1 mb-1">
                        ₹{{ level.toFixed(2) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Price Targets -->
              <div v-if="technicalAnalysis.aiAnalysis?.priceTargets" class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-2">🎯 Price Targets & Risk Management</h5>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                  <div class="bg-blue-50 p-3 rounded">
                    <span class="text-blue-600 font-medium">Short-term Target:</span>
                    <div class="text-blue-800 font-semibold">{{ technicalAnalysis.aiAnalysis.priceTargets.shortTerm }}
                    </div>
                  </div>
                  <div class="bg-green-50 p-3 rounded">
                    <span class="text-green-600 font-medium">Medium-term Target:</span>
                    <div class="text-green-800 font-semibold">{{ technicalAnalysis.aiAnalysis.priceTargets.mediumTerm
                    }}
                    </div>
                  </div>
                  <div class="bg-red-50 p-3 rounded">
                    <span class="text-red-600 font-medium">Stop Loss:</span>
                    <div class="text-red-800 font-semibold">{{ technicalAnalysis.aiAnalysis.priceTargets.stopLoss }}
                    </div>
                  </div>
                </div>
              </div>

              <!-- Trading Strategy -->
              <div v-if="technicalAnalysis.aiAnalysis?.tradingStrategy" class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-2">💡 Trading Strategy</h5>
                <div class="text-gray-700 text-sm"
                  v-html="formatAnalysisText(technicalAnalysis.aiAnalysis.tradingStrategy)"></div>
              </div>

              <!-- Technical Charts -->
              <div v-if="technicalAnalysis && technicalAnalysis.technicalIndicators" class="bg-white rounded-lg p-4">
                <h5 class="font-semibold text-gray-900 mb-4">📈 Technical Charts</h5>

                <!-- Chart Controls -->
                <div class="flex flex-wrap items-center justify-between gap-4 mb-4">
                  <!-- Chart Type Selector -->
                  <div class="flex flex-wrap gap-2">
                    <button @click="activeChart = 'price'" :class="[
                      'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                      activeChart === 'price'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    ]">
                      📊 Price & SMA
                    </button>
                    <button @click="activeChart = 'candlestick'" :class="[
                      'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                      activeChart === 'candlestick'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    ]">
                      🕯️ Candlestick
                    </button>
                    <button @click="activeChart = 'rsi'" :class="[
                      'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                      activeChart === 'rsi'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    ]">
                      📈 RSI
                    </button>
                    <button @click="activeChart = 'macd'" :class="[
                      'px-3 py-1 rounded-lg text-sm font-medium transition-colors',
                      activeChart === 'macd'
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    ]">
                      📉 MACD
                    </button>
                  </div>

                  <!-- Period Filter and Chart Controls -->
                  <div class="flex items-center gap-2">
                    <select v-model="chartPeriod" @change="updateChartPeriod"
                      class="px-2 py-1 text-xs border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-blue-500">
                      <option value="30">30 Days</option>
                      <option value="60">60 Days</option>
                      <option value="90">90 Days</option>
                      <option value="180">6 Months</option>
                      <option value="365">1 Year</option>
                      <option value="all">All Data</option>
                    </select>
                    <div class="flex items-center gap-1">
                      <button @click="zoomIn"
                        class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        title="Zoom In">
                        🔍+
                      </button>
                      <button @click="zoomOut"
                        class="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors"
                        title="Zoom Out">
                        🔍-
                      </button>
                      <button @click="resetChartZoom"
                        class="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 transition-colors"
                        title="Reset Zoom">
                        🔄 Reset
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Chart Container -->
                <div class="relative" style="height: 400px;">
                  <canvas ref="technicalChartCanvas"></canvas>
                </div>

                <!-- Chart Info -->
                <div class="mt-3 text-xs text-gray-500 text-center">
                  <span v-if="technicalAnalysis.historicalDataPoints">Data Points: {{
                    technicalAnalysis.historicalDataPoints }} | </span>
                  <span v-if="technicalAnalysis.historicalDataPoints">Period: {{ getChartPeriodText() }} | </span>
                  <span v-if="technicalAnalysis.analysisTimestamp">Last Updated: {{
                    formatTimestamp(technicalAnalysis.analysisTimestamp) }}</span>
                  <span v-else class="text-gray-400">Chart data not available</span>
                </div>
              </div>
            </div>

            <!-- Initial State -->
            <div v-else class="bg-white rounded-lg p-4 text-center">
              <p class="text-gray-600 mb-3">Click the button above to get comprehensive 200-day technical analysis for
                {{
                  stock.symbol }}</p>
              <p class="text-gray-500 text-sm">AI will analyze moving averages, RSI, MACD, support/resistance levels,
                and
                provide trading recommendations</p>
            </div>
          </div>
        </div>


        <!-- Tab 3: Technical Analysis-CLIENT -->
        <div v-show="activeTab === 'technical-client'" class="tab-panel">
          <ClientTechnicalAnalysisTab :stock="stock" />
        </div>



        <!-- Tab 4: Fundamental Analysis-CLIENT -->
        <div v-show="activeTab === 'fundamental-client'" class="tab-panel">
          <ClientFundamentalAnalysisTab :stock="stock" />
        </div>
      </div>

      <!-- AI Disclaimer -->
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
        <div class="flex items-start space-x-2">
          <span class="text-yellow-600 text-lg">⚠️</span>
          <div class="text-sm text-yellow-800">
            <p class="font-medium mb-1">AI Analysis Disclaimer</p>
            <p>This analysis is generated by AI and should not be considered as financial advice. Always conduct your
              own
              research and consult with financial advisors before making investment decisions. Past performance does
              not
              guarantee future results.</p>
          </div>
        </div>
      </div>

      <!-- Actions -->
      <div class="flex justify-between items-center mt-6 pt-4 border-t border-gray-200">
        <div class="flex flex-wrap gap-3">
          <button v-if="analysis" @click="refreshAnalysis" :disabled="loading"
            class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
            🔄 Refresh Analysis
          </button>
          <button v-if="!technicalLoading && !technicalAnalysis" @click="fetchTechnicalAnalysis"
            class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
            📊 Technical Analysis
          </button>

          <button v-if="!fundamentalLoading && !fundamentalAnalysis" @click="fetchFundamentalAnalysis"
            class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors">
            💰 Fundamental Analysis
          </button>
        </div>
        <button @click="$emit('close')"
          class="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
          Close
        </button>
      </div>

      <!-- AI Provider Footer -->
      <div class="px-6 py-3 border-t border-gray-200 bg-gray-50">
        <div class="flex items-center justify-between text-xs text-gray-500">
          <div class="flex items-center space-x-4">
            <span class="flex items-center">
              🤖 <span class="ml-1 font-medium">AI Provider:</span>
              <span class="ml-1 text-gray-700">{{ currentAIProvider || 'Not Configured' }}</span>
            </span>
            <span class="flex items-center">
              🧠 <span class="ml-1 font-medium">Model:</span>
              <span class="ml-1 text-gray-700">{{ currentAIModel || 'Not Selected' }}</span>
            </span>
          </div>
          <div class="flex items-center space-x-2">
            <span v-if="isConfigured"
              class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-100 text-green-800">
              ✅ Configured
            </span>
            <span v-else class="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-red-100 text-red-800">
              ❌ Not Configured
            </span>
            <span class="text-gray-600">CLIENT-SIDE Processing</span>
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import {
  Chart,
  BarController,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineController,
  TimeScale,
  TimeSeriesScale
} from 'chart.js'
import { CandlestickController, OhlcController, CandlestickElement, OhlcElement } from 'chartjs-chart-financial'
import zoomPlugin from 'chartjs-plugin-zoom'
import 'chartjs-adapter-luxon'
import { useAIApi } from '~/composables/ai/useAIApi'
import { useAIConfig } from '~/composables/ai/useAIConfig'

// Register all Chart.js components including zoom plugin
Chart.register(
  BarController,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  BarElement,
  Title,
  Tooltip,
  Legend,
  LineController,
  TimeScale,
  TimeSeriesScale,
  CandlestickController,
  OhlcController,
  CandlestickElement,
  OhlcElement,
  zoomPlugin
)

console.log('📊 Chart.js plugins registered:', Chart.registry.plugins.items)
console.log('📊 Zoom plugin registered:', !!Chart.registry.plugins.get('zoom'))

import useApiWithAuth from '~/composables/auth/useApiWithAuth'
import ClientTechnicalAnalysisTab from '~/components/stock-market/client-technical-analysis/ClientTechnicalAnalysisTab.vue'
import ClientFundamentalAnalysisTab from '~/components/stock-market/client-fundamental-analysis/ClientFundamentalAnalysisTab.vue'
import ClientStockNewsTab from '~/components/stock-market/client-stock-news/ClientStockNewsTab.vue'
import ClientAIAnalysisTab from '~/components/stock-market/client-ai-analysis/ClientAIAnalysisTab.vue'

// Props
const props = defineProps({
  stock: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['close'])

// Initialize API
const api = useApiWithAuth()





// Technical Analysis State
const technicalLoading = ref(false)
const technicalError = ref('')
const technicalAnalysis = ref(null)
const technicalJobId = ref(null)
const technicalProgress = ref(0)
const technicalStatusMessage = ref('')

// Fundamental Analysis State
const fundamentalLoading = ref(false)
const fundamentalError = ref('')
const fundamentalAnalysis = ref(null)
const fundamentalJobId = ref(null)
const fundamentalProgress = ref(0)
const fundamentalStatusMessage = ref('')

// Tab State
const activeTab = ref('analysis')

const { makeAIRequest } = useAIApi()

// AI Configuration for footer display
const { aiConfig, isConfigured } = useAIConfig()

// Computed properties for AI provider display
const currentAIProvider = computed(() => {
  if (!aiConfig.value?.provider) return null

  // Map provider IDs to display names
  const providerNames = {
    'openai': 'OpenAI',
    'google': 'Google AI',
    'anthropic': 'Anthropic',
    'openrouter': 'OpenRouter',
    'custom': 'Custom Provider'
  }

  return providerNames[aiConfig.value.provider] || aiConfig.value.provider
})

const currentAIModel = computed(() => {
  if (!aiConfig.value?.model) return null

  // For OpenRouter models, show just the model name without the provider prefix
  if (aiConfig.value.model.includes('/')) {
    return aiConfig.value.model.split('/').pop()
  }

  return aiConfig.value.model
})

// Chart State
const activeChart = ref('price')
const technicalChartCanvas = ref(null)
const chartPeriod = ref('180') // Default to 6 months
let technicalChart = null

// Methods
const formatPrice = (price) => {
  if (!price) return '0'
  return new Intl.NumberFormat('en-IN', { minimumFractionDigits: 2 }).format(price)
}

const formatVolume = (volume) => {
  if (!volume) return '0'
  const num = parseInt(volume)
  if (num >= 10000000) return (num / 10000000).toFixed(1) + 'Cr'
  if (num >= 100000) return (num / 100000).toFixed(1) + 'L'
  if (num >= 1000) return (num / 1000).toFixed(1) + 'K'
  return num.toString()
}



const formatAnalysisText = (text) => {
  if (!text) return ''

  // Ensure text is a string
  const textString = typeof text === 'string' ? text : String(text)

  return textString
    .replace(/\n/g, '<br>')
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
}







// Fetch Technical Analysis using AI Background Job
const fetchTechnicalAnalysis = async () => {
  console.log('📊 Starting technical analysis for:', props.stock.symbol)
  technicalLoading.value = true
  technicalError.value = ''
  technicalAnalysis.value = null
  technicalProgress.value = 0
  technicalStatusMessage.value = 'Queuing technical analysis...'

  try {
    // Step 1: Queue the background technical analysis job using AI API
    const queueResponse = await makeAIRequest('/api/stock-market/technical-analysis', {
      method: 'POST',
      body: {
        symbol: props.stock.symbol,
        companyName: props.stock.meta?.companyName || props.stock.symbol,
        currentPrice: props.stock.lastPrice,
        change: props.stock.change,
        pChange: props.stock.pChange
      }
    })

    if (!queueResponse.success) {
      throw new Error(queueResponse.message || 'Failed to queue technical analysis')
    }

    technicalJobId.value = queueResponse.jobId
    console.log('📋 Technical analysis queued with job ID:', technicalJobId.value)

    // Step 2: Start polling for technical analysis results
    await pollTechnicalAnalysisJobStatus()

  } catch (err) {
    console.error('Technical Analysis Error:', err)
    technicalError.value = err.message
    technicalLoading.value = false
  }
}



const pollTechnicalAnalysisJobStatus = async () => {
  if (!technicalJobId.value) return

  const startTime = Date.now()
  const maxPollingTime = 140000 // 140 seconds (2.3 minutes) max polling for technical analysis
  const pollInterval = 2000 // Poll every 2 seconds

  const poll = async () => {
    try {
      const elapsed = Math.floor((Date.now() - startTime) / 1000)

      // Check if we've exceeded max polling time
      if (elapsed > maxPollingTime / 1000) {
        throw new Error('Technical analysis timed out. Please try again.')
      }

      const statusResponse = await api.get(`/api/stock-market/ai-analysis-status/${technicalJobId.value}`)

      if (!statusResponse.success) {
        throw new Error(statusResponse.message || 'Failed to check technical analysis status')
      }

      const job = statusResponse
      technicalProgress.value = job.progress || 0
      technicalStatusMessage.value = job.message

      console.log(`📊 Technical analysis job ${technicalJobId.value} status: ${job.status} (${job.progress}%)`)

      switch (job.status) {
        case 'completed':
          technicalAnalysis.value = job.analysis
          technicalLoading.value = false
          console.log('✅ Background technical analysis completed:', job.analysis)
          return

        case 'failed':
          throw new Error(job.error || 'Technical analysis failed')

        case 'queued':
        case 'processing':
          // Continue polling
          setTimeout(poll, pollInterval)
          break

        default:
          throw new Error(`Unknown technical analysis job status: ${job.status}`)
      }

    } catch (pollError) {
      console.error('Technical analysis polling error:', pollError)
      technicalError.value = pollError.message
      technicalLoading.value = false
    }
  }

  // Start polling
  setTimeout(poll, 1000) // Start after 1 second
}



// Fetch Fundamental Analysis using AI Background Job
const fetchFundamentalAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings first.')
    return
  }

  console.log('💰 Starting AI fundamental analysis for:', props.stock.symbol)
  fundamentalLoading.value = true
  fundamentalError.value = ''
  fundamentalAnalysis.value = null
  fundamentalProgress.value = 0
  fundamentalStatusMessage.value = ''

  try {
    // Step 1: Queue the background fundamental analysis job using AI API
    const queueResponse = await makeAIRequest('/api/stock-market/fundamental-analysis', {
      method: 'POST',
      body: {
        symbol: props.stock.symbol,
        companyName: props.stock.meta?.companyName || props.stock.symbol,
        currentPrice: props.stock.lastPrice,
        change: props.stock.change,
        pChange: props.stock.pChange,
        volume: props.stock.totalTradedVolume,
        dayHigh: props.stock.dayHigh,
        dayLow: props.stock.dayLow,
        previousClose: props.stock.previousClose
      }
    })

    if (!queueResponse.success) {
      throw new Error(queueResponse.message || 'Failed to queue fundamental analysis')
    }

    fundamentalJobId.value = queueResponse.jobId
    console.log('📋 Fundamental analysis queued with job ID:', fundamentalJobId.value)

    // Step 2: Start polling for fundamental analysis results
    await pollFundamentalAnalysisJobStatus()

  } catch (err) {
    console.error('Fundamental Analysis Error:', err)
    fundamentalError.value = err.message
    fundamentalLoading.value = false
  }
}

// Poll for fundamental analysis job status
const pollFundamentalAnalysisJobStatus = async () => {
  const maxPollingTime = 5 * 60 * 1000 // 5 minutes
  const pollInterval = 3000 // 3 seconds
  const startTime = Date.now()

  const poll = async () => {
    try {
      const elapsed = Date.now() - startTime
      if (elapsed > maxPollingTime) {
        throw new Error('Fundamental analysis timed out. Please try again.')
      }

      const statusResponse = await api.get(`/api/stock-market/ai-analysis-status/${fundamentalJobId.value}`)

      if (!statusResponse.success) {
        throw new Error(statusResponse.message || 'Failed to check fundamental analysis status')
      }

      const job = statusResponse
      fundamentalProgress.value = job.progress || 0
      fundamentalStatusMessage.value = job.message

      console.log(`📊 Fundamental analysis job ${fundamentalJobId.value} status: ${job.status} (${job.progress}%)`)

      switch (job.status) {
        case 'completed':
          // Validate and sanitize fundamental analysis data
          const sanitizedFundamentalAnalysis = {
            metrics: job.analysis?.metrics,
            financialHealth: job.analysis?.financialHealth,
            profitability: job.analysis?.profitability,
            pros: job.analysis?.pros || [],
            cons: job.analysis?.cons || [],
            recommendation: job.analysis?.recommendation,
            confidence: job.analysis?.confidence,
            summary: job.analysis?.summary,
            analysisTimestamp: job.analysis?.analysisTimestamp || new Date().toISOString()
          }

          fundamentalAnalysis.value = sanitizedFundamentalAnalysis
          fundamentalLoading.value = false
          console.log('✅ Background fundamental analysis completed:', sanitizedFundamentalAnalysis)
          return

        case 'failed':
          throw new Error(job.error || 'Fundamental analysis failed')

        case 'queued':
        case 'processing':
          // Continue polling
          setTimeout(poll, pollInterval)
          break

        default:
          throw new Error(`Unknown fundamental analysis job status: ${job.status}`)
      }

    } catch (pollError) {
      console.error('Fundamental analysis polling error:', pollError)
      fundamentalError.value = pollError.message
      fundamentalLoading.value = false
    }
  }

  // Start polling
  setTimeout(poll, 1000) // Start after 1 second
}

// Helper function to get technical recommendation styling
const getTechnicalRecommendationClass = (recommendation) => {
  switch (recommendation) {
    case 'STRONG_BUY':
      return 'bg-green-600 text-white'
    case 'BUY':
      return 'bg-green-500 text-white'
    case 'HOLD':
      return 'bg-yellow-500 text-white'
    case 'SELL':
      return 'bg-red-500 text-white'
    case 'STRONG_SELL':
      return 'bg-red-600 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

// Helper function to get fundamental recommendation styling
const getFundamentalRecommendationClass = (recommendation) => {
  switch (recommendation) {
    case 'STRONG_BUY':
      return 'bg-green-600 text-white'
    case 'BUY':
      return 'bg-green-500 text-white'
    case 'HOLD':
      return 'bg-yellow-500 text-white'
    case 'SELL':
      return 'bg-red-500 text-white'
    case 'STRONG_SELL':
      return 'bg-red-600 text-white'
    default:
      return 'bg-gray-500 text-white'
  }
}

// Helper function to get RSI styling
const getRSIClass = (rsi) => {
  if (!rsi) return 'text-gray-600'
  if (rsi >= 70) return 'text-red-600 font-semibold' // Overbought
  if (rsi <= 30) return 'text-green-600 font-semibold' // Oversold
  return 'text-gray-600'
}

// Open AI News Reader (for general news) - REMOVED
const openNewsReader = () => {
  console.log('📰 News Reader functionality has been removed')
  // News reader modal has been removed from the application
}

// Chart Period and Zoom Functions
const updateChartPeriod = () => {
  console.log('📊 Updating chart period to:', chartPeriod.value)
  initTechnicalChart()
}

const resetChartZoom = () => {
  if (technicalChart) {
    try {
      technicalChart.resetZoom()
      console.log('🔍 Chart zoom reset')
    } catch (error) {
      console.error('❌ Error resetting zoom:', error)
    }
  }
}

const zoomIn = () => {
  if (technicalChart) {
    try {
      technicalChart.zoom(1.2)
      console.log('🔍+ Chart zoomed in')
    } catch (error) {
      console.error('❌ Error zooming in:', error)
    }
  }
}

const zoomOut = () => {
  if (technicalChart) {
    try {
      technicalChart.zoom(0.8)
      console.log('🔍- Chart zoomed out')
    } catch (error) {
      console.error('❌ Error zooming out:', error)
    }
  }
}

// Helper function to get optimized zoom options
const getZoomOptions = () => {
  if (!process.client) return {}

  return {
    zoom: {
      zoom: {
        wheel: {
          enabled: true,
          speed: 0.1
        },
        pinch: {
          enabled: true
        },
        mode: 'x'
      },
      pan: {
        enabled: true,
        mode: 'x',
        threshold: 10
      },
      limits: {
        x: {
          min: 'original',
          max: 'original'
        }
      }
    }
  }
}

// Helper function to get performance-optimized chart options
const getPerformanceOptions = () => {
  return {
    responsive: true,
    maintainAspectRatio: false,
    resizeDelay: 0,
    // Disable animations for better drag performance
    animation: {
      duration: 0
    },
    // Optimize interactions
    interaction: {
      mode: 'nearest',
      intersect: false,
      includeInvisible: false
    },
    // Optimize elements
    elements: {
      point: {
        radius: 0,
        hoverRadius: 3
      },
      line: {
        tension: 0.1
      }
    }
  }
}

const getFilteredChartData = (data) => {
  if (!data || !Array.isArray(data)) return data

  if (chartPeriod.value === 'all') return data

  const days = parseInt(chartPeriod.value)
  const cutoffDate = new Date()
  cutoffDate.setDate(cutoffDate.getDate() - days)

  return data.filter(item => {
    const itemDate = new Date(item.date)
    return itemDate >= cutoffDate
  })
}

// Chart Functions
const initTechnicalChart = async () => {
  await nextTick()

  // Check if we have a canvas and sufficient historical data to render.
  if (!technicalChartCanvas.value || !technicalAnalysis.value.historicalData || !Array.isArray(technicalAnalysis.value.historicalData) || technicalAnalysis.value.historicalData.length === 0) {
    return // Exit if no data or canvas. This is expected when data is not yet available.
  }

  // Filter data based on selected period
  let data = getFilteredChartData(technicalAnalysis.value.historicalData)

  // Performance optimization: limit data points for very large datasets
  if (data.length > 1000) {
    console.log('📊 Large dataset detected, applying data decimation for performance')
    const step = Math.ceil(data.length / 1000)
    data = data.filter((_, index) => index % step === 0)
  }

  // Destroy existing chart
  if (technicalChart) {
    technicalChart.destroy()
    technicalChart = null
  }

  const ctx = technicalChartCanvas.value.getContext('2d')

  // Prepare chart data based on active chart type
  let chartConfig = {}

  if (activeChart.value === 'price') {
    chartConfig = getPriceChartConfig(data)
  } else if (activeChart.value === 'candlestick') {
    console.log('📊 Initializing candlestick chart with', data.length, 'data points')
    chartConfig = getCandlestickChartConfig(data)
  } else if (activeChart.value === 'rsi') {
    chartConfig = getRSIChartConfig(data)
  } else if (activeChart.value === 'macd') {
    chartConfig = getMACDChartConfig(data)
  }

  // Add global performance optimizations to chart config
  if (chartConfig.options) {
    // Ensure responsive behavior without performance impact
    chartConfig.options.responsive = true
    chartConfig.options.maintainAspectRatio = false
    chartConfig.options.resizeDelay = 0

    // Disable animations for better drag performance
    if (!chartConfig.options.animation) {
      chartConfig.options.animation = {}
    }
    chartConfig.options.animation.duration = 0

    // Optimize interactions for better drag performance
    if (!chartConfig.options.interaction) {
      chartConfig.options.interaction = {}
    }
    chartConfig.options.interaction.mode = 'nearest'
    chartConfig.options.interaction.intersect = false
    chartConfig.options.interaction.includeInvisible = false
  }

  try {
    console.log('📊 Chart config plugins:', chartConfig.options?.plugins)
    technicalChart = new Chart(ctx, chartConfig)
    console.log('📊 Technical chart initialized:', activeChart.value, 'with', data.length, 'data points')
    console.log('📊 Chart zoom plugin available:', !!technicalChart.zoom)
  } catch (error) {
    console.error('❌ Error initializing technical chart:', error)
    console.error('Chart config:', chartConfig)
    // Only show real data - no fallbacks allowed
    return
  }
}

// Sample chart data generator removed - using only real data

const getPriceChartConfig = (data) => {
  const labels = data.map(item => {
    const date = new Date(item.date)
    return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })
  })

  const prices = data.map(item => parseFloat(item.close) || 0)
  const sma50Data = data.map(item => item.sma50 ? parseFloat(item.sma50) : null)
  const sma200Data = data.map(item => item.sma200 ? parseFloat(item.sma200) : null)

  // Calculate min and max for better scaling
  const allValues = [
    ...prices.filter(p => p > 0),
    ...sma50Data.filter(v => v !== null),
    ...sma200Data.filter(v => v !== null)
  ]
  const minValue = Math.min(...allValues) * 0.95
  const maxValue = Math.max(...allValues) * 1.05

  return {
    type: 'line',
    data: {
      labels,
      datasets: (() => {
        const sets = [
          {
            label: 'Close Price',
            data: prices,
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            tension: 0.1,
            fill: false,
            pointRadius: 1,
            pointHoverRadius: 4
          }
        ]

        // Conditionally add 50-day SMA if data exists
        if (sma50Data.some(v => v !== null && v !== undefined)) {
          sets.push({
            label: '50-day SMA',
            data: sma50Data,
            borderColor: '#f59e0b',
            backgroundColor: 'transparent',
            borderWidth: 1.5,
            tension: 0.1,
            pointRadius: 0,
            pointHoverRadius: 3,
            spanGaps: true
          })
        }

        // Conditionally add 200-day SMA if data exists
        if (sma200Data.some(v => v !== null && v !== undefined)) {
          sets.push({
            label: '200-day SMA',
            data: sma200Data,
            borderColor: '#ef4444',
            backgroundColor: 'transparent',
            borderWidth: 1.5,
            tension: 0.1,
            pointRadius: 0,
            pointHoverRadius: 3,
            spanGaps: true
          })
        }

        return sets
      })()
    },
    options: {
      ...getPerformanceOptions(),
      plugins: {
        title: {
          display: true,
          text: `${props.stock.symbol} - Price & Moving Averages`,
          font: { size: 14, weight: 'bold' }
        },
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 15
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          animation: false, // Disable tooltip animations
          callbacks: {
            label: function (context) {
              const value = context.parsed.y
              return value !== null ? context.dataset.label + ': ₹' + value.toFixed(2) : null
            }
          }
        },
        zoom: getZoomOptions().zoom
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Date',
            font: { size: 12 }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            maxTicksLimit: 8
          }
        },
        y: {
          display: true,
          min: minValue,
          max: maxValue,
          title: {
            display: true,
            text: 'Price (₹)',
            font: { size: 12 }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            callback: function (value) {
              return '₹' + value.toFixed(2)
            }
          }
        }
      }
    }
  }
}

const getRSIChartConfig = (data) => {
  const labels = data.map(item => {
    const date = new Date(item.date)
    return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })
  })
  const rsiData = data.map(item => item.rsi ? parseFloat(item.rsi) : null)

  return {
    type: 'line',
    data: {
      labels,
      datasets: [
        {
          label: 'RSI (14)',
          data: rsiData,
          borderColor: '#8b5cf6',
          backgroundColor: 'rgba(139, 92, 246, 0.1)',
          borderWidth: 2,
          tension: 0.1,
          fill: false,
          pointRadius: 1,
          pointHoverRadius: 4,
          spanGaps: true
        }
      ]
    },
    options: {
      ...getPerformanceOptions(),
      plugins: {
        title: {
          display: true,
          text: `${props.stock.symbol} - RSI (Relative Strength Index)`,
          font: { size: 14, weight: 'bold' }
        },
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 15
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          animation: false, // Disable tooltip animations
          callbacks: {
            label: function (context) {
              const value = context.parsed.y
              if (value === null) return null
              let status = ''
              if (value >= 70) status = ' (Overbought)'
              else if (value <= 30) status = ' (Oversold)'
              return context.dataset.label + ': ' + value.toFixed(2) + status
            }
          }
        },
        zoom: getZoomOptions().zoom
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Date',
            font: { size: 12 }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            maxTicksLimit: 8
          }
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'RSI',
            font: { size: 12 }
          },
          min: 0,
          max: 100,
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            stepSize: 10,
            callback: function (value) {
              if (value === 70) return '70 (Overbought)'
              if (value === 30) return '30 (Oversold)'
              return value
            }
          }
        }
      },
      elements: {
        point: {
          radius: 0,
          hoverRadius: 4
        }
      }
    }
  }
}

const getCandlestickChartConfig = (data) => {
  console.log('📊 Configuring candlestick chart with data:', data.length, 'points')

  // Optimize data processing for better performance
  const candlestickData = data.map(d => {
    const candleData = {
      x: new Date(d.date).valueOf(),
      o: parseFloat(d.open) || 0,
      h: parseFloat(d.high) || 0,
      l: parseFloat(d.low) || 0,
      c: parseFloat(d.close) || 0
    }
    return candleData
  }).filter(d => d.o > 0 && d.h > 0 && d.l > 0 && d.c > 0) // Filter out invalid data

  const sma50Data = data.map(d => ({ x: new Date(d.date).valueOf(), y: d.sma50 })).filter(d => d.y !== null);
  const sma200Data = data.map(d => ({ x: new Date(d.date).valueOf(), y: d.sma200 })).filter(d => d.y !== null);

  console.log('📊 Candlestick data processed:', candlestickData.length, 'valid candles')

  const datasets = [{
    label: 'Candlestick',
    data: candlestickData,
    color: {
      up: '#10b981',
      down: '#ef4444',
      unchanged: '#6b7280',
    },
    borderColor: {
      up: '#10b981',
      down: '#ef4444',
      unchanged: '#6b7280',
    }
  }];

  if (sma50Data.length > 0) {
    datasets.push({
      label: '50-day SMA',
      data: sma50Data,
      type: 'line',
      borderColor: 'rgba(234, 179, 8, 1)',
      backgroundColor: 'rgba(234, 179, 8, 0.2)',
      borderWidth: 2,
      pointRadius: 0,
      pointHoverRadius: 0,
      tension: 0.4,
      spanGaps: true
    });
  }

  if (sma200Data.length > 0) {
    datasets.push({
      label: '200-day SMA',
      data: sma200Data,
      type: 'line',
      borderColor: 'rgba(192, 38, 211, 1)',
      backgroundColor: 'rgba(192, 38, 211, 0.2)',
      borderWidth: 2,
      pointRadius: 0,
      pointHoverRadius: 0,
      tension: 0.4,
      spanGaps: true
    });
  }

  console.log('📊 Final candlestick datasets:', datasets)

  return {
    type: 'candlestick',
    data: {
      datasets: datasets
    },
    options: {
      responsive: true,
      maintainAspectRatio: false,
      resizeDelay: 0,
      // Performance optimizations
      animation: {
        duration: 0 // Disable animations for better performance during interactions
      },
      interaction: {
        mode: 'nearest',
        intersect: false,
        includeInvisible: false
      },
      scales: {
        x: {
          type: 'time',
          time: {
            unit: 'day'
          },
          grid: {
            display: false
          }
        },
        y: {
          grid: {
            color: 'rgba(200, 200, 200, 0.1)'
          },
          ticks: {
            callback: function (value) {
              return '₹' + value.toFixed(2);
            }
          }
        }
      },
      plugins: {
        legend: {
          display: true,
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 20
          }
        },
        tooltip: {
          enabled: true,
          mode: 'index',
          intersect: false,
          animation: false, // Disable tooltip animations for better performance
          callbacks: {
            title: function (context) {
              return new Date(context[0].parsed.x).toLocaleDateString();
            },
            label: function (context) {
              const data = context.raw;
              if (data.o !== undefined) {
                return [
                  `Open: ₹${data.o.toFixed(2)}`,
                  `High: ₹${data.h.toFixed(2)}`,
                  `Low: ₹${data.l.toFixed(2)}`,
                  `Close: ₹${data.c.toFixed(2)}`
                ];
              }
              return `${context.dataset.label}: ₹${context.parsed.y.toFixed(2)}`;
            }
          }
        },
        zoom: getZoomOptions().zoom
      }
    }
  };
};

const getMACDChartConfig = (data) => {
  const labels = data.map(item => {
    const date = new Date(item.date)
    return date.toLocaleDateString('en-IN', { month: 'short', day: 'numeric' })
  })

  const macdLineData = data.map(item => {
    const value = item.macdLine || item.macd_line || item.macd
    return value ? parseFloat(value) : null
  })
  const signalLineData = data.map(item => {
    const value = item.macdSignal || item.macd_signal || item.signal
    return value ? parseFloat(value) : null
  })
  const histogramData = data.map(item => {
    const value = item.macdHistogram || item.macd_histogram || item.histogram
    return value ? parseFloat(value) : null
  })

  // Calculate min and max values for better scaling
  const allValues = [...macdLineData, ...signalLineData, ...histogramData].filter(v => v !== null && !isNaN(v))
  const minValue = allValues.length > 0 ? Math.min(...allValues) : -1
  const maxValue = allValues.length > 0 ? Math.max(...allValues) : 1
  const padding = Math.abs(maxValue - minValue) * 0.1 || 0.1

  return {
    type: 'line',
    data: {
      labels,
      datasets: [
        {
          label: 'MACD Line',
          data: macdLineData,
          borderColor: '#06b6d4',
          backgroundColor: 'transparent',
          borderWidth: 2,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 3,
          spanGaps: true
        },
        {
          label: 'Signal Line',
          data: signalLineData,
          borderColor: '#f59e0b',
          backgroundColor: 'transparent',
          borderWidth: 2,
          tension: 0.1,
          pointRadius: 0,
          pointHoverRadius: 3,
          spanGaps: true
        },
        {
          label: 'MACD Histogram',
          data: histogramData,
          type: 'bar',
          backgroundColor: histogramData.map(item =>
            item !== null && item >= 0 ? 'rgba(16, 185, 129, 0.6)' : 'rgba(239, 68, 68, 0.6)'
          ),
          borderColor: histogramData.map(item =>
            item !== null && item >= 0 ? '#10b981' : '#ef4444'
          ),
          borderWidth: 1,
          yAxisID: 'y1'
        }
      ]
    },
    options: {
      ...getPerformanceOptions(),
      plugins: {
        title: {
          display: true,
          text: `${props.stock.symbol} - MACD (Moving Average Convergence Divergence)`,
          font: { size: 14, weight: 'bold' }
        },
        legend: {
          position: 'top',
          labels: {
            usePointStyle: true,
            padding: 15
          }
        },
        tooltip: {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
          titleColor: 'white',
          bodyColor: 'white',
          borderColor: 'rgba(255, 255, 255, 0.2)',
          borderWidth: 1,
          animation: false, // Disable tooltip animations
          callbacks: {
            label: function (context) {
              const value = context.parsed.y
              return value !== null ? context.dataset.label + ': ' + value.toFixed(4) : null
            }
          }
        },
        zoom: getZoomOptions().zoom
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Date',
            font: { size: 12 }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          ticks: {
            maxTicksLimit: 8
          }
        },
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          title: {
            display: true,
            text: 'MACD',
            font: { size: 12 }
          },
          grid: {
            color: 'rgba(0, 0, 0, 0.1)'
          },
          suggestedMin: minValue - padding,
          suggestedMax: maxValue + padding,
          ticks: {
            callback: function (value) {
              return value.toFixed(4)
            }
          }
        },
        y1: {
          type: 'linear',
          display: false,
          position: 'right',
          grid: {
            drawOnChartArea: false
          },
          suggestedMin: minValue - padding,
          suggestedMax: maxValue + padding
        }
      },
      elements: {
        point: {
          radius: 0,
          hoverRadius: 4
        }
      }
    }
  }
}

// Helper functions for chart
const getChartPeriodText = () => {
  if (chartPeriod.value === 'all') {
    const dataPoints = technicalAnalysis.value?.historicalDataPoints
    if (!dataPoints) return 'All available data'
    return `All data (${dataPoints} days)`
  }

  const days = parseInt(chartPeriod.value)
  if (days === 30) return '30 Days'
  if (days === 60) return '60 Days'
  if (days === 90) return '90 Days'
  if (days === 180) return '6 Months'
  if (days === 365) return '1 Year'
  return `${days} Days`
}

const formatTimestamp = (timestamp) => {
  if (!timestamp) return 'N/A'
  return new Date(timestamp).toLocaleString()
}

// Watch for chart type changes
watch(activeChart, () => {
  if (technicalAnalysis.value) {
    initTechnicalChart()
  }
})

// Watch for technical analysis data changes
watch(() => technicalAnalysis.value, (newData) => {
  if (newData && activeTab.value === 'technical') {
    nextTick(() => {
      initTechnicalChart()
    })
  }
})

// Watch for tab changes to initialize chart when switching to technical tab
watch(activeTab, (newTab) => {
  if (newTab === 'technical' && technicalAnalysis.value) {
    nextTick(() => {
      initTechnicalChart()
    })
  }
})

// Watch for chart type changes to reinitialize chart
watch(activeChart, () => {
  if (activeTab.value === 'technical' && technicalAnalysis.value) {
    nextTick(() => {
      initTechnicalChart()
    })
  }
})

// Cleanup chart on unmount
onBeforeUnmount(() => {
  if (technicalChart) {
    technicalChart.destroy()
    technicalChart = null
  }
})

// CLIENT-ONLY tabs handle their own initialization
</script>
