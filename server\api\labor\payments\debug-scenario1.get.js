/**
 * Simple debug endpoint to test the findFinalPayments logic
 */
export default defineEventHandler(async (event) => {
  try {
    // Mock data exactly as described in the scenario
    const payments = [
      { id: '1', payment_date: '2025-04-06', amount: 2000, payment_type: 'Advance' },
      { id: '2', payment_date: '2025-04-09', amount: 1500, payment_type: 'Advance' },
      { id: '3', payment_date: '2025-04-14', amount: 2000, payment_type: 'Site Expense' },
      { id: '4', payment_date: '2025-04-20', amount: 3000, payment_type: 'Advance' },
      { id: '5', payment_date: '2025-04-28', amount: 6500, payment_type: 'Final Payment' }
    ]

    const periods = [
      { period_start: '2025-04-04', period_end: '2025-04-26' }
    ]

    // Manual calculation to verify logic
    const period = periods[0]
    const periodStart = new Date(period.period_start)
    const periodEnd = new Date(period.period_end)
    
    console.log('Period:', period)
    console.log('Period Start Date:', periodStart)
    console.log('Period End Date:', periodEnd)

    // Find payments in period (excluding final payments)
    const paymentsInPeriod = payments.filter(p => 
      p.payment_date >= period.period_start && 
      p.payment_date <= period.period_end &&
      p.payment_type !== 'Final Payment'
    )
    
    console.log('Payments in period:', paymentsInPeriod)
    
    const totalPaymentsInPeriod = paymentsInPeriod.reduce((sum, p) => sum + p.amount, 0)
    console.log('Total payments in period:', totalPaymentsInPeriod)

    // Find final payment
    const finalPayment = payments.find(p => p.payment_type === 'Final Payment')
    console.log('Final payment:', finalPayment)
    
    if (finalPayment) {
      const finalPaymentDate = new Date(finalPayment.payment_date)
      console.log('Final payment date:', finalPaymentDate)
      console.log('Days after period end:', Math.floor((finalPaymentDate.getTime() - periodEnd.getTime()) / (1000 * 60 * 60 * 24)))
    }

    // Test the new findFinalPayments logic manually
    const finalPaymentsList = payments.filter(p => p.payment_type === 'Final Payment')
      .sort((a, b) => new Date(a.payment_date) - new Date(b.payment_date))
    
    let bestFinalPayment = null
    let bestScore = -1
    
    finalPaymentsList.forEach(finalPayment => {
      const paymentDate = new Date(finalPayment.payment_date)
      let score = 0
      
      if (paymentDate >= periodStart && paymentDate <= periodEnd) {
        // Final payment within period - highest priority
        score = 1000 + (periodEnd.getTime() - paymentDate.getTime())
        console.log(`Final payment ${finalPayment.id} within period, score: ${score}`)
      } else if (paymentDate > periodEnd) {
        // Final payment after period - check if it could settle this period
        const daysDiff = Math.floor((paymentDate.getTime() - periodEnd.getTime()) / (1000 * 60 * 60 * 24))
        if (daysDiff <= 30) { // Within 30 days of period end
          score = 500 - daysDiff
          console.log(`Final payment ${finalPayment.id} after period (${daysDiff} days), score: ${score}`)
        }
      }
      
      if (score > bestScore) {
        bestScore = score
        bestFinalPayment = finalPayment
      }
    })

    console.log('Best final payment:', bestFinalPayment)
    console.log('Best score:', bestScore)

    // Calculate expected result
    const expectedTotalPayments = totalPaymentsInPeriod + (bestFinalPayment ? bestFinalPayment.amount : 0)
    const expectedUnpaidAmount = bestFinalPayment ? 0 : 15000 - totalPaymentsInPeriod // Assuming 15000 total earnings

    return {
      success: true,
      debug: {
        inputData: {
          payments,
          periods,
          totalEarnings: 15000
        },
        calculations: {
          paymentsInPeriod,
          totalPaymentsInPeriod,
          finalPayment,
          bestFinalPayment,
          bestScore,
          expectedTotalPayments,
          expectedUnpaidAmount
        },
        expectedResult: {
          period: period,
          totalPayments: expectedTotalPayments,
          unpaidAmount: expectedUnpaidAmount,
          type: bestFinalPayment ? 'settled' : 'ongoing'
        },
        userExpectedResult: {
          period: '04-04-2025 - 26-04-2025',
          totalPayments: 15000,
          unpaidAmount: 0,
          explanation: 'Final payment made out of range but included as final settlement'
        }
      }
    }

  } catch (error) {
    console.error('Debug error:', error)
    return {
      success: false,
      error: error.message,
      stack: error.stack
    }
  }
})
