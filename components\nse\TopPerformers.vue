<template>
  <div class="bg-white shadow rounded-lg p-6 mb-6">
    <h2 class="text-lg font-medium text-gray-900 mb-4">Top Performers</h2>

    <div v-if="isLoading" class="flex justify-center items-center h-40">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div>
        <h3 class="text-md font-medium text-gray-700 mb-3">Top Gainers</h3>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Symbol</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Close Price</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Current Price</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Change %</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="stock in topGainers" :key="stock.SYMBOL">
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">{{ stock.SYMBOL }}</td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">₹{{ stock.CLOSE_PRICE.toFixed(2) }}</td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">₹{{ stock.LAST_PRICE.toFixed(2) }}</td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-green-600">+{{ calculateChangePercentage(stock).toFixed(2) }}%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <div>
        <h3 class="text-md font-medium text-gray-700 mb-3">Top Losers</h3>
        <div class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
          <table class="min-w-full divide-y divide-gray-300">
            <thead class="bg-gray-50">
              <tr>
                <th scope="col" class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6">Symbol</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Close Price</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Current Price</th>
                <th scope="col" class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900">Change %</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
              <tr v-for="stock in topLosers" :key="stock.SYMBOL">
                <td class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6">{{ stock.SYMBOL }}</td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">₹{{ stock.CLOSE_PRICE.toFixed(2) }}</td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-gray-500">₹{{ stock.LAST_PRICE.toFixed(2) }}</td>
                <td class="whitespace-nowrap px-3 py-4 text-sm text-red-600">{{ calculateChangePercentage(stock).toFixed(2) }}%</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';

const props = defineProps({
  nseRecords: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

// Calculate percentage change
function calculateChangePercentage(stock) {
  return ((stock.CLOSE_PRICE - stock.PREV_CLOSE) / stock.PREV_CLOSE) * 100;
}

// Get top 5 gainers
const topGainers = computed(() => {
  return [...props.nseRecords]
    .filter(stock => stock.CLOSE_PRICE > stock.PREV_CLOSE)
    .sort((a, b) => calculateChangePercentage(b) - calculateChangePercentage(a))
    .slice(0, 5);
});

// Get top 5 losers
const topLosers = computed(() => {
  return [...props.nseRecords]
    .filter(stock => stock.CLOSE_PRICE < stock.PREV_CLOSE)
    .sort((a, b) => calculateChangePercentage(a) - calculateChangePercentage(b))
    .slice(0, 5);
});
</script>
