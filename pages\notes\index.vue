<template>
  <div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4 sm:gap-0">
      <h1 class="text-2xl font-bold">My Notes</h1>
      <button @click="openNewNoteModal"
              class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow transition duration-300 w-full sm:w-auto">
        <span class="flex items-center justify-center">
          <Icon name="heroicons:plus" class="w-5 h-5 mr-2" />
          New Note
        </span>
      </button>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
    </div>

    <!-- Notes Grid -->
    <div v-else-if="notes.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div v-for="note in notes" :key="note.id"
           class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 border border-gray-200">
        <div class="p-4">
          <div class="flex justify-between items-start mb-2">
            <h3 class="text-lg font-semibold text-gray-800 break-words">{{ note.title }}</h3>
            <div class="flex space-x-2 ml-2 flex-shrink-0">
              <button @click="editNote(note)"
                      class="text-blue-500 hover:text-blue-600 p-1 rounded-full hover:bg-blue-50">
                <Icon name="heroicons:pencil-square" class="w-5 h-5" />
              </button>
              <button @click="confirmDelete(note)"
                      class="text-red-500 hover:text-red-600 p-1 rounded-full hover:bg-red-50">
                <Icon name="heroicons:trash" class="w-5 h-5" />
              </button>
            </div>
          </div>
          <NoteContent :content="note.content" class="text-gray-600 whitespace-pre-wrap line-clamp-3 break-words" />
          <div class="mt-4 flex justify-between items-center flex-wrap gap-2">
            <div class="text-sm text-gray-500">
              Last updated: {{ formatDate(note.updatedAt || note.createdAt) }}
            </div>
            <button v-if="isContentTruncated(note.content)"
                    @click="editNote(note)"
                    class="text-sm text-blue-500 hover:text-blue-600">
              Read more
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-else class="text-center py-8">
      <div class="text-gray-500 mb-4">No notes yet</div>
      <button @click="openNewNoteModal"
              class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg shadow transition duration-300">
        Create your first note
      </button>
    </div>

    <!-- Note Modal -->
    <Teleport to="body">
      <div v-if="showNoteModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 md:p-4 modal-wrapper">
        <NoteForm
          :note="currentNote"
          :is-editing="!!currentNote"
          @submit="handleSubmit"
          @cancel="closeModal"
        />
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useNotes } from '~/composables/business/useNotes';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import type { Note } from '~/server/models/Note';
import NoteContent from '~/components/NoteContent.vue';

definePageMeta({
  requiresAuth: true,
  middleware: ['sub-contractor-access']
});

// Set page title
usePageTitle('My Notes', 'Manage and organize your personal notes');

// State
const showNoteModal = ref(false);
const currentNote = ref<Note | undefined>();
const { notes, isLoading, fetchNotes, createNote, updateNote, deleteNote } = useNotes();

// Fetch notes on component mount
onMounted(async () => {
  await fetchNotes();
});

// Actions
const editNote = (note: Note) => {
  currentNote.value = note;
  showNoteModal.value = true;
  // Add overflow hidden to body to prevent scrolling
  if (typeof document !== 'undefined') {
    document.body.style.overflow = 'hidden';
  }
};

const openNewNoteModal = () => {
  currentNote.value = undefined;
  showNoteModal.value = true;
  // Add overflow hidden to body to prevent scrolling
  if (typeof document !== 'undefined') {
    document.body.style.overflow = 'hidden';
  }
};

const closeModal = () => {
  showNoteModal.value = false;
  currentNote.value = undefined;

  // Make sure body scrolling is enabled
  if (typeof document !== 'undefined') {
    // Remove overflow hidden from body
    document.body.style.overflow = '';

    // More aggressive cleanup of any editor elements
    setTimeout(() => {
      // Find and remove any hanging toolbar elements
      // This includes all possible floating toolbars and panels
      const elements = document.querySelectorAll(
        '.rte-toolbar, .rte-statusbar, .rte-floatingtoolbar, .rte-floatpanel, rte-floatpanel, .rte-floatpanel-paragraphop'
      );
      elements.forEach(element => {
        if (element && element.parentNode) {
          element.parentNode.removeChild(element);
        }
      });

      // Additional cleanup for any floating panels that might be in the body
      const floatPanels = document.querySelectorAll('body > .rte-floatpanel, body > rte-floatpanel, body > .rte-floatpanel-paragraphop');
      floatPanels.forEach(panel => {
        if (panel && panel.parentNode) {
          panel.parentNode.removeChild(panel);
        }
      });
    }, 100);
  }
};

const handleSubmit = async (data: { title: string; content: string }) => {
  try {
    if (currentNote.value) {
      await updateNote(currentNote.value.id!, data);
    } else {
      await createNote(data);
    }
    closeModal();
  } catch (error) {
    // Silent error handling - UI feedback is provided by the composable
  }
};

const confirmDelete = async (note: Note) => {
  if (!confirm('Are you sure you want to delete this note?')) return;

  try {
    await deleteNote(note.id!);
  } catch (error) {
    console.error('Error deleting note:', error);
  }
};

// Utilities
const isContentTruncated = (content: string) => {
  return content.split('\n').length > 3 || content.length > 200;
};

const formatDate = (ts) => {
    let date;

// If ts is a Firestore Timestamp instance with a toDate method
if (ts && typeof ts.toDate === "function") {
  date = ts.toDate();
}
// Else if it's an object with _seconds and _nanoseconds properties
else if (
  ts &&
  typeof ts._seconds === "number" &&
  typeof ts._nanoseconds === "number"
) {
  // Convert seconds and nanoseconds to milliseconds.
  date = new Date(ts._seconds * 1000 + ts._nanoseconds / 1000000);
}
// Otherwise, try to make a new Date from the value directly
else {
  date = new Date(ts);
}

// Check if the date is valid.
if (isNaN(date.getTime())) {
  return "Invalid Date";
}

// Format the date as DD-MM-YYYY
const day = String(date.getDate()).padStart(2, "0");
const month = String(date.getMonth() + 1).padStart(2, "0"); // Months are zero-indexed.
const year = date.getFullYear();

return `${day}-${month}-${year}`;
};
</script>

<style>
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  max-height: 4.5em; /* Fallback for non-webkit browsers */
}

/* Make the modal wrapper better on mobile */
.modal-wrapper {
  padding: 0.5rem;
  box-sizing: border-box;
}

@media (max-width: 640px) {
  .modal-wrapper {
    padding: 0.25rem;
  }
}

/* Fix line wrapping for long text in note titles and content */
.break-words {
  word-break: break-word;
  overflow-wrap: break-word;
}
</style>