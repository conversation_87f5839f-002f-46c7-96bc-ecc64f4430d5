<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <div class="text-center">
        <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
          Reset your password
        </h2>
        <p class="mt-2 text-sm text-gray-600">
          Enter your new password below
        </p>
      </div>
    </div>

    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
        <form v-if="!resetComplete" @submit.prevent="handleSubmit" class="space-y-6">
          <div>
            <label for="password" class="block text-sm font-medium text-gray-700">
              New Password
            </label>
            <div class="mt-1">
              <input
                id="password"
                v-model="password"
                name="password"
                type="password"
                autocomplete="new-password"
                required
                :disabled="loading"
                @input="validatePassword"
                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Enter your new password"
              />
            </div>
          </div>

          <div>
            <label for="confirmPassword" class="block text-sm font-medium text-gray-700">
              Confirm New Password
            </label>
            <div class="mt-1">
              <input
                id="confirmPassword"
                v-model="confirmPassword"
                name="confirmPassword"
                type="password"
                autocomplete="new-password"
                required
                :disabled="loading"
                class="appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                placeholder="Confirm your new password"
              />
            </div>
          </div>

          <!-- Password Strength Indicator -->
          <div v-if="passwordValidation" class="space-y-2">
            <div class="flex items-center space-x-2">
              <span class="text-sm font-medium text-gray-700">Password Strength:</span>
              <span 
                :class="{
                  'text-red-600': passwordValidation.strength === 'weak',
                  'text-yellow-600': passwordValidation.strength === 'medium',
                  'text-green-600': passwordValidation.strength === 'strong'
                }"
                class="text-sm font-medium capitalize"
              >
                {{ passwordValidation.strength }}
              </span>
            </div>
            
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                :class="{
                  'bg-red-500': passwordValidation.strength === 'weak',
                  'bg-yellow-500': passwordValidation.strength === 'medium',
                  'bg-green-500': passwordValidation.strength === 'strong'
                }"
                class="h-2 rounded-full transition-all duration-300"
                :style="{ width: passwordValidation.score + '%' }"
              ></div>
            </div>

            <div v-if="passwordValidation.errors.length > 0" class="text-sm text-red-600">
              <ul class="list-disc list-inside space-y-1">
                <li v-for="error in passwordValidation.errors" :key="error">{{ error }}</li>
              </ul>
            </div>
          </div>

          <div v-if="errorMsg" class="rounded-md bg-red-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">
                  {{ errorMsg }}
                </h3>
              </div>
            </div>
          </div>

          <div>
            <button
              type="submit"
              :disabled="loading || !password || !confirmPassword || password !== confirmPassword || (passwordValidation && !passwordValidation.isValid)"
              class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <svg v-if="loading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ loading ? 'Resetting...' : 'Reset Password' }}
            </button>
          </div>
        </form>

        <!-- Success Message -->
        <div v-else class="text-center space-y-4">
          <div class="rounded-md bg-green-50 p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">
                  Password reset successfully!
                </h3>
                <p class="mt-2 text-sm text-green-700">
                  Your password has been updated. You can now log in with your new password.
                </p>
              </div>
            </div>
          </div>
          
          <NuxtLink
            to="/login"
            class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Go to Login
          </NuxtLink>
        </div>

        <div v-if="!resetComplete" class="text-center mt-6">
          <NuxtLink
            to="/login"
            class="text-sm text-blue-600 hover:text-blue-500"
          >
            Back to login
          </NuxtLink>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from '#app'
import { usePageTitle } from '~/composables/ui/usePageTitle'

// Set page title
usePageTitle('Reset Password')

// Define page meta
definePageMeta({
  layout: false,
  auth: false
})

// Router and route
const route = useRoute()
const router = useRouter()

// Reactive data
const password = ref('')
const confirmPassword = ref('')
const loading = ref(false)
const errorMsg = ref('')
const resetComplete = ref(false)
const passwordValidation = ref(null)
const token = ref('')

// Validate password in real-time
const validatePassword = async () => {
  if (!password.value) {
    passwordValidation.value = null
    return
  }

  try {
    const response = await $fetch('/api/auth/validate-password', {
      method: 'POST',
      body: {
        password: password.value
      }
    })
    
    passwordValidation.value = response
  } catch (error) {
    console.error('Password validation error:', error)
  }
}

// Handle form submission
const handleSubmit = async () => {
  if (!password.value || !confirmPassword.value) {
    errorMsg.value = 'Please fill in all fields'
    return
  }

  if (password.value !== confirmPassword.value) {
    errorMsg.value = 'Passwords do not match'
    return
  }

  loading.value = true
  errorMsg.value = ''

  try {
    const response = await $fetch('/api/auth/reset-password', {
      method: 'POST',
      body: {
        token: token.value,
        password: password.value,
        confirmPassword: confirmPassword.value
      }
    })

    resetComplete.value = true

  } catch (error) {
    console.error('Password reset error:', error)
    errorMsg.value = error.data?.message || 'An error occurred. Please try again.'
  } finally {
    loading.value = false
  }
}

// Check for token on mount
onMounted(() => {
  token.value = route.query.token || '';

  if (!token.value) {
    errorMsg.value = 'Invalid or missing reset token';
    router.push('/forgot-password');
  }
});
</script>
