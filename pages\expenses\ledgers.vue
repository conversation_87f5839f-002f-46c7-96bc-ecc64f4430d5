<template>
  <div class="container mx-auto px-4 py-8 mt-0">
    <!-- Index notification component -->
    <IndexNotification v-if="showIndexNotification" :index-url="indexUrl" />
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-2xl font-bold text-gray-900">Cash & Bank Accounts Management</h1>

      <button
        @click="showAddLedgerModal = true"
        class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
      >
        <span class="flex items-center">
          <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add Ledger
        </span>
      </button>
    </div>

    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading ledgers...</span>
    </div>

    <LedgerList
      v-else
      :ledgers="ledgers"
      :is-loading="isLoading"
      @view="viewLedger"
      @edit="editLedger"
      @delete="deleteLedger"
    />

    <!-- Add/Edit Ledger Modal -->
    <div v-if="showAddLedgerModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">{{ editingLedger ? 'Edit Ledger' : 'Add New Ledger' }}</h3>
          <button
            @click="closeAddLedgerModal"
            class="text-gray-400 hover:text-gray-500"
          >
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-4">
          <LedgerForm
            :ledger="editingLedger"
            :is-loading="isSubmitting"
            @submit="saveLedger"
            @cancel="closeAddLedgerModal"
          />
        </div>
      </div>
    </div>

    <!-- View Ledger Modal -->
    <div v-if="showViewLedgerModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-6xl w-full mx-4 my-8 max-h-[90vh] overflow-y-auto">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Ledger Details</h3>
          <button
            @click="closeViewLedgerModal"
            class="text-gray-400 hover:text-gray-500"
          >
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-6">
          <div v-if="viewingLedger" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="text-sm font-medium text-gray-500">Name</h4>
                <p class="mt-1 text-sm text-gray-900">{{ viewingLedger.name }}</p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">Type</h4>
                <p class="mt-1">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': viewingLedger.type === 'cash',
                      'bg-blue-100 text-blue-800': viewingLedger.type === 'bank',
                      'bg-purple-100 text-purple-800': viewingLedger.type === 'party'
                    }"
                  >
                    {{
                      viewingLedger.type === 'cash' ? 'Cash' :
                      viewingLedger.type === 'bank' ? 'Bank' :
                      viewingLedger.type === 'party' ? 'Party' :
                      viewingLedger.type
                    }}
                  </span>
                </p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">Opening Balance</h4>
                <p class="mt-1 text-sm text-gray-900">{{ formatCurrency(viewingLedger.openingBalance) }}</p>
              </div>

              <div class="flex flex-col">
                <div class="flex items-center justify-between">
                  <h4 class="text-sm font-medium text-gray-500">Current Balance</h4>
                  <button
                    @click="recalculateLedgerBalance"
                    class="px-2 py-1 text-xs bg-blue-500 hover:bg-blue-600 text-white rounded flex items-center"
                    :disabled="isRecalculating"
                  >
                    <svg v-if="isRecalculating" class="animate-spin -ml-1 mr-2 h-3 w-3 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span>{{ isRecalculating ? 'Recalculating...' : 'Recalculate Balance' }}</span>
                  </button>
                </div>
                <p class="mt-1 text-sm font-medium" :class="getBalanceClass(viewingLedger.currentBalance)">
                  {{ formatCurrency(viewingLedger.currentBalance) }}
                </p>
              </div>

              <div v-if="viewingLedger.type === 'bank' && viewingLedger.bankDetails" class="md:col-span-2">
                <h4 class="text-sm font-medium text-gray-500">Bank Details</h4>
                <div class="mt-1 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p class="text-xs text-gray-500">Bank Name</p>
                    <p class="text-sm text-gray-900">{{ viewingLedger.bankDetails.bankName || '-' }}</p>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Account Number</p>
                    <p class="text-sm text-gray-900">{{ viewingLedger.bankDetails.accountNumber || '-' }}</p>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">IFSC Code</p>
                    <p class="text-sm text-gray-900">{{ viewingLedger.bankDetails.ifscCode || '-' }}</p>
                  </div>
                  <div>
                    <p class="text-xs text-gray-500">Branch</p>
                    <p class="text-sm text-gray-900">{{ viewingLedger.bankDetails.branch || '-' }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- Transactions Section -->
            <div class="mt-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Transactions</h3>

              <div v-if="!viewingLedger.transactions || viewingLedger.transactions.length === 0" class="text-center py-8 text-gray-500">
                No transactions found for this ledger.
              </div>

              <div v-else class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-indigo-600">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Description
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Type
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        Balance
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="transaction in viewingLedger.transactions" :key="transaction.id" class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ formatDate(transaction.date) }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ transaction.description }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span
                          class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                          :class="transaction.type === 'credit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                        >
                          {{ transaction.type === 'credit' ? 'Credit' : 'Debit' }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="transaction.type === 'credit' ? 'text-green-600' : 'text-red-600'">
                        {{ formatCurrency(transaction.amount) }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getBalanceClass(getBalanceByCreatedAt(transaction))">
                        {{ formatCurrency(getBalanceByCreatedAt(transaction)) }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
              <button
                @click="editLedger(viewingLedger.id)"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Edit
              </button>
              <button
                @click="closeViewLedgerModal"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true
});

import { ref, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useLedgers } from '~/composables/expenses/useLedgers';
import LedgerList from '~/components/expenses/LedgerList.vue';
import LedgerForm from '~/components/expenses/LedgerForm.vue';
import { usePageTitle } from '~/composables/ui/usePageTitle';

// Set page title
usePageTitle('Cash & Bank Accounts', 'Manage your cash and bank accounts ledgers');

// Import components
const IndexNotification = () => import('~/components/expenses/IndexNotification.vue');

const route = useRoute();
const router = useRouter();

// State
const isLoading = ref(true);
const isSubmitting = ref(false);
const isRecalculating = ref(false);
const showAddLedgerModal = ref(false);
const showViewLedgerModal = ref(false);
const editingLedger = ref(null);
const viewingLedger = ref(null);
const showIndexNotification = ref(false);
const indexUrl = ref('');

// Get composables
const {
  ledgers,
  currentLedger,
  fetchLedgers,
  fetchLedgerById,
  createLedger,
  updateLedger,
  deleteLedger: deleteLedgerAction
} = useLedgers();

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};

const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString();
};

const getBalanceClass = (balance) => {
  return balance < 0 ? 'text-red-600' : 'text-green-600';
};

const getBalanceByCreatedAt = (targetTransaction) => {
  if (!viewingLedger.value?.transactions) return 0;

  // Sort transactions by createdAt (oldest first) for balance calculation
  const sortedByCreatedAt = [...viewingLedger.value.transactions].sort((a, b) => {
    return new Date(a.createdAt) - new Date(b.createdAt);
  });

  // Start with opening balance
  let runningBalance = viewingLedger.value.openingBalance || 0;

  // Calculate balance up to and including the target transaction
  for (const transaction of sortedByCreatedAt) {
    if (transaction.type === 'credit') {
      runningBalance += transaction.amount;
    } else if (transaction.type === 'debit') {
      runningBalance -= transaction.amount;
    }

    // If this is our target transaction, return the balance
    if (transaction.id === targetTransaction.id) {
      return runningBalance;
    }
  }

  return runningBalance;
};

const viewLedger = async (id) => {
  try {
    isLoading.value = true;

    try {
      const ledger = await fetchLedgerById(id);

      if (ledger) {
        viewingLedger.value = ledger;
        showViewLedgerModal.value = true;
      } else {
        console.error('No ledger data received');
        alert('Failed to load ledger details. Please try again.');
      }

      // Check if there are no transactions but should be
      if (ledger && (!ledger.transactions || ledger.transactions.length === 0) &&
          ledger.currentBalance !== ledger.openingBalance) {
        console.warn('Ledger has transactions but none were returned');

        // Show a notification about missing transactions
        indexUrl.value = 'https://console.firebase.google.com';
        showIndexNotification.value = true;

        alert('Note: This ledger has transactions, but they could not be loaded due to a missing database index. ' +
              'Please check the notification at the bottom of the page for instructions.');
      }
    } catch (fetchError) {
      console.error('Error in fetchLedgerById:', fetchError);

      // Check if this is an index error
      const errorMessage = fetchError?.message || '';
      if (errorMessage.includes('FAILED_PRECONDITION') && errorMessage.includes('index')) {

        // Extract the index URL from the error message if available
        const indexUrlMatch = errorMessage.match(/https:\/\/console\.firebase\.google\.com[^\s]+/);

        if (indexUrlMatch) {
          indexUrl.value = indexUrlMatch[0];
          showIndexNotification.value = true;
        } else {
          indexUrl.value = 'https://console.firebase.google.com';
          showIndexNotification.value = true;
        }

        alert(`Error: The database is missing a required index. Please check the notification at the bottom of the page for instructions.`);
      } else {
        alert(`Error fetching ledger details: ${fetchError.message || 'Unknown error'}`);
      }
    }
  } catch (error) {
    console.error('Error fetching ledger details:', error);
    alert(`Error: ${error.message || 'Failed to load ledger details'}`);
  } finally {
    isLoading.value = false;
  }
};

const editLedger = async (id) => {
  try {
    isLoading.value = true;

    try {
      const ledger = await fetchLedgerById(id);

      if (ledger) {
        editingLedger.value = ledger;
        showAddLedgerModal.value = true;

        // Close view modal if open
        showViewLedgerModal.value = false;

        // Check if there are no transactions but should be
        if ((!ledger.transactions || ledger.transactions.length === 0) &&
            ledger.currentBalance !== ledger.openingBalance) {
          console.warn('Ledger has transactions but none were returned during edit');
          alert('Note: This ledger has transactions, but they could not be loaded due to a missing database index. ' +
                'The administrator needs to create the required index in Firebase.');
        }
      } else {
        console.error('No ledger data received for editing');
        alert('Failed to load ledger details for editing. Please try again.');
      }
    } catch (fetchError) {
      console.error('Error in fetchLedgerById for editing:', fetchError);
      alert(`Error fetching ledger details for editing: ${fetchError.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error fetching ledger for editing:', error);
    alert(`Error: ${error.message || 'Failed to load ledger details for editing'}`);
  } finally {
    isLoading.value = false;
  }
};

const deleteLedger = async (id) => {
  try {
    isLoading.value = true;
    await deleteLedgerAction(id);

    // Close view modal if open
    if (viewingLedger.value && viewingLedger.value.id === id) {
      showViewLedgerModal.value = false;
    }
  } catch (error) {
    console.error('Error deleting ledger:', error);
  } finally {
    isLoading.value = false;
  }
};

const saveLedger = async (formData) => {
  try {
    isSubmitting.value = true;

    if (editingLedger.value) {
      // Update existing ledger
      await updateLedger(editingLedger.value.id, formData);
    } else {
      // Create new ledger
      await createLedger(formData);
    }

    // Close modal
    closeAddLedgerModal();
  } catch (error) {
    console.error('Error saving ledger:', error);
  } finally {
    isSubmitting.value = false;
  }
};

const closeAddLedgerModal = () => {
  showAddLedgerModal.value = false;
  editingLedger.value = null;
};

const closeViewLedgerModal = () => {
  showViewLedgerModal.value = false;
  viewingLedger.value = null;
};

// Recalculate ledger balance
const recalculateLedgerBalance = async () => {
  if (!viewingLedger.value || !viewingLedger.value.id) {
    alert('No ledger selected');
    return;
  }

  isRecalculating.value = true;

  try {
    // Use the same fetch method that's used elsewhere in the codebase
    const response = await $fetch(`/api/ledgers/recalculate/${viewingLedger.value.id}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      }
    });

    if (response.success) {
      // If balance was updated
      if (response.newBalance !== undefined) {
        // Update the local balance
        const oldBalance = viewingLedger.value.currentBalance;
        viewingLedger.value.currentBalance = response.newBalance;

        // Show success message with details
        const message = `Balance recalculated successfully.\n\nPrevious: ${formatCurrency(oldBalance)}\nNew: ${formatCurrency(response.newBalance)}\nDifference: ${formatCurrency(response.difference)}`;

        // Add transaction update info and adjustment removal info if available
        let detailMessage = message;

        if (response.transactionsUpdated !== undefined) {
          detailMessage += `\n\nTransactions updated: ${response.transactionsUpdated} of ${response.transactionsCount}`;
        }

        if (response.adjustmentsRemoved !== undefined && response.adjustmentsRemoved > 0) {
          detailMessage += `\nPrevious adjustment entries removed: ${response.adjustmentsRemoved}`;
        }

        alert(detailMessage);

        // Refresh the ledger to get updated transactions
        await viewLedger(viewingLedger.value.id);
      } else {
        // Balance was already correct
        alert(`Balance is already correct: ${formatCurrency(response.balance)}`);
      }
    } else {
      alert(response.message || 'Failed to recalculate balance');
    }
  } catch (error) {
    console.error('Error recalculating ledger balance:', error);
    alert(`Error: ${error.message || 'Failed to recalculate balance'}`);
  } finally {
    isRecalculating.value = false;
  }
};

// Initialize
onMounted(async () => {
  try {
    // Fetch initial data
    await fetchLedgers();

    // Check if there's a ledger ID in the route query
    const ledgerId = route.query.id;
    if (ledgerId) {
      viewLedger(ledgerId);
    }
  } catch (error) {
    console.error('Error loading ledgers data:', error);
  } finally {
    isLoading.value = false;
  }
});

// Watch for changes to route query
watch(() => route.query.id, (newId) => {
  if (newId) {
    viewLedger(newId);
  }
});
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
