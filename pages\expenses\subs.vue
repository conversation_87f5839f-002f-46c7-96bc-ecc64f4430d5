<template>
  <div class="container mx-auto px-4 py-8 mt-0">
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-2xl font-bold text-gray-900">Subcontractor Accounts Management</h1>

      <div class="flex space-x-4">
        <button
          v-if="!isSubContractorUser"
          @click="showAddSubModal = true"
          class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add Sub
          </span>
        </button>

        <!-- Add Transaction button for sub-contractors (only shown when they have exactly one account) -->
        <button
          v-if="isSubContractorUser && filteredSubsModels.length === 1"
          @click="showAddTransactionModalForSubContractor()"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Add Transaction
          </span>
        </button>

        <!-- Add Transaction button for managers/admins -->
        <button
          v-if="!isSubContractorUser"
          @click="showAddTransactionModal = true"
          class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            Add Transaction
          </span>
        </button>
      </div>
    </div>

    <!-- Filter Section -->
    <div v-if="!isSubContractorUser" class="bg-white rounded-lg shadow p-4 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Filters</h2>
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <!-- Firm ID Filter -->
        <div>
          <label for="firmIdFilter" class="block text-sm font-medium text-gray-700 mb-1">Firm ID</label>
          <input
            id="firmIdFilter"
            v-model="filters.firmId"
            type="text"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
            placeholder="Enter firm ID"
          />
        </div>

        <!-- Status Filter -->
        <div>
          <label for="statusFilter" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
          <select
            id="statusFilter"
            v-model="filters.isActive"
            class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
          >
            <option value="">All</option>
            <option :value="true">Active</option>
            <option :value="false">Inactive</option>
          </select>
        </div>

        <!-- Filter Buttons -->
        <div class="flex items-end space-x-2">
          <button
            @click="applyFilters"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Apply Filters
          </button>
          <button
            @click="resetFilters"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Reset
          </button>
        </div>
      </div>
    </div>

    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading subs...</span>
    </div>

    <SubsList
      v-else
      :subs-models="filteredSubsModels"
      :is-loading="isLoading"
      @view="viewSub"
      @edit="editSub"
      @delete="deleteSubsModel"
    />

    <!-- Add/Edit Sub Modal -->
    <div v-if="showAddSubModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-[50] p-2 sm:p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <!-- Modal Header with Gradient -->
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 flex justify-between items-center">
          <h3 class="text-lg font-medium text-white">{{ editingSub ? 'Edit Sub' : 'Add New Sub' }}</h3>
          <button
            @click="closeAddSubModal"
            class="text-white hover:text-gray-200 focus:outline-none"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <!-- Modal Body with Scrollable Content -->
        <div class="flex-1 overflow-y-auto p-4">
          <SubsForm
            ref="subsFormRef"
            :subs-model="editingSub"
            :is-loading="isSubmitting"
            @submit="saveSub"
            @cancel="closeAddSubModal"
            :show-footer-buttons="false"
          />
        </div>
        <!-- Modal Footer with Gradient -->
        <div class="bg-gradient-to-r from-indigo-600 to-purple-600 p-4 flex justify-end space-x-4">
          <button
            type="button"
            @click="closeAddSubModal"
            class="px-4 py-2 border border-white rounded-md shadow-sm text-sm font-medium text-white bg-transparent hover:bg-white hover:bg-opacity-10"
            :disabled="isSubmitting"
          >
            Cancel
          </button>
          <button
            type="button"
            @click="submitSubsForm"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-indigo-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
            :disabled="isSubmitting"
          >
            <span v-if="isSubmitting">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-indigo-700 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </span>
            <span v-else>{{ editingSub ? 'Update' : 'Save' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Add Transaction Modal -->
    <div v-if="showAddTransactionModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-[60] p-2 sm:p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden flex flex-col">
        <!-- Modal Header with Gradient -->
        <div class="bg-gradient-to-r from-green-600 to-teal-600 p-4 flex justify-between items-center">
          <h3 class="text-lg font-medium text-white">{{ editingTransaction ? 'Edit Transaction' : 'Add New Transaction' }}</h3>
          <button
            @click="closeAddTransactionModal"
            class="text-white hover:text-gray-200 focus:outline-none"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Modal Body with Scrollable Content -->
        <div class="flex-1 overflow-y-auto p-4">
          <!-- Sub Selection Dropdown (only shown when adding a new transaction without a selected sub) -->
          <div v-if="!editingTransaction && !selectedSub" class="mb-6">
            <label for="subSelect" class="block text-sm font-medium text-gray-700 mb-1">Select Sub *</label>
            <select
              id="subSelect"
              v-model="selectedSubId"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              required
            >
              <option value="">Select a Sub</option>
              <option v-for="sub in subsModels" :key="sub.id" :value="sub.id">{{ sub.name }}</option>
            </select>
            <p v-if="!selectedSubId" class="mt-1 text-sm text-red-600">
              Please select a sub before adding a transaction
            </p>
          </div>

          <SubsTransactionForm
            ref="transactionFormRef"
            :transaction="editingTransaction"
            :subs-model="selectedSub || (selectedSubId ? subsModels.find(s => s.id === selectedSubId) : null)"
            :is-loading="isSubmitting"
            @submit="saveTransaction"
            @cancel="closeAddTransactionModal"
            :show-footer-buttons="false"
            class="subs-transaction-form"
          />
        </div>

        <!-- Modal Footer with Gradient -->
        <div class="bg-gradient-to-r from-green-600 to-teal-600 p-4 flex justify-end space-x-4">
          <button
            type="button"
            @click="closeAddTransactionModal"
            class="px-4 py-2 border border-white rounded-md shadow-sm text-sm font-medium text-white bg-transparent hover:bg-white hover:bg-opacity-10"
            :disabled="isSubmitting"
          >
            Close
          </button>
          <button
            type="button"
            @click="resetTransactionForm"
            class="px-4 py-2 border border-white rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            :disabled="isSubmitting"
          >
            Reset Form
          </button>
          <button
            type="button"
            @click="() => {
              const debugId = `buttonClick_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
              console.log(`🖱️ [${debugId}] SAVE BUTTON CLICKED`);
              console.log(`🖱️ [${debugId}] isSubmitting:`, isSubmitting);
              console.log(`🖱️ [${debugId}] editingTransaction:`, editingTransaction);
              console.log(`🖱️ [${debugId}] selectedSub:`, selectedSub);
              console.log(`🖱️ [${debugId}] selectedSubId:`, selectedSubId);
              submitTransactionForm();
            }"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-green-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white"
            :disabled="isSubmitting || (!editingTransaction && !selectedSub && !selectedSubId)"
          >
            <span v-if="isSubmitting">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-green-700 inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Saving...
            </span>
            <span v-else>{{ editingTransaction ? 'Update' : 'Save' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- View Sub Modal -->
    <div v-if="showViewSubModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-[50] p-2 sm:p-4">
      <div class="bg-white rounded-lg shadow-xl w-full max-w-6xl max-h-[90vh] overflow-hidden flex flex-col" style="max-width: 95vw;">
        <!-- Modal Header with Gradient -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-4 flex justify-between items-center">
          <h3 class="text-lg font-medium text-white">Sub Details</h3>
          <button
            @click="closeViewSubModal"
            class="text-white hover:text-gray-200 focus:outline-none"
          >
            <XMarkIcon class="h-6 w-6" />
          </button>
        </div>
        <!-- Modal Body with Scrollable Content -->
        <div class="flex-1 overflow-y-auto overflow-x-hidden p-4 sm:p-6">
          <div v-if="viewingSub" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="flex items-start">
                <UserIcon class="h-5 w-5 text-indigo-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="text-sm font-medium text-gray-500">Name</h4>
                  <p class="mt-1 text-sm text-gray-900">{{ viewingSub.name }}</p>
                </div>
              </div>

              <div class="flex items-start">
                <CurrencyRupeeIcon class="h-5 w-5 text-indigo-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="text-sm font-medium text-gray-500">Balance</h4>
                  <p class="mt-1 text-sm font-medium" :class="getBalanceClass(viewingSub.balance)">
                    {{ formatCurrency(viewingSub.balance) }}
                  </p>
                </div>
              </div>

              <div class="flex items-start">
                <CheckCircleIcon class="h-5 w-5 text-indigo-600 mr-2 mt-0.5" />
                <div>
                  <h4 class="text-sm font-medium text-gray-500">Status</h4>
                  <p class="mt-1">
                    <span
                      class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                      :class="viewingSub.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                    >
                      {{ viewingSub.isActive ? 'Active' : 'Inactive' }}
                    </span>
                  </p>
                </div>
              </div>

              <div v-if="viewingSub.contactInfo" class="md:col-span-2 border-t pt-4 mt-2">
                <h4 class="text-sm font-medium text-gray-500 flex items-center">
                  <UserIcon class="h-5 w-5 text-indigo-600 mr-2" />
                  Contact Information
                </h4>
                <div class="mt-3 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div v-if="viewingSub.contactInfo.phone" class="flex items-start">
                    <PhoneIcon class="h-4 w-4 text-indigo-500 mr-2 mt-0.5" />
                    <div>
                      <p class="text-xs text-gray-500">Phone</p>
                      <p class="text-sm text-gray-900">{{ viewingSub.contactInfo.phone }}</p>
                    </div>
                  </div>
                  <div v-if="viewingSub.contactInfo.email" class="flex items-start">
                    <EnvelopeIcon class="h-4 w-4 text-indigo-500 mr-2 mt-0.5" />
                    <div>
                      <p class="text-xs text-gray-500">Email</p>
                      <p class="text-sm text-gray-900">{{ viewingSub.contactInfo.email }}</p>
                    </div>
                  </div>
                  <div v-if="viewingSub.contactInfo.address" class="md:col-span-2 flex items-start">
                    <HomeIcon class="h-4 w-4 text-indigo-500 mr-2 mt-0.5" />
                    <div>
                      <p class="text-xs text-gray-500">Address</p>
                      <p class="text-sm text-gray-900">{{ viewingSub.contactInfo.address }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Transactions Section -->
            <div class="mt-8">
              <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900 flex items-center">
                  <TableCellsIcon class="h-5 w-5 text-indigo-600 mr-2" />
                  Transactions
                </h3>
                <div class="flex space-x-2">
                  <button
                    @click="fetchTransactions(viewingSub.id)"
                    class="px-3 py-1 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 text-sm flex items-center"
                    :disabled="isLoading"
                  >
                    <ArrowPathIcon class="h-4 w-4 mr-1" />
                    Refresh
                  </button>
                  <button
                    @click="addTransactionForSub(viewingSub)"
                    class="px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm flex items-center"
                  >
                    <PlusIcon class="h-4 w-4 mr-1" />
                    Add Transaction
                  </button>
                </div>
              </div>

              <!-- We'll let the SubsTransactionList component handle the empty state -->
              <div class="overflow-x-auto">
                <SubsTransactionList
                  :transactions="viewingSub.transactions || []"
                  :is-loading="isLoading"
                  @edit="editTransaction"
                  @delete="deleteTransaction"
                />
              </div>
              <!-- Detailed Report Section -->
              <div class="mt-6 bg-gray-50 p-4 rounded-lg border border-gray-200">
                <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
                  <DocumentTextIcon class="h-5 w-5 text-indigo-600 mr-2" />
                  Detailed Report
                </h3>

                <!-- Summary Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <!-- Total Payments Card -->
                  <div class="bg-white p-4 rounded-lg shadow border-l-4 border-red-500">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="text-sm font-medium text-gray-500">Total Payments</p>
                        <p class="text-xl font-bold text-red-600">{{ formatCurrency(getTotalPayments(viewingSub.transactions)) }}</p>
                      </div>
                      <div class="bg-red-100 p-2 rounded-full">
                        <svg class="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <!-- Total Receipts Card -->
                  <div class="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="text-sm font-medium text-gray-500">Total Receipts</p>
                        <p class="text-xl font-bold text-green-600">{{ formatCurrency(getTotalReceipts(viewingSub.transactions)) }}</p>
                      </div>
                      <div class="bg-green-100 p-2 rounded-full">
                        <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  <!-- Current Balance Card -->
                  <div class="bg-white p-4 rounded-lg shadow border-l-4" :class="viewingSub.balance >= 0 ? 'border-blue-500' : 'border-orange-500'">
                    <div class="flex items-center justify-between">
                      <div>
                        <p class="text-sm font-medium text-gray-500">Current Balance</p>
                        <p class="text-xl font-bold" :class="viewingSub.balance >= 0 ? 'text-blue-600' : 'text-orange-600'">{{ formatCurrency(viewingSub.balance) }}</p>
                      </div>
                      <div :class="viewingSub.balance >= 0 ? 'bg-blue-100' : 'bg-orange-100'" class="p-2 rounded-full">
                        <svg class="h-6 w-6" :class="viewingSub.balance >= 0 ? 'text-blue-500' : 'text-orange-500'" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Export Buttons moved to footer -->
              </div>
            </div>

          </div>
        </div>

        <!-- Modal Footer with Gradient -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 p-4 flex justify-between items-center">
          <!-- Export Report Buttons -->
          <div class="flex space-x-3" v-if="viewingSub">
            <a
              :href="`/api/subs/reports/pdf?subName=${encodeURIComponent(viewingSub.name)}`"
              target="_blank"
              class="px-3 py-1.5 bg-red-600 text-white text-sm rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 inline-flex items-center"
            >
              <DocumentTextIcon class="h-4 w-4 mr-1" />
              PDF Report
            </a>
            <a
              :href="`/api/subs/reports/excel?subName=${encodeURIComponent(viewingSub.name)}`"
              target="_blank"
              class="px-3 py-1.5 bg-green-600 text-white text-sm rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 inline-flex items-center"
            >
              <TableCellsIcon class="h-4 w-4 mr-1" />
              Excel Report
            </a>
          </div>

          <!-- Action Buttons -->
          <div class="flex space-x-3">
            <button
              type="button"
              @click="closeViewSubModal"
              class="px-4 py-2 border border-white rounded-md shadow-sm text-sm font-medium text-white bg-transparent hover:bg-white hover:bg-opacity-10 inline-flex items-center"
            >
              <XMarkIcon class="h-4 w-4 mr-1" />
              Close
            </button>
            <button
              type="button"
              @click="recalculateBalance"
              class="px-6 py-2 border border-white rounded-md shadow-sm text-sm font-medium text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500 inline-flex items-center justify-center min-w-[140px]"
              :disabled="isLoading"
            >
              <span v-if="isLoading" class="flex items-center">
                <svg class="animate-spin h-4 w-4 mr-1 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Recalculating...</span>
              </span>
              <span v-else class="flex items-center">
                <ArrowPathIcon class="h-4 w-4 mr-1 inline-block" />
                <span>Recalculate</span>
              </span>
            </button>
            <button
              type="button"
              @click="editSub(viewingSub.id)"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-blue-700 bg-white hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white inline-flex items-center"
            >
              <PencilSquareIcon class="h-4 w-4 mr-1" />
              Edit
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true,
  middleware: ['sub-contractor-access']
});

import { ref, onMounted, watch, computed } from 'vue';
import { useRoute, useRouter } from '#app';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { useSubs } from '~/composables/expenses/useSubs';
import useUserRole from '~/composables/auth/useUserRole';
import SubsList from '~/components/expenses/SubsList.vue';
import SubsForm from '~/components/expenses/SubsForm.vue';
import SubsTransactionForm from '~/components/expenses/SubsTransactionForm.vue';
import SubsTransactionList from '~/components/expenses/SubsTransactionList.vue';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import useToast from '~/composables/ui/useToast';

// Import icons from heroicons
import {
  UserIcon,
  CurrencyRupeeIcon,
  CheckCircleIcon,
  PhoneIcon,
  EnvelopeIcon,
  HomeIcon,
  DocumentTextIcon,
  TableCellsIcon,
  PencilSquareIcon,
  XMarkIcon,
  PlusIcon,
  ArrowPathIcon
} from '@heroicons/vue/24/outline';

// Set page title
usePageTitle('Subcontractor Accounts', 'Manage subcontractor accounts and transactions');

const route = useRoute();
const router = useRouter();

// State
const isLoading = ref(true);
const isSubmitting = ref(false);
const transactionFormRef = ref(null);

// Filter state
const filters = ref({
  firmId: '',
  isActive: ''
});
const showAddSubModal = ref(false);
const showAddTransactionModal = ref(false);
const showViewSubModal = ref(false);
const editingSub = ref(null);
const viewingSub = ref(null);
const selectedSub = ref(null);
const selectedSubId = ref('');
const editingTransaction = ref(null);
const subsFormRef = ref(null);

// Get user role information
const { isSubContractor, getUserId, getUserFullname } = useUserRole();
const isSubContractorUser = computed(() => isSubContractor());
const currentUserId = computed(() => getUserId());
const currentUserFullname = computed(() => getUserFullname());

// Initialize toast notifications
const toast = useToast();

// Filtered subsModels for sub-contractors (they can only see their own data)
const filteredSubsModels = computed(() => {
  if (!isSubContractorUser.value) {
    // Non-sub-contractors can see all subs
    return subsModels.value;
  } else {
    // Sub-contractors can only see their own data
    // First try to filter by userId (if available)
    const userIdMatches = subsModels.value.filter(sub => sub.userId === currentUserId.value);
    if (userIdMatches.length > 0) {
      return userIdMatches;
    }

    // If no matches by userId, try to match by name
    if (currentUserFullname.value) {

      return subsModels.value.filter(sub => {
        const nameMatch = sub.name && sub.name.toLowerCase() === currentUserFullname.value.toLowerCase();
        if (nameMatch) {

        }
        return nameMatch;
      });
    }

    // If all else fails, return empty array
    return [];
  }
});

// Get composables
const {
  subsModels,
  currentSubsModel,
  fetchSubsModels,
  fetchSubsModelById,
  createSubsModel,
  updateSubsModel,
  deleteSubsModel: deleteSubAction,
  createSub,
  updateSub,
  deleteSub,
  createSubsTransaction: createTransactionAction,
  deleteSubsTransaction: deleteTransactionAction,
  updateSubsTransaction: updateTransactionAction
} = useSubs();

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount || 0);
};

    const getBalanceClass = (balance) => {
      return balance < 0 ? 'text-red-600' : 'text-green-600';
    };

    const viewSub = async (id) => {
      try {
        isLoading.value = true;

        const sub = await fetchSubsModelById(id);

        // Check if transactions array exists
        if (sub && !sub.transactions) {
          sub.transactions = [];
        }

        if (sub) {
          // Trust the balance from the API - don't auto-correct it
          // The API handles balance calculations correctly during transactions
          console.log('Loaded sub with balance:', sub.balance);

          viewingSub.value = sub;
          showViewSubModal.value = true;

          // If no transactions were found, show a message
          if (!sub.transactions || sub.transactions.length === 0) {
            // We'll let the UI handle showing the empty state
          }
        } else {
          toast.error('Failed to load sub details. Please try again.', 'Error');
        }
      } catch (error) {
        toast.error(`Error fetching sub details: ${error.message || 'Unknown error'}`, 'Error');
      } finally {
        isLoading.value = false;
      }
    };

    const editSub = async (id) => {
      try {
        isLoading.value = true;
        const sub = await fetchSubsModelById(id);
        editingSub.value = sub;
        showAddSubModal.value = true;

        // Close view modal if open
        showViewSubModal.value = false;
      } catch (error) {

      } finally {
        isLoading.value = false;
      }
    };

    const deleteSubsModel = async (id) => {
      try {
        isLoading.value = true;
        await deleteSubAction(id);

        // Close view modal if open
        if (viewingSub.value && viewingSub.value.id === id) {
          showViewSubModal.value = false;
        }
      } catch (error) {
        console.error('Error deleting subs model:', error);
        toast.error(`Error: ${error.message || 'Unknown error'}`, 'Delete Failed');
      } finally {
        isLoading.value = false;
      }
    };

    const saveSub = async (formData) => {
      try {
        isSubmitting.value = true;

        if (editingSub.value) {
          // Update existing sub
          await updateSubsModel(editingSub.value.id, formData);
        } else {
          // Create new sub
          await createSubsModel(formData);
        }

        // Close modal
        closeAddSubModal();
      } catch (error) {

      } finally {
        isSubmitting.value = false;
      }
    };

    // Method to submit the form using the ref
    const submitSubsForm = () => {
      if (subsFormRef.value) {
        // Call the handleSubmit method on the SubsForm component
        subsFormRef.value.handleSubmit();
      } else {

      }
    };

    const addTransactionForSub = (sub) => {
      selectedSub.value = sub;

      // Store the current viewingSub
      const tempViewingSub = viewingSub.value;

      // Close the view sub modal before opening the add transaction modal
      showViewSubModal.value = false;

      // Wait a moment for the view sub modal to close
      setTimeout(() => {
        // Open the add transaction modal
        showAddTransactionModal.value = true;
      }, 100);
    };

    const editTransaction = async (id) => {
      try {
        isLoading.value = true;

        toast.info('Loading transaction...', 'Edit Transaction');

        // Find the transaction in the current sub's transactions
        if (viewingSub.value && viewingSub.value.transactions) {
          const transaction = viewingSub.value.transactions.find(t => t.id === id);

          if (transaction) {
            console.log('Found transaction for editing:', {
              id: transaction.id,
              amount: transaction.amount,
              category: transaction.category,
              paidTo: transaction.paidTo
            });

            // Set editing state BEFORE opening modal
            editingTransaction.value = { ...transaction }; // Create a copy to avoid reference issues
            selectedSub.value = viewingSub.value;

            // Close the view sub modal before opening the edit transaction modal
            showViewSubModal.value = false;

            // Wait a moment for the view sub modal to close
            setTimeout(() => {
              // Open the edit transaction modal
              showAddTransactionModal.value = true;
              toast.success('Transaction loaded for editing', 'Edit Transaction');
            }, 150);
          } else {
            console.error('Transaction not found with ID:', id);
            toast.error('Transaction not found', 'Error');
          }
        } else {
          console.error('No transactions available for editing');
          toast.error('No transactions available', 'Error');
        }
      } catch (error) {
        console.error('Error in editTransaction:', error);
        toast.error(`Error: ${error.message || 'Unknown error'}`, 'Edit Failed');
      } finally {
        isLoading.value = false;
      }
    };

    const deleteTransaction = async (id) => {
      try {
        isLoading.value = true;

        toast.info('Deleting transaction...', 'Delete Transaction');

        await deleteTransactionAction(id);

        // Refresh the current sub view
        if (viewingSub.value) {

          await viewSub(viewingSub.value.id);
          toast.success('Transaction deleted successfully', 'Delete Transaction');
        }
      } catch (error) {

        toast.error(`Error: ${error.message || 'Unknown error'}`, 'Delete Failed');
      } finally {
        isLoading.value = false;
      }
    };

    const saveTransaction = async (formData) => {
      const debugId = `saveTransaction_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      console.log(`🚀 [${debugId}] ===== SAVE TRANSACTION STARTED =====`);
      console.log(`🚀 [${debugId}] Call stack:`, new Error().stack);
      console.log(`🚀 [${debugId}] isSubmitting.value BEFORE:`, isSubmitting.value);
      console.log(`🚀 [${debugId}] Form data received:`, JSON.stringify(formData, null, 2));
      console.log(`🚀 [${debugId}] editingTransaction.value:`, editingTransaction.value);
      console.log(`🚀 [${debugId}] selectedSub.value:`, selectedSub.value);
      console.log(`🚀 [${debugId}] selectedSubId.value:`, selectedSubId.value);

      try {
        if (isSubmitting.value) {
          console.log(`⚠️ [${debugId}] ALREADY SUBMITTING - BLOCKING DUPLICATE CALL`);
          toast.warning('Transaction is already being saved. Please wait...', 'Duplicate Request');
          return;
        }

        isSubmitting.value = true;
        console.log(`🚀 [${debugId}] isSubmitting.value set to TRUE`);
        toast.info('Saving transaction...', 'Transaction');

        // We need to know which sub is making the transaction
        let subForTransaction;

        if (selectedSub.value) {
          // If a sub was selected directly (e.g., from the view sub modal)
          subForTransaction = selectedSub.value;
          console.log(`🚀 [${debugId}] Using selectedSub.value:`, subForTransaction);
        } else if (selectedSubId.value) {
          // If a sub was selected from the dropdown
          subForTransaction = subsModels.value.find(s => s.id === selectedSubId.value);
          console.log(`🚀 [${debugId}] Found sub from dropdown:`, subForTransaction);
          if (!subForTransaction) {
            console.log(`❌ [${debugId}] Sub not found for selectedSubId:`, selectedSubId.value);
            toast.error('Please select a valid sub', 'Validation Error');
            throw new Error('Please select a valid sub');
          }
        } else {
          console.log(`❌ [${debugId}] No sub selected`);
          toast.error('Please select a sub', 'Validation Error');
          throw new Error('Please select a sub');
        }

        // Add the subsModelId to the formData for the new transaction API
        formData.subsModelId = subForTransaction.id;
        formData.subName = subForTransaction.name;

        console.log(`🚀 [${debugId}] === SAVE TRANSACTION DEBUG ===`);
        console.log(`🚀 [${debugId}] Selected sub for transaction:`, subForTransaction);
        console.log(`🚀 [${debugId}] Form data with subsModelId:`, JSON.stringify(formData, null, 2));

        if (editingTransaction.value) {
          // Update existing transaction
          console.log(`🔄 [${debugId}] Updating existing transaction:`, {
            id: editingTransaction.value.id,
            originalData: editingTransaction.value,
            newData: formData
          });

          await updateTransactionAction(editingTransaction.value.id, formData);
          console.log(`✅ [${debugId}] Transaction update completed`);
          toast.success('Transaction updated successfully', 'Update Transaction');
        } else {
          // Create new transaction
          console.log(`🆕 [${debugId}] Creating NEW transaction with data:`, formData);
          console.log(`🆕 [${debugId}] About to call createTransactionAction...`);

          const result = await createTransactionAction(formData);

          console.log(`✅ [${debugId}] Transaction creation completed with result:`, result);
          toast.success('Transaction added successfully', 'Add Transaction');
        }

        // Reset the editing state first
        console.log(`🧹 [${debugId}] Resetting editing state...`);
        editingTransaction.value = null;

        // Reset the form only if not editing
        console.log(`🧹 [${debugId}] Calling resetTransactionForm...`);
        resetTransactionForm();

        // Refresh the current sub view if we're viewing a sub
        if (viewingSub.value) {
          console.log(`🔄 [${debugId}] Refreshing sub data after transaction update...`);
          await refreshSubData(viewingSub.value.id);
          console.log(`✅ [${debugId}] Sub data refresh completed`);
        }

        console.log(`🎉 [${debugId}] ===== SAVE TRANSACTION COMPLETED SUCCESSFULLY =====`);
      } catch (error) {
        console.error(`❌ [${debugId}] SAVE TRANSACTION ERROR:`, error);
        console.error(`❌ [${debugId}] Error stack:`, error.stack);
        toast.error(`Error: ${error.message || 'Unknown error'}`, 'Save Failed');
      } finally {
        console.log(`🏁 [${debugId}] Setting isSubmitting to FALSE`);
        isSubmitting.value = false;
        console.log(`🏁 [${debugId}] ===== SAVE TRANSACTION ENDED =====`);
      }
    };

    // Function to reset the transaction form
    const resetTransactionForm = () => {
      console.log('Resetting transaction form...');

      // Only reset if we're not in editing mode
      if (editingTransaction.value) {
        console.log('Skipping form reset - currently editing transaction');
        return;
      }

      // Create default form data
      const defaultFormData = {
        date: new Date().toISOString().split('T')[0],
        paidTo: '',
        amount: '',
        category: 'PAYMENT',
        project: '',
        description: ''
      };

      // Find the form element
      const form = document.querySelector('.subs-transaction-form form');
      if (form) {
        // First reset the HTML form to clear all inputs
        form.reset();

        // Then dispatch a custom event to reset the Vue form data
        form.dispatchEvent(new CustomEvent('reset-form', { detail: defaultFormData }));
        console.log('Form reset completed');
      }
    };

    const closeAddSubModal = () => {
      showAddSubModal.value = false;
      editingSub.value = null;
    };

    const closeAddTransactionModal = () => {
      console.log('Closing transaction modal...');
      showAddTransactionModal.value = false;

      // Store the current state before resetting
      const wasEditing = !!editingTransaction.value;
      const currentSub = selectedSub.value;

      // Reset the form state
      editingTransaction.value = null;
      selectedSub.value = null;
      selectedSubId.value = '';

      // Always reopen the view sub modal if we have a currentSub
      if (currentSub) {
        console.log('Reopening view modal for sub:', currentSub.name);
        // Wait a moment for the add transaction modal to close
        setTimeout(() => {
          // Ensure we have the most current data
          if (viewingSub.value && viewingSub.value.id === currentSub.id) {
            // Data is already current, just show the modal
            showViewSubModal.value = true;
          } else {
            // Set the viewing sub and show modal
            viewingSub.value = currentSub;
            showViewSubModal.value = true;
          }
        }, 150); // Slightly longer delay for better UX
      }
    };

    const closeViewSubModal = () => {
      showViewSubModal.value = false;
      viewingSub.value = null;
    };

    // Optimized function to refresh sub data without full modal reload
    const refreshSubData = async (id) => {
      try {
        console.log('Refreshing sub data for ID:', id);

        // Fetch fresh data from API
        const freshSub = await fetchSubsModelById(id);

        if (freshSub && viewingSub.value) {
          // Update the viewing sub with fresh data
          viewingSub.value = {
            ...viewingSub.value,
            ...freshSub,
            transactions: freshSub.transactions || []
          };

          console.log('Sub data refreshed successfully:', {
            transactionCount: freshSub.transactions?.length || 0,
            balance: freshSub.balance
          });
        }
      } catch (error) {
        console.error('Error refreshing sub data:', error);
        toast.error('Failed to refresh data. Please close and reopen the modal.', 'Refresh Error');
      }
    };

    const fetchTransactions = async (id) => {
      try {
        isLoading.value = true;

        // Use the correct API endpoint that reads from subs collection
        const api = useApiWithAuth();
        const response = await api.get(`/api/subs/models/${id}`);

        if (response && viewingSub.value) {
          // Update only the transactions array to avoid UI flicker
          if (response.transactions) {
            viewingSub.value.transactions = response.transactions;
            console.log('Transactions updated:', response.transactions.length);
          } else {
            viewingSub.value.transactions = [];
            console.log('No transactions found');
          }
        }
      } catch (error) {

        alert(`Error fetching transactions: ${error.message || 'Unknown error'}`);
      } finally {
        isLoading.value = false;
      }
    };

    // Helper methods for detailed report
    const getTotalPayments = (transactions) => {
      if (!transactions || !Array.isArray(transactions)) return 0;

      // Calculate total payments (negative amounts)
      // We take the absolute value because payments are stored as negative numbers
      return transactions
        .filter(tx => tx.amount < 0)
        .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    };

    const getTotalReceipts = (transactions) => {
      if (!transactions || !Array.isArray(transactions)) return 0;

      // Calculate total receipts (positive amounts)
      return transactions
        .filter(tx => tx.amount > 0)
        .reduce((sum, tx) => sum + tx.amount, 0);
    };

    // Function to recalculate the balance based on transactions
    const recalculateBalance = async () => {
      try {
        if (!viewingSub.value || !viewingSub.value.id) {
          toast.error('No sub selected', 'Recalculate Error');
          return;
        }

        isLoading.value = true;
        const loadingToastId = toast.loading('Recalculating balance...', { title: 'Recalculate' });

        // Get the latest transactions
        const api = useApiWithAuth();
        const response = await api.get(`/api/subs/models/${viewingSub.value.id}`);

        if (response) {
          // Update the transactions array
          if (response.transactions) {
            viewingSub.value.transactions = response.transactions;
          } else {
            viewingSub.value.transactions = [];
          }

          // Calculate the new balance
          const totalPayments = getTotalPayments(viewingSub.value.transactions);
          const totalReceipts = getTotalReceipts(viewingSub.value.transactions);
          const newBalance = totalReceipts - totalPayments;
          const oldBalance = viewingSub.value.balance;

          // Dismiss the loading toast
          toast.dismiss(loadingToastId);

          // If the balance has changed, update it
          if (newBalance !== oldBalance) {
            // Create a clean data object with only the necessary fields
            // This ensures we don't send unnecessary data to the API
            const updateData = {
              name: viewingSub.value.name,
              balance: newBalance,
              isActive: viewingSub.value.isActive !== false,
              contactInfo: viewingSub.value.contactInfo || {}
            };

            // Update the balance in the database
            await updateSubsModel(viewingSub.value.id, updateData);

            // Also update the local subsModels array to keep UI in sync
            const subIndex = subsModels.value.findIndex(sub => sub.id === viewingSub.value.id);
            if (subIndex !== -1) {
              subsModels.value[subIndex].balance = newBalance;
            }

            // Update the local balance in viewingSub
            viewingSub.value.balance = newBalance;

            toast.success(`Balance recalculated: ${formatCurrency(oldBalance)} → ${formatCurrency(newBalance)}`, 'Recalculate');
          } else {
            toast.success(`Balance is already correct: ${formatCurrency(newBalance)}`, 'Recalculate');
          }

          // Show the detailed calculation
          console.log('Recalculation details:', {
            totalPayments,
            totalReceipts,
            newBalance,
            oldBalance,
            difference: newBalance - oldBalance,
            transactions: viewingSub.value.transactions.length
          });
        }
      } catch (error) {
        toast.error(`Error: ${error.message || 'Unknown error'}`, 'Recalculate Failed');
      } finally {
        isLoading.value = false;
      }
    };

    // Function to submit the transaction form programmatically
    const submitTransactionForm = () => {
      const debugId = `submitTransactionForm_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

      console.log(`🎯 [${debugId}] ===== SUBMIT TRANSACTION FORM CALLED =====`);
      console.log(`🎯 [${debugId}] Call stack:`, new Error().stack);
      console.log(`🎯 [${debugId}] isSubmitting.value:`, isSubmitting.value);
      console.log(`🎯 [${debugId}] transactionFormRef.value:`, transactionFormRef.value);

      if (transactionFormRef.value && transactionFormRef.value.submitForm) {
        console.log(`🎯 [${debugId}] Calling transactionFormRef.value.submitForm()`);
        transactionFormRef.value.submitForm();
        console.log(`🎯 [${debugId}] transactionFormRef.value.submitForm() called`);
      } else {
        console.error(`❌ [${debugId}] transactionFormRef.value or submitForm method not available`);
        toast.error('Could not submit the form. Please try again.', 'Form Error');
      }

      console.log(`🎯 [${debugId}] ===== SUBMIT TRANSACTION FORM ENDED =====`);
    };

    // Function to show the Add Transaction modal for sub-contractors
    const showAddTransactionModalForSubContractor = () => {
      // For sub-contractors, we automatically select their account
      if (filteredSubsModels.value.length === 1) {
        const subAccount = filteredSubsModels.value[0];


        // Set the selected sub
        selectedSub.value = subAccount;

        // Open the Add Transaction modal
        showAddTransactionModal.value = true;
        toast.info('Add a new transaction to your account', 'Add Transaction');
      } else {

        toast.error('Could not determine your account. Please contact support.', 'Account Error');
      }
    };

    // Filter methods
    const applyFilters = async () => {
      try {
        isLoading.value = true;
        toast.info('Applying filters...', 'Filters');

        // Prepare filter object
        const filterParams = {};

        if (filters.value.firmId) {
          filterParams.firmId = filters.value.firmId;
        }

        if (filters.value.isActive !== '') {
          filterParams.isActive = filters.value.isActive === true || filters.value.isActive === 'true';
        }



        // Fetch data with filters
        await fetchSubsModels(filterParams);

        toast.success('Filters applied successfully', 'Filters');
      } catch (error) {

        toast.error('Failed to apply filters', 'Error');
      } finally {
        isLoading.value = false;
      }
    };

    const resetFilters = async () => {
      try {
        isLoading.value = true;
        toast.info('Resetting filters...', 'Filters');

        // Reset filter values
        filters.value = {
          firmId: '',
          isActive: ''
        };

        // Fetch all data
        await fetchSubsModels();

        toast.success('Filters reset successfully', 'Filters');
      } catch (error) {

        toast.error('Failed to reset filters', 'Error');
      } finally {
        isLoading.value = false;
      }
    };

    // Initialize
    onMounted(async () => {
      try {
        // Fetch initial data
        await fetchSubsModels();

        // Debug information for sub-contractor filtering
        if (isSubContractorUser.value) {





        }

        // Check if there's a sub ID in the route query
        const subId = route.query.id;
        if (subId) {
          viewSub(subId);
        }
      } catch (error) {

      } finally {
        isLoading.value = false;
      }
    });

    // Watch for changes to route query
    watch(() => route.query.id, (newId) => {
      if (newId) {
        viewSub(newId);
      }
    });

// No return statement needed in <script setup>

</script>

<style scoped>
/* Prevent horizontal scrolling in modals */
.modal-content {
  max-width: 95vw;
  overflow-x: hidden;
}

/* Ensure transaction table is responsive */
.transaction-table-container {
  overflow-x: auto;
  max-width: 100%;
}

/* Prevent text overflow in table cells */
.transaction-table td {
  word-wrap: break-word;
  max-width: 200px;
}

/* Responsive modal adjustments */
@media (max-width: 768px) {
  .modal-content {
    max-width: 98vw;
    margin: 0.5rem;
  }
}
</style>
