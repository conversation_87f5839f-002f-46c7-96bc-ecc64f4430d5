<template>
  <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8 mt-0">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 space-y-4 sm:space-y-0">
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Financial Transactions</h1>

      <div class="flex flex-wrap gap-2 sm:gap-4">
        <button
          @click="showAddExpenseModal = true"
          class="px-3 py-2 sm:px-4 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm sm:text-base flex-1 sm:flex-none"
        >
          <span class="flex items-center justify-center">
            <svg class="h-5 w-5 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            <span class="hidden xs:inline">Add Expense</span>
            <span class="xs:hidden">Add</span>
          </span>
        </button>

        <NuxtLink
          to="/expenses/transfer"
          class="px-3 py-2 sm:px-4 sm:py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 text-sm sm:text-base flex-1 sm:flex-none"
        >
          <span class="flex items-center justify-center">
            <svg class="h-5 w-5 mr-1 sm:mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
            </svg>
            <span class="hidden xs:inline">Transfer</span>
            <span class="xs:hidden">Transfer</span>
          </span>
        </NuxtLink>
      </div>
    </div>

    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading expenses...</span>
    </div>

    <ExpenseList
      v-else
      :expenses="expenses"
      :is-loading="isLoading"
      @view="viewExpense"
      @edit="editExpense"
      @delete="deleteExpense"
      @filter="applyFilters"
    />



    <!-- View Expense Modal -->
    <div v-if="showViewExpenseModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto">
        <div class="p-4 border-b border-gray-200 flex justify-between items-center">
          <h3 class="text-lg font-medium text-gray-900">Expense Details</h3>
          <button
            @click="closeViewExpenseModal"
            class="text-gray-400 hover:text-gray-500"
          >
            <svg class="h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div class="p-6">
          <div v-if="viewingExpense" class="space-y-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 class="text-sm font-medium text-gray-500">Date</h4>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(viewingExpense.date) }}</p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">
                  {{ viewingExpense.hasDeductions ? 'Gross Amount' : 'Amount' }}
                </h4>
                <p class="mt-1 text-sm font-medium" :class="getAmountClass(viewingExpense.amount)">
                  {{ formatCurrency(viewingExpense.amount) }}
                  <span v-if="viewingExpense.hasDeductions" class="text-xs text-gray-500 ml-1">
                    (before deductions)
                  </span>
                </p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">Paid To/From</h4>
                <p class="mt-1 text-sm text-gray-900">{{ viewingExpense.paidTo }}</p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">Category</h4>
                <p class="mt-1">
                  <span
                    class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="getCategoryClass(viewingExpense.category)"
                  >
                    {{ viewingExpense.category || 'PAYMENT' }}
                  </span>
                </p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">Payment Mode</h4>
                <p class="mt-1 text-sm text-gray-900">
                  {{ viewingExpense.paymentMode.type === 'cash' ? 'Cash' : 'Bank' }}
                  <span v-if="viewingExpense.paymentMode.instrumentNo" class="ml-1 text-xs text-gray-500">
                    ({{ viewingExpense.paymentMode.instrumentNo }})
                  </span>
                </p>
              </div>

              <div>
                <h4 class="text-sm font-medium text-gray-500">Project</h4>
                <p class="mt-1 text-sm text-gray-900">{{ viewingExpense.project || '-' }}</p>
              </div>

              <div v-if="viewingExpense.paidToGroup" class="md:col-span-2">
                <h4 class="text-sm font-medium text-gray-500">Paid To Group</h4>
                <p class="mt-1 text-sm text-gray-900">{{ viewingExpense.paidToGroup }}</p>
              </div>

              <div class="md:col-span-2">
                <h4 class="text-sm font-medium text-gray-500">Description</h4>
                <p class="mt-1 text-sm text-gray-900">{{ viewingExpense.description || '-' }}</p>
              </div>
            </div>

            <!-- Deductions Section (if any) -->
            <DeductionDisplay
              :hasDeductions="viewingExpense.hasDeductions"
              :deductions="viewingExpense.deductions"
              :grossAmount="Math.abs(viewingExpense.amount)"
              :netAmount="getNetAmount(viewingExpense)"
              :transactionType="viewingExpense.category"
            />

            <!-- Sub-Expenses Section (if any) -->
            <div v-if="viewingExpense.subExpenses && viewingExpense.subExpenses.length > 0" class="mt-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Sub-Expenses</h3>

              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Date
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Paid To
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Amount
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Category
                      </th>
                      <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Description
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(subExpense, index) in viewingExpense.subExpenses" :key="index" class="hover:bg-gray-50">
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ formatDate(subExpense.date) }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {{ subExpense.paidTo }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(subExpense.amount)">
                        {{ formatCurrency(subExpense.amount) }}
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        <span
                          class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                          :class="getCategoryClass(subExpense.category)"
                        >
                          {{ subExpense.category || 'PAYMENT' }}
                        </span>
                      </td>
                      <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {{ subExpense.description || '-' }}
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Transfer Details Section (if any) -->
            <div v-if="viewingExpense.isTransfer && viewingExpense.transferDetails" class="mt-8">
              <h3 class="text-lg font-medium text-gray-900 mb-4">Transfer Details</h3>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="text-sm font-medium text-gray-500">From Account</h4>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ viewingExpense.transferDetails.fromMode === 'cash' ? 'Cash' : 'Bank' }}
                    <span v-if="viewingExpense.transferDetails.fromBankId" class="ml-1 text-xs text-gray-500">
                      ({{ getBankName(viewingExpense.transferDetails.fromBankId) }})
                    </span>
                  </p>
                </div>

                <div>
                  <h4 class="text-sm font-medium text-gray-500">To Account</h4>
                  <p class="mt-1 text-sm text-gray-900">
                    {{ viewingExpense.transferDetails.toMode === 'cash' ? 'Cash' : 'Bank' }}
                    <span v-if="viewingExpense.transferDetails.toBankId" class="ml-1 text-xs text-gray-500">
                      ({{ getBankName(viewingExpense.transferDetails.toBankId) }})
                    </span>
                  </p>
                </div>
              </div>
            </div>

            <div class="mt-8 flex justify-end space-x-4">
              <button
                @click="editExpense(viewingExpense.id)"
                class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Edit
              </button>
              <button
                @click="closeViewExpenseModal"
                class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Expense Modal -->
    <ExpenseModal
      :show="showAddExpenseModal"
      title="Add New Expense"
      @close="showAddExpenseModal = false"
      @saved="handleExpenseSaved"
    />
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true
});

import { ref, computed, onMounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useLedgers } from '~/composables/expenses/useLedgers';
import ExpenseModal from '~/components/expenses/ExpenseModal.vue';
import DeductionDisplay from '~/components/expenses/DeductionDisplay.vue';
import { usePageTitle } from '~/composables/ui/usePageTitle';

// Set page title
usePageTitle('Financial Transactions', 'View and manage all financial transactions');

const route = useRoute();
const router = useRouter();

// State
const isLoading = ref(true);
const showViewExpenseModal = ref(false);
const showAddExpenseModal = ref(false);
const viewingExpense = ref(null);

// Methods
const handleExpenseSaved = (expense) => {

  // Refresh data without filters
  fetchExpenses();
};

// Get composables
const {
  expenses,
  fetchExpenses,
  fetchExpenseById,
  deleteExpense: deleteExpenseAction
} = useExpenses();

const {
  bankLedgers,
  fetchLedgers,
  ensureDefaultCashBook
} = useLedgers();

// Computed properties
const getBankName = (bankId) => {
  const bank = bankLedgers.value.find(b => b.id === bankId);
  return bank ? bank.name : 'Unknown Bank';
};

// Methods
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(amount);
};

const formatDate = (date) => {
  if (!date) return '-';
  return new Date(date).toLocaleDateString();
};

const getAmountClass = (amount, category) => {
  // Use category to determine color, not the amount sign
  if (category === 'PAYMENT') {
    return 'text-red-600'; // PAYMENT should be red
  } else if (category === 'RECEIPT') {
    return 'text-green-600'; // RECEIPT should be green
  } else {
    // Fallback to using amount sign
    return amount < 0 ? 'text-red-600' : 'text-green-600';
  }
};

const getCategoryClass = (category) => {
  switch (category) {
    case 'PAYMENT':
      return 'bg-red-100 text-red-800';
    case 'RECEIPT':
      return 'bg-green-100 text-green-800';
    case 'TRANSFER':
      return 'bg-blue-100 text-blue-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Helper functions for deductions
const getTotalDeductions = (expense) => {
  if (!expense.deductions || !Array.isArray(expense.deductions)) {
    return 0;
  }
  return expense.deductions.reduce((sum, deduction) => sum + (deduction.amount || 0), 0);
};

const getNetAmount = (expense) => {
  // Use netAmount if available, otherwise calculate it
  if (expense.netAmount !== undefined) {
    return expense.netAmount;
  }

  // Fallback calculation
  const totalDeductions = getTotalDeductions(expense);
  if (expense.category === 'RECEIPT' || expense.amount > 0) {
    return expense.amount - totalDeductions;
  } else {
    return expense.amount + totalDeductions; // For payments, amount is negative
  }
};

const viewExpense = async (id) => {
  try {
    isLoading.value = true;

    try {
      const expense = await fetchExpenseById(id);

      if (expense) {
        viewingExpense.value = expense;
        showViewExpenseModal.value = true;
      } else {
        console.error('No expense data received');
        alert('Failed to load expense details. Please try again.');
      }
    } catch (fetchError) {
      console.error('Error in fetchExpenseById:', fetchError);
      alert(`Error fetching expense details: ${fetchError.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error fetching expense details:', error);
    alert(`Error: ${error.message || 'Failed to load expense details'}`);
  } finally {
    isLoading.value = false;
  }
};

const editExpense = (id) => {
  // Navigate to the edit page
  router.push(`/expenses/edit/${id}`);
};

const deleteExpense = async (id) => {
  try {
    isLoading.value = true;
    await deleteExpenseAction(id);

    // Close view modal if open
    if (viewingExpense.value && viewingExpense.value.id === id) {
      showViewExpenseModal.value = false;
    }
  } catch (error) {
    console.error('Error deleting expense:', error);
  } finally {
    isLoading.value = false;
  }
};

const closeViewExpenseModal = () => {
  showViewExpenseModal.value = false;
  viewingExpense.value = null;
};

const applyFilters = async (filters) => {
  try {
    isLoading.value = true;
    await fetchExpenses(filters);
  } catch (error) {
    console.error('Error applying filters:', error);
  } finally {
    isLoading.value = false;
  }
};

// Initialize
onMounted(async () => {
  try {
    // Ensure default cash book exists
    await ensureDefaultCashBook();

    // Fetch initial data
    await Promise.all([
      fetchExpenses(),
      fetchLedgers()
    ]);

    // Check if there's an expense ID in the route query
    const expenseId = route.query.id;
    if (expenseId) {
      viewExpense(expenseId);
    }
  } catch (error) {
    console.error('Error loading expenses data:', error);
  } finally {
    isLoading.value = false;
  }
});

// Watch for changes to route query
watch(() => route.query.id, (newId) => {
  if (newId) {
    viewExpense(newId);
  }
});
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
