<template>
  <div class="relative">
    <!-- Input field -->
    <input
      ref="inputRef"
      v-model="searchQuery"
      type="text"
      :class="inputClass"
      :placeholder="placeholder"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @keydown="handleKeydown"
      v-bind="$attrs"
    />

    <!-- Custom dropdown -->
    <Teleport to="body">
      <div
        v-if="showDropdown && filteredStocks.length > 0"
        ref="dropdownRef"
        class="fixed z-[9999] bg-white border border-gray-300 rounded-lg shadow-2xl overflow-hidden"
        :style="dropdownStyle"
      >
        <!-- Header with colorful columns -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white text-xs font-semibold">
          <div class="grid grid-cols-7 px-2 py-2" style="grid-template-columns: 2fr 1fr 1fr 1fr 0.8fr 0.8fr 0.8fr;">
            <div class="text-left px-1">Item</div>
            <div class="text-left px-1">PN</div>
            <div class="text-left px-1">Batch</div>
            <div class="text-left px-1">HSN</div>
            <div class="text-right px-1">Qty</div>
            <div class="text-left px-1">UOM</div>
            <div class="text-center px-1">Status</div>
          </div>
        </div>

        <!-- Scrollable content -->
        <div class="max-h-80 overflow-y-auto">
          <div
            v-for="(stock, index) in filteredStocks"
            :key="stock._id"
            :class="[
              'grid grid-cols-7 px-2 py-2 cursor-pointer hover:bg-blue-50 border-b border-gray-100 text-xs',
              index === selectedIndex ? 'bg-blue-100 font-semibold border-l-4 border-blue-500' : ''
            ]"
            style="grid-template-columns: 2fr 1fr 1fr 1fr 0.8fr 0.8fr 0.8fr;"
            @mousedown.prevent="selectStock(stock)"
            @mouseover="selectedIndex = index"
          >
            <!-- Item Name -->
            <div class="text-left truncate font-medium text-gray-800 px-1" :title="stock.item">
              {{ stock.item }}
            </div>

            <!-- Part Number -->
            <div class="text-left truncate text-gray-600 px-1" :title="stock.pno || 'N/A'">
              {{ stock.pno || 'N/A' }}
            </div>

            <!-- Batch -->
            <div class="text-left truncate text-gray-600 px-1" :title="stock.batch || 'N/A'">
              {{ stock.batch || 'N/A' }}
            </div>

            <!-- HSN -->
            <div class="text-left truncate text-gray-600 px-1" :title="stock.hsn">
              {{ stock.hsn }}
            </div>

            <!-- Quantity -->
            <div class="text-right font-medium px-1" :class="getQuantityColor(stock.qty)">
              {{ stock.qty }}
            </div>

            <!-- UOM -->
            <div class="text-left truncate text-gray-600 px-1" :title="stock.uom">
              {{ stock.uom }}
            </div>

            <!-- Status Badge -->
            <div class="text-center px-1">
              <span
                :class="getStatusBadgeClass(stock.qty)"
                class="px-1 py-0.5 rounded-full text-xs font-medium"
              >
                {{ getStockStatus(stock.qty) }}
              </span>
            </div>
          </div>
        </div>

        <!-- No results message -->
        <div v-if="filteredStocks.length === 0 && searchQuery" class="p-4 text-center text-gray-500 text-sm">
          No items found matching "{{ searchQuery }}"
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, nextTick, onMounted, onUnmounted, watch } from 'vue'

// Props
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  stocks: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Search stock items...'
  },
  inputClass: {
    type: String,
    default: 'w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500'
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'stock-selected', 'refresh-stocks'])

// Refs
const inputRef = ref(null)
const dropdownRef = ref(null)
const showDropdown = ref(false)
const searchQuery = ref(props.modelValue)
const selectedIndex = ref(-1)
const inputPosition = ref({ top: 0, left: 0, width: 0 })

// Computed
const filteredStocks = computed(() => {
  // ✅ DEBUG: Log stocks availability for troubleshooting
  console.log('🔍 StockItemDropdown - Stocks available:', props.stocks?.length || 0);
  console.log('🔍 StockItemDropdown - Search query:', searchQuery.value);

  if (!props.stocks || props.stocks.length === 0) {
    console.log('⚠️ StockItemDropdown - No stocks available!');
    return [];
  }

  if (!searchQuery.value) {
    const result = props.stocks.slice(0, 20); // Show first 20 items when empty
    console.log('🔍 StockItemDropdown - Showing first 20 stocks:', result.length);
    return result;
  }

  const query = searchQuery.value.toLowerCase()
  const filtered = props.stocks
    .filter(stock =>
      stock.item?.toLowerCase().includes(query) ||
      stock.pno?.toLowerCase().includes(query) ||
      stock.batch?.toLowerCase().includes(query) ||
      stock.hsn?.toLowerCase().includes(query)
    )
    .slice(0, 20); // Limit to 20 results for performance

  console.log('🔍 StockItemDropdown - Filtered results:', filtered.length);
  return filtered;
})

const dropdownStyle = computed(() => ({
  top: `${inputPosition.value.top}px`,
  left: `${inputPosition.value.left}px`,
  minWidth: `${Math.max(inputPosition.value.width, 600)}px`, // Minimum 600px width
  maxWidth: '800px' // Maximum width
}))

// Methods
const updateInputPosition = () => {
  if (inputRef.value) {
    const rect = inputRef.value.getBoundingClientRect()
    inputPosition.value = {
      top: rect.bottom + 2, // No scroll offset needed for position: fixed
      left: rect.left,      // No scroll offset needed for position: fixed
      width: rect.width
    }
  }
}

const handleInput = () => {
  emit('update:modelValue', searchQuery.value)
  selectedIndex.value = -1
  showDropdown.value = true
  updateInputPosition()
}

const handleFocus = () => {
  // ✅ FIX: Check if stocks are available when focusing
  if (!props.stocks || props.stocks.length === 0) {
    console.log('⚠️ StockItemDropdown - No stocks available on focus, emitting refresh request');
    emit('refresh-stocks');
  }
  showDropdown.value = true
  updateInputPosition()
}

const handleBlur = () => {
  // Delay hiding to allow for click events
  setTimeout(() => {
    showDropdown.value = false
  }, 150)
}

const handleKeydown = (event) => {
  if (!showDropdown.value) return

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      selectedIndex.value = Math.min(selectedIndex.value + 1, filteredStocks.value.length - 1)
      scrollToSelected()
      break
    case 'ArrowUp':
      event.preventDefault()
      selectedIndex.value = Math.max(selectedIndex.value - 1, -1)
      scrollToSelected()
      break
    case 'Enter':
      event.preventDefault()
      if (selectedIndex.value >= 0 && filteredStocks.value[selectedIndex.value]) {
        selectStock(filteredStocks.value[selectedIndex.value])
      }
      break
    case 'Escape':
      event.preventDefault()
      showDropdown.value = false
      break
  }
}

const scrollToSelected = () => {
  nextTick(() => {
    if (!dropdownRef.value || selectedIndex.value < 0) return

    const dropdown = dropdownRef.value.querySelector('.max-h-80')
    const selectedElement = dropdown?.children[selectedIndex.value]
    
    if (!selectedElement) return

    const dropdownRect = dropdown.getBoundingClientRect()
    const selectedRect = selectedElement.getBoundingClientRect()

    if (selectedRect.bottom > dropdownRect.bottom) {
      dropdown.scrollTop += selectedRect.bottom - dropdownRect.bottom
    } else if (selectedRect.top < dropdownRect.top) {
      dropdown.scrollTop -= dropdownRect.top - selectedRect.top
    }
  })
}

const selectStock = (stock) => {
  searchQuery.value = stock.item
  emit('update:modelValue', stock.item)
  emit('stock-selected', stock)
  showDropdown.value = false
  
  // Focus the input after selection
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const getStockStatus = (qty) => {
  if (qty <= 5) return 'Low'
  if (qty <= 20) return 'Med'
  return 'High'
}

const getQuantityColor = (qty) => {
  if (qty <= 5) return 'text-red-600'
  if (qty <= 20) return 'text-yellow-600'
  return 'text-green-600'
}

const getStatusBadgeClass = (qty) => {
  if (qty <= 5) return 'bg-red-100 text-red-800'
  if (qty <= 20) return 'bg-yellow-100 text-yellow-800'
  return 'bg-green-100 text-green-800'
}

// Handle window resize
const handleResize = () => {
  if (showDropdown.value) {
    updateInputPosition()
  }
}

// ✅ DEBUG: Watch for props.stocks changes
watch(() => props.stocks, (newStocks, oldStocks) => {
  console.log('🔄 StockItemDropdown - Props.stocks changed!');
  console.log('🔄 Old stocks length:', oldStocks?.length || 0);
  console.log('🔄 New stocks length:', newStocks?.length || 0);
}, { immediate: true });

// Lifecycle
onMounted(() => {
  window.addEventListener('resize', handleResize)
  window.addEventListener('scroll', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  window.removeEventListener('scroll', handleResize)
})

// Watch for modelValue changes
watch(() => props.modelValue, (newValue) => {
  searchQuery.value = newValue
})
</script>
