<template>
  <div v-if="isOpen" class="fixed inset-0 z-50 overflow-y-auto" @click="closeModal">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"></div>

      <!-- Modal panel - 80% width and height on large screens -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-4 sm:align-middle w-full h-full sm:w-[80%] sm:h-[80%] max-w-none" @click.stop>
        <!-- Header -->
        <div class="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <Icon name="heroicons:question-mark-circle" class="w-8 h-8 text-white mr-3" />
              <div>
                <h3 class="text-xl font-bold text-white">{{ helpTitle }}</h3>
                <p v-if="helpDescription" class="text-blue-100 text-sm mt-1">{{ helpDescription }}</p>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <!-- Search help content -->
              <div class="relative">
                <input
                  v-model="searchQuery"
                  type="text"
                  placeholder="Search help..."
                  class="w-64 px-3 py-1 text-sm bg-white bg-opacity-20 text-white placeholder-blue-200 border border-blue-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-white focus:bg-opacity-30"
                />
                <Icon name="heroicons:magnifying-glass" class="absolute right-2 top-1.5 w-4 h-4 text-blue-200" />
              </div>
              <!-- Close button -->
              <button @click="closeModal" class="text-white hover:text-blue-200 transition-colors">
                <Icon name="heroicons:x-mark" class="w-6 h-6" />
              </button>
            </div>
          </div>
        </div>

        <!-- Content -->
        <div class="flex h-[calc(80vh-200px)] min-h-96">
          <!-- Sidebar with help topics -->
          <div class="w-1/3 bg-gray-50 border-r border-gray-200 overflow-y-auto">
            <div class="p-4">
              <h4 class="font-semibold text-gray-900 mb-3">Help Topics</h4>
              <div class="space-y-1">
                <button
                  v-for="topic in filteredTopics"
                  :key="topic.key"
                  @click="selectTopic(topic.key)"
                  :class="[
                    'w-full text-left px-3 py-2 rounded-lg text-sm transition-colors',
                    selectedTopic === topic.key
                      ? 'bg-blue-100 text-blue-800 font-medium'
                      : 'text-gray-700 hover:bg-gray-100'
                  ]"
                >
                  <Icon :name="topic.icon" class="w-4 h-4 inline mr-2" />
                  {{ topic.title }}
                </button>
              </div>
            </div>
          </div>

          <!-- Main content area -->
          <div class="flex-1 overflow-y-auto">
            <div class="p-6">
              <div v-if="selectedHelpContent" class="prose prose-sm max-w-none">
                <div class="whitespace-pre-wrap">{{ selectedHelpContent }}</div>
              </div>
              <div v-else class="text-center text-gray-500 mt-8">
                <Icon name="heroicons:document-text" class="w-12 h-12 mx-auto mb-4 text-gray-300" />
                <p>Select a help topic from the sidebar to view detailed information.</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="bg-gray-50 px-6 py-3 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button @click="printHelp" class="text-sm text-gray-600 hover:text-gray-800 flex items-center">
                <Icon name="heroicons:printer" class="w-4 h-4 mr-1" />
                Print
              </button>
              <button @click="copyHelpContent" class="text-sm text-gray-600 hover:text-gray-800 flex items-center">
                <Icon name="heroicons:clipboard" class="w-4 h-4 mr-1" />
                Copy
              </button>
            </div>
            <div class="flex items-center space-x-2">
              <button @click="closeModal" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500">
                Close
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  helpTopic: {
    type: String,
    default: 'general'
  }
})

// Emits
const emit = defineEmits(['close'])

// Reactive data
const searchQuery = ref('')
const selectedTopic = ref(props.helpTopic)

// Help content mapping based on actual codebase analysis
const helpTopics = [
  {
    key: 'public/home',
    title: 'Home Page Guide',
    icon: 'heroicons:home',
    category: 'Getting Started'
  },
  {
    key: 'public/login',
    title: 'Login Guide',
    icon: 'heroicons:key',
    category: 'Getting Started'
  },
  {
    key: 'public/register',
    title: 'Registration Guide',
    icon: 'heroicons:user-plus',
    category: 'Getting Started'
  },
  {
    key: 'user/dashboard',
    title: 'Dashboard Guide',
    icon: 'heroicons:squares-2x2',
    category: 'User Dashboard'
  },
  {
    key: 'stock-market/overview',
    title: 'Stock Market Overview',
    icon: 'heroicons:chart-bar',
    category: 'Stock Market'
  },
  {
    key: 'stock-market/analysis',
    title: 'Stock Analysis',
    icon: 'heroicons:chart-pie',
    category: 'Stock Market'
  },
  {
    key: 'stock-market/nse-trading',
    title: 'NSE Trading',
    icon: 'heroicons:banknotes',
    category: 'Stock Market'
  },
  {
    key: 'ai/assistant',
    title: 'AI Assistant',
    icon: 'heroicons:cpu-chip',
    category: 'AI Tools'
  },
  {
    key: 'financial/dashboard',
    title: 'Financial Management',
    icon: 'heroicons:calculator',
    category: 'Financial'
  },
  {
    key: 'wages/dashboard',
    title: 'Employee Wages',
    icon: 'heroicons:currency-rupee',
    category: 'Wages'
  },
  {
    key: 'inventory/dashboard',
    title: 'Inventory Management',
    icon: 'heroicons:clipboard-document-list',
    category: 'Inventory'
  },
  {
    key: 'documents/management',
    title: 'Document Management',
    icon: 'heroicons:document-text',
    category: 'Documents'
  },
  {
    key: 'admin/dashboard',
    title: 'Admin Panel',
    icon: 'heroicons:cog-6-tooth',
    category: 'Administration'
  },
  {
    key: 'public/about',
    title: 'About Page',
    icon: 'heroicons:information-circle',
    category: 'Getting Started'
  },
  {
    key: 'public/contact',
    title: 'Contact Page',
    icon: 'heroicons:phone',
    category: 'Getting Started'
  }
]

// Help content based on actual codebase analysis
const helpContentMap = {
  'public/home': {
    title: 'Home Page Guide',
    description: 'Navigate the main landing page and explore BusinessPro Suite features',
    content: `# Home Page Guide

Welcome to BusinessPro Suite! The home page serves as your gateway to all business management features.

## Step-by-Step Navigation Process

### 1. First Visit Process
**Step 1:** Open BusinessPro Suite in your web browser
**Step 2:** Review the main navigation menu at the top
**Step 3:** Read feature overview cards on the homepage
**Step 4:** Click "About" to learn more about the platform
**Step 5:** Use "Contact" for support if needed

### 2. Getting Started Process
**Step 1:** Click "Register" if you're a new user
**Step 2:** Or click "Login" if you have existing account
**Step 3:** After login, you'll be redirected to your dashboard
**Step 4:** Explore available modules based on your role

## Main Navigation Menu
- **Home**: Current page overview
- **About**: Company information and mission
- **Contact**: Support and contact information
- **Login**: Access for existing users (use USERNAME, not email)
- **Register**: New user account creation

## Key Features Available
- **Financial Management**: Expense tracking using Firestore
- **Employee Wages**: Payroll with EPF/ESIC calculations
- **Inventory Management**: Stock tracking with GST using MongoDB
- **AI Assistant**: Multi-provider AI support (Gemini, OpenAI, Claude)
- **Stock Market**: Live NSE data with real-time updates

## Troubleshooting Navigation Issues
- **Page not loading**: Refresh browser or check internet connection
- **Menu not visible**: Try different browser or clear cache
- **Links not working**: Ensure JavaScript is enabled in browser`
  },
  'public/login': {
    title: 'Login Guide',
    description: 'User authentication and login procedures',
    content: `# Login Guide

## Step-by-Step Login Process

### 1. Accessing Login Page
**Step 1:** Click "Login" button from homepage
**Step 2:** You'll be redirected to login page
**Step 3:** Login form will be displayed

### 2. Entering Credentials
**Step 1:** Enter your USERNAME (NOT email address)
**Step 2:** Enter your password in the password field
**Step 3:** Ensure Caps Lock is OFF
**Step 4:** Double-check spelling of username

### 3. Completing Login
**Step 1:** Click "Login" button
**Step 2:** System will validate credentials via /api/auth/login
**Step 3:** If successful, redirected to dashboard
**Step 4:** If failed, error message will appear

## Authentication System Details
- **Username Required**: Login uses username field, not email
- **Token-Based**: Uses JWT tokens with 15-minute access tokens and 7-day refresh tokens
- **Role-Based Access**: Features available based on your assigned role
- **Session Management**: Automatic token refresh for extended sessions

## Account Status Requirements
- **Approved Users**: Only users with status = 1 can login
- **Pending Approval**: Status = 0 users see "pending approval" message
- **Rejected Users**: Status = -1 users see "account rejected" message
- **Admin Exception**: Admins can login regardless of status

## Troubleshooting Login Issues
- **Invalid credentials**: Verify username spelling and password
- **Account pending**: Contact your manager for account approval
- **Account rejected**: Contact your manager for assistance
- **Page not loading**: Clear browser cache and cookies`
  },
  'stock-market/overview': {
    title: 'Stock Market Overview Guide',
    description: 'Navigate and use the Stock Market analysis and trading tools',
    content: `# Stock Market Overview Guide

## Step-by-Step Getting Started Process

### 1. Accessing Stock Market Module
**Step 1:** Login to your BusinessPro Suite account
**Step 2:** Click "Stock Market" in main navigation menu
**Step 3:** Wait for live NSE data to load (takes 2-3 seconds)
**Step 4:** Data will auto-refresh every 30 seconds

### 2. Understanding the Dashboard
**Step 1:** Review the 4 main tabs:
   - Live Market: Current market data
   - Investments: Your portfolio
   - Contract Notes: Trading records
   - Mutual Funds: Fund investments
**Step 2:** Start with "Live Market" tab for market overview
**Step 3:** Check top gainers and losers for the day
**Step 4:** Monitor Nifty 50 index performance

### 3. Searching for Stocks
**Step 1:** Use search box at top of page
**Step 2:** Type stock symbol (e.g., "RELIANCE", "TCS")
**Step 3:** Select stock from dropdown results
**Step 4:** Stock details will open in modal/popup
**Step 5:** Review price, volume, and day's performance

### 4. Adding Contract Notes
**Step 1:** Go to "Contract Notes" tab
**Step 2:** Click "Add Contract Note" button
**Step 3:** Fill contract note details:
   - Date and time of transaction
   - Stock symbol and quantity
   - Buy/Sell transaction type
   - Price and brokerage details
**Step 4:** Upload contract note document (optional)
**Step 5:** Save contract note record

### 5. Managing Mutual Fund Investments
**Step 1:** Go to "Mutual Funds" tab
**Step 2:** Click "Add Mutual Fund" button
**Step 3:** Enter fund details:
   - Fund name and scheme code
   - Investment amount and date
   - NAV (Net Asset Value)
   - SIP details if applicable
**Step 4:** Track fund performance over time
**Step 5:** Calculate XIRR for fund returns

## Key Features Explained
- **Real-time Data**: Live stock market prices with auto-refresh
- **Portfolio Tracking**: Automatic P&L calculations with current market prices
- **XIRR Analysis**: Extended Internal Rate of Return calculations
- **Data Export**: Export data for external analysis
- **Contract Note Management**: Complete trading record management
- **Mutual Fund Tracking**: Comprehensive mutual fund portfolio management

## Troubleshooting Stock Market Issues
- **Data not loading**: Check internet connection and wait for refresh
- **Prices not updating**: Verify auto-refresh is enabled
- **Contract note upload failed**: Check file size and format
- **XIRR calculation error**: Ensure all dates and amounts are correct
- **Export failed**: Try again or contact support`
  },
  'public/register': {
    title: 'Registration Guide',
    description: 'Create your BusinessPro Suite account',
    content: `# Registration Guide

## Step-by-Step Registration Process

### 1. Accessing Registration Page
**Step 1:** Click "Register" button from homepage
**Step 2:** Registration form will be displayed
**Step 3:** Review all required fields

### 2. Filling Personal Information
**Step 1:** Enter your full name (first and last name)
**Step 2:** Choose a unique username (this will be used for login)
**Step 3:** Enter your email address
**Step 4:** Create a strong password (minimum 8 characters)
**Step 5:** Confirm your password by typing it again

### 3. Business Information
**Step 1:** Enter your firm/company name
**Step 2:** Select your role (User, Manager, Admin, Sub-contractor)
**Step 3:** Provide business contact information
**Step 4:** Add any additional business details

### 4. Completing Registration
**Step 1:** Review all entered information
**Step 2:** Check the terms and conditions checkbox
**Step 3:** Click "Register" button
**Step 4:** Wait for account creation confirmation
**Step 5:** Check email for verification (if required)

## Important Registration Notes
- **Username is permanent** - choose carefully
- **Email must be valid** - used for notifications
- **Password requirements** - minimum 8 characters
- **Role selection** - determines access permissions

## Troubleshooting Registration Issues
- **Username taken**: Try different username
- **Email already exists**: Use different email or login instead
- **Password too weak**: Add numbers and special characters
- **Form not submitting**: Check all required fields are filled`
  },
  'user/dashboard': {
    title: 'Dashboard Guide',
    description: 'Navigate and use your personal dashboard',
    content: `# Dashboard Guide

## Step-by-Step Dashboard Navigation Process

### 1. Accessing Your Dashboard
**Step 1:** Login to your BusinessPro Suite account
**Step 2:** After successful login, you'll be redirected to dashboard
**Step 3:** Dashboard loads with your personal information
**Step 4:** Review your profile details and firm association

### 2. Understanding Dashboard Sections
**Step 1:** Check your profile information at the top
**Step 2:** Review your user role and permissions
**Step 3:** Note your associated firm details
**Step 4:** Explore available feature cards/modules

### 3. Navigating to Different Modules
**Step 1:** Click on "Financial Management" for expense tracking
**Step 2:** Select "Employee Wages" for payroll management
**Step 3:** Choose "Inventory Management" for stock control
**Step 4:** Access "AI Assistant" for business guidance
**Step 5:** Use "Stock Market" for investment tracking

### 4. Profile Management Process
**Step 1:** Click on your profile section
**Step 2:** Review personal information
**Step 3:** Update details if needed
**Step 4:** Save any changes made

## Dashboard Features by Role
- **Regular Users**: Access to all business features
- **Managers**: Additional team oversight capabilities
- **Admins**: Full system access and management
- **Sub-contractors**: Limited access to relevant features

## Quick Actions Available
- View recent transactions
- Check latest stock market updates
- Access AI assistant quickly
- Generate quick reports
- Navigate to frequently used modules`
  },
  'stock-market/analysis': {
    title: 'Stock Analysis Guide',
    description: 'Detailed stock analysis and technical indicators',
    content: `# Stock Analysis Guide

## Step-by-Step Stock Analysis Process

### 1. Finding and Selecting Stocks
**Step 1:** Go to Stock Market page from main menu
**Step 2:** Use search box to find stock (enter symbol like "RELIANCE" or "TCS")
**Step 3:** Click on stock from search results dropdown
**Step 4:** Stock details modal will open with comprehensive information
**Step 5:** Review basic stock information (price, volume, change)

### 2. Reading Stock Information Process
**Step 1:** Check current price and day's change (green = profit, red = loss)
**Step 2:** Review trading volume (higher volume = more activity)
**Step 3:** Note day's high and low prices
**Step 4:** Compare with previous close price
**Step 5:** Check market cap for company size assessment

### 3. Technical Analysis Process
**Step 1:** Click on "Charts" or "Technical Analysis" tab in stock modal
**Step 2:** Select time period (1D, 1W, 1M, 3M, 1Y)
**Step 3:** Choose chart type:
   - Line chart for simple price movement
   - Candlestick for detailed OHLC data
**Step 4:** Add technical indicators:
   - Moving averages (20-day, 50-day)
   - RSI for momentum analysis
   - Volume indicators
**Step 5:** Analyze trends and patterns for decision making

### 4. Fundamental Analysis Process
**Step 1:** Click on "Fundamental Analysis" tab
**Step 2:** Review P/E ratio for valuation assessment
**Step 3:** Check debt-to-equity ratio for financial health
**Step 4:** Analyze dividend yield for income potential
**Step 5:** Review profit/loss trends over quarters
**Step 6:** Read pros and cons analysis

### 5. AI-Powered Analysis Process
**Step 1:** Click "AI Analysis" button on stock page
**Step 2:** Wait for AI to process stock data (takes 10-15 seconds)
**Step 3:** Review AI insights:
   - Buy/Sell/Hold recommendation
   - Risk assessment score
   - Price target predictions
   - Market sentiment analysis
**Step 4:** Use AI insights to make informed decisions
**Step 5:** Save or export AI analysis for future reference

### 6. Adding Stock to Portfolio Process
**Step 1:** Click "Add to Portfolio" button in stock modal
**Step 2:** Enter purchase details:
   - Number of shares bought
   - Purchase price per share
   - Purchase date
   - Brokerage charges paid
**Step 3:** Select broker name from dropdown
**Step 4:** Add any notes about the investment
**Step 5:** Save investment record
**Step 6:** Stock appears in your portfolio with P&L calculations

## Key Analysis Metrics to Monitor
- **P/E Ratio**: Price-to-earnings ratio for valuation
- **Volume**: Trading activity indicator
- **52-Week High/Low**: Annual price range
- **Market Cap**: Company size and stability
- **Dividend Yield**: Income potential from stock
- **RSI**: Relative Strength Index for momentum
- **Moving Averages**: Trend direction indicators`
  },
  'stock-market/nse-trading': {
    title: 'NSE Trading Guide',
    description: 'National Stock Exchange trading procedures and tools',
    content: `# NSE Trading Guide

## Step-by-Step NSE Data Access Process

### 1. Accessing Live NSE Data
**Step 1:** Navigate to Stock Market from main menu
**Step 2:** System automatically loads live NSE data (takes 2-3 seconds)
**Step 3:** Data refreshes every 30 seconds automatically
**Step 4:** View Nifty 50 stocks with real-time prices
**Step 5:** Check top gainers and losers for the day

### 2. Contract Note Management Process
**Step 1:** Go to "Contract Notes" tab in Stock Market
**Step 2:** Click "Add New Contract Note" button
**Step 3:** Fill contract note details:
   - Contract note number from broker
   - Trading date
   - Broker name selection
   - Transaction type (Buy/Sell)
**Step 4:** Add individual stock transactions:
   - Stock symbol (e.g., RELIANCE, TCS)
   - Quantity of shares
   - Price per share
   - Brokerage charges
**Step 5:** Upload contract note document (PDF/image)
**Step 6:** Save contract note
**Step 7:** System calculates total investment and charges

### 3. Portfolio Tracking Process
**Step 1:** Add all your stock purchases via contract notes
**Step 2:** System fetches current prices from NSE automatically
**Step 3:** View real-time P&L calculations:
   - Total invested amount
   - Current portfolio value
   - Unrealized profit/loss
   - Day's P&L changes
**Step 4:** Monitor sector-wise allocation
**Step 5:** Track XIRR (Extended Internal Rate of Return)

## NSE Market Hours and Important Notes
- **Trading Hours**: 9:15 AM to 3:30 PM (Monday to Friday)
- **Pre-market**: 9:00 AM to 9:15 AM
- **Post-market**: 3:40 PM to 4:00 PM
- **Data Delay**: Real-time data with minimal delay

## Troubleshooting NSE Data Issues
- **Data not loading**: Check internet connection
- **Prices not updating**: Wait for 30-second refresh cycle
- **Contract note upload failed**: Check file size and format`
  },
  'ai/assistant': {
    title: 'AI Assistant Guide',
    description: 'Use the AI-powered assistant with Gemini 2.5 Flash Preview',
    content: `# AI Assistant Guide

## Step-by-Step AI Assistant Usage Process

### 1. Accessing AI Assistant
**Step 1:** Navigate to AI Assistant from main menu
**Step 2:** AI chat interface will load with Gemini 2.5 Flash Preview
**Step 3:** Choose between Normal Chat and Document Generation modes
**Step 4:** Start typing your question or request

### 2. AI Mode Selection Process
**Step 1:** Select AI mode:
   - **Normal Chat**: General conversation, questions, explanations
   - **Document Generation**: Professional documents with download options
**Step 2:** Mode description will update based on selection
**Step 3:** Chat interface adapts to selected mode

### 3. Normal Chat Process
**Step 1:** Type your question in the chat box
**Step 2:** Click "Send" button or press Enter
**Step 3:** AI responds using Gemini 2.5 Flash Preview model
**Step 4:** Continue conversation with follow-up questions
**Step 5:** Use "Save Conversation" to save chat history
**Step 6:** Use "Clear Chat" to start fresh conversation

### 4. Document Generation Process
**Step 1:** Select "Document Generation" mode
**Step 2:** Provide document requirements and details
**Step 3:** AI generates structured document with download options
**Step 4:** Review generated content
**Step 5:** Download in preferred format (Word, PDF, etc.)

### 5. Chat History Management
**Step 1:** Click "Chat History" to view saved conversations
**Step 2:** Browse through previous chat sessions
**Step 3:** Click on any conversation to view details
**Step 4:** Use search to find specific conversations
**Step 5:** Delete conversations if needed

## Key Features Explained
- **Real-time Streaming**: Responses appear as they're generated
- **Conversation History**: All chats saved for future reference
- **Document Generation**: Professional document creation
- **Multiple Modes**: Chat and document generation options
- **Error Handling**: Graceful error handling with retry options

## Troubleshooting AI Issues
- **No response**: Check internet connection and try again
- **Slow responses**: Normal for complex queries, wait patiently
- **Error messages**: Try refreshing the page
- **History not saving**: Ensure you're logged in and try again
- **Streaming stopped**: Refresh page and restart conversation`
  },
  'financial/dashboard': {
    title: 'Financial Management Guide',
    description: 'Manage expenses, ledgers, and financial reports',
    content: `# Financial Management Guide

## Step-by-Step Financial Management Process

### 1. Initial Setup (First Time Users)
**Step 1:** Navigate to Financial Management from main menu
**Step 2:** Click "Add New Expense" to create your first expense entry
**Step 3:** Set up expense categories (PAYMENT, RECEIPT, TRANSFER)
**Step 4:** Configure payment methods (Cash, Bank)
**Step 5:** Create ledgers for different accounts

### 2. Daily Expense Recording Process
**Step 1:** Click "Add New Expense" button
**Step 2:** Fill required fields:
   - Date of expense (use date picker)
   - Amount in rupees (negative for expenses, positive for receipts)
   - Category selection (PAYMENT/RECEIPT/TRANSFER)
   - Payment method (Cash/Bank with instrumentNo for bank)
   - Description/remarks (optional)
**Step 3:** Select "Paid To" from existing contacts or add new
**Step 4:** Choose project if applicable
**Step 5:** Click "Save Expense" to record transaction

### 3. Transfer Between Accounts Process
**Step 1:** Click "Transfer Funds" button
**Step 2:** Select source account (From)
**Step 3:** Select destination account (To)
**Step 4:** Enter transfer amount
**Step 5:** Add transfer description
**Step 6:** Click "Execute Transfer" (creates isTransfer: true record)

### 4. Subcontractor Management Process
**Step 1:** Go to "Subcontractor Accounts" tab
**Step 2:** Click "Add New Subcontractor"
**Step 3:** Enter subcontractor details (name, contact, rates)
**Step 4:** Record payments made to subcontractors
**Step 5:** Track outstanding amounts and payment history

### 5. Reports and Analytics Process
**Step 1:** Navigate to "Reports & Analytics" section
**Step 2:** Select report type:
   - Monthly Summary
   - Category-wise breakdown
   - Project-wise expenses
   - Financial year reports
**Step 3:** Choose date range using date pickers
**Step 4:** Click "Generate Report"
**Step 5:** Export to Excel/PDF if needed

## Key Features Explained
- **Expense Tracking**: Record and categorize business expenses
- **Receipt Management**: Track income and receipts
- **Fund Transfers**: Move money between accounts
- **Reporting**: Generate financial reports and analytics
- **Export Options**: Download data in various formats

## Troubleshooting Common Issues
- **Expense not saving**: Check all required fields are filled
- **Report showing no data**: Verify date range includes expense dates
- **Export not working**: Ensure you have expenses in selected date range
- **Categories not loading**: Refresh page or check internet connection
- **Transfer not working**: Verify both source and destination accounts exist`
  },
  'wages/dashboard': {
    title: 'Employee Wages Guide',
    description: 'Complete payroll and wage management system',
    content: `# Employee Wages Guide

## Step-by-Step Wage Management Process

### 1. Accessing Wages Module
**Step 1:** Navigate to "Employee Wages" from main menu
**Step 2:** Select "Dashboard" to view wages overview
**Step 3:** Review current payroll status and statistics
**Step 4:** Check pending wages and recent payments

### 2. Setting Up Master Roll (First Time)
**Step 1:** Go to "Master Roll" tab
**Step 2:** Click "Add New Employee" button
**Step 3:** Fill employee details:
   - Employee name and ID
   - Department and designation
   - Basic salary amount
   - Allowances (HRA, DA, etc.)
   - Deductions (PF, ESI, etc.)
**Step 4:** Set wage calculation method (monthly/daily/hourly)
**Step 5:** Save employee record
**Step 6:** Repeat for all employees

### 3. Processing Monthly Wages
**Step 1:** Go to "Employee Wages" main tab
**Step 2:** Select the month and year for processing
**Step 3:** Click "Generate Wages" button
**Step 4:** System calculates wages for all employees:
   - Basic salary calculations (pDayWage * wage_Days)
   - EPF and ESIC calculations
   - Advance recovery deductions
   - Other deductions and benefits
**Step 5:** Review calculated wages for accuracy
**Step 6:** Make manual adjustments if needed
**Step 7:** Click "Finalize Wages" to complete processing

### 4. Managing Employee Advances
**Step 1:** Go to "Employee Advances" tab
**Step 2:** Click "Add New Advance" for advance payments
**Step 3:** Fill advance details:
   - Employee selection from MasterRoll
   - Advance amount
   - Advance date
   - Recovery method (monthly installments)
**Step 4:** Save advance record
**Step 5:** System automatically deducts from future wages via advance_recovery field

### 5. Generating Wage Reports
**Step 1:** Go to "Reports & Analytics" tab
**Step 2:** Select report type:
   - Monthly wage summary
   - Employee-wise wage slip
   - EPF/ESIC compliance report
   - Advance recovery report
**Step 3:** Choose date range (month/year)
**Step 4:** Click "Generate Report"
**Step 5:** Review report data
**Step 6:** Export to Excel/PDF for records

## Key Features Explained
- **EPF Calculation**: Automatic EPF deductions
- **ESIC Calculation**: Automatic ESIC deductions
- **Advance Recovery**: Automatic deduction from monthly wages
- **Financial Integration**: Wages automatically added to expense system

## Troubleshooting Common Issues
- **Wages not calculating**: Check employee setup and data
- **Wrong calculations**: Verify wage rates and working days
- **Advance not deducting**: Check advance recovery settings
- **Reports not generating**: Ensure data exists for selected period`
  },
  'inventory/dashboard': {
    title: 'Inventory Management Guide',
    description: 'Stock tracking and inventory control system',
    content: `# Inventory Management Guide

## Step-by-Step Inventory Management Process

### 1. Accessing Inventory Module
**Step 1:** Navigate to "Inventory" from main menu
**Step 2:** Select "Dashboard" to view inventory overview
**Step 3:** Review current stock levels and alerts
**Step 4:** Check recent transactions and movements

### 2. Setting Up Initial Stock (First Time)
**Step 1:** Go to "Inventory Management" main tab
**Step 2:** Click "Add New Item" button
**Step 3:** Fill item details:
   - Item name and description
   - Part number (pno) - optional but unique
   - Batch number - optional but unique
   - HSN code (required for GST)
   - Unit of measurement (uom)
   - OEM details (optional)
**Step 4:** Set initial stock quantity and rates:
   - Quantity (qty)
   - Rate per unit
   - GST rate (grate)
   - Total value calculation
   - MRP (optional)
   - Expiry date (optional)
**Step 5:** Save stock record
**Step 6:** Repeat for all inventory items

### 3. Recording Purchase Bills
**Step 1:** Go to "Bills" tab or use /inventory/edit-bill
**Step 2:** Click "Add New Purchase Bill"
**Step 3:** Fill bill details:
   - Bill type (PURCHASE, SALES, DEBIT NOTE, CREDIT NOTE)
   - Party/Supplier selection or create new
   - Bill number and date
   - Items with quantities, rates, HSN codes
   - GST calculations (CGST, SGST, IGST)
**Step 4:** System calculates total amount with GST
**Step 5:** Save bill record
**Step 6:** Stock levels automatically updated via StockReg

### 4. Managing Parties (Suppliers/Customers)
**Step 1:** Go to Party management section
**Step 2:** Click "Add New Party"
**Step 3:** Fill party details:
   - Supply type (supplier/customer)
   - Address and contact details
   - GSTIN (or mark as UNREGISTERED)
   - State and state code
   - PAN and PIN details
**Step 4:** Save party record
**Step 5:** Link bills to parties for tracking

### 5. Generating Stock Reports
**Step 1:** Go to "Stock Report" tab
**Step 2:** Select report type:
   - Current stock summary
   - Stock movement report
   - Low stock alert report (items ≤ 5 units)
   - Purchase analysis report
   - GST summary report
**Step 3:** Choose date range for reports
**Step 4:** Apply filters (category, supplier, etc.)
**Step 5:** Click "Generate Report"
**Step 6:** Review report data
**Step 7:** Export to Excel for analysis

## Key Features Explained
- **Real-time Stock Tracking**: Live updates with every transaction
- **GST Integration**: Automatic GST calculations
- **Low Stock Alerts**: Notifications for items running low
- **Multi-bill Types**: Support for purchase, sales, debit/credit notes
- **Party Management**: Comprehensive supplier/customer database

## Troubleshooting Common Issues
- **Stock not updating**: Check if transactions are properly saved
- **GST calculation wrong**: Verify HSN codes and GST rates
- **Reports showing no data**: Check date range and filters
- **Bill creation failed**: Verify all required fields are filled
- **Party not found**: Ensure party is created before bill entry`
  },
  'documents/management': {
    title: 'Document Management Guide',
    description: 'Organize and manage business documents with Google Drive integration',
    content: `# Document Management Guide

## Step-by-Step Document Management Process

### 1. Accessing Documents Module
**Step 1:** Navigate to "Docs" from main menu
**Step 2:** Document management interface will load
**Step 3:** Review existing documents list
**Step 4:** Check document expiry dates and status

### 2. Adding New Documents
**Step 1:** Click "Add New Document" button
**Step 2:** Fill required document details:
   - Document name (required)
   - Description
   - Reference number (ref_no) - required
   - Value amount (required)
   - Start date (optional)
   - Closed date (optional)
   - Original expiry date (oExpiryDate) - required
   - Current expiry date (expiryDate) - required
**Step 3:** Upload document file (optional):
   - File gets uploaded to Google Drive
   - File ID stored for future access
**Step 4:** Click "Save Document"
**Step 5:** Document appears in your document list

### 3. Managing Document Files
**Step 1:** Click on document to view details
**Step 2:** Use "Upload File" to add document file
**Step 3:** Files are stored in Google Drive
**Step 4:** Access files directly from document list
**Step 5:** Download files when needed

### 4. Searching and Filtering Documents
**Step 1:** Use the search functionality to find documents
**Step 2:** Filter by document properties
**Step 3:** Sort by expiry date, creation date, or name
**Step 4:** Click on document to view details
**Step 5:** Edit or update document information

### 5. Document Expiry Management
**Step 1:** Monitor document expiry dates
**Step 2:** System shows expiring documents
**Step 3:** Update expiry dates when renewed
**Step 4:** Track document status changes
**Step 5:** Set reminders for important documents

## Key Features Explained
- **Cloud Storage**: Files stored securely in cloud storage
- **Expiry Tracking**: Monitor document expiration dates
- **File Management**: Upload and download document files
- **Status Tracking**: Track document lifecycle
- **Search & Filter**: Find documents quickly

## Troubleshooting Common Issues
- **Document not saving**: Check all required fields are filled
- **File upload failed**: Check file size and format
- **Document not found**: Use search or check filters
- **Expiry date issues**: Ensure expiry dates are properly set
- **Access denied**: Check your permissions with administrator`
  },
  'admin/dashboard': {
    title: 'Admin Panel Guide',
    description: 'System administration and management tools',
    content: `# Admin Panel Guide

## Step-by-Step Admin Management Process

### 1. Accessing Admin Panel
**Step 1:** Login with admin credentials
**Step 2:** Navigate to Admin Panel from main menu
**Step 3:** Admin dashboard will load with system overview
**Step 4:** Review system status and recent activities

### 2. User Management Process
**Step 1:** Go to "Management" tab
**Step 2:** Select "Users" sub-tab
**Step 3:** View all registered users and their details
**Step 4:** For user actions:
   - Edit user details and roles
   - Reset user passwords
   - Activate/deactivate accounts
   - Delete users if necessary
**Step 5:** Click "Save Changes" after modifications

### 3. Firm Management Process
**Step 1:** Go to "Management" tab
**Step 2:** Select "Firms" sub-tab
**Step 3:** Review all registered firms
**Step 4:** For firm actions:
   - Approve pending firm registrations
   - Edit firm details and status
   - View firm users and activities
   - Suspend or activate firms
**Step 5:** Update firm status as needed

### 4. Database Management Process
**Step 1:** Go to "Database" tab
**Step 2:** Select database type (MongoDB)
**Step 3:** View MongoDB models and collections:
   - User, Firm, Role, Document, AIHistory
   - Wage, MasterRoll, EmployeeAdvance, AdvanceRecovery
   - Stocks, Bills, StockReg, Party
   - NSE, Folio, CNNote, MutualFund
**Step 4:** Use DatabaseActions component for:
   - Backup Database: Create full database backups
   - Export Data: Export specific models to Excel/JSON
   - API Logs Backup: Automatic API logs backup
**Step 5:** Monitor real-time operations with progress tracking

### 5. System Monitoring Process
**Step 1:** Go to "Dashboard" tab for system overview
**Step 2:** Monitor key metrics via DashboardOverview component:
   - Total users, firms, and system statistics
   - Recent activities and registrations
   - System health indicators
**Step 3:** Use RealTimeStatus component for live monitoring
**Step 4:** Check ApiLogsAutoBackup status
**Step 5:** Review system alerts and notifications

### 6. Manager Code Management
**Step 1:** Go to "Management" tab
**Step 2:** Select "Manager Codes" sub-tab
**Step 3:** View all manager codes and their status
**Step 4:** For code management:
   - Generate new manager codes using nanoid
   - View code usage history
   - Track which users used which codes
   - Monitor code creation timestamps
**Step 5:** Distribute codes to authorized managers

## Key Admin Features
- **System Overview**: Monitor system statistics and health
- **User Management**: Manage user accounts and permissions
- **Firm Management**: Oversee firm registrations and settings
- **Database Operations**: Backup and maintenance tools
- **Real-time Monitoring**: Live system status updates

## Critical Security Notes
- **Admin Access Only**: All admin functions require admin privileges
- **Data Protection**: Handle all operations with extreme caution
- **Backup Strategy**: Regular automated backups for data safety
- **Access Control**: Strict role-based access control
- **Audit Trail**: All admin actions are logged for security

## Troubleshooting Admin Issues
- **Access denied**: Verify you have admin privileges
- **Data not loading**: Check your connection and try refreshing
- **Operations failing**: Contact system administrator
- **Backup issues**: Ensure sufficient storage space
- **Console not updating**: Refresh page or check connection`
  },
  'public/about': {
    title: 'About Page Guide',
    description: 'Learn about BusinessPro Suite and our mission',
    content: `# About Page Guide

## Step-by-Step About Page Navigation

### 1. Accessing About Page
**Step 1:** Click "About" in the main navigation menu
**Step 2:** About page will load with company information
**Step 3:** Scroll through different sections for complete information
**Step 4:** Use navigation links to jump to specific sections

### 2. Understanding Our Mission
**Step 1:** Read the mission statement at the top
**Step 2:** Understand our commitment to business management solutions
**Step 3:** Learn about our core values and principles
**Step 4:** See how we help businesses grow and succeed

### 3. Platform Overview Section
**Step 1:** Review the comprehensive feature list
**Step 2:** Understand each module's purpose:
   - Financial Management capabilities
   - Employee Wages and payroll features
   - Inventory Management tools
   - AI Assistant functionality
   - Stock Market analysis tools
**Step 3:** See how modules integrate together
**Step 4:** Identify which features benefit your business

### 4. Technology Stack Information
**Step 1:** Learn about our modern technology foundation
**Step 2:** Understand security measures and data protection
**Step 3:** Review scalability and performance features
**Step 4:** See integration capabilities with other systems

### 5. Getting Started Information
**Step 1:** Find quick start guides and resources
**Step 2:** Access registration and setup information
**Step 3:** Review system requirements
**Step 4:** Get contact information for support

## Key Information Available
- **Company Background**: Our history and expertise
- **Feature Overview**: Complete module descriptions
- **Technology Details**: Security and performance information
- **Support Resources**: Help and contact information
- **Business Benefits**: How we help your business succeed

## Next Steps After Reading About Page
- **Register**: Create your BusinessPro Suite account
- **Contact Us**: Get in touch for questions or demos
- **Explore Features**: Navigate to specific modules
- **Start Trial**: Begin using the platform
- **Get Support**: Access help resources and documentation`
  },
  'public/contact': {
    title: 'Contact Page Guide',
    description: 'Get in touch with BusinessPro Suite support',
    content: `# Contact Page Guide

## Step-by-Step Contact Process

### 1. Accessing Contact Page
**Step 1:** Click "Contact" in the main navigation menu
**Step 2:** Contact page will load with contact form and information
**Step 3:** Review available contact methods
**Step 4:** Choose the best contact method for your needs

### 2. Using the Contact Form
**Step 1:** Fill out the contact form with your details:
   - Full name (required)
   - Email address (required)
   - Phone number (optional)
   - Company name (optional)
   - Subject of inquiry (required)
**Step 2:** Select inquiry type from dropdown:
   - General Information
   - Technical Support
   - Sales Inquiry
   - Feature Request
   - Bug Report
**Step 3:** Write detailed message in the message box
**Step 4:** Review all information for accuracy
**Step 5:** Click "Send Message" to submit

### 3. Direct Contact Methods
**Step 1:** Use email for detailed inquiries
**Step 2:** Call phone number for urgent support
**Step 3:** Use business hours for fastest response
**Step 4:** Include relevant details in your communication

### 4. Support Categories Available
**Step 1:** Technical Support:
   - Login and access issues
   - Feature functionality problems
   - Data import/export issues
   - Performance concerns
**Step 2:** Sales Inquiries:
   - Pricing information
   - Feature comparisons
   - Custom solutions
   - Enterprise packages
**Step 3:** General Information:
   - Product demonstrations
   - Implementation guidance
   - Training resources
   - Partnership opportunities

### 5. Response Time Expectations
**Step 1:** General inquiries: 24-48 hours response
**Step 2:** Technical support: 4-8 hours during business hours
**Step 3:** Urgent issues: Same day response
**Step 4:** Sales inquiries: 24 hours response

## Contact Information Available
- **Email Support**: Direct email for detailed inquiries
- **Phone Support**: Voice support during business hours
- **Business Hours**: Operating hours for live support
- **Response Times**: Expected response timeframes
- **Support Categories**: Types of assistance available

## Tips for Effective Contact
- **Be Specific**: Provide detailed description of issues
- **Include Screenshots**: Visual aids help with technical issues
- **Provide Context**: Explain what you were trying to accomplish
- **Include Error Messages**: Copy exact error text if applicable
- **Be Patient**: Allow appropriate response time for quality support

## Troubleshooting Contact Issues
- **Form not submitting**: Check all required fields are filled
- **Email not working**: Verify email address is correct
- **No response received**: Check spam folder and wait for response time
- **Phone not connecting**: Verify calling during business hours
- **Message too long**: Keep messages concise but detailed`
  }
}

// Computed properties
const helpTitle = computed(() => {
  const topic = helpTopics.find(t => t.key === selectedTopic.value)
  return topic ? topic.title : 'Help Center'
})

const helpDescription = computed(() => {
  const content = helpContentMap[selectedTopic.value]
  return content ? content.description : 'Select a topic for detailed help'
})

const selectedHelpContent = computed(() => {
  const content = helpContentMap[selectedTopic.value]
  return content ? content.content : ''
})

const filteredTopics = computed(() => {
  if (!searchQuery.value) return helpTopics
  
  return helpTopics.filter(topic =>
    topic.title.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
    topic.category.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// Methods
const closeModal = () => {
  emit('close')
}

const selectTopic = (topicKey) => {
  selectedTopic.value = topicKey
}

const printHelp = () => {
  window.print()
}

const copyHelpContent = async () => {
  if (selectedHelpContent.value) {
    try {
      await navigator.clipboard.writeText(selectedHelpContent.value)
      // You could add a toast notification here
    } catch (err) {
      console.error('Failed to copy help content:', err)
    }
  }
}

// Watch for prop changes
watch(() => props.helpTopic, (newTopic) => {
  selectedTopic.value = newTopic
})

// Set initial topic when modal opens
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && props.helpTopic) {
    selectedTopic.value = props.helpTopic
  }
})
</script>
