<template>
  <div>
    <!-- Report Summary -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-1">{{ getReportTitle() }}</h2>
      <p v-if="reportData && reportData.timePeriod" class="text-sm text-gray-500 mb-4">{{ formatTimePeriod(reportData.timePeriod, reportData.reportType) }}</p>
      <p v-else class="text-sm text-gray-500 mb-4">All time</p>

      <div v-if="isLoading" class="flex items-center justify-center py-8">
        <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <div v-else-if="!reportData" class="text-center py-8 text-gray-500">
        No report data available. Please generate a report.
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div class="bg-red-50 rounded-lg p-4 border border-red-100">
          <h3 class="text-sm font-medium text-red-800 mb-2">Total Payments</h3>
          <p class="text-2xl font-bold text-red-600">{{ formatCurrency(reportData.summary.totalExpenses) }}</p>
        </div>

        <div class="bg-green-50 rounded-lg p-4 border border-green-100">
          <h3 class="text-sm font-medium text-green-800 mb-2">Total Receipts</h3>
          <p class="text-2xl font-bold text-green-600">{{ formatCurrency(reportData.summary.totalReceipts) }}</p>
        </div>

        <div class="bg-indigo-50 rounded-lg p-4 border border-indigo-100">
          <h3 class="text-sm font-medium text-indigo-800 mb-2">Net Amount</h3>
          <p class="text-2xl font-bold" :class="getAmountClass(reportData.summary.netAmount)">
            {{ formatCurrency(reportData.summary.netAmount) }}
          </p>
        </div>
      </div>
    </div>

    <!-- Report Chart -->
    <div class="bg-white rounded-lg shadow p-6 mb-6">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Report Visualization</h2>

      <div v-if="isLoading" class="flex items-center justify-center h-64">
        <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <div v-else-if="!reportData || !chartData" class="flex items-center justify-center h-64 text-gray-500">
        No chart data available
      </div>

      <div v-else class="h-64">
        <canvas ref="reportChart"></canvas>
      </div>
    </div>

    <!-- Report Data Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Report Details</h2>
      </div>

      <div v-if="isLoading" class="flex items-center justify-center py-8">
        <svg class="animate-spin h-8 w-8 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      </div>

      <div v-else-if="!reportData || !reportData.data || reportData.data.length === 0" class="text-center py-8 text-gray-500">
        No report data available
      </div>

      <div v-else class="overflow-x-auto">
        <!-- Daily Report Table -->
        <table v-if="reportData.reportType === 'daily'" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Date
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transactions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Payments
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Receipts
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Net Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in reportData.data" :key="item.date" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(item.date) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                {{ formatCurrency(item.totalExpenses) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                {{ formatCurrency(item.totalReceipts) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(item.netAmount)">
                {{ formatCurrency(item.netAmount) }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Weekly Report Table -->
        <table v-else-if="reportData.reportType === 'weekly'" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Week
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transactions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Payments
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Receipts
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Net Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in reportData.data" :key="item.week" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ item.week }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                {{ formatCurrency(item.totalExpenses) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                {{ formatCurrency(item.totalReceipts) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(item.netAmount)">
                {{ formatCurrency(item.netAmount) }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Monthly Report Table -->
        <table v-else-if="reportData.reportType === 'monthly'" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Month
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transactions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Payments
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Receipts
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Net Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in reportData.data" :key="item.month" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatMonth(item.month) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                {{ formatCurrency(item.totalExpenses) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                {{ formatCurrency(item.totalReceipts) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(item.netAmount)">
                {{ formatCurrency(item.netAmount) }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Yearly Report Table -->
        <table v-else-if="reportData.reportType === 'yearly' || reportData.reportType === 'financial-year'" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                {{ reportData.reportType === 'yearly' ? 'Year' : 'Financial Year' }}
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transactions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Expenses
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Receipts
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Net Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in reportData.data" :key="item.year || item.financialYear" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ item.year || item.financialYear }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                {{ formatCurrency(item.totalExpenses) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                {{ formatCurrency(item.totalReceipts) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(item.netAmount)">
                {{ formatCurrency(item.netAmount) }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Category/PaidTo/Project Report Table -->
        <table v-else-if="['category', 'paidTo', 'project', 'subs'].includes(reportData.reportType)" class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                {{ getGroupLabel() }}
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Transactions
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Expenses
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Receipts
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Net Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in reportData.data" :key="getGroupKey(item)" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ getGroupValue(item) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.count }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-red-600">
                {{ formatCurrency(item.totalExpenses) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-green-600">
                {{ formatCurrency(item.totalReceipts) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(item.netAmount)">
                {{ formatCurrency(item.netAmount) }}
              </td>
            </tr>
          </tbody>
        </table>

        <!-- Date Range Report Table -->
        <table v-else class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Date
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Paid To/From
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Category
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Payment Mode
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in reportData.data" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(item.date) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ item.paidTo }}
                <span v-if="item.paidToGroup" class="ml-1 text-xs text-gray-500">({{ item.paidToGroup }})</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getCategoryClass(item.category)"
                >
                  {{ item.category || 'PAYMENT' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ item.paymentMode.type === 'cash' ? 'Cash' : 'Bank' }}
                <span v-if="item.paymentMode.instrumentNo" class="ml-1 text-xs text-gray-500">({{ item.paymentMode.instrumentNo }})</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(item.amount)">
                {{ formatCurrency(item.amount) }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import Chart from 'chart.js/auto';

export default {
  name: 'ReportResults',

  props: {
    reportData: {
      type: Object,
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  setup(props) {
    // Chart references
    const reportChart = ref(null);

    // Chart instance
    let chartInstance = null;

    // Computed properties
    const chartData = computed(() => {
      if (!props.reportData || !props.reportData.data || props.reportData.data.length === 0) {
        return null;
      }

      const reportType = props.reportData.reportType;
      const data = props.reportData.data;

      // Generate chart data based on report type
      switch (reportType) {
        case 'daily':
        case 'weekly':
        case 'monthly':
        case 'yearly':
        case 'financial-year':
          return {
            labels: data.map(item => {
              if (reportType === 'daily') return formatDate(item.date);
              if (reportType === 'weekly') return item.week;
              if (reportType === 'monthly') return formatMonth(item.month);
              if (reportType === 'yearly') return item.year;
              if (reportType === 'financial-year') return item.financialYear;
              return '';
            }),
            datasets: [
              {
                label: 'Payments',
                data: data.map(item => item.totalExpenses),
                backgroundColor: 'rgba(239, 68, 68, 0.5)',
                borderColor: 'rgb(239, 68, 68)',
                borderWidth: 1
              },
              {
                label: 'Receipts',
                data: data.map(item => item.totalReceipts),
                backgroundColor: 'rgba(34, 197, 94, 0.5)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 1
              }
            ]
          };

        case 'category':
        case 'paidTo':
        case 'project':
        case 'subs':
          const backgroundColors = [
            'rgba(239, 68, 68, 0.5)',
            'rgba(34, 197, 94, 0.5)',
            'rgba(59, 130, 246, 0.5)',
            'rgba(139, 92, 246, 0.5)',
            'rgba(236, 72, 153, 0.5)',
            'rgba(245, 158, 11, 0.5)',
            'rgba(16, 185, 129, 0.5)',
            'rgba(107, 114, 128, 0.5)'
          ];

          return {
            labels: data.map(item => getGroupValue(item)),
            datasets: [
              {
                label: 'Amount',
                data: data.map(item => item.totalExpenses),
                backgroundColor: data.map((_, index) => backgroundColors[index % backgroundColors.length]),
                borderWidth: 1
              }
            ]
          };

        default:
          return null;
      }
    });

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount);
    };

    const formatDate = (date) => {
      if (!date) return '-';
      return new Date(date).toLocaleDateString();
    };

    const formatMonth = (monthStr) => {
      if (!monthStr) return '-';

      const [year, month] = monthStr.split('-');
      return new Date(year, month - 1).toLocaleDateString('default', { month: 'long', year: 'numeric' });
    };

    const getAmountClass = (amount) => {
      return amount < 0 ? 'text-red-600' : 'text-green-600';
    };

    const getCategoryClass = (category) => {
      switch (category) {
        case 'PAYMENT':
          return 'bg-red-100 text-red-800';
        case 'RECEIPT':
          return 'bg-green-100 text-green-800';
        case 'TRANSFER':
          return 'bg-blue-100 text-blue-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    const getGroupLabel = () => {
      const reportType = props.reportData?.reportType;

      switch (reportType) {
        case 'category':
          return 'Category';
        case 'paidTo':
          return 'Paid To/From';
        case 'project':
          return 'Project';
        case 'subs':
          return 'Sub';
        default:
          return 'Group';
      }
    };

    const getGroupKey = (item) => {
      const reportType = props.reportData?.reportType;

      switch (reportType) {
        case 'category':
          return item.category || 'Uncategorized';
        case 'paidTo':
          return item.paidTo;
        case 'project':
          return item.project || 'No Project';
        case 'subs':
          return item.paidTo;
        default:
          return '';
      }
    };

    const getGroupValue = (item) => {
      const reportType = props.reportData?.reportType;

      switch (reportType) {
        case 'category':
          return item.category || 'Uncategorized';
        case 'paidTo':
          return item.paidTo;
        case 'project':
          return item.project || 'No Project';
        case 'subs':
          return item.paidTo;
        default:
          return '';
      }
    };

    const renderChart = () => {
      if (!chartData.value) return;

      if (chartInstance) {
        chartInstance.destroy();
      }

      const ctx = reportChart.value.getContext('2d');
      const reportType = props.reportData.reportType;

      // Determine chart type based on report type
      let chartType = 'bar'; // Default to bar chart

      if (['category', 'paidTo', 'project', 'subs'].includes(reportType)) {
        chartType = 'doughnut';
      }

      chartInstance = new Chart(ctx, {
        type: chartType,
        data: chartData.value,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          scales: chartType === 'bar' ? {
            y: {
              beginAtZero: true
            }
          } : undefined,
          plugins: {
            legend: {
              position: chartType === 'doughnut' ? 'right' : 'top'
            }
          }
        }
      });
    };

    // Watch for changes to report data
    watch(() => props.reportData, () => {
      if (props.reportData && !props.isLoading) {
        // Wait for the next tick to ensure the canvas is available
        setTimeout(() => {
          renderChart();
        }, 0);
      }
    }, { deep: true });

    // Initialize
    onMounted(() => {
      if (props.reportData && !props.isLoading) {
        renderChart();
      }
    });

    // Helper methods for displaying report information
    const getReportTitle = () => {
      if (!props.reportData) return 'Report Summary';

      const type = props.reportData.reportType;
      const capitalized = type.charAt(0).toUpperCase() + type.slice(1).replace(/-/g, ' ');
      return `${capitalized} Report Summary`;
    };

    const formatTimePeriod = (timePeriod, reportType) => {
      if (!timePeriod) return '';

      switch (reportType) {
        case 'weekly': {
          const [start, end] = timePeriod.split('|');
          return `Week of ${new Date(start).toLocaleDateString()} to ${new Date(end).toLocaleDateString()}`;
        }

        case 'monthly': {
          const [year, month] = timePeriod.split('-');
          const date = new Date(parseInt(year), parseInt(month) - 1, 1);
          return date.toLocaleString('default', { month: 'long', year: 'numeric' });
        }

        case 'yearly': {
          return `Year ${timePeriod}`;
        }

        case 'financial-year': {
          const [startYear, endYear] = timePeriod.split('-');
          return `Financial Year ${startYear}-${endYear}`;
        }

        default:
          return timePeriod;
      }
    };

    return {
      reportChart,
      chartData,
      formatCurrency,
      formatDate,
      formatMonth,
      getAmountClass,
      getCategoryClass,
      getGroupLabel,
      getGroupKey,
      getGroupValue,
      getReportTitle,
      formatTimePeriod
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
