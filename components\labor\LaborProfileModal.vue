<template>
  <div v-if="isOpen" class="modal-overlay" @click="closeModal">
    <div class="modal-container" @click.stop>
      <div class="modal-header">
        <h3 class="text-lg font-semibold text-gray-900">
          {{ isEditing ? 'Edit' : 'Add' }} Labor Profile
        </h3>
        <button
          @click="closeModal"
          class="text-gray-400 hover:text-gray-600 transition-colors"
        >
          <Icon name="heroicons:x-mark" class="w-6 h-6" />
        </button>
      </div>

      <div class="modal-body">
        <form @submit.prevent="saveProfile" class="space-y-6">
          <!-- Basic Information -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Name -->
            <div class="md:col-span-2">
              <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                Name <span class="text-red-500">*</span>
              </label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'border-red-500': errors.name }"
                placeholder="Enter worker name"
                @blur="validateName"
              />
              <p v-if="errors.name" class="mt-1 text-sm text-red-600">{{ errors.name }}</p>
              <p v-if="nameValidation.checking" class="mt-1 text-sm text-blue-600">
                <Icon name="heroicons:arrow-path" class="w-4 h-4 animate-spin inline mr-1" />
                Checking name availability...
              </p>
            </div>

            <!-- Daily Rate -->
            <div>
              <label for="dailyRate" class="block text-sm font-medium text-gray-700 mb-2">
                Daily Rate (₹) <span class="text-red-500">*</span>
              </label>
              <input
                id="dailyRate"
                v-model="form.daily_rate"
                type="number"
                step="0.01"
                min="0"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                :class="{ 'border-red-500': errors.daily_rate }"
                placeholder="0.00"
              />
              <p v-if="errors.daily_rate" class="mt-1 text-sm text-red-600">{{ errors.daily_rate }}</p>
            </div>

            <!-- Group -->
            <div>
              <label for="group" class="block text-sm font-medium text-gray-700 mb-2">
                Group
              </label>
              <select
                id="group"
                v-model="form.group_id"
                class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">Select a group</option>
                <option
                  v-for="group in groups"
                  :key="group.id"
                  :value="group.id"
                >
                  {{ group.name }}
                </option>
              </select>
              <button
                type="button"
                @click="showGroupModal = true"
                class="mt-2 text-sm text-blue-600 hover:text-blue-800"
              >
                + Create new group
              </button>
            </div>

          </div>
        </form>
      </div>

      <div class="modal-footer">
        <button
          type="button"
          @click="closeModal"
          class="btn btn-secondary mr-3"
        >
          Cancel
        </button>
        <button
          @click="saveProfile"
          :disabled="saving || !isFormValid || nameValidation.checking"
          class="btn btn-primary"
        >
          <Icon v-if="saving" name="heroicons:arrow-path" class="w-4 h-4 animate-spin mr-2" />
          {{ saving ? 'Saving...' : (isEditing ? 'Update' : 'Create') }} Profile
        </button>
      </div>
    </div>

    <!-- Group Creation Modal -->
    <GroupManagementModal
      v-if="showGroupModal"
      :is-open="showGroupModal"
      :firm-id="firmId"
      :user-id="userId"
      @close="showGroupModal = false"
      @saved="onGroupCreated"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import GroupManagementModal from '~/components/labor/GroupManagementModal.vue'

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  profile: {
    type: Object,
    default: null
  },
  groups: {
    type: Array,
    default: () => []
  },
  firmId: {
    type: String,
    required: true
  },
  userId: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['close', 'saved'])

// Form data
const form = ref({
  name: '',
  daily_rate: '',
  group_id: ''
})

// State
const saving = ref(false)
const errors = ref({})
const nameValidation = ref({
  checking: false,
  available: true
})
const showGroupModal = ref(false)

// Computed
const isEditing = computed(() => !!props.profile)
const isFormValid = computed(() => {
  return form.value.name && 
         form.value.daily_rate && 
         !errors.value.name && 
         !errors.value.daily_rate &&
         nameValidation.value.available
})

// Methods
const closeModal = () => {
  emit('close')
  resetForm()
}

const resetForm = () => {
  form.value = {
    name: '',
    daily_rate: '',
    group_id: ''
  }
  errors.value = {}
  nameValidation.value = { checking: false, available: true }
}

const loadProfile = () => {
  if (props.profile) {
    form.value = {
      name: props.profile.name || '',
      daily_rate: props.profile.daily_rate || '',
      group_id: props.profile.group_id || ''
    }
  }
}

const validateName = async () => {
  if (!form.value.name || form.value.name.trim().length < 2) {
    errors.value.name = 'Name must be at least 2 characters long'
    return
  }

  // Skip validation if editing and name hasn't changed
  if (isEditing.value && form.value.name === props.profile?.name) {
    errors.value.name = null
    nameValidation.value.available = true
    return
  }

  nameValidation.value.checking = true
  errors.value.name = null

  try {
    const response = await $fetch('/api/labor/profiles', {
      query: { 
        firmId: props.firmId, 
        search: form.value.name.trim() 
      }
    })

    const existingProfile = response.data?.find(p => 
      p.name.toLowerCase() === form.value.name.trim().toLowerCase() &&
      (!isEditing.value || p.id !== props.profile?.id)
    )

    if (existingProfile) {
      errors.value.name = 'A labor profile with this name already exists'
      nameValidation.value.available = false
    } else {
      nameValidation.value.available = true
    }
  } catch (error) {
    console.error('Error validating name:', error)
  } finally {
    nameValidation.value.checking = false
  }
}

const saveProfile = async () => {
  if (!isFormValid.value) return

  saving.value = true

  try {
    const profileData = {
      ...form.value,
      firm_id: props.firmId,
      user_id: props.userId
    }

    let response
    if (isEditing.value) {
      response = await $fetch(`/api/labor/profiles/${props.profile.id}`, {
        method: 'PUT',
        body: profileData
      })
    } else {
      response = await $fetch('/api/labor/profiles', {
        method: 'POST',
        body: profileData
      })
    }

    if (response.success) {
      emit('saved', response.data)
      closeModal()
    }
  } catch (error) {
    console.error('Error saving profile:', error)
    // Handle specific error cases
    if (error.data?.statusMessage?.includes('already exists')) {
      errors.value.name = 'A labor profile with this name already exists'
    }
  } finally {
    saving.value = false
  }
}

const onGroupCreated = (group) => {
  showGroupModal.value = false
  form.value.group_id = group.id
  // Emit event to parent to refresh groups list
  emit('group-created', group)
}

// Watch for modal open to load profile data
watch(() => props.isOpen, (isOpen) => {
  if (isOpen) {
    nextTick(() => {
      loadProfile()
    })
  }
})

// Watch for profile changes
watch(() => props.profile, () => {
  if (props.isOpen) {
    loadProfile()
  }
})
</script>

<style scoped>
.modal-overlay {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto;
}

.modal-header {
  @apply p-6 border-b border-gray-200 flex justify-between items-center;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply p-6 border-t border-gray-200 flex justify-end;
}

.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>