<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-3xl sm:w-full">
        <!-- Gradient Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-3 rounded-t-lg shadow-md">
          <h3 class="text-lg leading-6 font-medium text-white">Order & Dispatch Details</h3>
        </div>
        
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">

              <form @submit.prevent="submitForm">
                <!-- Order Information Section -->
                <div v-if="featureConfig?.showOrderInfo" class="mb-4">
                  <h4 class="text-md font-medium text-gray-700 mb-2 border-b pb-1">Order Information</h4>
                  <div class="grid grid-cols-2 gap-4">
                    <!-- Order Number -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Order Number</label>
                      <input v-model="form.orderNo" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Order Date -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Order Date</label>
                      <input v-model="form.orderDate" type="date"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>
                  </div>
                </div>

                <!-- Dispatch Information Section -->
                <div v-if="featureConfig?.showDispatchInfo || featureConfig?.showVehicleNumber" class="mb-4">
                  <h4 class="text-md font-medium text-gray-700 mb-2 border-b pb-1">Dispatch Information</h4>
                  <div class="grid grid-cols-2 gap-4">
                    <!-- Dispatch Through -->
                    <div v-if="featureConfig?.showDispatchInfo">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Dispatch Through</label>
                      <input v-model="form.dispatchThrough" type="text" list="dispatchMethodList"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <datalist id="dispatchMethodList">
                        <option value="By Road"></option>
                        <option value="By Air"></option>
                        <option value="By Sea"></option>
                        <option value="By Rail"></option>
                        <option value="By Courier"></option>
                      </datalist>
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Docket Number -->
                    <div v-if="featureConfig?.showDispatchInfo">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Docket Number</label>
                      <input v-model="form.docketNo" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Vehicle Number -->
                    <div v-if="featureConfig?.showVehicleNumber" :class="{ 'col-span-2': !featureConfig?.showDispatchInfo }">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Vehicle Number</label>
                      <input v-model="form.vehicleNo" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter vehicle registration number" />
                      <small class="text-gray-500">Optional</small>
                    </div>
                  </div>
                </div>

                <!-- Consignee Details Section -->
                <div v-if="featureConfig?.showConsigneeDetails">
                  <h4 class="text-md font-medium text-gray-700 mb-2 border-b pb-1">Consignee Details</h4>
                  <div class="grid grid-cols-2 gap-4">
                    <!-- Consignee Name -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Consignee Name</label>
                      <input v-model="form.consigneeName" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Consignee GSTIN -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Consignee GSTIN</label>
                      <input v-model="form.consigneeGstin" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Consignee Address -->
                    <div class="col-span-2">
                      <label class="block text-sm font-medium text-gray-700 mb-1">Consignee Address</label>
                      <textarea v-model="form.consigneeAddress" rows="2"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Consignee State -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Consignee State</label>
                      <input v-model="form.consigneeState" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>

                    <!-- Consignee PIN -->
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Consignee PIN</label>
                      <input v-model="form.consigneePin" type="text"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" />
                      <small class="text-gray-500">Optional</small>
                    </div>
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                  <button type="submit"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm">
                    Save Details
                  </button>
                  <button type="button" @click="close"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
        
        <!-- Gradient Footer -->
        <div class="bg-gradient-to-r from-purple-600 to-blue-500 px-4 py-3 rounded-b-lg shadow-md"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  orderDetails: {
    type: Object,
    default: () => ({
      orderNo: '',
      orderDate: '',
      dispatchThrough: '',
      docketNo: '',
      vehicleNo: '',
      consigneeName: '',
      consigneeGstin: '',
      consigneeAddress: '',
      consigneeState: '',
      consigneePin: ''
    })
  },
  featureConfig: {
    type: Object,
    default: () => ({
      enabled: true,
      showOrderInfo: true,
      showDispatchInfo: true,
      showVehicleNumber: true,
      showConsigneeDetails: true
    })
  }
});

const emit = defineEmits(['update:show', 'submit-order-details']);

const form = ref({
  orderNo: '',
  orderDate: '',
  dispatchThrough: '',
  docketNo: '',
  vehicleNo: '',
  consigneeName: '',
  consigneeGstin: '',
  consigneeAddress: '',
  consigneeState: '',
  consigneePin: ''
});

// Watch for changes to orderDetails and update form accordingly
watch(() => props.orderDetails, (newVal) => {
  if (newVal) {
    // Populate form with orderDetails data
    form.value = { ...newVal };
  }
}, { immediate: true });

const close = () => {
  emit('update:show', false);
};

const submitForm = () => {
  emit('submit-order-details', { ...form.value });
  close();
};
</script>