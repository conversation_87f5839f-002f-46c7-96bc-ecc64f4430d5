#!/usr/bin/env node

/**
 * Simple migration script to add multi-GST support to existing data
 */

import mongoose from 'mongoose';

// MongoDB connection
const MONGODB_URI = 'mongodb+srv://anjan:<EMAIL>/nuxt?retryWrites=true&w=majority&appName=ANJAN';

async function connectToDatabase() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function migrateFirms() {
  console.log('🔄 Migrating Firm collection...');
  
  const result = await mongoose.connection.db.collection('firms').updateMany(
    { 
      $or: [
        { additionalGSTs: { $exists: false } },
        { hasMultipleGSTs: { $exists: false } }
      ]
    },
    { 
      $set: { 
        additionalGSTs: [],
        hasMultipleGSTs: false
      } 
    }
  );
  
  console.log(`✅ Updated ${result.modifiedCount} firm records`);
  return result.modifiedCount;
}

async function migrateParties() {
  console.log('🔄 Migrating Party collection...');
  
  const result = await mongoose.connection.db.collection('parties').updateMany(
    { 
      $or: [
        { additionalGSTs: { $exists: false } },
        { hasMultipleGSTs: { $exists: false } }
      ]
    },
    { 
      $set: { 
        additionalGSTs: [],
        hasMultipleGSTs: false
      } 
    }
  );
  
  console.log(`✅ Updated ${result.modifiedCount} party records`);
  return result.modifiedCount;
}

async function migrateBills() {
  console.log('🔄 Migrating Bills collection...');
  
  const result = await mongoose.connection.db.collection('bills').updateMany(
    { gstSelection: { $exists: false } },
    { 
      $set: { 
        gstSelection: null
      } 
    }
  );
  
  console.log(`✅ Updated ${result.modifiedCount} bill records`);
  return result.modifiedCount;
}

async function validateMigration() {
  console.log('🔍 Validating migration...');
  
  // Check firms
  const firmsWithoutFields = await mongoose.connection.db.collection('firms').countDocuments({
    $or: [
      { additionalGSTs: { $exists: false } },
      { hasMultipleGSTs: { $exists: false } }
    ]
  });
  
  // Check parties
  const partiesWithoutFields = await mongoose.connection.db.collection('parties').countDocuments({
    $or: [
      { additionalGSTs: { $exists: false } },
      { hasMultipleGSTs: { $exists: false } }
    ]
  });
  
  // Check bills
  const billsWithoutFields = await mongoose.connection.db.collection('bills').countDocuments({
    gstSelection: { $exists: false }
  });
  
  if (firmsWithoutFields === 0 && partiesWithoutFields === 0 && billsWithoutFields === 0) {
    console.log('✅ Migration validation passed');
    return true;
  } else {
    console.log(`❌ Migration validation failed:`);
    console.log(`  - Firms missing fields: ${firmsWithoutFields}`);
    console.log(`  - Parties missing fields: ${partiesWithoutFields}`);
    console.log(`  - Bills missing fields: ${billsWithoutFields}`);
    return false;
  }
}

async function runMigration() {
  console.log('🚀 Starting Multi-GST Migration');
  console.log('================================');
  
  try {
    await connectToDatabase();
    
    // Run migrations
    const firmCount = await migrateFirms();
    const partyCount = await migrateParties();
    const billCount = await migrateBills();
    
    // Validate migration
    const isValid = await validateMigration();
    
    console.log('\n📊 Migration Summary:');
    console.log('====================');
    console.log(`Firms updated: ${firmCount}`);
    console.log(`Parties updated: ${partyCount}`);
    console.log(`Bills updated: ${billCount}`);
    console.log(`Validation: ${isValid ? 'PASSED' : 'FAILED'}`);
    
    if (isValid) {
      console.log('\n✅ Multi-GST Migration completed successfully!');
      process.exit(0);
    } else {
      console.log('\n❌ Multi-GST Migration failed validation!');
      process.exit(1);
    }
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    process.exit(1);
  } finally {
    await mongoose.connection.close();
  }
}

// Run migration
runMigration();
