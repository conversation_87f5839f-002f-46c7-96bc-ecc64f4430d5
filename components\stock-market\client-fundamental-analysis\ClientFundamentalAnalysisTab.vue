<template>
  <div class="bg-gradient-to-r from-orange-50 to-amber-50 border border-orange-200 rounded-lg p-6">
    <div class="flex items-center justify-between mb-4">
      <h4 class="text-lg font-semibold text-gray-900">💰 AI Fundamental Analysis-CLIENT</h4>
      <div class="flex items-center space-x-3">
        <!-- Analysis Button -->
        <button
          v-if="!clientFundamentalLoading && !clientFundamentalAnalysis"
          @click="startFundamentalAnalysis"
          :disabled="!isConfigured"
          class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 transition-colors"
        >
          🚀 Start Analysis
        </button>

        <!-- Refresh Button -->
        <button
          v-if="clientFundamentalAnalysis && !clientFundamentalLoading"
          @click="startFundamentalAnalysis"
          class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors"
        >
          🔄 Refresh Analysis
        </button>
      </div>
    </div>

    <!-- AI Configuration Warning -->
    <div v-if="!isConfigured" class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
      <div class="flex items-center space-x-2">
        <span class="text-yellow-600 text-lg">⚠️</span>
        <div>
          <h5 class="font-medium text-yellow-800">AI Configuration Required</h5>
          <p class="text-yellow-700 text-sm">Please configure your AI settings in Global Settings to use fundamental analysis.</p>
        </div>
      </div>
    </div>

    <!-- Fundamental Analysis Loading State -->
    <div v-if="clientFundamentalLoading" class="mb-4">
      <div class="bg-white rounded-lg p-4">
        <div class="flex items-center space-x-3 mb-3">
          <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-orange-500"></div>
          <span class="text-gray-700">{{ clientFundamentalStatusMessage || 'Analyzing financial statements and fundamentals...' }}</span>
        </div>
        <div class="bg-gray-200 rounded-full h-2">
          <div
            class="bg-orange-500 h-2 rounded-full transition-all duration-500"
            :style="{ width: clientFundamentalProgress + '%' }"
          ></div>
        </div>
        <div class="flex justify-between items-center mt-2 text-xs text-gray-500">
          <span>Progress: {{ clientFundamentalProgress }}%</span>
          <span v-if="currentOperation">{{ currentOperation }}</span>
        </div>
      </div>
    </div>

    <!-- Fundamental Analysis Error State -->
    <div v-else-if="clientFundamentalError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
      <div class="flex items-center space-x-2">
        <span class="text-red-600 text-lg">❌</span>
        <div>
          <h5 class="font-medium text-red-800">Analysis Failed</h5>
          <p class="text-red-700 text-sm">{{ clientFundamentalError }}</p>
          <button
            @click="startFundamentalAnalysis"
            class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700 transition-colors"
          >
            🔄 Retry Analysis
          </button>
        </div>
      </div>
    </div>

    <!-- Fundamental Analysis Results -->
    <div v-else-if="clientFundamentalAnalysis" class="space-y-6">
      <!-- Financial Metrics Overview -->
      <div class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-3">📊 Key Financial Metrics</h5>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div v-if="clientFundamentalAnalysis.metrics?.pe">
            <span class="text-gray-600">P/E Ratio:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.pe }}</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.pb">
            <span class="text-gray-600">P/B Ratio:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.pb }}</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.roe">
            <span class="text-gray-600">ROE:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.roe }}%</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.debtToEquity">
            <span class="text-gray-600">Debt/Equity:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.debtToEquity }}</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.dividendYield">
            <span class="text-gray-600">Dividend Yield:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.dividendYield }}%</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.marketCap">
            <span class="text-gray-600">Market Cap:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.marketCap }}</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.eps">
            <span class="text-gray-600">EPS:</span>
            <span class="font-medium ml-1">₹{{ clientFundamentalAnalysis.metrics.eps }}</span>
          </div>
          <div v-if="clientFundamentalAnalysis.metrics?.revenue">
            <span class="text-gray-600">Revenue:</span>
            <span class="font-medium ml-1">{{ clientFundamentalAnalysis.metrics.revenue }}</span>
          </div>
        </div>
      </div>

      <!-- Financial Health -->
      <div class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-3">🏥 Financial Health</h5>
        <div v-if="clientFundamentalAnalysis.financialHealth" class="prose prose-sm max-w-none">
          <div v-html="clientFundamentalAnalysis.financialHealth"></div>
        </div>
        <div v-else class="text-gray-500 text-sm">Financial health analysis not available</div>
      </div>

      <!-- Profitability Analysis -->
      <div class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-3">💹 Profitability & Growth</h5>
        <div v-if="clientFundamentalAnalysis.profitability" class="prose prose-sm max-w-none">
          <div v-html="clientFundamentalAnalysis.profitability"></div>
        </div>
        <div v-else class="text-gray-500 text-sm">Profitability analysis not available</div>
      </div>

      <!-- Pros and Cons -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Pros -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
          <h5 class="font-semibold text-green-800 mb-3">✅ Strengths</h5>
          <div v-if="clientFundamentalAnalysis.pros" class="space-y-2">
            <div v-for="(pro, index) in clientFundamentalAnalysis.pros" :key="index" class="text-sm text-green-700">
              • {{ pro }}
            </div>
          </div>
          <div v-else class="text-green-600 text-sm">Strengths analysis not available</div>
        </div>

        <!-- Cons -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-4">
          <h5 class="font-semibold text-red-800 mb-3">⚠️ Weaknesses</h5>
          <div v-if="clientFundamentalAnalysis.cons" class="space-y-2">
            <div v-for="(con, index) in clientFundamentalAnalysis.cons" :key="index" class="text-sm text-red-700">
              • {{ con }}
            </div>
          </div>
          <div v-else class="text-red-600 text-sm">Weaknesses analysis not available</div>
        </div>
      </div>

      <!-- Investment Recommendation -->
      <div class="bg-white rounded-lg p-4">
        <h5 class="font-semibold text-gray-900 mb-3">🎯 Investment Recommendation</h5>
        <div v-if="clientFundamentalAnalysis.recommendation" class="flex items-center space-x-3 mb-3">
          <span class="px-3 py-1 rounded-full text-sm font-medium"
                :class="getFundamentalRecommendationClass(clientFundamentalAnalysis.recommendation)">
            {{ clientFundamentalAnalysis.recommendation }}
          </span>
          <span v-if="clientFundamentalAnalysis.confidence" class="text-gray-600 text-sm">
            Confidence: {{ clientFundamentalAnalysis.confidence }}
          </span>
        </div>
        <div v-if="clientFundamentalAnalysis.summary" class="prose prose-sm max-w-none">
          <div v-html="clientFundamentalAnalysis.summary"></div>
        </div>
        <div v-else class="text-gray-500 text-sm">Investment summary not available</div>
      </div>

      <!-- Analysis Timestamp -->
      <div class="text-xs text-gray-500 text-center">
        <span v-if="clientFundamentalAnalysis.analysisTimestamp">
          Analysis completed: {{ formatTimestamp(clientFundamentalAnalysis.analysisTimestamp) }}
        </span>
      </div>
    </div>

    <!-- Initial State -->
    <div v-else class="bg-white rounded-lg p-4 text-center">
      <p class="text-gray-600 mb-3">Click the button above to start comprehensive fundamental analysis for {{ stock.symbol }}</p>
      <p class="text-gray-500 text-sm">AI will analyze financial statements, ratios, profitability, debt levels, dividend history, and provide investment recommendations</p>
    </div>
  </div>

</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useClientFundamentalAnalysis } from '~/composables/stock-market/client-fundamental-analysis/useClientFundamentalAnalysis'

// Props
const props = defineProps<{
  stock: any
}>()

// Client-side fundamental analysis composable
const {
  loading: clientFundamentalLoading,
  error: clientFundamentalError,
  analysis: clientFundamentalAnalysis,
  progress: clientFundamentalProgress,
  statusMessage: clientFundamentalStatusMessage,
  currentOperation,
  isConfigured,
  performFundamentalAnalysis,
  reset: clientFundamentalReset
} = useClientFundamentalAnalysis()

// Start fundamental analysis
const startFundamentalAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings in Global Settings first.')
    return
  }

  try {
    await performFundamentalAnalysis({
      symbol: props.stock.symbol,
      companyName: props.stock.meta?.companyName || props.stock.symbol,
      currentPrice: props.stock.lastPrice,
      change: props.stock.change,
      pChange: props.stock.pChange,
      volume: props.stock.totalTradedVolume,
      dayHigh: props.stock.dayHigh,
      dayLow: props.stock.dayLow
    })
  } catch (error) {
    console.error('Fundamental analysis failed:', error)
  }
}

// Helper function for recommendation styling
const getFundamentalRecommendationClass = (recommendation: string) => {
  const rec = recommendation?.toUpperCase()
  if (rec === 'BUY' || rec === 'STRONG_BUY') {
    return 'bg-green-100 text-green-800'
  } else if (rec === 'SELL' || rec === 'STRONG_SELL') {
    return 'bg-red-100 text-red-800'
  } else if (rec === 'HOLD') {
    return 'bg-yellow-100 text-yellow-800'
  }
  return 'bg-gray-100 text-gray-800'
}

// Format timestamp
const formatTimestamp = (timestamp: string) => {
  return new Date(timestamp).toLocaleString()
}
</script>
