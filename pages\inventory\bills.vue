<template>
  <div class="max-w-10xl mx-auto py-4 sm:py-6 px-4 sm:px-6 lg:px-8">
    <!-- Bill Cancellation Modal -->
    <BillCancellationModal
      :show="showCancellationModal"
      :bill-data="selectedBillForCancellation"
      :bill-type="selectedBillForCancellation?.btype || ''"
      @close="closeCancelModal"
      @cancelled="handleBillCancelled"
    />

    <div class="sm:px-0 relative">
      <div class="bg-white p-4 rounded-lg shadow">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold">Bills Management</h2>
          <div class="flex space-x-2">
            <button @click="refreshData" class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <span class="flex items-center">
                <ArrowPathIcon v-if="!isLoading" class="h-4 w-4 mr-1" />
                <svg v-else class="animate-spin h-4 w-4 mr-1 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ isLoading ? 'Loading...' : 'Refresh' }}
              </span>
            </button>
            <button @click="exportToExcel" :disabled="isLoading" class="px-3 py-1 bg-green-500 text-white rounded hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed">
              <span class="flex items-center">
                <DocumentArrowDownIcon class="h-4 w-4 mr-1" />
                Export Excel
              </span>
            </button>
            <button @click="exportToPdf" :disabled="isLoading" class="px-3 py-1 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 ml-2 disabled:opacity-50 disabled:cursor-not-allowed">
              <span class="flex items-center">
                <DocumentArrowDownIcon class="h-4 w-4 mr-1" />
                Export PDF
              </span>
            </button>
          </div>
        </div>

        <!-- Search and Filter Controls -->
        <div class="mb-4 grid grid-cols-1 md:grid-cols-7 gap-4">
          <div class="relative">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Search in all columns..."
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              @keydown="handleKeyboardNavigation"
              ref="searchInput"
            />
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
            </div>
          </div>

          <div>
            <select
              v-model="filterType"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
            >
              <option value="">All Bill Types</option>
              <option value="SALES">SALES</option>
              <option value="PURCHASE">PURCHASE</option>
              <option value="DEBIT NOTE">DEBIT NOTE</option>
              <option value="CREDIT NOTE">CREDIT NOTE</option>
            </select>
          </div>

          <div>
            <select
              v-model="selectedFinancialYear"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              :disabled="dateRangeActive"
            >
              <option v-for="year in financialYears" :key="year.value" :value="year.value">{{ year.label }}</option>
            </select>
          </div>

          <div>
            <select
              v-model="selectedMonth"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              :disabled="dateRangeActive"
            >
              <option value="">All Months</option>
              <option v-for="month in months" :key="month.value" :value="month.value">{{ month.label }}</option>
            </select>
          </div>

          <div>
            <input
              v-model="dateFrom"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              placeholder="From Date"
              @change="handleDateRangeChange"
            />
          </div>

          <div>
            <input
              v-model="dateTo"
              type="date"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 text-sm"
              placeholder="To Date"
              @change="handleDateRangeChange"
            />
          </div>

          <div>
            <button
              @click="resetFilters"
              class="w-full px-3 py-2 bg-yellow-500 text-white rounded-md shadow-sm hover:bg-yellow-600 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 flex items-center justify-center"
            >
              <ArrowPathIcon class="h-4 w-4 mr-1" />
              Reset Filters
            </button>
          </div>
        </div>

        <!-- Bills Table with jqGrid-like styling -->
        <div class="overflow-x-auto relative">
          <!-- Loading Overlay -->
          <div v-if="isLoading" class="absolute inset-0 bg-white bg-opacity-70 flex items-center justify-center z-10">
            <div class="text-center">
              <svg class="animate-spin h-10 w-10 mx-auto text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <p class="mt-2 text-sm font-medium text-gray-700">Loading bills data...</p>
            </div>
          </div>

          <!-- Month Groups Table -->
          <table v-if="!dateRangeActive && groupedByMonth.length > 0" class="min-w-full divide-y divide-gray-200 ui-jqgrid-btable mb-4">
            <thead class="bg-gray-50 ui-jqgrid-htable">
              <tr>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Month</th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Total Bills</th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Net Total Amount</th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="(monthGroup, index) in groupedByMonth"
                :key="monthGroup.month"
                :class="{
                  'bg-blue-50': selectedMonthIndex === index,
                  'hover:bg-gray-50': selectedMonthIndex !== index
                }"
                @click="selectMonth(index)"
                @keydown.enter="selectMonth(index)"
                tabindex="0"
              >
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 font-medium">{{ monthGroup.monthName }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ monthGroup.bills.length }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900">{{ formatCurrency(monthGroup.totalAmount) }}</td>
                <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                  <button
                    @click.stop="toggleExpandMonth(monthGroup.month)"
                    class="text-blue-600 hover:text-blue-900 focus:outline-none px-2 py-1 rounded bg-blue-50 hover:bg-blue-100 flex items-center"
                  >
                    <ChevronUpIcon v-if="isMonthExpanded(monthGroup.month)" class="h-4 w-4 mr-1" />
                    <ChevronDownIcon v-else class="h-4 w-4 mr-1" />
                    {{ isMonthExpanded(monthGroup.month) ? 'Hide Details' : 'Show Details' }}
                  </button>
                </td>
              </tr>
              <tr v-if="groupedByMonth.length === 0">
                <td colspan="4" class="px-3 py-4 text-sm text-gray-500 text-center">
                  No bills found matching your criteria
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Bills Detail Table -->
          <table
            v-if="(dateRangeActive || expandedMonths.length > 0) && paginatedBills.length > 0"
            class="min-w-full divide-y divide-gray-200 ui-jqgrid-btable">
            <thead class="bg-gray-50 ui-jqgrid-htable">
              <tr>
                <th
                  v-for="column in columns"
                  :key="column.field"
                  @click="sortBy(column.field)"
                  class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer ui-jqgrid-th"
                >
                  <div class="flex items-center">
                    {{ column.label }}
                    <span v-if="sortColumn === column.field" class="ml-1">
                      {{ sortDirection === 'asc' ? '▲' : '▼' }}
                    </span>
                  </div>
                </th>
                <th class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Actions</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200 ui-jqgrid-btable">
              <template v-if="paginatedBills.length > 0">
                <template v-for="(bill, index) in paginatedBills" :key="bill._id">
                  <!-- Bill Row -->
                  <tr
                    :class="{
                      'hover:bg-gray-50': true,
                      'bg-green-200': selectedRowIndex === index,
                      'bg-red-100': bill.status === 'CANCELLED'
                    }"
                    @click="selectRow(index)"
                  >
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      {{ bill.bno }}
                      <span v-if="bill.status === 'CANCELLED'" class="ml-1 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-200 text-red-800 border border-red-400">
                        CANCELLED
                      </span>
                    </td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatDate(bill.bdate) }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ bill.btype }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ bill.supply }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ bill.gstin }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(bill.gtot) }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(bill.ntot) }}</td>
                    <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                      <div class="flex space-x-2">
                        <button
                          @click.stop="toggleExpandRow(bill._id)"
                          class="text-blue-600 hover:text-blue-900 focus:outline-none flex items-center"
                        >
                          <EyeSlashIcon v-if="isRowExpanded(bill._id)" class="h-4 w-4 mr-1" />
                          <EyeIcon v-else class="h-4 w-4 mr-1" />
                          {{ isRowExpanded(bill._id) ? 'Hide' : 'Show' }} Details
                        </button>
                        <NuxtLink
                          v-if="bill.status !== 'CANCELLED'"
                          :to="{ path: '/inventory/edit-bill', query: { id: bill._id } }"
                          class="text-indigo-600 hover:text-indigo-900 focus:outline-none flex items-center"
                        >
                          <PencilSquareIcon class="h-4 w-4 mr-1" />
                          Edit
                        </NuxtLink>
                        <button
                          v-if="bill.status !== 'CANCELLED'"
                          @click.stop="openCancelModal(bill)"
                          class="text-orange-600 hover:text-orange-900 focus:outline-none flex items-center"
                        >
                          <XCircleIcon class="h-4 w-4 mr-1" />
                          Cancel
                        </button>
                        <button
                          @click.stop="deleteBill(bill._id)"
                          class="text-red-600 hover:text-red-900 focus:outline-none flex items-center"
                        >
                          <TrashIcon class="h-4 w-4 mr-1" />
                          Delete
                        </button>
                        <button
                          v-if="bill.status !== 'CANCELLED'"
                          @click.stop="duplicateBill(bill._id)"
                          :disabled="isLoading"
                          class="text-purple-600 hover:text-purple-900 focus:outline-none flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <DocumentDuplicateIcon class="h-4 w-4 mr-1" />
                          {{ isDuplicating === bill._id ? 'Duplicating...' : 'Duplicate' }}
                        </button>
                      </div>
                    </td>
                  </tr>

                  <!-- Expanded Stock Items Row -->
                  <tr v-if="isRowExpanded(bill._id)">
                    <td colspan="8" class="px-3 py-2">
                      <div class="bg-gray-50 p-3 rounded-md">
                        <!-- Cancellation Info (if bill is cancelled) -->
                        <div v-if="bill.status === 'CANCELLED'" class="mb-4 bg-red-50 p-3 border border-red-200 rounded-md">
                          <h4 class="text-sm font-medium text-red-700 mb-2">Cancellation Details</h4>
                          <div class="grid grid-cols-2 gap-2 text-sm">
                            <div>
                              <span class="text-red-600">Reason:</span>
                              <span class="font-medium ml-1">{{ bill.cancellationReason || 'Not specified' }}</span>
                            </div>
                            <div>
                              <span class="text-red-600">Cancelled On:</span>
                              <span class="font-medium ml-1">{{ bill.cancelledAt ? formatDate(bill.cancelledAt) : 'Unknown' }}</span>
                            </div>
                          </div>
                        </div>

                        <h4 class="text-sm font-medium text-gray-700 mb-2">Stock Items</h4>
                        <div class="overflow-x-auto">
                          <table class="min-w-full divide-y divide-gray-200 ui-jqgrid-btable">
                            <thead class="bg-gray-100 ui-jqgrid-htable">
                              <tr>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Item</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">HSN</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Batch</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Qty</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">UOM</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Rate</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">GST Rate</th>
                                <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ui-jqgrid-th">Total</th>
                              </tr>
                            </thead>
                            <tbody class="bg-white divide-y divide-gray-200">
                              <tr v-if="!bill.stockItems || bill.stockItems.length === 0" class="hover:bg-gray-50">
                                <td colspan="8" class="px-2 py-2 text-center text-sm text-gray-500">No stock items found</td>
                              </tr>
                              <tr v-for="(item, itemIndex) in bill.stockItems" :key="itemIndex" class="hover:bg-gray-50">
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ item.item }}</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ item.hsn }}</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ item.batch || 'N/A' }}</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ item.qty }}</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ item.uom }}</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(item.rate) }}</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ item.grate }}%</td>
                                <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatCurrency(item.total) }}</td>
                              </tr>
                            </tbody>
                          </table>
                        </div>
                      </div>
                    </td>
                  </tr>
                </template>
              </template>
              <tr v-else>
                <td colspan="8" class="px-3 py-4 text-center text-sm text-gray-500">No bills found</td>
              </tr>
            </tbody>
          </table>

          <!-- No Results Message -->
          <div v-if="!dateRangeActive && groupedByMonth.length === 0 && filteredBills.length === 0" class="text-center py-4 text-gray-500">
            No bills found matching your criteria
          </div>

          <!-- No Bills in Selected Month Message -->
          <div v-if="!dateRangeActive && expandedMonths.length > 0 && paginatedBills.length === 0" class="text-center py-4 text-gray-500">
            No bills found in the selected month
          </div>
        </div>

        <!-- Pagination Controls -->
        <div v-if="paginatedBills.length > 0" class="flex justify-between items-center mt-4">
          <div class="text-sm text-gray-700">
            Showing {{ paginatedBills.length > 0 ? (currentPage - 1) * pageSize + 1 : 0 }}
            to {{ Math.min(currentPage * pageSize, getBillsCount()) }}
            of {{ getBillsCount() }} bills
            {{ expandedMonths.length > 0 && !dateRangeActive ? ' in selected month' : '' }}
          </div>
          <div class="flex space-x-2">
            <button
              @click="currentPage--"
              :disabled="currentPage === 1"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            <button
              @click="currentPage++"
              :disabled="currentPage >= totalPages"
              class="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Next
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import { useRouter } from 'vue-router';
import {
  DocumentArrowDownIcon,
  MagnifyingGlassIcon,
  ArrowPathIcon,
  EyeIcon,
  EyeSlashIcon,
  PencilSquareIcon,
  TrashIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  XCircleIcon,
  DocumentDuplicateIcon
} from '@heroicons/vue/24/outline';
import ExcelJS from 'exceljs';
import useToast from '~/composables/ui/useToast';
import BillCancellationModal from '~/components/inventory/BillCancellationModal.vue';

// Initialize composables
const router = useRouter();
const { success, info, error: showError } = useToast();
const api = useApiWithAuth();

// Set page title
usePageTitle('Bills Management', 'View and manage all bills');

const exportToExcel = async () => {
  if (isLoading.value) return; // Prevent export if already loading

  isLoading.value = true;
  try {
    // Build query parameters based on current filters
    const params = new URLSearchParams();
    if (filterType.value) params.append('filterType', filterType.value);

    // If date range is active, use dateFrom and dateTo
    if (dateRangeActive.value) {
      if (dateFrom.value) params.append('dateFrom', dateFrom.value);
      if (dateTo.value) params.append('dateTo', dateTo.value);
    }
    // Otherwise use financial year and month
    else {
      if (selectedFinancialYear.value) {
        const financialYear = financialYears.find(fy => fy.value === selectedFinancialYear.value);
        if (financialYear) {
          params.append('dateFrom', financialYear.startDate);
          params.append('dateTo', financialYear.endDate);
        }

        // If month is selected, override the dates
        if (selectedMonth.value) {
          const month = selectedMonth.value;
          const year = parseInt(selectedFinancialYear.value.split('-')[0]);
          // For months April-December (04-12), use the first year of the financial year
          // For months January-March (01-03), use the second year of the financial year
          const monthYear = month >= '04' && month <= '12' ? year : year + 1;

          const daysInMonth = new Date(monthYear, parseInt(month), 0).getDate();
          params.append('dateFrom', `${monthYear}-${month}-01`);
          params.append('dateTo', `${monthYear}-${month}-${daysInMonth}`);
        }
      }
    }

    if (searchQuery.value) params.append('searchQuery', searchQuery.value);

    // Fetch Excel with current filters
    const response = await api.fetchWithAuth(`/api/inventory/bills-export?${params.toString()}`, {
      responseType: 'blob'
    });

    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `bills_export_${new Date().toISOString().slice(0, 10)}.xlsx`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    success('Excel export completed successfully');
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    showError('Failed to export Excel file: ' + (error.message || 'Unknown error'));
  } finally {
    isLoading.value = false;
  }
};

const exportToPdf = async () => {
  if (isLoading.value) return; // Prevent export if already loading

  isLoading.value = true;
  try {
    // Build query parameters based on current filters
    const params = new URLSearchParams();
    if (filterType.value) params.append('filterType', filterType.value);

    // If date range is active, use dateFrom and dateTo
    if (dateRangeActive.value) {
      if (dateFrom.value) params.append('dateFrom', dateFrom.value);
      if (dateTo.value) params.append('dateTo', dateTo.value);
    }
    // Otherwise use financial year and month
    else {
      if (selectedFinancialYear.value) {
        const financialYear = financialYears.find(fy => fy.value === selectedFinancialYear.value);
        if (financialYear) {
          params.append('dateFrom', financialYear.startDate);
          params.append('dateTo', financialYear.endDate);
        }

        // If month is selected, override the dates
        if (selectedMonth.value) {
          const month = selectedMonth.value;
          const year = parseInt(selectedFinancialYear.value.split('-')[0]);
          // For months April-December (04-12), use the first year of the financial year
          // For months January-March (01-03), use the second year of the financial year
          const monthYear = month >= '04' && month <= '12' ? year : year + 1;

          const daysInMonth = new Date(monthYear, parseInt(month), 0).getDate();
          params.append('dateFrom', `${monthYear}-${month}-01`);
          params.append('dateTo', `${monthYear}-${month}-${daysInMonth}`);
        }
      }
    }

    if (searchQuery.value) params.append('searchQuery', searchQuery.value);

    // Fetch PDF with current filters
    const response = await api.fetchWithAuth(`/api/inventory/bills-pdf?${params.toString()}`, {
      responseType: 'blob'
    });

    const blob = new Blob([response], {
      type: 'application/pdf'
    });

    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `bills_export_${new Date().toISOString().slice(0, 10)}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
    success('PDF export completed successfully');
  } catch (error) {
    console.error('Error exporting to PDF:', error);
    showError('Failed to export PDF file: ' + (error.message || 'Unknown error'));
  } finally {
    isLoading.value = false;
  }
};

// Page title is already set above

// Data fetching
const inventoryData = ref(null);
const error = ref(null);
const isLoading = ref(false);

async function fetchData() {
  isLoading.value = true;
  let success = false;
  let retryCount = 0;
  const maxRetries = 3; // Reduced to 3 retries
  const retryDelay = 2000; // 2 seconds between retries

  do {
    try {
      // Simple fetch without modifying auth-related code
      const response = await api.fetchWithAuth('/api/inventory/bills');

      // Validate response structure
      if (!response || !response.bills) {
        console.error('Invalid response format:', response);
        throw new Error('Invalid response format from server');
      }

      inventoryData.value = response;
      success = true;

      if (retryCount > 0) {
        // Show success message if we had to retry
        success(`Data loaded successfully after ${retryCount} ${retryCount === 1 ? 'retry' : 'retries'}`);
      }
    } catch (err) {
      retryCount++;
      error.value = err;
      console.error(`Error fetching bills (attempt ${retryCount}):`, err);

      if (retryCount < maxRetries) {
        // Show info message about retry
        info(`Retrying... Attempt ${retryCount} of ${maxRetries}`);

        // Wait before retrying
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      } else {
        // Show error message when max retries reached
        showError(`Failed to load bills data. Please try again later.`);

        // Initialize with empty data to prevent UI errors
        if (!inventoryData.value) {
          inventoryData.value = { bills: [] };
        }

        break;
      }
    }
  } while (!success && retryCount < maxRetries);

  // Set loading state to false regardless of success or failure
  isLoading.value = false;
}

const refreshData = fetchData;


// Generate financial years (April to March)
const generateFinancialYears = () => {
  const currentYear = new Date().getFullYear();
  const years = [];

  // Generate last 5 financial years including current
  for (let i = 0; i < 5; i++) {
    const startYear = currentYear - i;
    const endYear = startYear + 1;
    years.push({
      label: `${startYear}-${endYear}`,
      value: `${startYear}-${endYear}`,
      startDate: `${startYear}-04-01`,
      endDate: `${endYear}-03-31`
    });
  }

  return years;
};

// Months for filtering
const months = [
  { label: 'April', value: '04' },
  { label: 'May', value: '05' },
  { label: 'June', value: '06' },
  { label: 'July', value: '07' },
  { label: 'August', value: '08' },
  { label: 'September', value: '09' },
  { label: 'October', value: '10' },
  { label: 'November', value: '11' },
  { label: 'December', value: '12' },
  { label: 'January', value: '01' },
  { label: 'February', value: '02' },
  { label: 'March', value: '03' }
];

// Get financial years
const financialYears = generateFinancialYears();

// Table state
const searchQuery = ref('');
const filterType = ref('SALES'); // Default to SALES

// Reset all filters to default values
const resetFilters = async () => {
  if (isLoading.value) return; // Prevent reset if already loading

  searchQuery.value = '';
  filterType.value = 'SALES';
  selectedFinancialYear.value = financialYears[0].value; // Current financial year
  selectedMonth.value = '';
  dateFrom.value = '';
  dateTo.value = '';
  dateRangeActive.value = false;
  expandedMonths.value = [];

  // Show loading state while refreshing data
  isLoading.value = true;
  try {
    await refreshData();
    success('Filters have been reset');
  } catch (err) {
    showError('Failed to reset filters: ' + (err.message || 'Unknown error'));
  }
};
const dateFrom = ref('');
const dateTo = ref('');
const selectedFinancialYear = ref(financialYears[0].value); // Default to current financial year
const selectedMonth = ref('');
const dateRangeActive = ref(false);
const expandedMonths = ref([]);
const selectedMonthIndex = ref(-1);
const sortColumn = ref('bdate');
const sortDirection = ref('desc');
const currentPage = ref(1);
const pageSize = ref(10);
const expandedRows = ref([]);
const selectedRowIndex = ref(null);
const searchInput = ref(null);

// Table columns definition
const columns = [
  { field: 'bno', label: 'Bill No' },
  { field: 'bdate', label: 'Date' },
  { field: 'btype', label: 'Type' },
  { field: 'supply', label: 'Party' },
  { field: 'gstin', label: 'GSTIN' },
  { field: 'gtot', label: 'Gross Total' },
  { field: 'ntot', label: 'Net Total' },
];

// Computed properties for filtering and sorting
const filteredBills = computed(() => {
  if (!inventoryData.value || !inventoryData.value.bills) return [];

  let result = inventoryData.value.bills;

  // Filter by search query (search in all fields)
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase();
    result = result.filter(bill => {
      // Search in bill properties
      return Object.keys(bill).some(key => {
        const value = bill[key];
        if (typeof value === 'string') {
          return value.toLowerCase().includes(query);
        } else if (typeof value === 'number') {
          return value.toString().includes(query);
        }
        return false;
      }) ||
      // Search in stock items if they exist
      (bill.stockItems && bill.stockItems.some(item => {
        return Object.keys(item).some(key => {
          const value = item[key];
          if (typeof value === 'string') {
            return value.toLowerCase().includes(query);
          } else if (typeof value === 'number') {
            return value.toString().includes(query);
          }
          return false;
        });
      }));
    });
  }

  // Filter by bill type
  if (filterType.value) {
    result = result.filter(bill => bill.btype === filterType.value);
  }

  // Filter by date range if active (bypasses financial year and month filters)
  if (dateRangeActive.value) {
    if (dateFrom.value) {
      const fromDate = new Date(dateFrom.value);
      fromDate.setHours(0, 0, 0, 0);
      result = result.filter(bill => new Date(bill.bdate) >= fromDate);
    }

    if (dateTo.value) {
      const toDate = new Date(dateTo.value);
      toDate.setHours(23, 59, 59, 999);
      result = result.filter(bill => new Date(bill.bdate) <= toDate);
    }
  }
  // Filter by financial year and month if date range is not active
  else {
    if (selectedFinancialYear.value) {
      const financialYear = financialYears.find(fy => fy.value === selectedFinancialYear.value);
      if (financialYear) {
        const startDate = new Date(financialYear.startDate);
        const endDate = new Date(financialYear.endDate);
        startDate.setHours(0, 0, 0, 0);
        endDate.setHours(23, 59, 59, 999);

        result = result.filter(bill => {
          const billDate = new Date(bill.bdate);
          return billDate >= startDate && billDate <= endDate;
        });

        // Further filter by month if selected
        if (selectedMonth.value) {
          result = result.filter(bill => {
            const billDate = new Date(bill.bdate);
            const billMonth = String(billDate.getMonth() + 1).padStart(2, '0');

            // For months April-December (04-12), compare directly
            if (selectedMonth.value >= '04' && selectedMonth.value <= '12') {
              return billMonth === selectedMonth.value;
            }
            // For months January-March (01-03), check if they're in the next calendar year
            else {
              const billYear = billDate.getFullYear();
              const financialYearStart = parseInt(selectedFinancialYear.value.split('-')[0]);
              return billMonth === selectedMonth.value && billYear === financialYearStart + 1;
            }
          });
        }
      }
    }
  }

  // Sort results
  result = [...result].sort((a, b) => {
    let aValue = a[sortColumn.value];
    let bValue = b[sortColumn.value];

    // Handle date sorting
    if (sortColumn.value === 'bdate') {
      aValue = new Date(aValue);
      bValue = new Date(bValue);
    }

    // Handle numeric sorting
    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection.value === 'asc' ? aValue - bValue : bValue - aValue;
    }

    // Handle string sorting
    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection.value === 'asc'
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Handle date sorting
    if (aValue instanceof Date && bValue instanceof Date) {
      return sortDirection.value === 'asc' ? aValue - bValue : bValue - aValue;
    }

    return 0;
  });

  return result;
});

// Paginated bills
const paginatedBills = computed(() => {
  // If date range is active, show all filtered bills
  // Otherwise, only show bills from expanded months
  let billsToShow = filteredBills.value;

  if (!dateRangeActive.value && expandedMonths.value.length > 0) {
    // Create a map of month groups for faster lookup
    const monthGroupsMap = {};
    groupedByMonth.value.forEach(group => {
      monthGroupsMap[group.month] = group.bills.map(bill => bill._id);
    });

    // Filter bills to only show those from expanded months
    billsToShow = filteredBills.value.filter(bill => {
      const billDate = new Date(bill.bdate);
      const month = String(billDate.getMonth() + 1).padStart(2, '0');
      const year = billDate.getFullYear();
      const monthKey = `${year}-${month}`;

      // Only include bills that belong to the expanded month
      return expandedMonths.value.includes(monthKey) &&
             (!monthGroupsMap[monthKey] || monthGroupsMap[monthKey].includes(bill._id));
    });
  }

  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return billsToShow.slice(start, end);
});

// Function to get the total count of bills based on current view
function getBillsCount() {
  if (!dateRangeActive.value && expandedMonths.length > 0) {
    // When a month is expanded, count only bills from that month
    return filteredBills.value.filter(bill => {
      const billDate = new Date(bill.bdate);
      const month = String(billDate.getMonth() + 1).padStart(2, '0');
      const year = billDate.getFullYear();
      const monthKey = `${year}-${month}`;
      return expandedMonths.value.includes(monthKey);
    }).length;
  } else {
    // Otherwise, count all filtered bills
    return filteredBills.value.length;
  }
}

// Total pages for pagination
const totalPages = computed(() => {
  const totalItems = getBillsCount();
  return Math.ceil(totalItems / pageSize.value) || 1;
});

// Group bills by month
const groupedByMonth = computed(() => {
  const result = [];
  const monthGroups = {};

  filteredBills.value.forEach(bill => {
    const billDate = new Date(bill.bdate);
    const month = String(billDate.getMonth() + 1).padStart(2, '0');
    const year = billDate.getFullYear();

    // Create a key that represents the month and year
    const monthKey = `${year}-${month}`;

    if (!monthGroups[monthKey]) {
      monthGroups[monthKey] = {
        month: monthKey,
        monthName: `${getMonthName(month)} ${year}`,
        bills: [],
        totalAmount: 0
      };
    }

    // Add bill to the appropriate month group
    monthGroups[monthKey].bills.push(bill);
    monthGroups[monthKey].totalAmount += parseFloat(bill.ntot || 0);
  });

  // Convert the object to an array and sort by month (most recent first)
  Object.values(monthGroups).forEach(group => {
    result.push(group);
  });

  return result.sort((a, b) => {
    // Parse the year and month from the month key (YYYY-MM)
    const [aYear, aMonth] = a.month.split('-').map(Number);
    const [bYear, bMonth] = b.month.split('-').map(Number);

    // Compare years first
    if (aYear !== bYear) {
      return bYear - aYear; // Most recent year first
    }

    // If years are the same, compare months
    return bMonth - aMonth; // Most recent month first
  });
});

// Methods
// Handle date range change
function handleDateRangeChange() {
  if (dateFrom.value || dateTo.value) {
    dateRangeActive.value = true;
  } else {
    dateRangeActive.value = false;
  }
}

// Toggle month expansion
function toggleExpandMonth(month) {
  // Reset pagination when toggling month expansion
  currentPage.value = 1;

  // Toggle the month in the expandedMonths array
  const index = expandedMonths.value.indexOf(month);
  if (index === -1) {
    // Close all other expanded months first to avoid showing mixed data
    expandedMonths.value = [month];
  } else {
    expandedMonths.value.splice(index, 1);
  }

  // Clear any expanded rows when toggling months
  expandedRows.value = [];
}

// Check if month is expanded
function isMonthExpanded(month) {
  return expandedMonths.value.includes(month);
}

// Select a month
function selectMonth(index) {
  // Update the selected month index
  selectedMonthIndex.value = index;

  // Get the month group at the selected index
  const monthGroup = groupedByMonth.value[index];
  if (monthGroup) {
    // Toggle the expansion of this month
    toggleExpandMonth(monthGroup.month);

    // Reset pagination to show the first page of bills for this month
    currentPage.value = 1;
  }
}

// Get month name from month number
function getMonthName(monthNum) {
  const monthObj = months.find(m => m.value === monthNum);
  return monthObj ? monthObj.label : 'Unknown';
}

function sortBy(column) {
  if (sortColumn.value === column) {
    // Toggle sort direction if clicking the same column
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // Set new sort column and default to ascending
    sortColumn.value = column;
    sortDirection.value = 'asc';
  }
}

// This function is kept for future use if needed
// function toggleDetails(index) {
//   const position = expandedRows.value.indexOf(index);
//   if (position !== -1) {
//     expandedRows.value.splice(position, 1);
//   } else {
//     expandedRows.value.push(index);
//   }
// }

function isRowExpanded(id) {
  return expandedRows.value.includes(id);
}

function toggleExpandRow(id) {
  const position = expandedRows.value.indexOf(id);
  if (position !== -1) {
    expandedRows.value.splice(position, 1);
  } else {
    expandedRows.value.push(id);
  }
}

function selectRow(index) {
  selectedRowIndex.value = index;
}

function formatDate(dateString) {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString();
}

function formatCurrency(value) {
  if (value === undefined || value === null) return 'N/A';
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2
  }).format(value);
}

function handleKeyboardNavigation(event) {
  // Handle keyboard navigation when search input is focused
  if (event.key === 'ArrowDown') {
    event.preventDefault();
    if (selectedRowIndex.value === null || selectedRowIndex.value >= paginatedBills.value.length - 1) {
      selectedRowIndex.value = 0;
    } else {
      selectedRowIndex.value++;
    }
  } else if (event.key === 'ArrowUp') {
    event.preventDefault();
    if (selectedRowIndex.value === null || selectedRowIndex.value <= 0) {
      selectedRowIndex.value = paginatedBills.value.length - 1;
    } else {
      selectedRowIndex.value--;
    }
  } else if (event.key === 'Enter') {
    event.preventDefault();
    // If a row is selected, toggle its expanded state
    if (selectedRowIndex.value !== null && selectedRowIndex.value >= 0 && selectedRowIndex.value < paginatedBills.value.length) {
      const selectedBill = paginatedBills.value[selectedRowIndex.value];
      toggleExpandRow(selectedBill._id);
    }
    // If search is focused and no row is selected, select the first row and expand it
    else if (document.activeElement === searchInput.value && paginatedBills.value.length > 0) {
      selectedRowIndex.value = 0;
      const firstBill = paginatedBills.value[0];
      toggleExpandRow(firstBill._id);
    }
  }
}

// Reset pagination when filters change
watch([searchQuery, filterType, dateFrom, dateTo, selectedFinancialYear, selectedMonth], () => {
  currentPage.value = 1;
  selectedMonthIndex.value = -1;

  // Clear expanded months when changing filters
  if (!dateRangeActive.value) {
    expandedMonths.value = [];
  }
});

// This function is kept for future use if needed
// function editBill(billId) {
//   // Navigate to the edit page with the bill ID
//   window.location.href = `/inventory/edit-bill?id=${billId}`;
// }

// Delete bill function
async function deleteBill(billId) {
  if (isLoading.value) return; // Prevent deletion if already loading

  if (confirm('Are you sure you want to delete this bill? This action cannot be undone.')) {
    isLoading.value = true;
    try {
      await api.delete(`/api/inventory/bills/${billId}`);
      success('Bill deleted successfully');
      // Refresh data after deletion
      await fetchData();
    } catch (err) {
      showError('Failed to delete bill: ' + (err.message || 'Unknown error'));
      isLoading.value = false; // Reset loading state in case of error (fetchData will reset it on success)
    }
  }
}

// Bill cancellation modal state and functions
const showCancellationModal = ref(false);
const selectedBillForCancellation = ref(null);

function openCancelModal(bill) {
  selectedBillForCancellation.value = bill;
  showCancellationModal.value = true;
}

function closeCancelModal() {
  showCancellationModal.value = false;
  selectedBillForCancellation.value = null;
}

async function handleBillCancelled(cancelledBill) {
  // Refresh data after cancellation
  await fetchData();
  success(`${cancelledBill.btype} #${cancelledBill.bno} has been cancelled successfully`);
}

// Track which bill is being duplicated
const isDuplicating = ref(null);

// Function to duplicate a bill
async function duplicateBill(billId) {
  if (isLoading.value) return; // Prevent duplication if already loading

  // Set the duplicating state for this bill
  isDuplicating.value = billId;

  try {
    // Navigate to the edit bill page with duplicate=true parameter
    await router.push({
      path: '/inventory/edit-bill',
      query: {
        id: billId,
        duplicate: 'true'
      }
    });

    // Show a message to the user
    info('Loading bill data for duplication...');
  } catch (error) {
    console.error('Error navigating to edit page:', error);
    showError('Failed to duplicate bill. Please try again.');
  } finally {
    // Reset the duplicating state
    isDuplicating.value = null;
  }
}

// Focus search input and fetch data on mount
onMounted(() => {
  fetchData();
  if (searchInput.value) {
    searchInput.value.focus();
  }
});
</script>

<style scoped>
/* jqGrid-like styling */
.ui-jqgrid-btable {
  border-collapse: separate;
  border-spacing: 0;
}

.ui-jqgrid-htable {
  background: linear-gradient(to bottom, #f8f8f8 0%, #e8e8e8 100%);
  border-bottom: 1px solid #ddd;
}

.ui-jqgrid-th {
  border-right: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
  font-weight: bold;
  position: relative;
}

.ui-jqgrid-th:last-child {
  border-right: none;
}

/* jQuery UI Blitzer theme-like colors */
.ui-jqgrid-htable {
  background: linear-gradient(to bottom, #cc0000 0%, #990000 100%);
  color: white;
}

.ui-jqgrid-th {
  background: linear-gradient(to bottom, #cc0000 0%, #990000 100%);
  color: white;
  border-right: 1px solid #dd0000;
  border-bottom: 1px solid #dd0000;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .ui-jqgrid-btable {
    display: block;
    width: 100%;
    overflow-x: auto;
  }

  .ui-jqgrid-th, .ui-jqgrid-btable td {
    white-space: normal;
    word-break: break-word;
  }
}
</style>