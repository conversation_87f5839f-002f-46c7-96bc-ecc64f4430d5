import SupabaseConfig from '~/server/models/SupabaseConfig.js'
import { createClient } from '@supabase/supabase-js'

export default defineEventHandler(async (event) => {
  try {
    const query = getQuery(event)
    const { firmId, fromDate, toDate, groupId } = query

    if (!firmId || !fromDate || !toDate) {
      throw createError({
        statusCode: 400,
        statusMessage: 'Missing required fields'
      })
    }

    const config = await SupabaseConfig.findOne({
      firmId,
      isActive: true
    })

    if (!config) {
      throw createError({
        statusCode: 404,
        statusMessage: 'No active Supabase configuration found'
      })
    }

    const supabase = createClient(
      config.supabaseUrl,
      config.getDecryptedServiceKey()
    )

    let profilesQuery = supabase
      .from('labor_profiles')
      .select('id, name, daily_rate, group_id')
      .eq('firm_id', firmId)
      .eq('is_active', true)

    if (groupId) {
      profilesQuery = profilesQuery.eq('group_id', groupId)
    }

    const { data: profiles, error: profilesError } = await profilesQuery

    if (profilesError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Database error: ${profilesError.message}`
      })
    }

    const laborIds = profiles.map(p => p.id)

    const { data: attendance, error: attendanceError } = await supabase
      .from('attendance_records')
      .select('labor_id, attendance_date, days_worked')
      .in('labor_id', laborIds)
      .gte('attendance_date', fromDate)
      .lte('attendance_date', toDate)

    if (attendanceError) {
      throw createError({
        statusCode: 500,
        statusMessage: `Database error: ${attendanceError.message}`
      })
    }

    const attendanceData = profiles.map(profile => {
      const profileAttendance = {}
      attendance
        .filter(a => a.labor_id === profile.id)
        .forEach(a => {
          profileAttendance[a.attendance_date] = a.days_worked
        })
      return {
        profile,
        attendance: profileAttendance
      }
    })

    return {
      success: true,
      data: attendanceData
    }
  } catch (error) {
    console.error('Error fetching attendance:', error)
    
    if (error.statusCode) {
      throw error
    }
    
    throw createError({
      statusCode: 500,
      statusMessage: error.message || 'Failed to fetch attendance'
    })
  }
})