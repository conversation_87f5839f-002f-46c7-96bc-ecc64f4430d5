<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>
    
    <!-- Modal panel -->
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full max-w-lg" @click.stop>
        <!-- Modal header -->
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900" id="modal-title">
              Column Visibility Settings
            </h3>
            <button @click="closeModal" class="text-gray-400 hover:text-gray-600 focus:outline-none focus:text-gray-600 transition ease-in-out duration-150">
              <XMarkIcon class="h-6 w-6" />
            </button>
          </div>
          
          <!-- Column visibility options -->
          <div class="space-y-4">
            <p class="text-sm text-gray-600 mb-4">
              Select which columns to show in the inventory table:
            </p>
            
            <div class="grid grid-cols-1 gap-3">
              <!-- MRP Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.mrp" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">MRP</span>
              </label>
              
              <!-- Expiry Date Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.expiryDate" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">Expiry Date</span>
              </label>
              
              <!-- Project Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.project" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">Project</span>
              </label>
              
              <!-- Discount Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.disc" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">Discount (%)</span>
              </label>
              
              <!-- CGST Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.cgst" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">CGST</span>
              </label>
              
              <!-- SGST Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.sgst" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">SGST</span>
              </label>
              
              <!-- IGST Column -->
              <label class="flex items-center">
                <input 
                  v-model="localColumnVisibility.igst" 
                  type="checkbox" 
                  class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <span class="ml-3 text-sm text-gray-900">IGST</span>
              </label>
            </div>
          </div>
        </div>
        
        <!-- Modal footer -->
        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button @click="saveSettings" type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm">
            Save Changes
          </button>
          <button @click="closeModal" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

// Props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  columnVisibility: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['update:show', 'update:columnVisibility'])

// Local state for column visibility
const localColumnVisibility = ref({ ...props.columnVisibility })

// Watch for changes in props
watch(() => props.columnVisibility, (newValue) => {
  localColumnVisibility.value = { ...newValue }
}, { deep: true })

// Watch for changes in show prop to reset local state
watch(() => props.show, (newValue) => {
  if (newValue) {
    localColumnVisibility.value = { ...props.columnVisibility }
  }
})

// Methods
const closeModal = () => {
  emit('update:show', false)
}

const saveSettings = () => {
  emit('update:columnVisibility', { ...localColumnVisibility.value })
  closeModal()
}
</script>
