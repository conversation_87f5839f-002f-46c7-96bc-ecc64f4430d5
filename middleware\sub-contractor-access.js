/**
 * Middleware to restrict access for sub-contractor users
 *
 * This middleware checks if the current user is a sub-contractor and restricts access
 * to pages that sub-contractors should not be able to access.
 */
import useUserRole from '~/composables/auth/useUserRole';

export default defineNuxtRouteMiddleware((to) => {
  // Skip middleware on server-side
  if (process.server) return;

  // Get user role
  const { isSubContractor } = useUserRole();

  // If user is not a sub-contractor, allow access to all pages
  if (!isSubContractor()) return;

  // List of allowed paths for sub-contractors
  const allowedPaths = [
    '/',                    // Home
    '/about',               // About
    '/contact',             // Contact
    '/dashboard',           // Dashboard
    '/notes',               // Notes
    '/expenses/subs'        // Subcontractor accounts
  ];

  // Explicitly disallowed paths for sub-contractors (for clarity)
  const disallowedPaths = [
    '/expenses',            // Financial Expenses Dashboard
    '/expenses/list',       // Transactions list
    '/expenses/add',        // Add transaction
    '/expenses/ledgers',    // Ledgers
    '/expenses/reports'     // Reports
  ];

  // Check if the current path starts with any of the allowed paths
  const isAllowed = allowedPaths.some(path =>
    to.path === path ||
    (path !== '/' && to.path.startsWith(`${path}/`))
  );

  // If the path is not allowed, redirect to dashboard
  if (!isAllowed) {
    console.warn(`Sub-contractor attempted to access restricted page: ${to.path}`);
    return navigateTo('/dashboard');
  }
});
