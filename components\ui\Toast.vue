<template>
  <div>
    <transition-group
      name="toast"
      tag="div"
      class="fixed inset-0 flex flex-col items-center z-[9999] pointer-events-none"
      :class="{
        'justify-start pt-16': true, // Always at the top with padding for navbar
      }"
    >
      <div
        v-for="toast in toasts"
        :key="toast.id"
        :class="[
          'px-6 py-4 rounded-lg shadow-xl transform transition-all duration-300 max-w-md',
          'flex items-center mb-4 pointer-events-auto',
          toast.type === 'success' ? 'bg-emerald-500 text-white border-l-4 border-emerald-700' :
          toast.type === 'error' ? 'bg-red-600 text-white border-l-4 border-red-800' :
          toast.type === 'warning' ? 'bg-yellow-600 text-white border-l-4 border-yellow-800' :
          toast.type === 'loading' ? 'bg-indigo-600 text-white border-l-4 border-indigo-800' :
          'bg-blue-600 text-white border-l-4 border-blue-800'
        ]"
      >
        <!-- Icon based on type -->
        <div class="mr-3 flex-shrink-0">
          <!-- Success Icon -->
          <svg v-if="toast.type === 'success'" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
          </svg>
          <!-- Error Icon -->
          <svg v-else-if="toast.type === 'error'" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
          <!-- Warning Icon -->
          <svg v-else-if="toast.type === 'warning'" class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
          <!-- Loading Icon (Spinner) -->
          <svg v-else-if="toast.type === 'loading'" class="h-5 w-5 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <!-- Info Icon -->
          <svg v-else class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>

        <!-- Toast content -->
        <div class="flex-1">
          <div v-if="toast.title" class="font-medium">{{ toast.title }}</div>
          <div class="text-sm">{{ toast.message }}</div>
        </div>

        <!-- Close button -->
        <button
          @click="removeToast(toast.id)"
          class="ml-4 flex-shrink-0 text-white focus:outline-none"
        >
          <svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>
    </transition-group>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue';

export default {
  name: 'Toast',

  setup() {
    const toasts = ref([]);
    let toastEventListener = null;

    // Function to add a new toast
    const addToast = (toast) => {
      const id = toast.id || Date.now().toString();
      toasts.value.push({
        id,
        title: toast.title || '',
        message: toast.message,
        type: toast.type || 'info',
        duration: toast.duration !== undefined ? toast.duration : 3000,
        position: toast.position || 'top-center'
      });

      // Auto-remove toast after duration (if not infinite)
      if (toast.duration !== 0) {
        setTimeout(() => {
          removeToast(id);
        }, toast.duration || 3000);
      }
    };

    // Function to remove a toast by id
    const removeToast = (id) => {
      const index = toasts.value.findIndex(toast => toast.id === id);
      if (index !== -1) {
        toasts.value.splice(index, 1);
      }
    };

    // Setup event listeners for custom toast events
    onMounted(() => {
      // Show toast event listener
      toastEventListener = (event) => {
        if (event.detail) {
          addToast(event.detail);
        }
      };
      window.addEventListener('show-toast', toastEventListener);

      // Dismiss toast event listener
      const dismissToastListener = (event) => {
        if (event.detail && event.detail.id) {
          removeToast(event.detail.id);
        }
      };
      window.addEventListener('dismiss-toast', dismissToastListener);

      // Store the dismiss listener for cleanup
      window.dismissToastListener = dismissToastListener;
    });

    // Clean up event listeners
    onUnmounted(() => {
      if (toastEventListener) {
        window.removeEventListener('show-toast', toastEventListener);
      }

      if (window.dismissToastListener) {
        window.removeEventListener('dismiss-toast', window.dismissToastListener);
        delete window.dismissToastListener;
      }
    });

    return {
      toasts,
      removeToast
    };
  }
};
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
  transition: all 0.3s ease;
}

.toast-enter-from {
  opacity: 0;
  transform: translateY(20px) scale(0.9);
}

.toast-leave-to {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}
</style>
