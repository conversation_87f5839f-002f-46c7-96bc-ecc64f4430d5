<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-2 sm:p-4 overflow-hidden">
    <div
      class="bg-white rounded-lg w-full max-w-[95vw] md:max-w-5xl lg:max-w-6xl xl:max-w-7xl mx-auto border border-gray-200 shadow-xl transition-all duration-1000 transform animate-modal-appear"
      @click.stop
    >
      <!-- Header with gradient background -->
      <div
        :class="{
          'bg-gradient-to-r from-blue-400 to-purple-400': !isFirestore,
          'bg-gradient-to-r from-orange-400 to-red-400': isFirestore
        }"
        class="p-4 rounded-t-lg flex justify-between items-center bg-gradient-animate"
      >
        <div class="flex items-center">
          <h2 class="text-xl font-bold text-white drop-shadow-sm">{{ modelName }} Details ({{ data.length }} Records)</h2>
          <span v-if="isFirestore" class="ml-3 px-2 py-1 bg-orange-600 text-white text-xs rounded-full shadow-sm">Firestore</span>
        </div>
        <button @click="$emit('close')" class="text-white hover:text-gray-200 transition-colors">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Content -->
      <div class="p-4 sm:p-6 max-h-[70vh] overflow-y-auto bg-white">
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
        </div>

        <div v-else>
          <div class="mb-6 flex flex-wrap items-center gap-2 sm:gap-4">
            <div class="bg-blue-100 px-4 py-3 rounded-lg shadow-sm stagger-item hover:shadow-md transition-shadow duration-700" :style="{ animationDelay: '0.2s' }">
              <span class="text-blue-800">Total Records:</span>
              <span class="ml-2 text-xl font-bold text-blue-900">{{ data.length }}</span>
            </div>

            <div class="bg-purple-100 px-4 py-3 rounded-lg shadow-sm stagger-item hover:shadow-md transition-shadow duration-700" :style="{ animationDelay: '0.4s' }">
              <span class="text-purple-800">Schema Fields:</span>
              <span class="ml-2 text-xl font-bold text-purple-900">{{ Object.keys(schema).length }}</span>
            </div>

            <div class="bg-green-100 px-4 py-3 rounded-lg shadow-sm stagger-item hover:shadow-md transition-shadow duration-700" :style="{ animationDelay: '0.6s' }">
              <span class="text-green-800">Collection Name:</span>
              <span class="ml-2 text-xl font-bold text-green-900">{{ modelName }}</span>
            </div>
          </div>

          <!-- Tabs Navigation -->
          <div class="mb-6 border-b border-gray-200">
            <ul class="flex flex-wrap -mb-px text-sm font-medium text-center">
              <li class="mr-2">
                <button
                  @click="activeTab = 'data'"
                  class="inline-block p-4 rounded-t-lg transition-all duration-600 border-b-2 focus:outline-none relative overflow-hidden"
                  :class="activeTab === 'data' ? 'text-blue-600 border-blue-600 bg-blue-50 active tab-active' : 'border-transparent hover:text-gray-600 hover:border-gray-300 hover:bg-gray-50'"
                >
                  <span class="flex items-center relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
                    </svg>
                    Data Preview
                  </span>
                </button>
              </li>
              <li class="mr-2">
                <button
                  @click="activeTab = 'schema'"
                  class="inline-block p-4 rounded-t-lg transition-all duration-600 border-b-2 focus:outline-none relative overflow-hidden"
                  :class="activeTab === 'schema' ? 'text-purple-600 border-purple-600 bg-purple-50 active tab-active' : 'border-transparent hover:text-gray-600 hover:border-gray-300 hover:bg-gray-50'"
                >
                  <span class="flex items-center relative z-10">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                    </svg>
                    Schema Structure
                  </span>
                </button>
              </li>
            </ul>
          </div>

          <!-- Tab Content -->
          <div class="tab-content">
            <!-- Schema Information Tab -->
            <div v-if="activeTab === 'schema'" class="admin-fade-in">
              <div class="bg-gradient-to-br from-purple-50 to-white rounded-lg p-4 overflow-x-auto border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-700">
                <h3 class="text-lg font-semibold text-purple-700 mb-3 flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
                  </svg>
                  Schema Structure for {{ modelName }}
                </h3>
                <pre class="text-gray-800 text-sm bg-white p-3 rounded shadow-inner"><code>{{ JSON.stringify(schema, null, 2) }}</code></pre>
              </div>
            </div>

            <!-- Data Preview Tab -->
            <div v-if="activeTab === 'data'" class="admin-fade-in">
              <div class="bg-gradient-to-br from-blue-50 to-white rounded-lg p-4 overflow-hidden border border-gray-200 shadow-md hover:shadow-lg transition-shadow duration-700">
                <div class="flex flex-wrap justify-between items-center mb-3 gap-2">
                  <h3 class="text-lg font-semibold text-blue-700 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
                    </svg>
                    Data Records ({{ filteredData.length }} of {{ data.length }} items)
                  </h3>

                  <div class="flex items-center gap-2">
                    <!-- Show All / Paginate Toggle -->
                    <button
                      @click="toggleShowAll"
                      class="px-3 py-2 text-sm font-medium rounded-md transition-colors duration-500"
                      :class="itemsPerPage === 'All' ? 'bg-blue-100 text-blue-700' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'"
                    >
                      {{ itemsPerPage === 'All' ? 'Show Paginated' : 'Show All' }}
                    </button>

                    <!-- Search input -->
                    <div class="relative">
                      <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <svg class="w-4 h-4 text-gray-500" aria-hidden="true" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 20 20">
                          <path stroke="currentColor" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"/>
                        </svg>
                      </div>
                      <input
                        type="text"
                        v-model="searchQuery"
                        class="block w-full p-2 pl-10 text-sm text-gray-900 border border-gray-300 rounded-lg bg-white focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Search records..."
                      >
                    </div>
                  </div>
                </div>
                <div class="overflow-hidden shadow-md rounded-lg border border-gray-200">
                  <!-- Simple table with scrollable body -->
                  <div class="relative">
                    <div class="max-h-[50vh] overflow-y-auto overflow-x-auto">
                      <table class="min-w-full bg-white rounded-lg border border-gray-200 table-shine">
                        <thead>
                          <tr>
                            <th
                              v-for="field in Object.keys(schema)"
                              :key="field"
                              class="py-3 px-4 text-left text-gray-700 font-semibold border-b border-gray-200 sticky top-0"
                              :class="{
                                'bg-gradient-to-r from-blue-100 to-purple-100': !isFirestore,
                                'bg-gradient-to-r from-orange-100 to-red-100': isFirestore
                              }"
                            >
                              {{ field }}
                            </th>
                            <!-- User Full Name column if applicable -->
                            <th
                              v-if="hasUserField"
                              class="py-3 px-4 text-left text-gray-700 font-semibold border-b border-gray-200 sticky top-0"
                              :class="isFirestore ? 'bg-yellow-100' : 'bg-green-100'"
                            >
                              User Full Name
                            </th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(item, index) in paginatedData" :key="index" class="border-t border-gray-200 hover:bg-blue-50 transition-colors duration-500 stagger-item even:bg-gray-50" :style="{ animationDelay: `${index * 0.05}s` }">
                            <td v-for="field in Object.keys(schema)" :key="`${index}-${field}`" class="py-2 px-4 text-gray-800 border-r border-gray-100 last:border-r-0">
                              {{ formatValue(item[field]) }}
                            </td>
                            <!-- User Full Name column if applicable -->
                            <td v-if="hasUserField" class="py-2 px-4 text-gray-800 border-r border-gray-100 bg-green-50">
                              {{ getUserFullName(item) || '-' }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <!-- Pagination controls -->
                  <div v-if="filteredData.length > 0 && itemsPerPage !== 'All'" class="flex flex-wrap items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
                    <div class="flex items-center mb-2 sm:mb-0">
                      <span class="text-sm text-gray-700">
                        Showing
                        <span class="font-medium">{{ itemsPerPage === 'All' ? 1 : (currentPage - 1) * itemsPerPage + 1 }}</span>
                        to
                        <span class="font-medium">{{ itemsPerPage === 'All' ? filteredData.length : Math.min(currentPage * itemsPerPage, filteredData.length) }}</span>
                        of
                        <span class="font-medium">{{ filteredData.length }}</span>
                        results
                      </span>

                      <!-- Items per page selector -->
                      <div class="ml-4">
                        <select
                          v-model="itemsPerPage"
                          @change="changeItemsPerPage(itemsPerPage)"
                          class="block w-full py-1 px-2 text-sm border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        >
                          <option v-for="option in pageSizeOptions" :key="option" :value="option">{{ option }} per page</option>
                        </select>
                      </div>
                    </div>

                    <!-- Pagination buttons -->
                    <div class="flex justify-center sm:justify-end space-x-1">
                      <!-- Previous page button -->
                      <button
                        @click="changePage(currentPage - 1)"
                        :disabled="currentPage === 1"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === 1}"
                        class="relative inline-flex items-center px-2 py-2 rounded-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                      >
                        <span class="sr-only">Previous</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                        </svg>
                      </button>

                      <!-- Page numbers -->
                      <button
                        v-for="page in displayedPages"
                        :key="page"
                        @click="changePage(page)"
                        :disabled="page === '...'"
                        :class="{
                          'bg-blue-50 border-blue-500 text-blue-600': page === currentPage,
                          'bg-white border-gray-300 text-gray-500 hover:bg-gray-50': page !== currentPage,
                          'cursor-default': page === '...'
                        }"
                        class="relative inline-flex items-center px-4 py-2 border text-sm font-medium rounded-md"
                      >
                        {{ page }}
                      </button>

                      <!-- Next page button -->
                      <button
                        @click="changePage(currentPage + 1)"
                        :disabled="currentPage === totalPages"
                        :class="{'opacity-50 cursor-not-allowed': currentPage === totalPages}"
                        class="relative inline-flex items-center px-2 py-2 rounded-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50"
                      >
                        <span class="sr-only">Next</span>
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                          <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- No results message -->
                  <div v-if="filteredData.length === 0 && searchQuery" class="p-6 text-center text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p>No records match your search criteria.</p>
                    <button @click="searchQuery = ''" class="mt-2 text-blue-500 hover:text-blue-700 underline">Clear search</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer with gradient background -->
      <div
        :class="{
          'bg-gradient-to-r from-purple-400 to-blue-400': !isFirestore,
          'bg-gradient-to-r from-red-400 to-orange-400': isFirestore
        }"
        class="p-4 rounded-b-lg flex justify-end"
      >
        <button
          @click="$emit('close')"
          class="px-6 py-2 bg-white hover:bg-gray-100 text-gray-800 rounded-md transition-all duration-700 border border-gray-300 shadow-sm hover:shadow-md"
        >
          Close
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';

// Active tab state
const activeTab = ref('data');

// Search filter
const searchQuery = ref('');

// Pagination
const currentPage = ref(1);
const itemsPerPage = ref(10);
const pageSizeOptions = [5, 10, 25, 50, 100, 'All'];

// User data cache
const userFullNames = ref({});

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  modelName: {
    type: String,
    required: true
  },
  data: {
    type: Array,
    default: () => []
  },
  schema: {
    type: Object,
    default: () => ({})
  },
  loading: {
    type: Boolean,
    default: false
  },
  allModels: {
    type: Array,
    default: () => []
  },
  isFirestore: {
    type: Boolean,
    default: false
  }
});

// Set default pagination based on data size
watch(() => props.data, (newData) => {
  // If data is small (less than 20 items), show all by default
  if (newData && newData.length > 0 && newData.length <= 20) {
    itemsPerPage.value = 'All';
  }
}, { immediate: true });

// Check if any item has a user field
const hasUserField = computed(() => {
  if (!props.data || props.data.length === 0) return false;

  // Check the first few items for user fields
  const sampleSize = Math.min(props.data.length, 5);
  const userIdFields = ['userId', 'user_id', 'UserId', 'userid', 'user', 'username', 'userName', 'user_name'];

  for (let i = 0; i < sampleSize; i++) {
    const item = props.data[i];
    for (const field of userIdFields) {
      if (item[field]) return true;
    }
  }

  return false;
});

defineEmits(['close']);

// Filter data based on search query
const filteredData = computed(() => {
  if (!searchQuery.value.trim()) {
    return props.data;
  }

  const query = searchQuery.value.toLowerCase();
  return props.data.filter(item => {
    return Object.values(item).some(val => {
      if (val === null || val === undefined) return false;
      return String(val).toLowerCase().includes(query);
    });
  });
});

// Paginated data
const paginatedData = computed(() => {
  // If 'All' is selected, return all data
  if (itemsPerPage.value === 'All') {
    return filteredData.value;
  }

  // Otherwise, paginate normally
  const startIndex = (currentPage.value - 1) * itemsPerPage.value;
  const endIndex = startIndex + itemsPerPage.value;
  return filteredData.value.slice(startIndex, endIndex);
});

// Total pages
const totalPages = computed(() => {
  if (itemsPerPage.value === 'All') {
    return 1; // Just one page when showing all
  }
  return Math.ceil(filteredData.value.length / itemsPerPage.value);
});

// Page numbers to display
const displayedPages = computed(() => {
  const pages = [];
  const maxPagesToShow = 5;

  if (totalPages.value <= maxPagesToShow) {
    // If we have fewer pages than the max, show all pages
    for (let i = 1; i <= totalPages.value; i++) {
      pages.push(i);
    }
  } else {
    // Always include first page
    pages.push(1);

    // Calculate start and end of page range
    let startPage = Math.max(2, currentPage.value - 1);
    let endPage = Math.min(totalPages.value - 1, currentPage.value + 1);

    // Adjust if we're near the beginning
    if (currentPage.value <= 3) {
      endPage = Math.min(totalPages.value - 1, 4);
    }

    // Adjust if we're near the end
    if (currentPage.value >= totalPages.value - 2) {
      startPage = Math.max(2, totalPages.value - 3);
    }

    // Add ellipsis before if needed
    if (startPage > 2) {
      pages.push('...');
    }

    // Add the page range
    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    // Add ellipsis after if needed
    if (endPage < totalPages.value - 1) {
      pages.push('...');
    }

    // Always include last page
    if (totalPages.value > 1) {
      pages.push(totalPages.value);
    }
  }

  return pages;
});

// Function to change page
function changePage(page) {
  if (page === '...') return;
  currentPage.value = page;
}

// Function to change items per page
function changeItemsPerPage(newSize) {
  // Convert numeric strings to numbers
  itemsPerPage.value = newSize === 'All' ? 'All' : parseInt(newSize);
  currentPage.value = 1; // Reset to first page
}

// Toggle between showing all records and paginated view
function toggleShowAll() {
  if (itemsPerPage.value === 'All') {
    itemsPerPage.value = 10; // Default page size
  } else {
    itemsPerPage.value = 'All';
  }
  currentPage.value = 1; // Reset to first page
}

// Reset pagination when search changes
watch(searchQuery, (newQuery) => {
  currentPage.value = 1;

  // If search query is used and results are few, automatically show all
  if (newQuery && filteredData.value.length <= 50) {
    itemsPerPage.value = 'All';
  }
});

// Function to get user full name
function getUserFullName(item) {
  // Check for common user ID field patterns
  let userId = null;

  // Check for userId, user_id, UserId, etc.
  const userIdFields = ['userId', 'user_id', 'UserId', 'userid', 'user', 'username', 'userName', 'user_name'];

  for (const field of userIdFields) {
    if (item[field]) {
      userId = item[field];
      break;
    }
  }

  if (!userId) return null;

  // If we already have the full name cached, return it
  if (userFullNames.value[userId]) {
    return userFullNames.value[userId];
  }

  // Otherwise, look for it in the user data
  if (props.modelName.toLowerCase() === 'user') {
    // If we're viewing the user model itself, get the name from the current item
    const nameFields = ['fullname', 'full_name', 'name', 'displayName', 'display_name'];
    for (const field of nameFields) {
      if (item[field]) {
        userFullNames.value[userId] = item[field];
        return item[field];
      }
    }
  } else {
    // Try to find the user in the data if we have a User model
    const userModel = props.allModels?.find(model =>
      model.name.toLowerCase() === 'user' ||
      model.name.toLowerCase() === 'users'
    );

    if (userModel && userModel.data) {
      const user = userModel.data.find(u => {
        // Try to match by ID or username
        return u._id === userId || u.id === userId || u.username === userId;
      });

      if (user) {
        // Try common name fields
        const nameFields = ['fullname', 'full_name', 'name', 'displayName', 'display_name'];
        for (const field of nameFields) {
          if (user[field]) {
            userFullNames.value[userId] = user[field];
            return user[field];
          }
        }

        // If no name field found but we have first and last name
        if (user.firstName && user.lastName) {
          const fullName = `${user.firstName} ${user.lastName}`;
          userFullNames.value[userId] = fullName;
          return fullName;
        }

        // Fall back to username if available
        if (user.username) {
          userFullNames.value[userId] = user.username;
          return user.username;
        }
      }
    }
  }

  // If we couldn't find a full name, just return the userId
  return userId;
}

function formatValue(value) {
  if (value === undefined || value === null) {
    return '-';
  }

  if (typeof value === 'object') {
    if (value instanceof Date) {
      return value.toLocaleDateString();
    }
    if (value._id) {
      return value._id.toString().substring(0, 8) + '...';
    }
    return JSON.stringify(value).substring(0, 30) + (JSON.stringify(value).length > 30 ? '...' : '');
  }

  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }

  return String(value).substring(0, 30) + (String(value).length > 30 ? '...' : '');
}
</script>

<style scoped>
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.bg-gradient-to-r {
  background-size: 100% 100%;
  /* Removed gradient animation to prevent conflicts */
}

/* Modal shine effect */
.card-shine {
  position: relative;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.card-shine::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.3), transparent);
  transform: rotate(30deg);
  /* Removed shine animation to prevent conflicts */
  z-index: 1;
  pointer-events: none;
}

@keyframes shine {
  0% {
    transform: translateX(-100%) rotate(30deg);
  }
  20%, 100% {
    transform: translateX(100%) rotate(30deg);
  }
}

/* Modal appear animation */
@keyframes modal-appear {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-modal-appear {
  animation: modal-appear 1.0s ease-out forwards;
}

/* Table shine effect */
.table-shine {
  position: relative;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.table-shine::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, rgba(255, 255, 255, 0.2) 0%, transparent 100%);
  pointer-events: none;
}

/* Simple sticky header styles */
th.sticky {
  position: sticky;
  top: 0;
  z-index: 10;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Basic table styles */
table {
  width: 100%;
}

/* Add a subtle shadow to the header */
th.sticky::after {
  content: '';
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 2px;
  background: linear-gradient(to right, rgba(99, 102, 241, 0.2), rgba(168, 85, 247, 0.2));
}

/* Tab fade in animation */
@keyframes tab-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-tab-fade-in {
  animation: tab-fade-in 1.0s ease-out forwards;
}

/* Tab active shine effect */
.tab-active::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to right, transparent, rgba(255, 255, 255, 0.4), transparent);
  transform: translateX(-100%);
  /* Removed tab shine animation to prevent conflicts */
}

@keyframes tab-shine {
  100% {
    transform: translateX(100%);
  }
}
</style>
