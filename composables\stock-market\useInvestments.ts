import { ref, onUnmounted } from 'vue';

// Define types for our investment data
interface Investment {
  _id: string;
  cn_no: string;
  symbol: string;
  price: number;
  qnty: number;
  amt: number;
  brokerage: number;
  broker: string;
  pdate: string;
  namt: number;
  folio: string;
  type: string;
  rid: string;
  sector: string;
  user: string;
  cprice?: number;
  cval?: number;
  age?: number;
  pl?: number;
  prevDayPrice?: number;
  dayPL?: number;
  dayPLPercentage?: number;
}

interface InvestmentSummary {
  totalInvested: number;
  currentValue: number;
  totalProfitLoss: number;
  profitLossPercentage: number;
  investmentCount: number;
  todayTotalPL: number;
  todayPLPercentage: number;
}

interface SectorAllocation {
  sector: string;
  value: number;
  percentage: number;
}

interface BrokerAllocation {
  broker: string;
  value: number;
  percentage: number;
}

interface InvestmentData {
  investments: Investment[];
  summary: InvestmentSummary;
  sectorAllocation: SectorAllocation[];
  brokerAllocation: BrokerAllocation[];
  timestamp: string;
  error?: string;
}

export function useInvestments() {
  const isLoading = ref(true);
  const error = ref<string | null>(null);
  const investmentData = ref<InvestmentData>({
    investments: [],
    summary: {
      totalInvested: 0,
      currentValue: 0,
      totalProfitLoss: 0,
      profitLossPercentage: 0,
      investmentCount: 0,
      todayTotalPL: 0,
      todayPLPercentage: 0
    },
    sectorAllocation: [],
    brokerAllocation: [],
    timestamp: ''
  });

  // Auto-update interval reference
  let autoUpdateInterval: number | null = null;

  // Price updates are now handled by the visual countdown timer in the page component

  // Track if prices are currently being updated
  const isUpdatingPrices = ref(false);

  // Track the last price update time
  const lastPriceUpdate = ref<string | null>(null);

  // Function to fetch investment data
  async function fetchInvestmentData(silentUpdate = false) {
    if (!silentUpdate) {
      isLoading.value = true;
    }
    error.value = null;

    try {
      const response = await $fetch<InvestmentData>('/api/stock-market/investments', {
        // Handle HTTP errors properly
        onResponseError({ response }) {
          // Handle authentication errors
          if (response.status === 401) {
            error.value = 'Authentication required. Please log in to view your investments.';
            console.error('Authentication error:', response.statusText);
            return null;
          }

          // Handle other errors
          error.value = `Error: ${response.statusText || 'Unknown error'}`;
          console.error('API error:', response.statusText);
          return null;
        }
      });

      if (response && response.error) {
        error.value = response.error;
        isLoading.value = false;
        return null;
      }

      investmentData.value = response;
      isLoading.value = false;
      return response;
    } catch (err: any) {
      console.error('Error fetching investment data:', err);
      // Provide a user-friendly error message
      if (err.response && err.response.status === 401) {
        error.value = 'Authentication required. Please log in to view your investments.';
      } else {
        error.value = err.message || 'Failed to fetch investment data';
      }
      isLoading.value = false;
      return null; // Return null instead of throwing to handle errors gracefully
    }
  }

  // Function to fetch current prices from Yahoo Finance and update if needed
  async function updateCurrentPrices(updateDatabase = true) {
    if (isUpdatingPrices.value) {
      console.log('Price update already in progress, skipping...');
      return;
    }

    isUpdatingPrices.value = true;

    try {
      console.log('Fetching current stock prices from Yahoo Finance...');

      // Call the API to get current prices
      const response = await $fetch('/api/stock-market/prices', {
        params: {
          updateDatabase: updateDatabase.toString()
        },
        // Handle HTTP errors properly
        onResponseError({ response }) {
          console.error('API error when fetching stock prices:', response.statusText);
          return null;
        }
      });

      if (!response) {
        console.error('Failed to fetch current prices');
        isUpdatingPrices.value = false;
        return;
      }

      console.log(`Received price updates for ${Object.keys(response.priceUpdates).length} symbols`);

      // If we have investment data and there are price changes, update the UI
      if (investmentData.value.investments.length > 0 && response.changedCount > 0) {
        console.log(`Updating UI with ${response.changedCount} price changes`);

        // Create a map of the updates for easy lookup
        const updatesMap = new Map();
        response.investmentUpdates.forEach(update => {
          updatesMap.set(update.id, update);
        });

        // Update the investment data in place
        const updatedInvestments = investmentData.value.investments.map(inv => {
          const update = updatesMap.get(inv._id);
          if (update && update.changed) {
            // Create a new object with updated values
            return {
              ...inv,
              cprice: update.cprice,
              cval: update.cval,
              pl: update.pl,
              prevDayPrice: update.prevDayPrice,
              dayPL: update.dayPL,
              dayPLPercentage: update.dayPLPercentage
            };
          }
          return inv;
        });

        // Recalculate summary statistics
        let totalInvested = 0;
        let currentValue = 0;
        let totalProfitLoss = 0;

        updatedInvestments.forEach(inv => {
          totalInvested += Number(inv.namt) || 0;
          currentValue += Number(inv.cval) || 0;
          totalProfitLoss += Number(inv.pl) || 0;
        });

        const profitLossPercentage = totalInvested > 0 ? (totalProfitLoss / totalInvested) * 100 : 0;

        // Calculate today's total gain/loss
        let todayTotalPL = 0;
        updatedInvestments.forEach(inv => {
          todayTotalPL += Number(inv.dayPL) || 0;
        });

        // Get today's percentage from the response or calculate it
        let todayPLPercentage = 0;
        if (response.summary && response.summary.todayPLPercentage !== undefined) {
          todayPLPercentage = response.summary.todayPLPercentage;
          console.log(`Using todayPLPercentage from API: ${todayPLPercentage}`);

          // Force a non-zero value for testing if needed
          if (Math.abs(todayPLPercentage) < 0.01 && todayPLPercentage !== 0) {
            // If the percentage is very small but not zero, preserve its sign
            todayPLPercentage = todayPLPercentage > 0 ? 0.01 : -0.01;
            console.log(`Very small percentage detected from API, adjusting to ${todayPLPercentage} for visibility`);
          }
        } else {
          // Calculate based on current investments
          const totalPrevValue = updatedInvestments.reduce((sum, inv) => {
            return sum + ((Number(inv.prevDayPrice) || Number(inv.cprice) || 0) * Number(inv.qnty));
          }, 0);

          // Calculate and log the raw percentage
          const rawPercentage = totalPrevValue > 0 ? (todayTotalPL / totalPrevValue) * 100 : 0;
          console.log(`Calculated raw percentage: ${todayTotalPL} / ${totalPrevValue} * 100 = ${rawPercentage}`);

          // Format to 2 decimal places but ensure non-zero values are visible
          if (Math.abs(rawPercentage) < 0.01 && rawPercentage !== 0) {
            // If the percentage is very small but not zero, preserve its sign
            todayPLPercentage = rawPercentage > 0 ? 0.01 : -0.01;
            console.log(`Very small percentage detected, setting to ${todayPLPercentage} for visibility`);
          } else {
            todayPLPercentage = parseFloat(rawPercentage.toFixed(2));
          }

          console.log(`Final calculated percentage: ${todayPLPercentage}`);

          // Ensure we have a valid percentage value
          if (isNaN(todayPLPercentage)) {
            console.warn('Warning: todayPLPercentage is NaN, setting to 0');
            todayPLPercentage = 0;
          }
        }

        // Log the final percentage that will be used
        console.log(`Final todayPLPercentage to be displayed: ${todayPLPercentage}`);


        // Update the investment data
        investmentData.value = {
          ...investmentData.value,
          investments: updatedInvestments,
          summary: {
            ...investmentData.value.summary,
            currentValue,
            totalProfitLoss,
            profitLossPercentage,
            todayTotalPL,
            todayPLPercentage
          },
          timestamp: response.timestamp
        };

        // Update the last price update time
        lastPriceUpdate.value = new Date().toLocaleString();
        console.log('UI updated with new prices');
      } else {
        console.log('No price changes to update in UI');
      }
    } catch (err) {
      console.error('Error updating current prices:', err);
    } finally {
      isUpdatingPrices.value = false;
    }
  }

  // These functions are now handled by the visual countdown timer in the page component
  // Keeping empty implementations for backward compatibility
  function startPriceUpdates() {
    console.log('Price updates are now handled by the visual countdown timer');
    return stopPriceUpdates;
  }

  function stopPriceUpdates() {
    console.log('Price updates are now handled by the visual countdown timer');
  }

  // Function to start auto-updating
  function startAutoUpdate(intervalMinutes: number = 20) {
    // Clear any existing interval
    if (autoUpdateInterval) {
      clearInterval(autoUpdateInterval);
    }

    // Convert minutes to milliseconds
    const intervalMs = intervalMinutes * 60 * 1000;

    // Set up the interval
    autoUpdateInterval = window.setInterval(async () => {
      try {
        // Pass true for silentUpdate to avoid showing loading indicator
        await fetchInvestmentData(true);
        console.log(`Auto-updated investment data at ${new Date().toLocaleTimeString()}`);
      } catch (error) {
        console.error('Auto-update failed:', error);
      }
    }, intervalMs);

    console.log(`Investment data auto-update started. Will refresh every ${intervalMinutes} minutes.`);

    // Return a function to stop the auto-update
    return stopAutoUpdate;
  }

  // Function to stop auto-updating
  function stopAutoUpdate() {
    if (autoUpdateInterval) {
      clearInterval(autoUpdateInterval);
      autoUpdateInterval = null;
      console.log('Investment data auto-update stopped.');
    }
  }

  // Clean up intervals when component is unmounted
  onUnmounted(() => {
    stopAutoUpdate();
    // Price updates are now handled by the visual countdown timer
  });

  // Function to format currency
  function formatCurrency(amount: number | undefined | null) {
    // Handle undefined or null values
    if (amount === undefined || amount === null) {
      return 'N/A';
    }

    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  }

  // Function to format percentage
  function formatPercentage(percentage: number | undefined | null) {
    // Handle undefined, null, or NaN values
    if (percentage === undefined || percentage === null || isNaN(percentage)) {
      console.log('formatPercentage received invalid value:', percentage);
      return {
        value: '+0.00%', // Default to 0.00% instead of N/A
        color: 'text-gray-500'
      };
    }

    // Determine if positive (including zero)
    const isPositive = percentage >= 0;

    // Special handling for zero values
    if (percentage === 0) {
      return {
        value: '+0.00%',
        color: 'text-gray-500' // Use gray for zero values
      };
    }

    // Ensure percentage has exactly 2 decimal places
    const formattedValue = percentage.toFixed(2) + '%';

    return {
      value: isPositive ? '+' + formattedValue : formattedValue,
      color: isPositive ? 'text-green-600' : 'text-red-600'
    };
  }

  // Function to get monthly investment data for chart
  function getMonthlyInvestmentData() {
    // Return empty data if no investments
    if (!investmentData.value.investments || investmentData.value.investments.length === 0) {
      return {
        labels: [],
        investmentData: [],
        currentValueData: []
      };
    }

    // Create a map to store monthly investments
    const monthlyData = new Map();

    // Sort investments by date (oldest first)
    const sortedInvestments = [...investmentData.value.investments].sort(
      (a, b) => new Date(a.pdate).getTime() - new Date(b.pdate).getTime()
    );

    // Process each investment
    sortedInvestments.forEach(investment => {
      const date = new Date(investment.pdate);
      const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

      // Initialize month if not exists
      if (!monthlyData.has(monthYear)) {
        monthlyData.set(monthYear, {
          investments: [],
          label: new Date(date.getFullYear(), date.getMonth(), 1)
            .toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
          timestamp: date.getTime()
        });
      }

      // Add investment to month
      monthlyData.get(monthYear).investments.push(investment);
    });

    // Convert to array and sort by date
    const monthsArray = Array.from(monthlyData.values())
      .sort((a, b) => a.timestamp - b.timestamp);

    // Calculate cumulative values
    let cumulativeInvestment = 0;
    let cumulativeCurrentValue = 0;

    const chartData = {
      labels: [],
      investmentData: [],
      currentValueData: []
    };

    // Process all months to calculate cumulative values
    monthsArray.forEach(month => {
      // Calculate month's investment total
      const monthInvestment = month.investments.reduce((sum, inv) => sum + (inv.namt || 0), 0);
      cumulativeInvestment += monthInvestment;

      // For current value, we need to recalculate the total current value of all investments up to this point
      cumulativeCurrentValue = sortedInvestments
        .filter(inv => new Date(inv.pdate).getTime() <= month.timestamp)
        .reduce((sum, inv) => sum + (inv.cval || inv.namt || 0), 0);

      // Add to chart data
      chartData.labels.push(month.label);
      chartData.investmentData.push(cumulativeInvestment);
      chartData.currentValueData.push(cumulativeCurrentValue);
    });

    // Limit to the latest 12 months
    if (chartData.labels.length > 12) {
      const startIndex = chartData.labels.length - 12;
      chartData.labels = chartData.labels.slice(startIndex);
      chartData.investmentData = chartData.investmentData.slice(startIndex);
      chartData.currentValueData = chartData.currentValueData.slice(startIndex);
    }

    return chartData;
  }

  return {
    isLoading,
    isUpdatingPrices,
    error,
    investmentData,
    lastPriceUpdate,
    fetchInvestmentData,
    updateCurrentPrices,
    startPriceUpdates,
    stopPriceUpdates,
    startAutoUpdate,
    stopAutoUpdate,
    formatCurrency,
    formatPercentage,
    getMonthlyInvestmentData
  };
}
