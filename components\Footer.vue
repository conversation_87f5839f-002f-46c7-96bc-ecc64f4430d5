<template>
  <footer class="fixed inset-x-0 bottom-0 w-full bg-gradient-to-r from-teal-400 via-indigo-500 to-teal-400 shadow-lg py-1 px-6 z-50 flex items-center justify-between">
    <!-- Left-Aligned Links -->
    <div class="space-x-4">
      <NuxtLink to="/privacy-policy" class="text-white hover:text-teal-200 transition duration-300">
        Privacy Policy
      </NuxtLink>
      <NuxtLink to="/terms" class="text-white hover:text-teal-200 transition duration-300">
        Terms of Service
      </NuxtLink>
    </div>

    <!-- Center Text -->
    <p class="text-white text-xs font-bold">
      &copy; {{ new Date().getFullYear() }} BusinessPro Suite. All rights reserved.
    </p>

    <!-- Right-Aligned Links -->
    <div class="space-x-4">
      <NuxtLink to="/contact" class="text-white hover:text-teal-200 transition duration-300">
        Contact Us
      </NuxtLink>
      <button @click="openHelpModal" class="text-white hover:text-teal-200 transition duration-300">
        Help
      </button>
    </div>
  </footer>
</template>

<script setup>
import { useRoute } from 'vue-router'
import { useHelpModal } from '~/composables/ui/useHelpModal'

// Get current route for context-aware help
const route = useRoute()
const { openContextualHelp } = useHelpModal()

// Open help modal with context-aware content
const openHelpModal = () => {
  openContextualHelp(route)
}
</script>