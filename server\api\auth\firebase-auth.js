/**
 * API endpoint for Firebase authentication
 * 
 * This endpoint authenticates with Firebase using email/password
 * without exposing Firebase credentials to the frontend
 */
export default defineEventHandler(async (event) => {
  try {
    // Get runtime config to access environment variables
    const config = useRuntimeConfig();
    const apiKey = config.firebaseApiKey;

    if (!apiKey) {
      throw createError({
        statusCode: 500,
        message: 'Firebase API key not configured'
      });
    }

    // Hardcoded credentials - in a real app, you might want to store these securely
    // or implement a more sophisticated authentication mechanism
    const email = process.env.FB_USER;
    const password = process.env.FB_PASS;

    // Make the request to Firebase Auth API
    const response = await fetch(
      `https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=${apiKey}`,
      {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email,
          password,
          returnSecureToken: true,
        }),
      }
    );

    // Parse the response
    const data = await response.json();

    // Check for errors
    if (!response.ok) {
      console.error('Firebase authentication error:', data.error);
      throw createError({
        statusCode: response.status,
        message: data.error?.message || 'Authentication failed'
      });
    }

    // Return the Firebase auth token and user info
    return {
      success: true,
      idToken: data.idToken,
      refreshToken: data.refreshToken,
      expiresIn: data.expiresIn,
      message: 'Firebase authentication successful'
    };
  } catch (error) {
    console.error('Error in Firebase authentication:', error);
    throw createError({
      statusCode: error.statusCode || 500,
      message: error.message || 'Internal server error'
    });
  }
});
