/**
 * Add Record Tab JavaScript - Vue-based Invoice System Implementation
 */

// ============================================================================
// GST API CONFIGURATION
// ============================================================================
// To enable real GST verification, get a RapidAPI key:
// 1. Sign up at https://rapidapi.com
// 2. Subscribe to a GST verification API (many have free tiers)
// 3. Replace 'YOUR_RAPIDAPI_KEY_HERE' with your actual API key
// 4. Recommended APIs:
//    - Powerful GSTIN Tool: https://rapidapi.com/velocityhub/api/powerful-gstin-tool
//    - Verification Solutions: https://rapidapi.com/idfy-idfy-default/api/verification-solutions
// ============================================================================
const GST_API_CONFIG = {
    // RapidAPI key removed - was used for removed news reader functionality
    enableDemo: false, // Demo mode disabled
    enableLogging: true // Set to false to disable API logging
};

// Global variables for invoice functionality
let billForm = {
    type: '',
    bno: '',
    bdate: new Date().toISOString().slice(0, 10),
    partyName: '',
    partyAddress: '',
    partyGstin: '',
    partyState: '',
    partyPin: '',
    gtot: 0,
    cgst: 0,
    sgst: 0,
    igst: 0,
    rof: 0,
    ntot: 0,
    orderNo: '',
    orderDate: '',
    dispatchThrough: '',
    docketNo: '',
    vehicleNo: '',
    consigneeName: '',
    consigneeGstin: '',
    consigneeAddress: '',
    consigneeState: '',
    consigneePin: '',
    reasonForNote: '',
    originalBillNo: '',
    originalBillDate: '',
    narration: '',
    oth_chg: [],
    stockItems: []
};

// State variables
let isEditMode = false;
let isSubmitting = false;
let invoiceSubmitted = false;
let currentFocusSection = 'billInfo';
let currentTableFocus = { table: null, row: -1, col: -1 };
let columnVisibility = {
    project: true,
    disc: true,
    cgst: true,
    sgst: true,
    igst: true,
    mrp: true,
    expiryDate: true
};

// Database will be accessed via window.jsonDB
// Legacy inventoryData object for backward compatibility
let inventoryData = {
    get parties() {
        return window.jsonDB ? window.jsonDB.getParties() : [];
    },
    get stocks() {
        return window.jsonDB ? window.jsonDB.getStocks() : [];
    }
};

// Firm details
let firmState = 'Maharashtra';
let firmName = 'Sample Company';
let firmGst = '27SAMPLE1234F1Z5';

// Auto-save configuration for form data preservation
const AUTO_SAVE_CONFIG = {
    enabled: true,
    storageKey: 'invoiceFormDraft',
    saveInterval: 5000, // Auto-save every 5 seconds (less frequent)
    enableLogging: false // Disable logging to reduce console noise
};

// Auto-save timer
let autoSaveTimer = null;

// Predefined Other Charges for autocomplete
const predefinedOtherCharges = [
    {
        description: 'Transportation Charges',
        defaultAmount: 500,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Logistics'
    },
    {
        description: 'Freight Charges',
        defaultAmount: 300,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Logistics'
    },
    {
        description: 'Loading Charges',
        defaultAmount: 200,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Logistics'
    },
    {
        description: 'Unloading Charges',
        defaultAmount: 150,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Logistics'
    },
    {
        description: 'Packaging Charges',
        defaultAmount: 100,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Services'
    },
    {
        description: 'Handling Charges',
        defaultAmount: 75,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Services'
    },
    {
        description: 'Insurance Charges',
        defaultAmount: 250,
        defaultGstRate: 18,
        defaultHsn: '996711',
        category: 'Insurance'
    },
    {
        description: 'Installation Charges',
        defaultAmount: 1000,
        defaultGstRate: 18,
        defaultHsn: '998314',
        category: 'Services'
    },
    {
        description: 'Service Charges',
        defaultAmount: 500,
        defaultGstRate: 18,
        defaultHsn: '998314',
        category: 'Services'
    },
    {
        description: 'Delivery Charges',
        defaultAmount: 100,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Logistics'
    },
    {
        description: 'Round Trip Charges',
        defaultAmount: 800,
        defaultGstRate: 18,
        defaultHsn: '996511',
        category: 'Logistics'
    },
    {
        description: 'Miscellaneous Charges',
        defaultAmount: 50,
        defaultGstRate: 18,
        defaultHsn: '999999',
        category: 'Others'
    }
];

/**
 * Initialize add record tab - Load content and setup
 */
function initializeAddRecord() {
    console.log('Initializing Vue-based Invoice System...');

    // Load the HTML content
    loadAddRecordContent();

    // Setup after content is loaded
    setTimeout(() => {
        setupInitialData();
        setupEventHandlers();
        populateStateDropdowns();
        populatePartyList();
        initializeJqGrids();
        calculateBillTotal();

        // Initialize auto-save system after everything is loaded
        setTimeout(() => {
            initializeAutoSaveSystem();
        }, 500);
    }, 100);
}

/**
 * Load add record content - embedded HTML to avoid CORS issues
 */
function loadAddRecordContent() {
    const addRecordContainer = document.getElementById('add-record');

    const htmlContent = `
<!-- Compact Invoice System with jqGrid -->
<div class="invoice-container">
    <!-- Header with Action Buttons -->
    <div class="invoice-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <h2 id="pageTitle">Invoice Management System</h2>

        <!-- Action Buttons - Moved to Top -->
        <div style="display: flex; gap: 8px;">
            <button type="button" id="submitBillBtn" class="ui-button ui-widget ui-corner-all ui-button-success" title="Save Invoice (Alt+S)" style="font-size: 12px; padding: 6px 12px;">
                💾 Save Invoice
            </button>
            <button type="button" id="resetBillBtn" class="ui-button ui-widget ui-corner-all" title="Reset Form (Alt+R)" style="font-size: 12px; padding: 6px 12px;">
                🔄 Reset
            </button>
            <button type="button" id="newInvoiceBtn" class="ui-button ui-widget ui-corner-all ui-button-primary" style="display: none; font-size: 12px; padding: 6px 12px;" title="New Invoice (Alt+N)">
                📄 New Invoice
            </button>
            <button type="button" onclick="showKeyboardShortcutsModal()" class="ui-button ui-widget ui-corner-all" title="Keyboard Shortcuts (F1)" style="font-size: 12px; padding: 6px 12px;">
                ❓ Help
            </button>
        </div>
    </div>

    <!-- Bill Information Section -->
    <div class="section-container">
        <div class="section-header-compact">Bill Information</div>
        <div class="section-content-compact">
            <div class="form-row">
                <div class="form-group-compact">
                    <label for="billType">Type:</label>
                    <select id="billType" required>
                        <option value="">Select</option>
                        <option value="SALES">Sales</option>
                        <option value="PURCHASE">Purchase</option>
                        <option value="CREDIT NOTE">Credit Note</option>
                        <option value="DEBIT NOTE">Debit Note</option>
                    </select>
                </div>
                <div class="form-group-compact">
                    <label for="billNumber">Bill No:</label>
                    <input type="text" id="billNumber" placeholder="Bill number" required>
                </div>
                <div class="form-group-compact">
                    <label for="billDate">Date:</label>
                    <input type="date" id="billDate" required>
                </div>
            </div>
        </div>
    </div>

    <!-- Party Details Section -->
    <div class="section-container">
        <div class="section-header-compact">Party Details</div>
        <div class="section-content-compact">
            <div class="form-row">
                <div class="form-group-compact">
                    <label for="partyName">Party:</label>
                    <div style="display: flex; gap: 5px;">
                        <input type="text" id="partyName" placeholder="Select party" list="partyList" required style="flex: 1;">
                        <button type="button" class="ui-button ui-widget ui-corner-all" onclick="showPartyModal()" title="Add New Party (Alt+C)" style="padding: 4px 8px; font-size: 11px;">+</button>
                    </div>
                    <datalist id="partyList"></datalist>
                </div>
                <div class="form-group-compact">
                    <label for="partyGstin">GSTIN:</label>
                    <div style="display: flex; gap: 5px;">
                        <input type="text" id="partyGstin" placeholder="GSTIN" style="flex: 1;">
                        <button type="button" class="ui-button ui-widget ui-corner-all" onclick="fetchPartyByGST(this)" title="Fetch Party Details by GST" style="padding: 4px 8px; font-size: 11px;">🔍</button>
                    </div>
                </div>
                <div class="form-group-compact">
                    <label for="partyState">State:</label>
                    <select id="partyState">
                        <option value="">Select State</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group-compact">
                    <label for="partyAddress">Address:</label>
                    <textarea id="partyAddress" placeholder="Party address"></textarea>
                </div>
                <div class="form-group-compact">
                    <label for="partyPin">PIN:</label>
                    <input type="text" id="partyPin" placeholder="PIN Code">
                </div>
            </div>
        </div>
    </div>

    <!-- Stock Items Section with jqGrid -->
    <div class="section-container">
        <div class="section-header-compact">
            Stock Items
            <div style="float: right;">
                <button type="button" id="addItemBtn" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px;">Add Item</button>
                <button type="button" onclick="manualCalculateTable()" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px; background: #ff9800; color: white;" title="Calculate Table">🧮 Calculate</button>
                <button type="button" onclick="showStockItemModal()" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px;" title="Add New Stock Item">+ Stock</button>
                <button type="button" onclick="showColumnVisibilityModal()" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px;" title="Column Settings">⚙️</button>
            </div>
        </div>
        <div class="section-content-compact">
            <table id="stockItemsGrid"></table>
            <div id="stockItemsPager"></div>
        </div>
    </div>

    <!-- Other Charges Section with jqGrid -->
    <div class="section-container">
        <div class="section-header-compact">
            Other Charges
            <div style="float: right;">
                <button type="button" id="addChargeBtn" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px;">Add Charge</button>
                <button type="button" onclick="manualCalculateTable()" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px; background: #ff9800; color: white;" title="Calculate Charges">🧮 Calculate</button>
                <button type="button" onclick="showOtherChargesModal()" class="ui-button ui-widget ui-corner-all" style="font-size: 10px; padding: 2px 8px;" title="Add Other Charge (Alt+O)">+ Charge</button>
            </div>
        </div>
        <div class="section-content-compact">
            <table id="otherChargesGrid"></table>
            <div id="otherChargesPager"></div>
        </div>
    </div>

    <!-- Amount Summary Section -->
    <div class="section-container">
        <div class="section-header-compact">Amount Summary</div>
        <div class="section-content-compact">
            <div class="form-row">
                <div class="form-group-compact">
                    <label>Gross Total:</label>
                    <span id="grossTotal" style="font-weight: bold;">₹0.00</span>
                </div>
                <div class="form-group-compact">
                    <label>CGST:</label>
                    <span id="cgstTotal" style="font-weight: bold;">₹0.00</span>
                </div>
                <div class="form-group-compact">
                    <label>SGST:</label>
                    <span id="sgstTotal" style="font-weight: bold;">₹0.00</span>
                </div>
                <div class="form-group-compact">
                    <label>IGST:</label>
                    <span id="igstTotal" style="font-weight: bold;">₹0.00</span>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group-compact">
                    <label>Round Off:</label>
                    <span id="roundOff" style="font-weight: bold;">₹0.00</span>
                </div>
                <div class="form-group-compact">
                    <label style="color: #0066cc;">Net Total:</label>
                    <span id="netTotal" style="font-weight: bold; color: #0066cc; font-size: 14px;">₹0.00</span>
                </div>
            </div>
        </div>
    </div>


</div>

<!-- Party Modal -->
<div id="partyModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Add New Party</h3>
            <button type="button" class="modal-close" onclick="closePartyModal()">×</button>
        </div>
        <div class="modal-body">
            <form id="partyForm">
                <div class="form-group">
                    <label for="modalPartyName">Party Name *</label>
                    <input type="text" id="modalPartyName" class="form-input" required>
                </div>
                <div class="form-group">
                    <label for="modalPartyAddress">Address</label>
                    <textarea id="modalPartyAddress" class="form-input" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label for="modalPartyGstin">GSTIN</label>
                    <div style="display: flex; gap: 5px;">
                        <input type="text" id="modalPartyGstin" class="form-input" placeholder="Enter 15-digit GSTIN" style="flex: 1;">
                        <button type="button" class="ui-button ui-widget ui-corner-all" onclick="fetchPartyByGSTModal(this)" title="Fetch Party Details by GST" style="padding: 4px 8px; font-size: 11px;">🔍</button>
                    </div>
                </div>
                <div class="form-group">
                    <label for="modalPartyState">State</label>
                    <select id="modalPartyState" class="form-input">
                        <option value="">Select State</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="modalPartyPin">PIN Code</label>
                    <input type="text" id="modalPartyPin" class="form-input">
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="closePartyModal()">Cancel</button>
                    <button type="submit" class="btn-primary">Add Party</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Stock Item Modal -->
<div id="stockItemModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Add New Stock Item</h3>
            <button type="button" class="modal-close" onclick="closeStockItemModal()">×</button>
        </div>
        <div class="modal-body">
            <form id="stockItemForm">
                <div class="form-group">
                    <label for="modalItemName">Item Name *</label>
                    <input type="text" id="modalItemName" class="form-input" required>
                </div>
                <div class="form-group">
                    <label for="modalHsn">HSN Code</label>
                    <input type="text" id="modalHsn" class="form-input">
                </div>
                <div class="form-group">
                    <label for="modalBatch">Batch</label>
                    <input type="text" id="modalBatch" class="form-input">
                </div>
                <div class="form-group">
                    <label for="modalOem">OEM</label>
                    <input type="text" id="modalOem" class="form-input">
                </div>
                <div class="form-group">
                    <label for="modalPno">Part Number</label>
                    <input type="text" id="modalPno" class="form-input">
                </div>
                <div class="form-group">
                    <label for="modalUom">UOM</label>
                    <select id="modalUom" class="form-input">
                        <option value="Pcs">Pcs</option>
                        <option value="Kg">Kg</option>
                        <option value="Ltr">Ltr</option>
                        <option value="Box">Box</option>
                        <option value="Mtr">Mtr</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="modalRate">Rate</label>
                    <input type="number" id="modalRate" class="form-input" step="0.01">
                </div>
                <div class="form-group">
                    <label for="modalGstRate">GST Rate (%)</label>
                    <input type="number" id="modalGstRate" class="form-input" step="0.01" value="18">
                </div>
                <div class="form-group">
                    <label for="modalMrp">MRP</label>
                    <input type="number" id="modalMrp" class="form-input" step="0.01">
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="closeStockItemModal()">Cancel</button>
                    <button type="submit" class="btn-primary">Add Item</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Other Charges Modal -->
<div id="otherChargesModal" class="modal" style="display: none;">
    <div class="modal-content" style="width: 600px; max-width: 90vw;">
        <div class="modal-header">
            <h3>Add Other Charge</h3>
            <button type="button" class="modal-close" onclick="closeOtherChargesModal()">×</button>
        </div>
        <div class="modal-body">
            <form id="otherChargesForm">
                <div class="form-group">
                    <label for="modalChargeDescription">Description *</label>
                    <input type="text" id="modalChargeDescription" class="form-input" required
                           placeholder="Start typing to see suggestions..."
                           autocomplete="off">
                    <small class="form-help">Common charges: Transportation, Freight, Loading, Installation, etc.</small>
                </div>
                <div class="form-row" style="display: flex; gap: 15px;">
                    <div class="form-group" style="flex: 1;">
                        <label for="modalChargeAmount">Amount *</label>
                        <input type="number" id="modalChargeAmount" class="form-input" step="0.01" required
                               placeholder="0.00" min="0">
                    </div>
                    <div class="form-group" style="flex: 1;">
                        <label for="modalChargeGstRate">GST Rate (%)</label>
                        <input type="number" id="modalChargeGstRate" class="form-input" step="0.01" value="18"
                               placeholder="18.00" min="0" max="100">
                    </div>
                </div>
                <div class="form-group">
                    <label for="modalChargeHsn">HSN Code</label>
                    <input type="text" id="modalChargeHsn" class="form-input"
                           placeholder="e.g., 996511 for Transportation">
                </div>
                <div class="form-group">
                    <label>Preview</label>
                    <div id="chargePreview" style="background: #f8f9fa; padding: 10px; border-radius: 4px; border: 1px solid #e9ecef;">
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>Amount:</span>
                            <span id="previewAmount">₹0.00</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; margin-bottom: 5px;">
                            <span>GST (<span id="previewGstRate">18</span>%):</span>
                            <span id="previewGst">₹0.00</span>
                        </div>
                        <div style="display: flex; justify-content: space-between; font-weight: bold; border-top: 1px solid #dee2e6; padding-top: 5px;">
                            <span>Total:</span>
                            <span id="previewTotal">₹0.00</span>
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="button" class="btn-secondary" onclick="closeOtherChargesModal()">Cancel</button>
                    <button type="button" class="btn-secondary" onclick="testOtherChargesAutocomplete()" style="background: #17a2b8; color: white;">Test Autocomplete</button>
                    <button type="submit" class="btn-primary">Add Charge</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Column Visibility Modal -->
<div id="columnVisibilityModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Column Visibility Settings</h3>
            <button type="button" class="modal-close" onclick="closeColumnVisibilityModal()">×</button>
        </div>
        <div class="modal-body">
            <div class="checkbox-grid">
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleProject" checked>
                    <label for="toggleProject">Project</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleDisc" checked>
                    <label for="toggleDisc">Discount %</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleCgst" checked>
                    <label for="toggleCgst">CGST</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleSgst" checked>
                    <label for="toggleSgst">SGST</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleIgst" checked>
                    <label for="toggleIgst">IGST</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleMrp" checked>
                    <label for="toggleMrp">MRP</label>
                </div>
                <div class="checkbox-item">
                    <input type="checkbox" id="toggleExpiryDate" checked>
                    <label for="toggleExpiryDate">Expiry Date</label>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-secondary" onclick="closeColumnVisibilityModal()">Cancel</button>
                <button type="button" class="btn-primary" onclick="applyColumnVisibility()">Apply</button>
            </div>
        </div>
    </div>
</div>

<!-- Keyboard Shortcuts Modal -->
<div id="keyboardShortcutsModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Keyboard Shortcuts</h3>
            <button type="button" class="modal-close" onclick="closeKeyboardShortcutsModal()">×</button>
        </div>
        <div class="modal-body">
            <div class="shortcuts-grid">
                <div class="shortcut-section">
                    <h4>Navigation</h4>
                    <div class="shortcut-item">
                        <span>Focus Bill Info</span>
                        <kbd>Alt + 1</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Focus Party Details</span>
                        <kbd>Alt + 2</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Focus Stock Items</span>
                        <kbd>Alt + 4</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Focus Other Charges</span>
                        <kbd>Alt + 5</kbd>
                    </div>
                </div>
                <div class="shortcut-section">
                    <h4>Actions</h4>
                    <div class="shortcut-item">
                        <span>Submit Form</span>
                        <kbd>Alt + S</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Reset Form</span>
                        <kbd>Alt + R</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>New Invoice</span>
                        <kbd>Alt + N</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Add Row</span>
                        <kbd>Alt + A</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Create Party</span>
                        <kbd>Alt + C</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Other Charges</span>
                        <kbd>Alt + O</kbd>
                    </div>
                    <div class="shortcut-item">
                        <span>Show Help</span>
                        <kbd>F1</kbd>
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button type="button" class="btn-primary" onclick="closeKeyboardShortcutsModal()">Close</button>
            </div>
        </div>
    </div>
</div>
    `;

    addRecordContainer.innerHTML = htmlContent;
}

/**
 * Setup initial form data
 */
function setupInitialData() {
    // Set current date
    const today = new Date().toISOString().slice(0, 10);
    const billDateInput = document.getElementById('billDate');
    if (billDateInput) {
        billDateInput.value = today;
        billForm.bdate = today;
    }

    // Update page title
    updatePageTitle();
}

/**
 * Synchronize jqGrid data with billForm arrays
 */
function syncGridDataWithBillForm() {
    console.log('Synchronizing grid data with billForm...');
    console.log('Current billForm.stockItems before sync:', billForm.stockItems);

    try {
        // Sync Stock Items Grid
        const stockGridData = $("#stockItemsGrid").jqGrid('getRowData');
        console.log('Stock grid data retrieved:', stockGridData);
        console.log('Stock grid data length:', stockGridData ? stockGridData.length : 'null');

        if (stockGridData && stockGridData.length > 0) {
            billForm.stockItems = stockGridData.map((row, index) => {
                // Parse numeric values properly
                const qty = parseFloat(row.qty) || 0;
                const rate = parseFloat(row.rate) || 0;
                const disc = parseFloat(row.disc) || 0;
                const grate = parseFloat(row.grate) || 18;

                // Calculate GST amounts
                const subtotal = qty * rate;
                const discountAmount = (subtotal * disc) / 100;
                const taxableAmount = subtotal - discountAmount;
                const gstAmount = (taxableAmount * grate) / 100;
                const cgst = gstAmount / 2;
                const sgst = gstAmount / 2;
                const total = taxableAmount + gstAmount;

                return {
                    item: row.item || '',
                    hsn: row.hsn || '',
                    batch: row.batch || '',
                    qty: qty,
                    oem: row.oem || '',
                    pno: row.pno || '',
                    uom: row.uom || 'Pcs',
                    rate: rate,
                    disc: disc,
                    grate: grate,
                    cgst: cgst,
                    sgst: sgst,
                    igst: 0, // Assuming intra-state
                    total: total,
                    mrp: parseFloat(row.mrp) || 0,
                    expiryDate: row.expiryDate || null
                };
            });
        }

        // Sync Other Charges Grid
        const chargesGridData = $("#otherChargesGrid").jqGrid('getRowData');
        console.log('Charges grid data:', chargesGridData);

        if (chargesGridData && chargesGridData.length > 0) {
            billForm.oth_chg = chargesGridData.map(row => {
                const amount = parseFloat(row.oth_amt) || 0;
                const grate = parseFloat(row.oth_grate) || 18;
                const gstAmount = (amount * grate) / 100;
                const cgst = gstAmount / 2;
                const sgst = gstAmount / 2;
                const total = amount + gstAmount;

                return {
                    description: row.description || '',
                    oth_amt: amount,
                    oth_grate: grate,
                    oth_cgst: cgst,
                    oth_sgst: sgst,
                    oth_igst: 0, // Assuming intra-state
                    oth_hsn: row.oth_hsn || '',
                    oth_tot: total
                };
            });
        }

        console.log('Synchronized billForm.stockItems:', billForm.stockItems);
        console.log('Synchronized billForm.oth_chg:', billForm.oth_chg);

    } catch (error) {
        console.error('Error synchronizing grid data:', error);
    }
}

/**
 * Initialize jqGrid tables
 */
function initializeJqGrids() {
    // Initialize Stock Items Grid
    $("#stockItemsGrid").jqGrid({
        datatype: "local",
        data: billForm.stockItems,
        onSelectRow: function(rowid) {
            console.log('Stock item row selected:', rowid);
        },

        colNames: ['Item Name', 'HSN', 'Batch', 'Qty', 'OEM', 'P.No', 'UOM', 'Rate', 'Disc%', 'CGST', 'SGST', 'IGST', 'GST%', 'Total', 'Actions'],
        colModel: [
            { name: 'item', index: 'item', width: 150, editable: true, edittype: 'text',
              editoptions: {
                  dataInit: function(elem) {
                      console.log('Initializing autocomplete for element:', elem);

                      // Enhanced autocomplete with stock item details
                      $(elem).autocomplete({
                          source: function(request, response) {
                              console.log('Autocomplete source called with term:', request.term);
                              const stockItems = getStockItemsForAutocomplete();
                              console.log('Available stock items:', stockItems);

                              // If no search term, show all items
                              if (!request.term || request.term.trim() === '') {
                                  response(stockItems);
                              } else {
                                  const filtered = stockItems.filter(item =>
                                      item.label.toLowerCase().includes(request.term.toLowerCase())
                                  );
                                  console.log('Filtered items:', filtered);
                                  response(filtered);
                              }
                          },
                          minLength: 0,
                          select: function(event, ui) {
                              console.log('Item selected from autocomplete:', ui.item);

                              // Check if we're in a modal dialog or inline editing
                              const isInModal = $(elem).closest('.ui-jqdialog').length > 0;
                              console.log('Is in modal:', isInModal);

                              if (isInModal) {
                                  // Modal dialog - auto-fill modal fields
                                  const form = $(elem).closest('form');
                                  if (ui.item.stockData) {
                                      setTimeout(() => {
                                          autoFillModalFields(form, ui.item.stockData);
                                      }, 100);
                                  }
                              } else {
                                  // Inline editing - auto-fill grid row
                                  const rowId = $(elem).closest('tr').attr('id');
                                  if (rowId && ui.item.stockData) {
                                      setTimeout(() => {
                                          autoFillStockItemInGrid(rowId, ui.item.stockData);
                                      }, 100);
                                  }
                              }
                              return true;
                          },
                          focus: function(event, ui) {
                              return false; // Prevent value change on focus
                          }
                      }).autocomplete("instance")._renderItem = function(ul, item) {
                          return $("<li>")
                              .append("<div><strong>" + item.label + "</strong><br>" +
                                     "<small>HSN: " + (item.stockData.hsn || 'N/A') +
                                     " | Rate: ₹" + (item.stockData.rate || 0) +
                                     " | GST: " + (item.stockData.grate || 0) + "%</small></div>")
                              .appendTo(ul);
                      };

                      console.log('Autocomplete initialized for element');
                  }
              }
            },
            { name: 'hsn', index: 'hsn', width: 80, editable: true },
            { name: 'batch', index: 'batch', width: 80, editable: true },
            { name: 'qty', index: 'qty', width: 60, editable: true, formatter: 'number', formatoptions: { decimalPlaces: 2 } },
            { name: 'oem', index: 'oem', width: 80, editable: true },
            { name: 'pno', index: 'pno', width: 80, editable: true },
            { name: 'uom', index: 'uom', width: 60, editable: true },
            { name: 'rate', index: 'rate', width: 80, editable: true, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'disc', index: 'disc', width: 60, editable: true, formatter: 'number', formatoptions: { decimalPlaces: 2 } },
            { name: 'cgst', index: 'cgst', width: 70, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'sgst', index: 'sgst', width: 70, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'igst', index: 'igst', width: 70, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'grate', index: 'grate', width: 60, editable: true, formatter: 'number', formatoptions: { decimalPlaces: 2 } },
            { name: 'total', index: 'total', width: 100, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'actions', index: 'actions', width: 80, editable: false, sortable: false,
              formatter: function(cellvalue, options, rowObject) {
                  const rowId = options.rowId;
                  return '<button type="button" class="ui-button ui-widget ui-corner-all delete-row-btn" ' +
                         'onclick="deleteStockItemRow(\'' + rowId + '\')" ' +
                         'style="padding: 2px; min-width: 20px; height: 20px;" ' +
                         'title="Delete this row">' +
                         '<span class="ui-icon ui-icon-trash"></span>' +
                         '</button>';
              }
            }
        ],
        rowNum: 20,
        rowList: [10, 20, 30],
        pager: '#stockItemsPager',
        sortname: 'item',
        viewrecords: true,
        sortorder: "asc",
        caption: "",
        height: 200,
        autowidth: true,
        shrinkToFit: true,
        multiselect: false,
        multiboxonly: false,
        editurl: 'clientArray',
        cellEdit: false,
        cellsubmit: 'clientArray',
        onSelectRow: function(rowid) {
            // Handle row selection
            console.log('Selected row:', rowid);
            // Store the selected row for deletion
            $("#stockItemsGrid").data('selectedRow', rowid);
        },
        ondblClickRow: function(rowid) {
            // Enable editing on double-click
            $("#stockItemsGrid").jqGrid('editRow', rowid, {
                keys: true,
                oneditfunc: function() {
                    console.log('Started editing row:', rowid);
                },
                successfunc: function() {
                    console.log('Finished editing row:', rowid);
                    // Recalculate totals
                    const rowIndex = $("#stockItemsGrid").jqGrid('getDataIDs').indexOf(rowid);
                    if (rowIndex >= 0) {
                        calculateItemTotal(rowIndex);
                    }
                    return true;
                }
            });
        },
        afterEditCell: function() {
            // Handle cell editing
        },
        afterSaveCell: function(rowid, cellname, value, iRow) {
            // Recalculate when cell is saved
            calculateItemTotal(iRow - 1);
        }
    });

    // Add jqGrid Navigator with CRUD dialogs for Stock Items
    $("#stockItemsGrid").jqGrid('navGrid', '#stockItemsPager', {
        edit: true,
        add: true,
        del: true,
        search: true,
        refresh: true,
        view: false,
        position: "left",
        cloneToTop: false
    }, {
        // Edit options
        width: 800,
        height: 480,
        reloadAfterSubmit: false,
        jqModal: true,
        modal: true,
        closeOnEscape: true,
        resize: true,
        drag: true,
        bottominfo: "Fields marked with (*) are required",
        checkOnSubmit: true,
        checkOnUpdate: true,
        savekey: [true, 13],
        navkeys: [true, 38, 40],
        bSubmit: "Submit",
        bCancel: "Cancel",
        beforeShowForm: function(form) {
            // Initialize autocomplete for item field in edit modal
            setTimeout(() => {
                console.log('Edit modal beforeShowForm called');
                initializeModalAutocomplete(form);

                // Also try direct initialization
                const itemInput = form.find('input[name="item"]');
                if (itemInput.length > 0) {
                    console.log('Found item input in edit modal, initializing autocomplete directly');
                    setupDirectAutocomplete(itemInput[0]);
                }

                // Center the modal
                centerModal(form);
            }, 200);
        },
        afterSubmit: function(response, postdata) {
            console.log('=== EDIT MODAL AFTERSUBMIT CALLED ===');
            console.log('Stock item edit afterSubmit called with postdata:', postdata);

            // Use the same approach as manual calculation
            setTimeout(() => {
                console.log('Auto-calculation: Syncing grid data and calculating...');

                // Sync jqGrid data to billForm (same as manual)
                syncGridDataToBillForm();

                // Calculate totals (same as manual)
                calculateBillTotal();

                console.log('Auto-calculation completed after edit');
            }, 100);

            return [true, ""];
        }
    }, {
        // Add options
        width: 800,
        height: 480,
        reloadAfterSubmit: false,
        jqModal: true,
        modal: true,
        closeOnEscape: true,
        resize: true,
        drag: true,
        bottominfo: "Fields marked with (*) are required",
        checkOnSubmit: true,
        savekey: [true, 13],
        navkeys: [true, 38, 40],
        bSubmit: "Submit",
        bCancel: "Cancel",
        beforeShowForm: function(form) {
            // Initialize autocomplete for item field in add modal
            console.log('Add modal beforeShowForm called with form:', form);

            setTimeout(() => {
                console.log('Setting up autocomplete in add modal...');

                // Try multiple approaches to find and setup autocomplete
                const itemInput = form.find('input[name="item"]');
                console.log('Found item inputs:', itemInput.length);

                if (itemInput.length > 0) {
                    console.log('Setting up autocomplete on item input');

                    // Method 1: Direct setup
                    setupDirectAutocomplete(itemInput[0]);

                    // Method 2: Add focus event to show all items when focused
                    itemInput.on('focus', function() {
                        console.log('Item input focused, triggering autocomplete');
                        $(this).autocomplete('search', '');
                    });

                    // Method 3: Add a visible indicator that autocomplete is ready
                    itemInput.attr('placeholder', 'Click here or type to search items...');
                    itemInput.attr('title', 'Click to see all items or start typing to search');
                }

                // Also try the original method
                initializeModalAutocomplete(form);

                // Center the modal
                centerModal(form);
            }, 300);
        },
        afterSubmit: function(response, postdata) {
            console.log('=== ADD MODAL AFTERSUBMIT CALLED ===');
            console.log('Stock item add afterSubmit called with postdata:', postdata);

            // Use the same approach as manual calculation
            setTimeout(() => {
                console.log('Auto-calculation: Syncing grid data and calculating...');

                // Sync jqGrid data to billForm (same as manual)
                syncGridDataToBillForm();

                // Calculate totals (same as manual)
                calculateBillTotal();

                console.log('Auto-calculation completed after add');
            }, 100);

            return [true, ""];
        }
    }, {
        // Delete options
        reloadAfterSubmit: false,
        jqModal: true,
        closeOnEscape: true,
        msg: "Delete selected item?",
        afterSubmit: function(response, postdata) {
            calculateBillTotal();
            return [true, ""];
        }
    });

    // Initialize Other Charges Grid
    $("#otherChargesGrid").jqGrid({
        datatype: "local",
        data: billForm.oth_chg,
        colNames: ['Description', 'Amount', 'GST%', 'CGST', 'SGST', 'IGST', 'HSN', 'Total', 'Actions'],
        colModel: [
            { name: 'description', index: 'description', width: 150, editable: true,
              edittype: 'text',
              editoptions: {
                  dataInit: function(elem) {
                      console.log('Initializing autocomplete for other charges description:', elem);

                      // Setup autocomplete for other charges description
                      $(elem).autocomplete({
                          source: function(request, response) {
                              const term = request.term.toLowerCase();
                              const allCharges = getOtherChargesForAutocomplete();
                              const filtered = allCharges.filter(charge =>
                                  charge.description.toLowerCase().includes(term) ||
                                  charge.category.toLowerCase().includes(term)
                              ).map(charge => ({
                                  label: charge.description,
                                  value: charge.description,
                                  chargeData: charge
                              }));

                              // Allow custom input
                              if (filtered.length === 0 && request.term.trim() !== '') {
                                  filtered.push({
                                      label: `"${request.term}" (Custom)`,
                                      value: request.term,
                                      chargeData: null
                                  });
                              }

                              response(filtered);
                          },
                          minLength: 0,
                          select: function(event, ui) {
                              console.log('Other charge selected in grid:', ui.item);

                              // Auto-fill other fields in the grid row if predefined charge
                              if (ui.item.chargeData) {
                                  setTimeout(() => {
                                      autoFillOtherChargeInGrid(elem, ui.item.chargeData);
                                  }, 100);
                              }
                              return true;
                          },
                          focus: function(event, ui) {
                              return false;
                          }
                      }).autocomplete("instance")._renderItem = function(ul, item) {
                          const chargeData = item.chargeData;
                          let html = `<div><strong>${item.label}</strong>`;

                          if (chargeData) {
                              const categoryColor = chargeData.isFromDatabase ? '#2563eb' : '#059669';
                              const categoryIcon = chargeData.isFromDatabase ? '🕒' : '⚡';

                              html += `<br><small style="color: ${categoryColor};">
                                  ${categoryIcon} ${chargeData.category} | ₹${chargeData.defaultAmount} |
                                  ${chargeData.defaultGstRate}% GST`;

                              if (chargeData.defaultHsn) {
                                  html += ` | HSN: ${chargeData.defaultHsn}`;
                              }

                              html += '</small>';
                          }

                          html += '</div>';

                          return $("<li>").append(html).appendTo(ul);
                      };

                      console.log('Other charges description autocomplete initialized');
                  }
              }
            },
            { name: 'oth_amt', index: 'oth_amt', width: 80, editable: true, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'oth_grate', index: 'oth_grate', width: 60, editable: true, formatter: 'number', formatoptions: { decimalPlaces: 2 } },
            { name: 'oth_cgst', index: 'oth_cgst', width: 70, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'oth_sgst', index: 'oth_sgst', width: 70, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'oth_igst', index: 'oth_igst', width: 70, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'oth_hsn', index: 'oth_hsn', width: 80, editable: true },
            { name: 'oth_tot', index: 'oth_tot', width: 100, editable: false, formatter: 'currency', formatoptions: { prefix: '₹' } },
            { name: 'actions', index: 'actions', width: 80, editable: false, sortable: false,
              formatter: function(cellvalue, options, rowObject) {
                  const rowId = options.rowId;
                  return '<button type="button" class="ui-button ui-widget ui-corner-all delete-row-btn" ' +
                         'onclick="deleteOtherChargeRow(\'' + rowId + '\')" ' +
                         'style="padding: 2px; min-width: 20px; height: 20px;" ' +
                         'title="Delete this row">' +
                         '<span class="ui-icon ui-icon-trash"></span>' +
                         '</button>';
              }
            }
        ],
        rowNum: 10,
        rowList: [5, 10, 15],
        pager: '#otherChargesPager',
        sortname: 'description',
        viewrecords: true,
        sortorder: "asc",
        caption: "",
        height: 150,
        autowidth: true,
        shrinkToFit: true,
        multiselect: false,
        multiboxonly: false,
        editurl: 'clientArray',
        cellEdit: false,
        cellsubmit: 'clientArray',
        onSelectRow: function(rowid) {
            // Handle row selection
            console.log('Selected charge row:', rowid);
            // Store the selected row for deletion
            $("#otherChargesGrid").data('selectedRow', rowid);
        },
        ondblClickRow: function(rowid) {
            // Enable editing on double-click
            $("#otherChargesGrid").jqGrid('editRow', rowid, {
                keys: true,
                oneditfunc: function() {
                    console.log('Started editing charge row:', rowid);
                },
                successfunc: function() {
                    console.log('Finished editing charge row:', rowid);
                    // Recalculate totals
                    const rowIndex = $("#otherChargesGrid").jqGrid('getDataIDs').indexOf(rowid);
                    if (rowIndex >= 0) {
                        calculateChargeTotal(rowIndex);
                    }
                    return true;
                }
            });
        },
        afterSaveCell: function(rowid, cellname, value, iRow) {
            // Recalculate when cell is saved
            calculateChargeTotal(iRow - 1);
        }
    });

    // Remove duplicate navGrid configuration - already configured above

    $("#otherChargesGrid").jqGrid('navGrid', '#otherChargesPager', {
        edit: true,
        add: true,
        del: true,
        search: true,
        refresh: true,
        view: false,
        position: "left",
        cloneToTop: false
    }, {
        // Edit options
        width: 700,
        height: 400,
        reloadAfterSubmit: false,
        jqModal: true,
        modal: true,
        closeOnEscape: true,
        resize: true,
        drag: true,
        bottominfo: "Fields marked with (*) are required",
        checkOnSubmit: true,
        checkOnUpdate: true,
        savekey: [true, 13],
        navkeys: [true, 38, 40],
        bSubmit: "Submit",
        bCancel: "Cancel",
        afterSubmit: function(response, postdata) {
            console.log('=== OTHER CHARGES EDIT MODAL AFTERSUBMIT CALLED ===');
            console.log('Other charge edit afterSubmit called with postdata:', postdata);

            // Use the same approach as manual calculation
            setTimeout(() => {
                console.log('Auto-calculation: Syncing grid data and calculating...');

                // Sync jqGrid data to billForm (same as manual)
                syncGridDataToBillForm();

                // Calculate totals (same as manual)
                calculateBillTotal();

                console.log('Auto-calculation completed after other charge edit');
            }, 100);

            return [true, ""];
        }
    }, {
        // Add options
        width: 700,
        height: 400,
        reloadAfterSubmit: false,
        jqModal: true,
        modal: true,
        closeOnEscape: true,
        resize: true,
        drag: true,
        bottominfo: "Fields marked with (*) are required",
        checkOnSubmit: true,
        savekey: [true, 13],
        navkeys: [true, 38, 40],
        bSubmit: "Submit",
        bCancel: "Cancel",
        afterSubmit: function(response, postdata) {
            console.log('=== OTHER CHARGES ADD MODAL AFTERSUBMIT CALLED ===');
            console.log('Other charge add afterSubmit called with postdata:', postdata);

            // Use the same approach as manual calculation
            setTimeout(() => {
                console.log('Auto-calculation: Syncing grid data and calculating...');

                // Sync jqGrid data to billForm (same as manual)
                syncGridDataToBillForm();

                // Calculate totals (same as manual)
                calculateBillTotal();

                console.log('Auto-calculation completed after other charge add');
            }, 100);

            return [true, ""];
        }
    }, {
        // Delete options
        reloadAfterSubmit: false,
        jqModal: true,
        closeOnEscape: true,
        msg: "Delete selected charge?",
        afterSubmit: function(response, postdata) {
            console.log('=== OTHER CHARGES DELETE AFTERSUBMIT CALLED ===');
            console.log('Other charge delete afterSubmit called with postdata:', postdata);

            // Use the same approach as manual calculation
            setTimeout(() => {
                console.log('Auto-calculation: Syncing grid data and calculating...');

                // Sync jqGrid data to billForm (same as manual)
                syncGridDataToBillForm();

                // Calculate totals (same as manual)
                calculateBillTotal();

                console.log('Auto-calculation completed after other charge delete');
            }, 100);

            return [true, ""];
        }
    });
}

/**
 * Update page title based on mode
 */
function updatePageTitle() {
    const titleElement = document.getElementById('pageTitle');
    if (titleElement) {
        titleElement.textContent = isEditMode ? 'Edit Invoice' : 'Create New Invoice';
    }
}

/**
 * Setup all event handlers
 */
function setupEventHandlers() {
    // Bill type change handler
    const billTypeSelect = document.getElementById('billType');
    if (billTypeSelect) {
        billTypeSelect.addEventListener('change', handleBillTypeChange);
    }

    // Party name change handler
    const partyNameInput = document.getElementById('partyName');
    if (partyNameInput) {
        partyNameInput.addEventListener('change', handlePartySelection);
        partyNameInput.addEventListener('keydown', handlePartyNameKeydown);
    }

    // Form input handlers
    setupFormInputHandlers();

    // Button handlers
    setupButtonHandlers();

    // Modal handlers
    setupModalHandlers();
}

/**
 * Setup form input change handlers
 */
function setupFormInputHandlers() {
    // Bill form fields
    const formFields = ['billNumber', 'billDate', 'partyAddress', 'partyGstin', 'partyState', 'partyPin'];
    formFields.forEach(fieldId => {
        const element = document.getElementById(fieldId);
        if (element) {
            element.addEventListener('change', (e) => {
                if (fieldId === 'billNumber') billForm.bno = e.target.value;
                else if (fieldId === 'billDate') billForm.bdate = e.target.value;
                else if (fieldId === 'partyAddress') billForm.partyAddress = e.target.value;
                else if (fieldId === 'partyGstin') billForm.partyGstin = e.target.value;
                else if (fieldId === 'partyState') billForm.partyState = e.target.value;
                else if (fieldId === 'partyPin') billForm.partyPin = e.target.value;
            });
        }
    });
}

/**
 * Setup button event handlers for compact design
 */
function setupButtonHandlers() {
    // Add item button for jqGrid
    const addItemBtn = document.getElementById('addItemBtn');
    if (addItemBtn) {
        addItemBtn.addEventListener('click', addStockItemToGrid);
    }

    // Add charge button for jqGrid
    const addChargeBtn = document.getElementById('addChargeBtn');
    if (addChargeBtn) {
        addChargeBtn.addEventListener('click', addOtherChargeToGrid);
    }

    // Submit bill button
    const submitBtn = document.getElementById('submitBillBtn');
    if (submitBtn) {
        submitBtn.addEventListener('click', submitBillForm);
    }

    // Reset bill button
    const resetBtn = document.getElementById('resetBillBtn');
    if (resetBtn) {
        resetBtn.addEventListener('click', resetBillForm);
    }

    // New invoice button
    const newInvoiceBtn = document.getElementById('newInvoiceBtn');
    if (newInvoiceBtn) {
        newInvoiceBtn.addEventListener('click', createNewInvoice);
    }
}



/**
 * Get stock items array for autocomplete (legacy)
 */
function getStockItemsArray() {
    if (window.jsonDB) {
        return window.jsonDB.getStocks().map(stock => stock.item);
    }
    return inventoryData.stocks.map(stock => stock.item);
}

/**
 * Get sample stock data with quantities
 */
function getSampleStockData() {
    return [
        {
            productId: 'P001',
            productName: 'Laptop Computer',
            category: 'Electronics',
            currentStock: 25,
            minStock: 10,
            unitPrice: 45000.00,
            totalValue: 1125000.00,
            lastUpdated: '2024-01-15'
        },
        {
            productId: 'P002',
            productName: 'Office Chair',
            category: 'Furniture',
            currentStock: 8,
            minStock: 15,
            unitPrice: 8000.00,
            totalValue: 64000.00,
            lastUpdated: '2024-01-14'
        },
        {
            productId: 'P003',
            productName: 'Wireless Mouse',
            category: 'Electronics',
            currentStock: 0,
            minStock: 20,
            unitPrice: 1500.00,
            totalValue: 0.00,
            lastUpdated: '2024-01-13'
        }
    ];
}

/**
 * Get stock items for enhanced autocomplete with details
 */
function getStockItemsForAutocomplete() {
    let stocks = [];

    if (window.jsonDB) {
        stocks = window.jsonDB.getStocks();
    } else {
        stocks = inventoryData.stocks;
    }

    // Get current stock quantities from stock report data
    const stockReportData = getSampleStockData();

    return stocks.map(stock => {
        // Find matching stock quantity from stock report
        const stockInfo = stockReportData.find(s => s.productName === stock.item);
        const currentStock = stockInfo ? stockInfo.currentStock : 0;
        const stockStatus = currentStock === 0 ? 'Out of Stock' :
                           currentStock <= 5 ? 'Low Stock' :
                           currentStock <= 20 ? 'Medium Stock' : 'In Stock';

        return {
            label: `${stock.item} | Qty: ${currentStock} ${stock.uom || 'Pcs'} | Rate: ₹${stock.rate || 0} | ${stockStatus}`,
            value: stock.item,
            stockData: {
                id: stock.id,
                item: stock.item,
                hsn: stock.hsn || '',
                pno: stock.pno || null, // ✅ Changed from '' to null
                oem: stock.oem || '',
                batch: stock.batch || null, // ✅ Changed from '' to null
                uom: stock.uom || 'Pcs',
                rate: stock.rate || 0,
                grate: stock.grate || 18,
                mrp: stock.mrp || 0,
                expiryDate: stock.expiryDate || null,
                currentStock: currentStock,
                stockStatus: stockStatus
            }
        };
    });
}

/**
 * Initialize autocomplete for jqGrid modal forms
 */
function initializeModalAutocomplete(form) {
    console.log('Initializing modal autocomplete for form:', form);

    try {
        // Find the item input field in the modal
        const itemInput = form.find('input[name="item"]');

        if (itemInput.length > 0) {
            console.log('Found item input field, setting up autocomplete');

            // Destroy existing autocomplete if any
            if (itemInput.hasClass('ui-autocomplete-input')) {
                itemInput.autocomplete('destroy');
            }

            // Initialize enhanced autocomplete
            itemInput.autocomplete({
                source: function(request, response) {
                    console.log('Modal autocomplete source called with term:', request.term);
                    const stockItems = getStockItemsForAutocomplete();

                    // If no search term, show all items
                    if (!request.term || request.term.trim() === '') {
                        response(stockItems);
                    } else {
                        const filtered = stockItems.filter(item =>
                            item.label.toLowerCase().includes(request.term.toLowerCase())
                        );
                        response(filtered);
                    }
                },
                minLength: 0,
                select: function(event, ui) {
                    console.log('Item selected from autocomplete:', ui.item);

                    // Auto-fill other fields in the modal
                    if (ui.item.stockData) {
                        setTimeout(() => {
                            autoFillModalFields(form, ui.item.stockData);
                        }, 100);
                    }
                    return true;
                },
                focus: function(event, ui) {
                    return false; // Prevent value change on focus
                }
            }).autocomplete("instance")._renderItem = function(ul, item) {
                return $("<li>")
                    .append("<div><strong>" + item.label + "</strong><br>" +
                           "<small>HSN: " + (item.stockData.hsn || 'N/A') +
                           " | Rate: ₹" + (item.stockData.rate || 0) +
                           " | GST: " + (item.stockData.grate || 0) + "%</small></div>")
                    .appendTo(ul);
            };

            console.log('Autocomplete initialized successfully');
        } else {
            console.log('Item input field not found in modal');
        }
    } catch (error) {
        console.error('Error initializing modal autocomplete:', error);
    }
}

/**
 * Setup direct autocomplete for modal input elements
 */
function setupDirectAutocomplete(element) {
    console.log('Setting up direct autocomplete for element:', element);

    try {
        const $elem = $(element);

        // Destroy existing autocomplete if any
        if ($elem.hasClass('ui-autocomplete-input')) {
            $elem.autocomplete('destroy');
        }

        // Initialize autocomplete
        $elem.autocomplete({
            source: function(request, response) {
                console.log('Direct autocomplete source called with term:', request.term);
                const stockItems = getStockItemsForAutocomplete();

                // If no search term, show all items
                if (!request.term || request.term.trim() === '') {
                    console.log('Showing all items (no search term)');
                    response(stockItems);
                } else {
                    const filtered = stockItems.filter(item =>
                        item.label.toLowerCase().includes(request.term.toLowerCase())
                    );
                    console.log('Direct autocomplete filtered items:', filtered);
                    response(filtered);
                }
            },
            minLength: 0,
            select: function(event, ui) {
                console.log('Direct autocomplete item selected:', ui.item);

                // Find the form and auto-fill fields
                const form = $elem.closest('form');
                if (ui.item.stockData) {
                    setTimeout(() => {
                        autoFillModalFields(form, ui.item.stockData);
                    }, 100);
                }
                return true;
            },
            focus: function(event, ui) {
                return false;
            }
        }).autocomplete("instance")._renderItem = function(ul, item) {
            return $("<li>")
                .append("<div><strong>" + item.label + "</strong><br>" +
                       "<small>HSN: " + (item.stockData.hsn || 'N/A') +
                       " | Rate: ₹" + (item.stockData.rate || 0) +
                       " | GST: " + (item.stockData.grate || 0) + "%</small></div>")
                .appendTo(ul);
        };

        // Add focus event to show all items when focused
        $elem.on('focus', function() {
            console.log('Direct autocomplete input focused, triggering search');
            $(this).autocomplete('search', '');
        });

        console.log('Direct autocomplete setup completed');
    } catch (error) {
        console.error('Error setting up direct autocomplete:', error);
    }
}

/**
 * Auto-fill modal form fields with stock data
 */
function autoFillModalFields(form, stockData) {
    console.log('Auto-filling modal fields with stock data:', stockData);

    try {
        // Fill all the fields in the modal form
        form.find('input[name="hsn"]').val(stockData.hsn || '');
        form.find('input[name="pno"]').val(stockData.pno || ''); // Keep as empty string for form display
        form.find('input[name="oem"]').val(stockData.oem || '');
        form.find('input[name="batch"]').val(stockData.batch || ''); // Keep as empty string for form display
        form.find('input[name="uom"]').val(stockData.uom || 'Pcs');
        form.find('input[name="rate"]').val(stockData.rate || 0);
        form.find('input[name="grate"]').val(stockData.grate || 18);

        console.log('Modal fields auto-filled successfully');
    } catch (error) {
        console.error('Error auto-filling modal fields:', error);
    }
}

/**
 * Auto-fill stock item details in jqGrid row
 */
function autoFillStockItemInGrid(rowId, stockData) {
    console.log('Auto-filling stock item in grid:', rowId, stockData);

    try {
        // Update the grid row with stock data
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'hsn', stockData.hsn);
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'pno', stockData.pno);
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'oem', stockData.oem);
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'batch', stockData.batch);
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'uom', stockData.uom);
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'rate', stockData.rate);
        $("#stockItemsGrid").jqGrid('setCell', rowId, 'grate', stockData.grate);

        // Update the underlying data array
        const allRowIds = $("#stockItemsGrid").jqGrid('getDataIDs');
        const rowIndex = allRowIds.indexOf(rowId);

        if (rowIndex >= 0 && billForm.stockItems[rowIndex]) {
            billForm.stockItems[rowIndex] = {
                ...billForm.stockItems[rowIndex],
                item: stockData.item,
                hsn: stockData.hsn,
                pno: stockData.pno,
                oem: stockData.oem,
                batch: stockData.batch,
                uom: stockData.uom,
                rate: stockData.rate,
                grate: stockData.grate,
                mrp: stockData.mrp,
                expiryDate: stockData.expiryDate
            };

            // Recalculate totals for this item
            calculateItemTotal(rowIndex);
        }

        console.log('Stock item auto-filled successfully');

    } catch (error) {
        console.error('Error auto-filling stock item:', error);
    }
}

/**
 * Add new stock item to jqGrid
 */
function addStockItemToGrid() {
    const newItem = {
        item: '',
        hsn: '',
        batch: null, // ✅ Changed from '' to null
        qty: 1,
        oem: '',
        pno: null, // ✅ Changed from '' to null
        uom: 'Pcs',
        rate: 0,
        grate: 18,
        cgst: 0,
        sgst: 0,
        igst: 0,
        disc: 0,
        total: 0
    };

    billForm.stockItems.push(newItem);
    const newRowId = billForm.stockItems.length;

    $("#stockItemsGrid").jqGrid('addRowData', newRowId, newItem);
    $("#stockItemsGrid").jqGrid('editRow', newRowId);
}

/**
 * Delete selected stock item from jqGrid
 */
function deleteStockItemFromGrid() {
    // Try multiple ways to get selected row
    let selectedRow = $("#stockItemsGrid").jqGrid('getGridParam', 'selrow');
    if (!selectedRow) {
        selectedRow = $("#stockItemsGrid").data('selectedRow');
    }

    console.log('Selected row ID:', selectedRow);

    // Get all row IDs to check if there are any rows
    const allRowIds = $("#stockItemsGrid").jqGrid('getDataIDs');
    console.log('All row IDs:', allRowIds);

    if (selectedRow && allRowIds.includes(selectedRow)) {
        // Confirm deletion
        if (confirm('Are you sure you want to delete this item?')) {
            $("#stockItemsGrid").jqGrid('delRowData', selectedRow);
            // Find the actual index in the array (row IDs might not match array indices)
            const rowIndex = allRowIds.indexOf(selectedRow);
            if (rowIndex >= 0) {
                billForm.stockItems.splice(rowIndex, 1);
            }
            // Clear stored selection
            $("#stockItemsGrid").removeData('selectedRow');
            calculateBillTotal();
        }
    } else {
        if (allRowIds.length === 0) {
            alert('No items to delete. Please add some items first.');
        } else {
            alert('Please select a row to delete by clicking on it first.');
        }
    }
}

/**
 * Delete specific stock item row by ID (called from action button)
 */
function deleteStockItemRow(rowId) {
    console.log('Deleting stock item row:', rowId);

    // Get all row IDs to find the index
    const allRowIds = $("#stockItemsGrid").jqGrid('getDataIDs');
    const rowIndex = allRowIds.indexOf(rowId);

    if (rowIndex >= 0) {
        // Confirm deletion
        if (confirm('Are you sure you want to delete this item?')) {
            // Remove from grid
            $("#stockItemsGrid").jqGrid('delRowData', rowId);
            // Remove from data array
            billForm.stockItems.splice(rowIndex, 1);
            // Recalculate totals
            calculateBillTotal();
        }
    } else {
        alert('Error: Could not find the row to delete.');
    }
}

/**
 * Add new other charge to jqGrid
 */
function addOtherChargeToGrid() {
    const newCharge = {
        description: '',
        oth_amt: 0,
        oth_grate: 18,
        oth_cgst: 0,
        oth_sgst: 0,
        oth_igst: 0,
        oth_hsn: '',
        oth_tot: 0
    };

    billForm.oth_chg.push(newCharge);
    const newRowId = billForm.oth_chg.length;

    $("#otherChargesGrid").jqGrid('addRowData', newRowId, newCharge);
    $("#otherChargesGrid").jqGrid('editRow', newRowId);
}

/**
 * Delete selected other charge from jqGrid
 */
function deleteOtherChargeFromGrid() {
    // Try multiple ways to get selected row
    let selectedRow = $("#otherChargesGrid").jqGrid('getGridParam', 'selrow');
    if (!selectedRow) {
        selectedRow = $("#otherChargesGrid").data('selectedRow');
    }

    console.log('Selected charge row ID:', selectedRow);

    // Get all row IDs to check if there are any rows
    const allRowIds = $("#otherChargesGrid").jqGrid('getDataIDs');
    console.log('All charge row IDs:', allRowIds);

    if (selectedRow && allRowIds.includes(selectedRow)) {
        // Confirm deletion
        if (confirm('Are you sure you want to delete this charge?')) {
            $("#otherChargesGrid").jqGrid('delRowData', selectedRow);
            // Find the actual index in the array (row IDs might not match array indices)
            const rowIndex = allRowIds.indexOf(selectedRow);
            if (rowIndex >= 0) {
                billForm.oth_chg.splice(rowIndex, 1);
            }
            // Clear stored selection
            $("#otherChargesGrid").removeData('selectedRow');
            calculateBillTotal();
        }
    } else {
        if (allRowIds.length === 0) {
            alert('No charges to delete. Please add some charges first.');
        } else {
            alert('Please select a row to delete by clicking on it first.');
        }
    }
}

/**
 * Delete specific other charge row by ID (called from action button)
 */
function deleteOtherChargeRow(rowId) {
    console.log('Deleting other charge row:', rowId);

    // Get all row IDs to find the index
    const allRowIds = $("#otherChargesGrid").jqGrid('getDataIDs');
    const rowIndex = allRowIds.indexOf(rowId);

    if (rowIndex >= 0) {
        // Confirm deletion
        if (confirm('Are you sure you want to delete this charge?')) {
            // Remove from grid
            $("#otherChargesGrid").jqGrid('delRowData', rowId);
            // Remove from data array
            billForm.oth_chg.splice(rowIndex, 1);
            // Recalculate totals
            calculateBillTotal();
        }
    } else {
        alert('Error: Could not find the charge to delete.');
    }
}

/**
 * Calculate charge total for other charges
 */
function calculateChargeTotal(index) {
    const charge = billForm.oth_chg[index];
    if (!charge) return;

    // Get party state from selected party
    const selectedParty = inventoryData.parties.find(p => p.supply === billForm.partyName);
    const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
    const firmStateNormalized = firmState?.toLowerCase()?.trim() || '';

    if (firmStateNormalized === partyState) {
        // Same state - CGST/SGST
        charge.oth_cgst = parseFloat((charge.oth_amt * charge.oth_grate / 100 / 2).toFixed(2));
        charge.oth_sgst = parseFloat((charge.oth_amt * charge.oth_grate / 100 / 2).toFixed(2));
        charge.oth_igst = 0;
    } else {
        // Different states - IGST
        charge.oth_cgst = 0;
        charge.oth_sgst = 0;
        charge.oth_igst = parseFloat((charge.oth_amt * charge.oth_grate / 100).toFixed(2));
    }

    charge.oth_tot = parseFloat((charge.oth_amt + charge.oth_cgst + charge.oth_sgst + charge.oth_igst).toFixed(2));

    // Update grid
    $("#otherChargesGrid").jqGrid('setRowData', index + 1, charge);
    calculateBillTotal();
}

/**
 * Setup keyboard shortcuts
 */
function setupKeyboardShortcuts() {
    document.addEventListener('keydown', handleGlobalKeydown);
}

/**
 * Handle global keyboard shortcuts
 */
function handleGlobalKeydown(event) {
    // Skip if user is typing in an input field and not using Alt key
    const isInputActive = ['INPUT', 'TEXTAREA', 'SELECT'].includes(document.activeElement.tagName);
    const isAltKey = event.altKey;

    // Allow Alt key shortcuts even when in input fields
    if (isInputActive && !isAltKey) return;

    // Section navigation shortcuts (Alt + number)
    if (isAltKey && !event.ctrlKey && !event.shiftKey) {
        switch (event.key) {
            case '1': // Bill Info
                event.preventDefault();
                focusSection('billInfo');
                break;
            case '2': // Party Details
                event.preventDefault();
                focusSection('partyDetails');
                break;
            case '3': // Amount Details
                event.preventDefault();
                focusSection('amountDetails');
                break;
            case '4': // Stock Items
                event.preventDefault();
                focusSection('stockItems');
                break;
            case '5': // Other Charges
                event.preventDefault();
                focusSection('otherCharges');
                break;
            case '6': // Action Buttons
                event.preventDefault();
                focusSection('actionButtons');
                break;
            case 's': // Submit form
            case 'S':
                event.preventDefault();
                if (!invoiceSubmitted) {
                    submitBillForm();
                }
                break;
            case 'r': // Reset form
            case 'R':
                event.preventDefault();
                resetBillForm();
                break;
            case 'n': // Create new invoice
            case 'N':
                event.preventDefault();
                if (isEditMode || invoiceSubmitted) {
                    createNewInvoice();
                }
                break;
            case 'a': // Add new row
            case 'A':
                event.preventDefault();
                if (currentFocusSection === 'stockItems') {
                    addStockItem();
                } else if (currentFocusSection === 'otherCharges') {
                    showOtherChargesModal();
                }
                break;
            case 'o': // Open Other Charges Modal
            case 'O':
                event.preventDefault();
                showOtherChargesModal();
                break;
            case 'c': // Create Party
            case 'C':
                event.preventDefault();
                showPartyModal();
                break;
        }
    }

    // Show keyboard shortcuts help dialog
    if (event.key === 'F1' || (event.key === '/' && event.shiftKey)) {
        event.preventDefault();
        showKeyboardShortcutsModal();
    }
}

/**
 * Focus a specific section of the form
 */
function focusSection(section) {
    currentFocusSection = section;

    let sectionElement;
    switch (section) {
        case 'billInfo':
            sectionElement = document.getElementById('billInfoSection');
            break;
        case 'partyDetails':
            sectionElement = document.getElementById('partyDetailsSection');
            break;
        case 'amountDetails':
            sectionElement = document.getElementById('amountDetailsSection');
            break;
        case 'stockItems':
            sectionElement = document.getElementById('stockItemsSection');
            break;
        case 'otherCharges':
            sectionElement = document.getElementById('otherChargesSection');
            break;
        case 'actionButtons':
            sectionElement = document.getElementById('actionButtonsSection');
            break;
    }

    if (sectionElement) {
        sectionElement.scrollIntoView({ behavior: 'smooth', block: 'start' });
        const firstInput = sectionElement.querySelector('input, select, button');
        if (firstInput) {
            setTimeout(() => firstInput.focus(), 300);
        }
    }
}

/**
 * Handle bill type change
 */
function handleBillTypeChange(event) {
    const billType = event.target.value;
    billForm.type = billType;

    // Show/hide note details section for credit/debit notes
    const noteDetailsSection = document.getElementById('noteDetailsSection');
    if (noteDetailsSection) {
        if (billType === 'CREDIT NOTE' || billType === 'DEBIT NOTE') {
            noteDetailsSection.style.display = 'block';
        } else {
            noteDetailsSection.style.display = 'none';
        }
    }

    updatePageTitle();
}

/**
 * Handle party selection
 */
function handlePartySelection() {
    const partyNameInput = document.getElementById('partyName');
    if (!partyNameInput) return;

    const partyName = partyNameInput.value;

    if (partyName === 'Create New Party') {
        partyNameInput.value = '';
        billForm.partyName = '';
        showPartyModal();
        return;
    }

    // Find the selected party from database
    let selectedParty = null;

    if (window.jsonDB) {
        const parties = window.jsonDB.getParties();

        // Try to find by ID first (if it's a number)
        if (!isNaN(partyName)) {
            selectedParty = parties.find(p => p.id == partyName);
        }

        // If not found by ID, try to find by name
        if (!selectedParty) {
            selectedParty = parties.find(p => p.supply === partyName);
        }
    }

    if (selectedParty) {
        // Update form fields with party data
        updateFormField('partyName', selectedParty.supply || '');
        updateFormField('partyAddress', selectedParty.addr || '');
        updateFormField('partyGstin', selectedParty.gstin || '');
        updateFormField('partyState', selectedParty.state || '');
        updateFormField('partyPin', selectedParty.pin || '');

        // Update billForm object
        billForm.partyName = selectedParty.supply || '';
        billForm.partyAddress = selectedParty.addr || '';
        billForm.partyGstin = selectedParty.gstin || '';
        billForm.partyState = selectedParty.state || '';
        billForm.partyPin = selectedParty.pin || '';

        // Recalculate totals as GST depends on state
        calculateBillTotal();

        console.log('Party selected:', selectedParty.supply);
    } else {
        // If no party found, clear other fields but keep the entered name
        billForm.partyName = partyName;
        updateFormField('partyAddress', '');
        updateFormField('partyGstin', '');
        updateFormField('partyState', '');
        updateFormField('partyPin', '');

        billForm.partyAddress = '';
        billForm.partyGstin = '';
        billForm.partyState = '';
        billForm.partyPin = '';
    }
}

/**
 * Handle party name keydown for shortcuts
 */
function handlePartyNameKeydown(event) {
    // Check if Alt+C was pressed
    if (event.altKey && (event.key === 'c' || event.key === 'C')) {
        event.preventDefault();
        showPartyModal();
    }
}

/**
 * Update form field value
 */
function updateFormField(fieldId, value) {
    const field = document.getElementById(fieldId);
    if (field) {
        field.value = value;
    }
}

/**
 * Add new stock item row
 */
function addStockItem() {
    const newItem = {
        item: '',
        hsn: '',
        batch: null, // ✅ Changed from '' to null
        qty: 0,
        oem: '',
        pno: null, // ✅ Changed from '' to null
        uom: '',
        rate: 0,
        grate: 0,
        cgst: 0,
        sgst: 0,
        igst: 0,
        disc: 0,
        project: '',
        total: 0,
        item_narration: '',
        mrp: null,
        expiryDate: null
    };

    billForm.stockItems.push(newItem);
    renderStockItemsTable();

    // Focus the first input of the new row
    setTimeout(() => {
        const tableBody = document.getElementById('stockItemsTableBody');
        if (tableBody) {
            const rows = tableBody.querySelectorAll('tr');
            const lastRow = rows[rows.length - 1];
            if (lastRow) {
                const firstInput = lastRow.querySelector('input');
                if (firstInput) {
                    firstInput.focus();
                }
            }
        }
    }, 100);
}

/**
 * Remove stock item
 */
function removeStockItem(index) {
    if (confirm('Are you sure you want to remove this item?')) {
        billForm.stockItems.splice(index, 1);
        renderStockItemsTable();
        calculateBillTotal();
    }
}

/**
 * Update stock item property
 */
function updateStockItem(index, property, value) {
    if (billForm.stockItems[index]) {
        billForm.stockItems[index][property] = value;

        // If item name changed, auto-fill from inventory
        if (property === 'item') {
            handleStockItemSelection(index);
        }

        // Recalculate if quantity, rate, discount, or GST rate changed
        if (['qty', 'rate', 'disc', 'grate'].includes(property)) {
            calculateItemTotal(index);
        }
    }
}

/**
 * Handle stock item selection and auto-fill fields
 */
function handleStockItemSelection(index) {
    const selectedItem = billForm.stockItems[index].item;
    const stockItem = inventoryData.stocks.find(stock => stock.item === selectedItem);

    if (stockItem) {
        billForm.stockItems[index] = {
            ...billForm.stockItems[index],
            hsn: stockItem.hsn || '',
            pno: stockItem.pno || null, // ✅ Changed from '' to null
            oem: stockItem.oem || '',
            batch: stockItem.batch || null, // ✅ Changed from '' to null
            uom: stockItem.uom || '',
            rate: stockItem.rate || 0,
            grate: stockItem.grate || 0,
            mrp: stockItem.mrp || null
        };
        calculateItemTotal(index);
        renderStockItemsTable();
    }
}

/**
 * Calculate total for a stock item
 */
function calculateItemTotal(index) {
    const item = billForm.stockItems[index];
    if (!item) return;

    const discamt = parseFloat(((item.rate * item.qty) * item.disc / 100).toFixed(2));

    // Get party state from selected party
    const selectedParty = inventoryData.parties.find(p => p.supply === billForm.partyName);
    const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
    const firmStateNormalized = firmState?.toLowerCase()?.trim() || '';

    // Compare states to determine GST calculation
    if (firmStateNormalized === partyState) {
        // Same state - apply CGST/SGST, set IGST to 0
        item.cgst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100 / 2).toFixed(2));
        item.sgst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100 / 2).toFixed(2));
        item.igst = 0;
    } else {
        // Different states - apply IGST, set CGST/SGST to 0
        item.cgst = 0;
        item.sgst = 0;
        item.igst = parseFloat((((item.rate * item.qty) - discamt) * item.grate / 100).toFixed(2));
    }

    // Calculate total
    item.total = parseFloat((item.rate * item.qty - discamt).toFixed(2));

    // Update the table display
    renderStockItemsTable();
    calculateBillTotal();
}

/**
 * Render stock items table
 */
function renderStockItemsTable() {
    const tableBody = document.getElementById('stockItemsTableBody');
    if (!tableBody) return;

    if (billForm.stockItems.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="18" style="text-align: center; padding: 2rem; color: #6b7280;">No items added. Click "Add Item" to get started.</td></tr>';
        return;
    }

    tableBody.innerHTML = billForm.stockItems.map((item, index) => `
        <tr>
            <td>
                <input type="text" value="${item.item}"
                       onchange="updateStockItem(${index}, 'item', this.value)"
                       list="stockItemList" class="form-input" placeholder="Select item">
            </td>
            <td>
                <input type="text" value="${item.hsn}"
                       onchange="updateStockItem(${index}, 'hsn', this.value)" class="form-input" placeholder="HSN">
            </td>
            <td>
                <input type="text" value="${item.batch}"
                       onchange="updateStockItem(${index}, 'batch', this.value)" class="form-input" placeholder="Batch">
            </td>
            <td>
                <input type="number" value="${item.qty}" step="0.01"
                       onchange="updateStockItem(${index}, 'qty', parseFloat(this.value) || 0)" class="form-input" placeholder="Qty">
            </td>
            <td>
                <input type="text" value="${item.oem}"
                       onchange="updateStockItem(${index}, 'oem', this.value)" class="form-input" placeholder="OEM">
            </td>
            <td>
                <input type="text" value="${item.pno}"
                       onchange="updateStockItem(${index}, 'pno', this.value)" class="form-input" placeholder="P.No">
            </td>
            <td>
                <input type="text" value="${item.uom}"
                       onchange="updateStockItem(${index}, 'uom', this.value)" class="form-input" placeholder="UOM">
            </td>
            <td>
                <input type="number" value="${item.rate}" step="0.01"
                       onchange="updateStockItem(${index}, 'rate', parseFloat(this.value) || 0)" class="form-input" placeholder="Rate">
            </td>
            <td class="column-disc">
                <input type="number" value="${item.disc}" step="0.01"
                       onchange="updateStockItem(${index}, 'disc', parseFloat(this.value) || 0)" class="form-input" placeholder="Disc%">
            </td>
            <td class="column-cgst">
                <input type="number" value="${item.cgst.toFixed(2)}" readonly class="form-input" style="background: #f9fafb;">
            </td>
            <td class="column-sgst">
                <input type="number" value="${item.sgst.toFixed(2)}" readonly class="form-input" style="background: #f9fafb;">
            </td>
            <td class="column-igst">
                <input type="number" value="${item.igst.toFixed(2)}" readonly class="form-input" style="background: #f9fafb;">
            </td>
            <td>
                <input type="number" value="${item.grate}" step="0.01"
                       onchange="updateStockItem(${index}, 'grate', parseFloat(this.value) || 0)" class="form-input" placeholder="GST%">
            </td>
            <td class="column-project">
                <input type="text" value="${item.project}"
                       onchange="updateStockItem(${index}, 'project', this.value)" class="form-input" placeholder="Project">
            </td>
            <td style="text-align: right; font-weight: 600; color: #059669;">
                ₹${item.total.toFixed(2)}
            </td>
            <td class="column-mrp">
                <input type="number" value="${item.mrp || ''}" step="0.01"
                       onchange="updateStockItem(${index}, 'mrp', parseFloat(this.value) || null)" class="form-input" placeholder="MRP">
            </td>
            <td class="column-expiryDate">
                <input type="date" value="${item.expiryDate || ''}"
                       onchange="updateStockItem(${index}, 'expiryDate', this.value)" class="form-input">
            </td>
            <td>
                <button type="button" onclick="removeStockItem(${index})" class="btn-icon" title="Remove Item" style="color: #ef4444;">
                    🗑️
                </button>
            </td>
        </tr>
    `).join('');

    // Update column visibility
    applyColumnVisibility();

    // Populate stock item datalist
    populateStockItemList();
}

/**
 * Calculate bill totals from stock items
 */
function calculateBillTotal() {
    console.log('calculateBillTotal called');
    console.log('Current billForm.stockItems:', billForm.stockItems);

    // Reset totals
    billForm.gtot = 0;
    billForm.cgst = 0;
    billForm.sgst = 0;
    billForm.igst = 0;

    // Get party state from selected party
    const selectedParty = inventoryData.parties.find(p => p.supply === billForm.partyName);
    const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
    const firmStateNormalized = firmState?.toLowerCase()?.trim() || '';

    // Sum up stock items
    billForm.stockItems.forEach(item => {
        // Calculate taxable amount (without tax) for gross total
        const qty = parseFloat(item.qty) || 0;
        const rate = parseFloat(item.rate) || 0;
        const disc = parseFloat(item.disc) || 0;
        const subtotal = qty * rate;
        const discountAmount = (subtotal * disc) / 100;
        const taxableAmount = subtotal - discountAmount;

        // Add taxable amount to gross total (not the total with tax)
        billForm.gtot += parseFloat(taxableAmount.toFixed(2));
        billForm.cgst += parseFloat((item.cgst || 0).toFixed(2));
        billForm.sgst += parseFloat((item.sgst || 0).toFixed(2));
        billForm.igst += parseFloat((item.igst || 0).toFixed(2));
    });

    // Format to 2 decimal places
    billForm.gtot = parseFloat(billForm.gtot.toFixed(2));
    billForm.cgst = parseFloat(billForm.cgst.toFixed(2));
    billForm.sgst = parseFloat(billForm.sgst.toFixed(2));
    billForm.igst = parseFloat(billForm.igst.toFixed(2));

    // Add other charges if present
    billForm.gtot += billForm.oth_chg.reduce((sum, charge) => sum + (charge.oth_amt || 0), 0);
    billForm.cgst += billForm.oth_chg.reduce((sum, charge) => sum + (charge.oth_cgst || 0), 0);
    billForm.sgst += billForm.oth_chg.reduce((sum, charge) => sum + (charge.oth_sgst || 0), 0);
    billForm.igst += billForm.oth_chg.reduce((sum, charge) => sum + (charge.oth_igst || 0), 0);

    // Calculate net total
    let netTotal;
    if (firmStateNormalized === partyState) {
        // Same state - use CGST and SGST for net total
        netTotal = parseFloat((billForm.gtot + billForm.cgst + billForm.sgst).toFixed(2));
    } else {
        // Different states - use IGST for net total
        netTotal = parseFloat((billForm.gtot + billForm.igst).toFixed(2));
    }

    // Calculate round-off amount to nearest whole number
    const roundedTotal = Math.round(netTotal);
    billForm.rof = parseFloat((roundedTotal - netTotal).toFixed(2));

    // Set final net total with round-off
    billForm.ntot = parseFloat(roundedTotal.toFixed(2));

    // Update display
    console.log('Calling updateAmountDisplay...');
    updateAmountDisplay();

    // Update jqGrid tables with calculated values
    console.log('Updating jqGrid tables...');
    updateJqGridTables();

    console.log('calculateBillTotal completed');
}

/**
 * Add new item row to the table
 */
function addNewItemRow() {
    itemRowCounter++;
    const newRow = createItemRow(itemRowCounter);
    $('#stockItemsTable').append(newRow);
}

/**
 * Create HTML for new item row
 */
function createItemRow(rowNumber) {
    return `
        <tr id="itemRow${rowNumber}" data-row="${rowNumber}">
            <td>${rowNumber}</td>
            <td><input type="text" placeholder="ITM-${String(rowNumber).padStart(3, '0')}" class="item-code"></td>
            <td><input type="text" placeholder="Product name" class="product-name" required></td>
            <td><input type="number" placeholder="0" class="quantity" min="0" step="1"></td>
            <td>
                <select class="unit">
                    <option value="pcs">Pcs</option>
                    <option value="kg">Kg</option>
                    <option value="ltr">Ltr</option>
                    <option value="box">Box</option>
                    <option value="mtr">Mtr</option>
                    <option value="set">Set</option>
                </select>
            </td>
            <td><input type="number" placeholder="0.00" class="rate" min="0" step="0.01"></td>
            <td><input type="number" placeholder="0.00" class="amount" readonly></td>
            <td><input type="date" class="expiry-date"></td>
            <td>
                <button type="button" class="remove-item-btn" onclick="removeItem(${rowNumber})" title="Remove Item">
                    ×
                </button>
            </td>
        </tr>
    `;
}

/**
 * Remove item row from table
 */
function removeItem(rowNumber) {
    if ($('#stockItemsTable tr').length > 1) {
        $(`#itemRow${rowNumber}`).remove();
        updateRowNumbers();
        updateTotals();
    } else {
        alert('At least one item is required.');
    }
}

/**
 * Update row numbers after deletion
 */
function updateRowNumbers() {
    $('#stockItemsTable tr').each(function(index) {
        const newRowNumber = index + 1;
        $(this).find('td:first').text(newRowNumber);
        $(this).attr('id', 'itemRow' + newRowNumber);
        $(this).attr('data-row', newRowNumber);
        $(this).find('.remove-item-btn').attr('onclick', `removeItem(${newRowNumber})`);
    });
}

/**
 * Manual calculate table function - triggered by Calculate button
 */
function manualCalculateTable() {
    console.log('=== MANUAL CALCULATE TABLE CALLED ===');

    try {
        // First, sync any data from jqGrid to billForm
        console.log('Syncing jqGrid data to billForm...');
        syncGridDataToBillForm();

        // Then calculate totals
        console.log('Calculating bill totals...');
        calculateBillTotal();

        // Show success message
        console.log('Manual calculation completed successfully');

        // Optional: Show a brief success indicator
        showCalculationSuccess();

    } catch (error) {
        console.error('Error in manual calculation:', error);
        alert('Error calculating totals. Please check the console for details.');
    }
}

/**
 * Sync jqGrid data to billForm arrays
 */
function syncGridDataToBillForm() {
    console.log('Syncing grid data to billForm arrays...');

    try {
        // Sync Stock Items Grid data
        const stockGridData = $("#stockItemsGrid").jqGrid('getRowData');
        console.log('Retrieved stock grid data:', stockGridData);

        if (stockGridData && stockGridData.length > 0) {
            // Get party state for GST calculation (same as other charges)
            const selectedParty = inventoryData.parties.find(p => p.supply === billForm.partyName);
            const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
            const firmStateNormalized = firmState?.toLowerCase()?.trim() || '';

            billForm.stockItems = stockGridData.map((row, index) => {
                const qty = parseFloat(row.qty) || 0;
                const rate = parseFloat(row.rate) || 0;
                const disc = parseFloat(row.disc) || 0;
                const grate = parseFloat(row.grate) || 18;

                // Calculate totals
                const subtotal = qty * rate;
                const discountAmount = (subtotal * disc) / 100;
                const taxableAmount = subtotal - discountAmount;
                const gstAmount = (taxableAmount * grate) / 100;

                let cgst = 0, sgst = 0, igst = 0;

                // Check if same state or different state for GST calculation (same logic as other charges)
                if (firmStateNormalized === partyState) {
                    // Same state - CGST/SGST
                    cgst = gstAmount / 2;
                    sgst = gstAmount / 2;
                    igst = 0;
                } else {
                    // Different states - IGST
                    cgst = 0;
                    sgst = 0;
                    igst = gstAmount;
                }

                const total = taxableAmount + gstAmount;

                return {
                    item: row.item || '',
                    hsn: row.hsn || '',
                    batch: row.batch || null, // ✅ Changed from '' to null
                    qty: qty,
                    oem: row.oem || '',
                    pno: row.pno || null, // ✅ Changed from '' to null
                    uom: row.uom || 'Pcs',
                    rate: rate,
                    disc: disc,
                    grate: grate,
                    cgst: cgst,
                    sgst: sgst,
                    igst: igst,
                    total: total,
                    mrp: parseFloat(row.mrp) || 0,
                    expiryDate: row.expiryDate || null
                };
            });

            console.log('Synced stock items:', billForm.stockItems);
        }

        // Sync Other Charges Grid data
        const chargesGridData = $("#otherChargesGrid").jqGrid('getRowData');
        console.log('Retrieved charges grid data:', chargesGridData);

        if (chargesGridData && chargesGridData.length > 0) {
            // Get party state for GST calculation
            const selectedParty = inventoryData.parties.find(p => p.supply === billForm.partyName);
            const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
            const firmStateNormalized = firmState?.toLowerCase()?.trim() || '';

            billForm.oth_chg = chargesGridData.map(row => {
                const amount = parseFloat(row.oth_amt) || 0;
                const grate = parseFloat(row.oth_grate) || 18;
                const gstAmount = (amount * grate) / 100;

                let cgst = 0, sgst = 0, igst = 0;

                // Check if same state or different state for GST calculation
                if (firmStateNormalized === partyState) {
                    // Same state - CGST/SGST
                    cgst = gstAmount / 2;
                    sgst = gstAmount / 2;
                    igst = 0;
                } else {
                    // Different states - IGST
                    cgst = 0;
                    sgst = 0;
                    igst = gstAmount;
                }

                const total = amount + gstAmount;

                return {
                    description: row.description || '',
                    oth_amt: amount,
                    oth_grate: grate,
                    oth_cgst: cgst,
                    oth_sgst: sgst,
                    oth_igst: igst,
                    oth_hsn: row.oth_hsn || '',
                    oth_tot: total
                };
            });

            console.log('Synced other charges:', billForm.oth_chg);
        }

    } catch (error) {
        console.error('Error syncing grid data:', error);
    }
}

/**
 * Show calculation success indicator
 */
function showCalculationSuccess() {
    // Create a temporary success message
    const successMsg = document.createElement('div');
    successMsg.innerHTML = '✅ Calculations Updated!';
    successMsg.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: #4CAF50;
        color: white;
        padding: 10px 20px;
        border-radius: 5px;
        z-index: 10000;
        font-weight: bold;
        box-shadow: 0 2px 10px rgba(0,0,0,0.2);
    `;

    document.body.appendChild(successMsg);

    // Remove after 2 seconds
    setTimeout(() => {
        if (successMsg.parentNode) {
            successMsg.parentNode.removeChild(successMsg);
        }
    }, 2000);
}

/**
 * Test calculation function
 */
function testCalculation() {
    console.log('=== TEST CALCULATION CALLED ===');

    // Add a test item to billForm.stockItems if empty
    if (billForm.stockItems.length === 0) {
        billForm.stockItems.push({
            item: 'Test Item',
            hsn: '1234',
            batch: 'B001',
            qty: 2,
            oem: 'Test OEM',
            pno: 'P001',
            uom: 'Pcs',
            rate: 100,
            disc: 10,
            grate: 18,
            cgst: 8.1,
            sgst: 8.1,
            igst: 0,
            total: 196.2,
            mrp: 120,
            expiryDate: null
        });
        console.log('Added test item to billForm.stockItems');
    }

    console.log('Current billForm.stockItems:', billForm.stockItems);

    // Call calculateBillTotal
    calculateBillTotal();

    alert('Test calculation completed! Check console for details.');
}

/**
 * Update jqGrid tables with calculated values
 */
function updateJqGridTables() {
    console.log('Updating jqGrid tables with calculated values...');

    try {
        // Update Stock Items Grid
        const stockGrid = $("#stockItemsGrid");
        if (stockGrid.length > 0) {
            console.log('Updating stock items grid...');

            // Clear existing data
            stockGrid.jqGrid('clearGridData');

            // Add updated data with calculated values
            billForm.stockItems.forEach((item, index) => {
                const rowData = {
                    id: index + 1,
                    item: item.item || '',
                    hsn: item.hsn || '',
                    batch: item.batch || '',
                    qty: item.qty || 0,
                    oem: item.oem || '',
                    pno: item.pno || '',
                    uom: item.uom || 'Pcs',
                    rate: item.rate || 0,
                    disc: item.disc || 0,
                    cgst: (item.cgst || 0).toFixed(2),
                    sgst: (item.sgst || 0).toFixed(2),
                    igst: (item.igst || 0).toFixed(2),
                    grate: item.grate || 18,
                    total: (item.total || 0).toFixed(2)
                };

                stockGrid.jqGrid('addRowData', rowData.id, rowData);
            });

            console.log('Stock items grid updated with', billForm.stockItems.length, 'items');
        }

        // Update Other Charges Grid
        const chargesGrid = $("#otherChargesGrid");
        if (chargesGrid.length > 0) {
            console.log('Updating other charges grid...');

            // Clear existing data
            chargesGrid.jqGrid('clearGridData');

            // Add updated data with calculated values
            billForm.oth_chg.forEach((charge, index) => {
                const rowData = {
                    id: index + 1,
                    description: charge.description || '',
                    oth_amt: charge.oth_amt || 0,
                    oth_grate: charge.oth_grate || 18,
                    oth_cgst: (charge.oth_cgst || 0).toFixed(2),
                    oth_sgst: (charge.oth_sgst || 0).toFixed(2),
                    oth_igst: (charge.oth_igst || 0).toFixed(2),
                    oth_hsn: charge.oth_hsn || '',
                    oth_tot: (charge.oth_tot || 0).toFixed(2)
                };

                chargesGrid.jqGrid('addRowData', rowData.id, rowData);
            });

            console.log('Other charges grid updated with', billForm.oth_chg.length, 'charges');
        }

    } catch (error) {
        console.error('Error updating jqGrid tables:', error);
    }
}

/**
 * Update bill total display (alias for updateAmountDisplay)
 */
function updateBillTotalDisplay() {
    updateAmountDisplay();
}

/**
 * Update amount display
 */
function updateAmountDisplay() {
    console.log('updateAmountDisplay called with billForm totals:', {
        gtot: billForm.gtot,
        cgst: billForm.cgst,
        sgst: billForm.sgst,
        igst: billForm.igst,
        rof: billForm.rof,
        ntot: billForm.ntot
    });

    const grossTotalEl = document.getElementById('grossTotal');
    const cgstTotalEl = document.getElementById('cgstTotal');
    const sgstTotalEl = document.getElementById('sgstTotal');
    const igstTotalEl = document.getElementById('igstTotal');
    const roundOffEl = document.getElementById('roundOff');
    const netTotalEl = document.getElementById('netTotal');

    console.log('Found elements:', {
        grossTotalEl: !!grossTotalEl,
        cgstTotalEl: !!cgstTotalEl,
        sgstTotalEl: !!sgstTotalEl,
        igstTotalEl: !!igstTotalEl,
        roundOffEl: !!roundOffEl,
        netTotalEl: !!netTotalEl
    });

    if (grossTotalEl) {
        grossTotalEl.textContent = `₹${billForm.gtot.toFixed(2)}`;
        console.log('Updated grossTotal to:', grossTotalEl.textContent);
    }
    if (cgstTotalEl) {
        cgstTotalEl.textContent = `₹${billForm.cgst.toFixed(2)}`;
        console.log('Updated cgstTotal to:', cgstTotalEl.textContent);
    }
    if (sgstTotalEl) {
        sgstTotalEl.textContent = `₹${billForm.sgst.toFixed(2)}`;
        console.log('Updated sgstTotal to:', sgstTotalEl.textContent);
    }
    if (igstTotalEl) {
        igstTotalEl.textContent = `₹${billForm.igst.toFixed(2)}`;
        console.log('Updated igstTotal to:', igstTotalEl.textContent);
    }
    if (roundOffEl) {
        roundOffEl.textContent = `₹${billForm.rof.toFixed(2)}`;
        console.log('Updated roundOff to:', roundOffEl.textContent);
    }
    if (netTotalEl) {
        netTotalEl.textContent = `₹${billForm.ntot.toFixed(2)}`;
        console.log('Updated netTotal to:', netTotalEl.textContent);
    }

    console.log('updateAmountDisplay completed');
}

/**
 * Populate state dropdowns
 */
function populateStateDropdowns() {
    const indianStates = [
        { code: 1, name: 'Jammu & Kashmir' },
        { code: 2, name: 'Himachal Pradesh' },
        { code: 3, name: 'Punjab' },
        { code: 4, name: 'Chandigarh' },
        { code: 5, name: 'Uttarakhand' },
        { code: 6, name: 'Haryana' },
        { code: 7, name: 'Delhi' },
        { code: 8, name: 'Rajasthan' },
        { code: 9, name: 'Uttar Pradesh' },
        { code: 10, name: 'Bihar' },
        { code: 11, name: 'Sikkim' },
        { code: 12, name: 'Arunachal Pradesh' },
        { code: 13, name: 'Nagaland' },
        { code: 14, name: 'Manipur' },
        { code: 15, name: 'Mizoram' },
        { code: 16, name: 'Tripura' },
        { code: 17, name: 'Meghalaya' },
        { code: 18, name: 'Assam' },
        { code: 19, name: 'West Bengal' },
        { code: 20, name: 'Jharkhand' },
        { code: 21, name: 'Odisha' },
        { code: 22, name: 'Chhattisgarh' },
        { code: 23, name: 'Madhya Pradesh' },
        { code: 24, name: 'Gujarat' },
        { code: 25, name: 'Daman & Diu' },
        { code: 26, name: 'Dadra & Nagar Haveli' },
        { code: 27, name: 'Maharashtra' },
        { code: 28, name: 'Andra Pradesh (Old)' },
        { code: 29, name: 'Karnataka' },
        { code: 30, name: 'Goa' },
        { code: 31, name: 'Lakshadweep' },
        { code: 32, name: 'Kerala' },
        { code: 33, name: 'Tamil Nadu' },
        { code: 34, name: 'Puducherry' },
        { code: 35, name: 'Andaman & Nicobar Islands' },
        { code: 36, name: 'Telangana' },
        { code: 37, name: 'Andhra Pradesh' },
        { code: 38, name: 'Ladakh' }
    ];

    const stateSelects = ['partyState', 'consigneeState', 'modalPartyState'];
    stateSelects.forEach(selectId => {
        const select = document.getElementById(selectId);
        if (select) {
            select.innerHTML = '<option value="">Select State</option>' +
                indianStates.map(state => `<option value="${state.name}">${state.name}</option>`).join('');
        }
    });
}

/**
 * Populate party list
 */
function populatePartyList() {
    const partyList = document.getElementById('partyList');
    if (!partyList) {
        // Create the datalist if it doesn't exist
        const datalist = document.createElement('datalist');
        datalist.id = 'partyList';
        document.body.appendChild(datalist);
    }

    const datalist = document.getElementById('partyList');
    if (datalist && window.jsonDB) {
        const parties = window.jsonDB.getParties();
        datalist.innerHTML = '<option value="Create New Party">Create New Party</option>' +
            parties.map(party => `<option value="${party.supply}" data-id="${party.id}">${party.supply} | ${party.gstin || 'No GSTIN'} | ${party.state || 'No State'}</option>`).join('');
    }
}

/**
 * Populate stock item list
 */
function populateStockItemList() {
    const stockItemList = document.getElementById('stockItemList');
    if (!stockItemList) {
        // Create the datalist if it doesn't exist
        const datalist = document.createElement('datalist');
        datalist.id = 'stockItemList';
        document.body.appendChild(datalist);
    }

    const datalist = document.getElementById('stockItemList');
    if (datalist && window.jsonDB) {
        const stocks = window.jsonDB.getStocks();
        datalist.innerHTML = stocks.map(stock =>
            `<option value="${stock.item}">${stock.item} | PN: ${stock.pno || 'N/A'} | Batch: ${stock.batch || 'N/A'} | HSN: ${stock.hsn} | Rate: ₹${stock.rate}</option>`
        ).join('');
    }
}

/**
 * Submit bill form
 */
function submitBillForm() {
    if (isSubmitting) return;

    // Validate form before submission
    if (!validateBillForm()) {
        return;
    }

    isSubmitting = true;
    updateSubmitButton(true);

    // Simulate API call
    setTimeout(() => {
        try {
            // Save to JSON database
            saveBillToDatabase();

            alert('Invoice saved successfully!');
            isSubmitting = false;
            invoiceSubmitted = true;
            isEditMode = true;

            updateSubmitButton(false);
            updateActionButtons();

            // Update database stats
            if (window.updateDatabaseStats) {
                window.updateDatabaseStats();
            }

        } catch (error) {
            alert('Error saving invoice: ' + error.message);
            console.error('Error saving invoice:', error);
            isSubmitting = false;
            updateSubmitButton(false);
        }
    }, 1000);
}

/**
 * Validate bill form
 */
function validateBillForm() {
    const errors = [];

    if (!billForm.type) {
        errors.push('Please select a bill type');
    }

    if (!billForm.bno) {
        errors.push('Please enter a bill number');
    }

    if (!billForm.partyName) {
        errors.push('Please select or add a party');
    }

    // Remove any blank rows in stock items before validation
    billForm.stockItems = billForm.stockItems.filter(item => {
        return item.item && item.item.trim() !== '';
    });

    if (billForm.stockItems.length === 0) {
        errors.push('Please add at least one stock item');
    }

    // Validate each stock item
    for (let i = 0; i < billForm.stockItems.length; i++) {
        const item = billForm.stockItems[i];
        if (!item.item) {
            errors.push(`Stock item #${i + 1}: Please select an item`);
        }
        if (!item.qty || item.qty <= 0) {
            errors.push(`Stock item #${i + 1}: Please enter a valid quantity`);
        }
        if (!item.rate || item.rate <= 0) {
            errors.push(`Stock item #${i + 1}: Please enter a valid rate`);
        }
    }

    if (errors.length > 0) {
        alert('Please fix the following errors:\n• ' + errors.join('\n• '));
        return false;
    }

    return true;
}

/**
 * Update submit button state
 */
function updateSubmitButton(isLoading) {
    const submitBtn = document.getElementById('submitBillBtn');
    if (submitBtn) {
        if (isLoading) {
            submitBtn.innerHTML = '⏳ Saving...';
            submitBtn.disabled = true;
        } else {
            submitBtn.innerHTML = '💾 Save Invoice';
            submitBtn.disabled = false;
        }
    }
}

/**
 * Update action buttons visibility
 */
function updateActionButtons() {
    const newInvoiceBtn = document.getElementById('newInvoiceBtn');
    if (newInvoiceBtn) {
        newInvoiceBtn.style.display = (isEditMode || invoiceSubmitted) ? 'inline-block' : 'none';
    }
}

/**
 * Reset bill form
 */
function resetBillForm() {
    if (confirm('Are you sure you want to reset the form? All data will be lost.')) {
        billForm = {
            type: '',
            bno: '',
            bdate: new Date().toISOString().slice(0, 10),
            partyName: '',
            partyAddress: '',
            partyGstin: '',
            partyState: '',
            partyPin: '',
            gtot: 0,
            cgst: 0,
            sgst: 0,
            igst: 0,
            rof: 0,
            ntot: 0,
            orderNo: '',
            orderDate: '',
            dispatchThrough: '',
            docketNo: '',
            vehicleNo: '',
            consigneeName: '',
            consigneeGstin: '',
            consigneeAddress: '',
            consigneeState: '',
            consigneePin: '',
            reasonForNote: '',
            originalBillNo: '',
            originalBillDate: '',
            oth_chg: [],
            stockItems: []
        };

        // Reset state variables
        isEditMode = false;
        invoiceSubmitted = false;

        // Clear form fields
        clearFormFields();

        // Add first empty stock item
        addStockItem();

        // Update displays
        updatePageTitle();
        updateActionButtons();
        calculateBillTotal();

        alert('Form reset successfully.');
    }
}

/**
 * Create new invoice
 */
function createNewInvoice() {
    resetBillForm();
}

/**
 * Clear form fields
 */
function clearFormFields() {
    const formFields = [
        'billType', 'billNumber', 'billDate', 'partyName', 'partyAddress',
        'partyGstin', 'partyState', 'partyPin', 'orderNo', 'orderDate',
        'dispatchThrough', 'docketNo', 'vehicleNo', 'consigneeName',
        'consigneeGstin', 'consigneeAddress', 'consigneeState', 'consigneePin',
        'reasonForNote', 'originalBillNo', 'originalBillDate'
    ];

    formFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            if (fieldId === 'billDate') {
                field.value = new Date().toISOString().slice(0, 10);
            } else {
                field.value = '';
            }
        }
    });
}

/**
 * Save bill to JSON database
 */
function saveBillToDatabase() {
    if (!window.jsonDB) {
        throw new Error('Database not initialized');
    }

    // Get party ID if party exists
    let partyId = null;
    if (billForm.partyName) {
        const parties = window.jsonDB.getParties();
        const existingParty = parties.find(p => p.supply === billForm.partyName);
        partyId = existingParty ? existingParty.id : null;
    }

    // Generate invoice number if not set
    if (!billForm.bno) {
        billForm.bno = window.jsonDB.getNextInvoiceNumber(billForm.type);
    }

    // Prepare invoice data
    const invoiceData = {
        type: billForm.type,
        bno: billForm.bno,
        bdate: billForm.bdate,
        partyId: partyId,
        partyName: billForm.partyName,
        partyGstin: billForm.partyGstin,
        partyAddress: billForm.partyAddress,
        partyState: billForm.partyState,
        partyPin: billForm.partyPin,
        gtot: billForm.gtot,
        cgst: billForm.cgst,
        sgst: billForm.sgst,
        igst: billForm.igst,
        rof: billForm.rof,
        ntot: billForm.ntot,
        orderNo: billForm.orderNo,
        orderDate: billForm.orderDate,
        dispatchThrough: billForm.dispatchThrough,
        docketNo: billForm.docketNo,
        vehicleNo: billForm.vehicleNo
    };

    // Prepare stock items
    const items = billForm.stockItems.map(item => ({
        stockId: null, // Could be linked to stock master in future
        item: item.item,
        hsn: item.hsn,
        batch: item.batch,
        qty: item.qty,
        oem: item.oem,
        pno: item.pno,
        uom: item.uom,
        rate: item.rate,
        grate: item.grate,
        cgst: item.cgst,
        sgst: item.sgst,
        igst: item.igst,
        disc: item.disc,
        total: item.total,
        mrp: item.mrp,
        expiryDate: item.expiryDate
    }));

    // Prepare other charges
    const charges = billForm.oth_chg.map(charge => ({
        description: charge.description,
        amount: charge.oth_amt,
        grate: charge.oth_grate,
        cgst: charge.oth_cgst,
        sgst: charge.oth_sgst,
        igst: charge.oth_igst,
        total: charge.oth_tot
    }));

    // Save to database
    const savedInvoice = window.jsonDB.createInvoice(invoiceData, items, charges);

    console.log('Invoice saved to database:', savedInvoice.bno);

    // Clear auto-saved data since invoice was successfully saved
    clearAutoSavedData();

    return savedInvoice;
}

/**
 * Save bill to localStorage (legacy backup)
 */
function saveBillToLocalStorage() {
    const bills = JSON.parse(localStorage.getItem('inventoryBills') || '[]');
    const billData = {
        ...billForm,
        id: Date.now().toString(),
        timestamp: new Date().toISOString()
    };
    bills.push(billData);
    localStorage.setItem('inventoryBills', JSON.stringify(bills));
}

/**
 * Setup modal handlers
 */
function setupModalHandlers() {
    // Party form submission
    const partyForm = document.getElementById('partyForm');
    if (partyForm) {
        partyForm.addEventListener('submit', handlePartyFormSubmit);
    }

    // Stock item form submission
    const stockItemForm = document.getElementById('stockItemForm');
    if (stockItemForm) {
        stockItemForm.addEventListener('submit', handleStockItemFormSubmit);
    }

    // Other charges form submission
    const otherChargesForm = document.getElementById('otherChargesForm');
    if (otherChargesForm) {
        otherChargesForm.addEventListener('submit', handleOtherChargesFormSubmit);
    }
}

/**
 * Show party modal
 */
function showPartyModal() {
    const modal = document.getElementById('partyModal');
    if (modal) {
        modal.style.display = 'flex';

        // Clear form
        const form = document.getElementById('partyForm');
        if (form) {
            form.reset();
        }

        // Ensure state dropdown is populated
        setTimeout(() => {
            populateStateDropdowns();

            // Test state selection functionality
            const modalPartyState = document.getElementById('modalPartyState');
            if (modalPartyState) {
                console.log('Modal opened - State dropdown options:',
                    Array.from(modalPartyState.options).map(opt => `"${opt.value}"`));
            }
        }, 50);
    }
}

/**
 * Close party modal
 */
function closePartyModal() {
    const modal = document.getElementById('partyModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Handle party form submission
 */
function handlePartyFormSubmit(event) {
    event.preventDefault();

    const partyData = {
        supply: document.getElementById('modalPartyName').value,
        addr: document.getElementById('modalPartyAddress').value,
        gstin: document.getElementById('modalPartyGstin').value,
        state: document.getElementById('modalPartyState').value,
        pin: document.getElementById('modalPartyPin').value,
        phone: document.getElementById('modalPartyPhone')?.value || '',
        email: document.getElementById('modalPartyEmail')?.value || ''
    };

    if (!partyData.supply) {
        alert('Party name is required');
        return;
    }

    try {
        // Add to database
        const newParty = window.jsonDB.createParty(partyData);

        // Update form
        billForm.partyName = newParty.supply;
        billForm.partyAddress = newParty.addr;
        billForm.partyGstin = newParty.gstin;
        billForm.partyState = newParty.state;
        billForm.partyPin = newParty.pin;

        // Update form fields
        updateFormField('partyName', newParty.supply);
        updateFormField('partyAddress', newParty.addr);
        updateFormField('partyGstin', newParty.gstin);
        updateFormField('partyState', newParty.state);
        updateFormField('partyPin', newParty.pin);

        // Update party list
        populatePartyList();

        // Close modal
        closePartyModal();

        // Recalculate totals
        calculateBillTotal();

        alert('Party added successfully!');

    } catch (error) {
        alert('Error adding party: ' + error.message);
        console.error('Error adding party:', error);
    }
}

/**
 * Show stock item modal
 */
function showStockItemModal() {
    const modal = document.getElementById('stockItemModal');
    if (modal) {
        modal.style.display = 'flex';

        // Clear form
        const form = document.getElementById('stockItemForm');
        if (form) {
            form.reset();
        }
    }
}

/**
 * Close stock item modal
 */
function closeStockItemModal() {
    const modal = document.getElementById('stockItemModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Handle stock item form submission
 */
function handleStockItemFormSubmit(event) {
    event.preventDefault();

    const stockItemData = {
        item: document.getElementById('modalItemName').value,
        hsn: document.getElementById('modalHsn').value,
        batch: document.getElementById('modalBatch').value,
        oem: document.getElementById('modalOem').value,
        pno: document.getElementById('modalPno').value,
        uom: document.getElementById('modalUom').value,
        rate: parseFloat(document.getElementById('modalRate').value) || 0,
        grate: parseFloat(document.getElementById('modalGstRate').value) || 18,
        mrp: parseFloat(document.getElementById('modalMrp').value) || 0,
        expiryDate: document.getElementById('modalExpiryDate').value || null
    };

    if (!stockItemData.item) {
        alert('Item name is required');
        return;
    }

    try {
        // Add to database
        const newStock = window.jsonDB.createStock(stockItemData);

        // Close modal
        closeStockItemModal();

        // Update stock item list
        populateStockItemList();

        alert('Stock item added successfully!');

    } catch (error) {
        alert('Error adding stock item: ' + error.message);
        console.error('Error adding stock item:', error);
    }
}

/**
 * Show other charges modal
 */
function showOtherChargesModal() {
    console.log('=== SHOWING OTHER CHARGES MODAL ===');

    const modal = document.getElementById('otherChargesModal');
    if (!modal) {
        console.error('Other charges modal not found!');
        return;
    }

    console.log('Modal found, displaying...');
    modal.style.display = 'flex';

    // Clear form
    const form = document.getElementById('otherChargesForm');
    if (form) {
        console.log('Resetting form...');
        form.reset();
        // Reset GST rate to default
        const gstRateInput = document.getElementById('modalChargeGstRate');
        if (gstRateInput) {
            gstRateInput.value = '18';
        }
    }

    // Wait a bit for modal to be fully displayed, then initialize
    setTimeout(() => {
        console.log('Initializing autocomplete and preview...');

        // Initialize autocomplete for description field
        initializeOtherChargesAutocomplete();

        // Setup preview calculation
        setupOtherChargesPreview();

        // Focus on description field and add click handler as fallback
        setTimeout(() => {
            const descInput = document.getElementById('modalChargeDescription');
            if (descInput) {
                console.log('Focusing on description field...');
                descInput.focus();

                // Add click handler as fallback to trigger autocomplete
                $(descInput).on('click focus', function() {
                    console.log('Input clicked/focused, triggering autocomplete...');
                    if ($(this).hasClass('ui-autocomplete-input')) {
                        $(this).autocomplete('search', '');
                    } else {
                        console.log('Autocomplete not initialized, reinitializing...');
                        setTimeout(() => initializeOtherChargesAutocomplete(), 100);
                    }
                });
            }
        }, 100);
    }, 200);
}

/**
 * Close other charges modal
 */
function closeOtherChargesModal() {
    const modal = document.getElementById('otherChargesModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Get other charges for autocomplete (predefined + database)
 */
function getOtherChargesForAutocomplete() {
    let allCharges = [...predefinedOtherCharges];

    // Get unique charges from database
    if (window.jsonDB) {
        try {
            const invoices = window.jsonDB.getInvoices();
            const databaseCharges = new Map(); // Use Map to avoid duplicates

            // Extract other charges from all invoices
            invoices.forEach(invoice => {
                if (invoice.id && window.jsonDB.data.otherCharges) {
                    const invoiceCharges = window.jsonDB.data.otherCharges.filter(charge =>
                        charge.invoiceId === invoice.id
                    );

                    invoiceCharges.forEach(charge => {
                        if (charge.description && charge.description.trim() !== '') {
                            const key = charge.description.toLowerCase().trim();
                            if (!databaseCharges.has(key)) {
                                databaseCharges.set(key, {
                                    description: charge.description,
                                    defaultAmount: charge.amount || 0,
                                    defaultGstRate: charge.grate || 18,
                                    defaultHsn: charge.hsn || '',
                                    category: 'Previously Used',
                                    isFromDatabase: true
                                });
                            }
                        }
                    });
                }
            });

            // Add database charges to the list
            allCharges = [...allCharges, ...Array.from(databaseCharges.values())];

            console.log('Found', databaseCharges.size, 'unique charges from database');
        } catch (error) {
            console.error('Error loading charges from database:', error);
        }
    }

    console.log('Total charges available for autocomplete:', allCharges.length);
    return allCharges;
}

/**
 * Initialize autocomplete for other charges description field
 */
function initializeOtherChargesAutocomplete() {
    console.log('Initializing other charges autocomplete...');

    const descriptionInput = document.getElementById('modalChargeDescription');
    if (!descriptionInput) {
        console.error('modalChargeDescription input not found!');
        return;
    }

    console.log('Found description input:', descriptionInput);

    // Check if jQuery and jQuery UI are available
    if (typeof $ === 'undefined') {
        console.error('jQuery not available for autocomplete!');
        return;
    }

    if (typeof $.fn.autocomplete === 'undefined') {
        console.error('jQuery UI autocomplete not available!');
        return;
    }

    // Destroy existing autocomplete if any
    if ($(descriptionInput).hasClass('ui-autocomplete-input')) {
        console.log('Destroying existing autocomplete...');
        $(descriptionInput).autocomplete('destroy');
    }

    // Get all available charges (predefined + database)
    const allCharges = getOtherChargesForAutocomplete();
    console.log('Setting up autocomplete with', allCharges.length, 'total charges');

    // Setup autocomplete with all charges
    $(descriptionInput).autocomplete({
        source: function(request, response) {
            const term = request.term.toLowerCase();
            const filtered = allCharges.filter(charge =>
                charge.description.toLowerCase().includes(term) ||
                charge.category.toLowerCase().includes(term)
            ).map(charge => ({
                label: charge.description,
                value: charge.description,
                chargeData: charge
            }));

            // If no matches, still allow custom input
            if (filtered.length === 0 && request.term.trim() !== '') {
                filtered.push({
                    label: `"${request.term}" (Custom Charge)`,
                    value: request.term,
                    chargeData: null
                });
            }

            response(filtered);
        },
        minLength: 0,
        select: function(event, ui) {
            console.log('Other charge selected:', ui.item);

            // Auto-fill fields if predefined charge is selected
            if (ui.item.chargeData) {
                setTimeout(() => {
                    autoFillOtherChargeFields(ui.item.chargeData);
                }, 50);
            }
            return true;
        },
        focus: function(event, ui) {
            return false; // Prevent value change on focus
        }
    }).autocomplete("instance")._renderItem = function(ul, item) {
        const chargeData = item.chargeData;
        let html = `<div><strong>${item.label}</strong>`;

        if (chargeData) {
            const categoryColor = chargeData.isFromDatabase ? '#2563eb' : '#059669'; // Blue for database, green for predefined
            const categoryIcon = chargeData.isFromDatabase ? '🕒' : '⚡'; // Clock for database, lightning for predefined

            html += `<br><small style="color: ${categoryColor};">
                ${categoryIcon} ${chargeData.category} |
                ₹${chargeData.defaultAmount} |
                ${chargeData.defaultGstRate}% GST`;

            if (chargeData.defaultHsn) {
                html += ` | HSN: ${chargeData.defaultHsn}`;
            }

            html += '</small>';
        }

        html += '</div>';

        return $("<li>").append(html).appendTo(ul);
    };

    console.log('Other charges autocomplete initialized');
}

/**
 * Auto-fill other charge fields from predefined data
 */
function autoFillOtherChargeFields(chargeData) {
    console.log('Auto-filling other charge fields:', chargeData);

    // Fill amount
    const amountInput = document.getElementById('modalChargeAmount');
    if (amountInput && chargeData.defaultAmount) {
        amountInput.value = chargeData.defaultAmount;
    }

    // Fill GST rate
    const gstRateInput = document.getElementById('modalChargeGstRate');
    if (gstRateInput && chargeData.defaultGstRate) {
        gstRateInput.value = chargeData.defaultGstRate;
    }

    // Fill HSN code
    const hsnInput = document.getElementById('modalChargeHsn');
    if (hsnInput && chargeData.defaultHsn) {
        hsnInput.value = chargeData.defaultHsn;
    }

    // Update preview
    updateOtherChargesPreview();
}

/**
 * Setup preview calculation for other charges
 */
function setupOtherChargesPreview() {
    const amountInput = document.getElementById('modalChargeAmount');
    const gstRateInput = document.getElementById('modalChargeGstRate');

    // Add event listeners for real-time preview
    if (amountInput) {
        amountInput.addEventListener('input', updateOtherChargesPreview);
    }
    if (gstRateInput) {
        gstRateInput.addEventListener('input', updateOtherChargesPreview);
    }

    // Initial preview update
    updateOtherChargesPreview();
}

/**
 * Update other charges preview calculation
 */
function updateOtherChargesPreview() {
    const amountInput = document.getElementById('modalChargeAmount');
    const gstRateInput = document.getElementById('modalChargeGstRate');

    const amount = parseFloat(amountInput?.value || 0);
    const gstRate = parseFloat(gstRateInput?.value || 18);

    const gstAmount = (amount * gstRate) / 100;
    const total = amount + gstAmount;

    // Update preview elements
    const previewAmount = document.getElementById('previewAmount');
    const previewGstRate = document.getElementById('previewGstRate');
    const previewGst = document.getElementById('previewGst');
    const previewTotal = document.getElementById('previewTotal');

    if (previewAmount) previewAmount.textContent = `₹${amount.toFixed(2)}`;
    if (previewGstRate) previewGstRate.textContent = gstRate.toFixed(0);
    if (previewGst) previewGst.textContent = `₹${gstAmount.toFixed(2)}`;
    if (previewTotal) previewTotal.textContent = `₹${total.toFixed(2)}`;
}

/**
 * Auto-fill other charge fields in jqGrid row
 */
function autoFillOtherChargeInGrid(descriptionElement, chargeData) {
    console.log('Auto-filling other charge in grid:', chargeData);

    try {
        // Find the row containing this element
        const row = $(descriptionElement).closest('tr');
        if (!row.length) {
            console.log('Could not find row for auto-fill');
            return;
        }

        // Check if we're in a modal dialog (jqGrid edit/add dialog)
        const isInModal = $(descriptionElement).closest('.ui-jqdialog').length > 0;

        if (isInModal) {
            // Modal dialog - find form fields by name
            const form = $(descriptionElement).closest('form');

            // Fill amount field
            const amountField = form.find('input[name="oth_amt"]');
            if (amountField.length && chargeData.defaultAmount) {
                amountField.val(chargeData.defaultAmount);
            }

            // Fill GST rate field
            const gstRateField = form.find('input[name="oth_grate"]');
            if (gstRateField.length && chargeData.defaultGstRate) {
                gstRateField.val(chargeData.defaultGstRate);
            }

            // Fill HSN field
            const hsnField = form.find('input[name="oth_hsn"]');
            if (hsnField.length && chargeData.defaultHsn) {
                hsnField.val(chargeData.defaultHsn);
            }

            console.log('Auto-filled modal form fields');
        } else {
            // Inline editing - find cells in the same row
            const cells = row.find('td');

            // Find amount cell (usually 2nd column after description)
            const amountCell = cells.eq(1).find('input');
            if (amountCell.length && chargeData.defaultAmount) {
                amountCell.val(chargeData.defaultAmount);
            }

            // Find GST rate cell (usually 3rd column)
            const gstRateCell = cells.eq(2).find('input');
            if (gstRateCell.length && chargeData.defaultGstRate) {
                gstRateCell.val(chargeData.defaultGstRate);
            }

            // Find HSN cell (usually 7th column)
            const hsnCell = cells.eq(6).find('input');
            if (hsnCell.length && chargeData.defaultHsn) {
                hsnCell.val(chargeData.defaultHsn);
            }

            console.log('Auto-filled inline edit fields');
        }

    } catch (error) {
        console.error('Error auto-filling other charge in grid:', error);
    }
}

/**
 * Test function for other charges autocomplete
 */
function testOtherChargesAutocomplete() {
    console.log('=== TESTING OTHER CHARGES AUTOCOMPLETE ===');

    const descInput = document.getElementById('modalChargeDescription');
    if (!descInput) {
        alert('Description input not found!');
        return;
    }

    console.log('Description input found:', descInput);
    console.log('Has autocomplete class:', $(descInput).hasClass('ui-autocomplete-input'));
    console.log('Predefined charges available:', predefinedOtherCharges.length);

    // Test by setting a value and triggering autocomplete
    descInput.value = 'trans';
    $(descInput).autocomplete('search', 'trans');

    alert(`Autocomplete test initiated!
    - Input found: ✓
    - Has autocomplete: ${$(descInput).hasClass('ui-autocomplete-input') ? '✓' : '✗'}
    - Predefined charges: ${predefinedOtherCharges.length}
    - Test search for "trans" triggered

    Check console for detailed logs.`);
}

/**
 * Auto-save form data to localStorage
 */
function autoSaveFormData() {
    if (!AUTO_SAVE_CONFIG.enabled) return;

    try {
        // Collect current form data
        const formData = {
            // Basic form fields (using correct IDs)
            type: document.getElementById('billType')?.value || '',
            bno: document.getElementById('billNumber')?.value || '',
            bdate: document.getElementById('billDate')?.value || '',
            partyName: document.getElementById('partyName')?.value || '',
            address: document.getElementById('partyAddress')?.value || '',
            gstin: document.getElementById('partyGstin')?.value || '',
            state: document.getElementById('partyState')?.value || '',
            pin: document.getElementById('partyPin')?.value || '',

            // Stock items grid data
            stockItems: [],

            // Other charges grid data
            otherCharges: [],

            // Totals
            gtot: billForm.gtot || 0,
            cgst: billForm.cgst || 0,
            sgst: billForm.sgst || 0,
            igst: billForm.igst || 0,
            roff: billForm.rof || 0,
            ntot: billForm.ntot || 0,

            // Timestamp
            savedAt: new Date().toISOString()
        };

        // Get stock items from grid
        try {
            const stockGridData = $('#stockItemsGrid').jqGrid('getRowData');
            formData.stockItems = stockGridData || [];
        } catch (error) {
            if (AUTO_SAVE_CONFIG.enableLogging) {
                console.log('Could not get stock grid data for auto-save:', error.message);
            }
        }

        // Get other charges from grid
        try {
            const chargesGridData = $('#otherChargesGrid').jqGrid('getRowData');
            formData.otherCharges = chargesGridData || [];
        } catch (error) {
            if (AUTO_SAVE_CONFIG.enableLogging) {
                console.log('Could not get charges grid data for auto-save:', error.message);
            }
        }

        // Save to localStorage
        localStorage.setItem(AUTO_SAVE_CONFIG.storageKey, JSON.stringify(formData));

        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Form data auto-saved:', {
                stockItems: formData.stockItems.length,
                otherCharges: formData.otherCharges.length,
                partyName: formData.partyName,
                savedAt: formData.savedAt
            });
        }

        // Auto-save indicator disabled for better UX
        // showAutoSaveIndicator();

    } catch (error) {
        console.error('Error auto-saving form data:', error);
    }
}

/**
 * Restore form data from localStorage
 */
function restoreFormData() {
    if (!AUTO_SAVE_CONFIG.enabled) return false;

    try {
        const savedData = localStorage.getItem(AUTO_SAVE_CONFIG.storageKey);
        if (!savedData) return false;

        const formData = JSON.parse(savedData);

        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Restoring form data:', formData);
        }

        // Check if data is recent (within 24 hours)
        const savedTime = new Date(formData.savedAt);
        const now = new Date();
        const hoursDiff = (now - savedTime) / (1000 * 60 * 60);

        if (hoursDiff > 24) {
            if (AUTO_SAVE_CONFIG.enableLogging) {
                console.log('Saved data is too old, not restoring');
            }
            clearAutoSavedData();
            return false;
        }

        // Auto-restore without asking (less intrusive UX)
        // Only restore if data is recent (within last 2 hours)
        const hoursThreshold = 2;
        if (hoursDiff > hoursThreshold) {
            if (AUTO_SAVE_CONFIG.enableLogging) {
                console.log(`Saved data is older than ${hoursThreshold} hours, not restoring`);
            }
            clearAutoSavedData();
            return false;
        }

        // Automatically restore recent data without confirmation
        console.log(`Auto-restoring recent invoice data from ${savedTime.toLocaleString()}`);
        console.log(`- Party: ${formData.partyName || 'Not specified'}`);
        console.log(`- Stock Items: ${formData.stockItems.length}`);
        console.log(`- Other Charges: ${formData.otherCharges.length}`);

        // Restore basic form fields (using correct IDs)
        if (formData.type) document.getElementById('billType').value = formData.type;
        if (formData.bno) document.getElementById('billNumber').value = formData.bno;
        if (formData.bdate) document.getElementById('billDate').value = formData.bdate;
        if (formData.partyName) document.getElementById('partyName').value = formData.partyName;
        if (formData.address) document.getElementById('partyAddress').value = formData.address;
        if (formData.gstin) document.getElementById('partyGstin').value = formData.gstin;
        if (formData.state) document.getElementById('partyState').value = formData.state;
        if (formData.pin) document.getElementById('partyPin').value = formData.pin;

        // Restore billForm totals
        billForm.gtot = formData.gtot || 0;
        billForm.cgst = formData.cgst || 0;
        billForm.sgst = formData.sgst || 0;
        billForm.igst = formData.igst || 0;
        billForm.rof = formData.roff || 0;
        billForm.ntot = formData.ntot || 0;

        // Restore stock items grid
        if (formData.stockItems && formData.stockItems.length > 0) {
            setTimeout(() => {
                try {
                    const stockGrid = $('#stockItemsGrid');
                    stockGrid.jqGrid('clearGridData');

                    formData.stockItems.forEach((item, index) => {
                        stockGrid.jqGrid('addRowData', index + 1, item);
                    });

                    if (AUTO_SAVE_CONFIG.enableLogging) {
                        console.log('Restored', formData.stockItems.length, 'stock items');
                    }
                } catch (error) {
                    console.error('Error restoring stock items:', error);
                }
            }, 500);
        }

        // Restore other charges grid
        if (formData.otherCharges && formData.otherCharges.length > 0) {
            setTimeout(() => {
                try {
                    const chargesGrid = $('#otherChargesGrid');
                    chargesGrid.jqGrid('clearGridData');

                    formData.otherCharges.forEach((charge, index) => {
                        chargesGrid.jqGrid('addRowData', index + 1, charge);
                    });

                    if (AUTO_SAVE_CONFIG.enableLogging) {
                        console.log('Restored', formData.otherCharges.length, 'other charges');
                    }
                } catch (error) {
                    console.error('Error restoring other charges:', error);
                }
            }, 600);
        }

        // Update totals display
        setTimeout(() => {
            updateTotalsDisplay();
        }, 700);

        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Form data restored successfully');
        }

        return true;

    } catch (error) {
        console.error('Error restoring form data:', error);
        clearAutoSavedData();
        return false;
    }
}

/**
 * Clear auto-saved data
 */
function clearAutoSavedData() {
    try {
        localStorage.removeItem(AUTO_SAVE_CONFIG.storageKey);
        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Auto-saved data cleared');
        }
    } catch (error) {
        console.error('Error clearing auto-saved data:', error);
    }
}

/**
 * Start auto-save timer
 */
function startAutoSave() {
    if (!AUTO_SAVE_CONFIG.enabled) return;

    // Clear existing timer
    if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
    }

    // Start new timer
    autoSaveTimer = setInterval(() => {
        autoSaveFormData();
    }, AUTO_SAVE_CONFIG.saveInterval);

    if (AUTO_SAVE_CONFIG.enableLogging) {
        console.log('Auto-save started, interval:', AUTO_SAVE_CONFIG.saveInterval, 'ms');
    }
}

/**
 * Stop auto-save timer
 */
function stopAutoSave() {
    if (autoSaveTimer) {
        clearInterval(autoSaveTimer);
        autoSaveTimer = null;

        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Auto-save stopped');
        }
    }
}

/**
 * Initialize auto-save system
 */
function initializeAutoSaveSystem() {
    if (!AUTO_SAVE_CONFIG.enabled) return;

    // Try to restore previous data
    restoreFormData();

    // Start auto-save timer
    startAutoSave();

    // Setup form change listeners for immediate auto-save
    setupFormChangeListeners();

    // Visual indicator disabled for better UX
    // addAutoSaveIndicator();
}

/**
 * Setup form change listeners for auto-save
 */
function setupFormChangeListeners() {
    if (!AUTO_SAVE_CONFIG.enabled) return;

    // Basic form fields (using correct IDs)
    const formFields = [
        'billType', 'billNumber', 'billDate', 'partyName',
        'partyGstin', 'partyState', 'partyAddress', 'partyPin'
    ];

    formFields.forEach(fieldId => {
        const field = document.getElementById(fieldId);
        if (field) {
            field.addEventListener('change', () => {
                autoSaveFormData();
            });
        }
    });
}

/**
 * Update totals display from billForm
 */
function updateTotalsDisplay() {
    try {
        // Update gross total
        const grossTotalElement = document.getElementById('grossTotal');
        if (grossTotalElement) {
            grossTotalElement.textContent = `₹${(billForm.gtot || 0).toFixed(2)}`;
        }

        // Update CGST
        const cgstTotalElement = document.getElementById('cgstTotal');
        if (cgstTotalElement) {
            cgstTotalElement.textContent = `₹${(billForm.cgst || 0).toFixed(2)}`;
        }

        // Update SGST
        const sgstTotalElement = document.getElementById('sgstTotal');
        if (sgstTotalElement) {
            sgstTotalElement.textContent = `₹${(billForm.sgst || 0).toFixed(2)}`;
        }

        // Update IGST
        const igstTotalElement = document.getElementById('igstTotal');
        if (igstTotalElement) {
            igstTotalElement.textContent = `₹${(billForm.igst || 0).toFixed(2)}`;
        }

        // Update Round Off
        const roundOffElement = document.getElementById('roundOff');
        if (roundOffElement) {
            roundOffElement.textContent = `₹${(billForm.rof || 0).toFixed(2)}`;
        }

        // Update Net Total
        const netTotalElement = document.getElementById('netTotal');
        if (netTotalElement) {
            netTotalElement.textContent = `₹${(billForm.ntot || 0).toFixed(2)}`;
        }

        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Totals display updated from billForm');
        }

    } catch (error) {
        console.error('Error updating totals display:', error);
    }
}

/**
 * Handle other charges form submission
 */
function handleOtherChargesFormSubmit(event) {
    event.preventDefault();

    const chargeData = {
        description: document.getElementById('modalChargeDescription').value,
        oth_amt: parseFloat(document.getElementById('modalChargeAmount').value) || 0,
        oth_grate: parseFloat(document.getElementById('modalChargeGstRate').value) || 0,
        oth_hsn: document.getElementById('modalChargeHsn').value
    };

    if (!chargeData.description) {
        alert('Description is required');
        return;
    }

    // Calculate GST for the charge
    const selectedParty = inventoryData.parties.find(p => p.supply === billForm.partyName);
    const partyState = selectedParty?.state?.toLowerCase()?.trim() || '';
    const firmStateNormalized = firmState?.toLowerCase()?.trim() || '';

    if (firmStateNormalized === partyState) {
        // Same state - CGST/SGST
        chargeData.oth_cgst = parseFloat((chargeData.oth_amt * chargeData.oth_grate / 100 / 2).toFixed(2));
        chargeData.oth_sgst = parseFloat((chargeData.oth_amt * chargeData.oth_grate / 100 / 2).toFixed(2));
        chargeData.oth_igst = 0;
    } else {
        // Different states - IGST
        chargeData.oth_cgst = 0;
        chargeData.oth_sgst = 0;
        chargeData.oth_igst = parseFloat((chargeData.oth_amt * chargeData.oth_grate / 100).toFixed(2));
    }

    chargeData.oth_tot = parseFloat((chargeData.oth_amt + chargeData.oth_cgst + chargeData.oth_sgst + chargeData.oth_igst).toFixed(2));

    // Add to bill form
    billForm.oth_chg.push(chargeData);

    // Close modal
    closeOtherChargesModal();

    // Render other charges table
    renderOtherChargesTable();

    // Recalculate totals
    calculateBillTotal();

    alert('Other charge added successfully!');
}

/**
 * Render other charges table
 */
function renderOtherChargesTable() {
    const tableBody = document.getElementById('otherChargesTableBody');
    if (!tableBody) return;

    if (billForm.oth_chg.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 2rem; color: #6b7280;">No charges added.</td></tr>';
        return;
    }

    tableBody.innerHTML = billForm.oth_chg.map((charge, index) => `
        <tr>
            <td>${charge.description}</td>
            <td style="text-align: right;">₹${charge.oth_amt.toFixed(2)}</td>
            <td style="text-align: right;">${charge.oth_grate}%</td>
            <td style="text-align: right;">₹${charge.oth_cgst.toFixed(2)}</td>
            <td style="text-align: right;">₹${charge.oth_sgst.toFixed(2)}</td>
            <td style="text-align: right;">₹${charge.oth_igst.toFixed(2)}</td>
            <td>${charge.oth_hsn}</td>
            <td style="text-align: right; font-weight: 600; color: #059669;">₹${charge.oth_tot.toFixed(2)}</td>
            <td>
                <button type="button" onclick="removeOtherCharge(${index})" class="btn-icon" title="Remove Charge" style="color: #ef4444;">
                    🗑️
                </button>
            </td>
        </tr>
    `).join('');
}

/**
 * Remove other charge
 */
function removeOtherCharge(index) {
    if (confirm('Are you sure you want to remove this charge?')) {
        billForm.oth_chg.splice(index, 1);
        renderOtherChargesTable();
        calculateBillTotal();
    }
}

/**
 * Show column visibility modal
 */
function showColumnVisibilityModal() {
    const modal = document.getElementById('columnVisibilityModal');
    if (modal) {
        modal.style.display = 'flex';

        // Update checkboxes with current visibility
        document.getElementById('toggleProject').checked = columnVisibility.project;
        document.getElementById('toggleDisc').checked = columnVisibility.disc;
        document.getElementById('toggleCgst').checked = columnVisibility.cgst;
        document.getElementById('toggleSgst').checked = columnVisibility.sgst;
        document.getElementById('toggleIgst').checked = columnVisibility.igst;
        document.getElementById('toggleMrp').checked = columnVisibility.mrp;
        document.getElementById('toggleExpiryDate').checked = columnVisibility.expiryDate;
    }
}

/**
 * Close column visibility modal
 */
function closeColumnVisibilityModal() {
    const modal = document.getElementById('columnVisibilityModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Apply column visibility settings
 */
function applyColumnVisibility() {
    // Update visibility settings
    columnVisibility.project = document.getElementById('toggleProject')?.checked ?? true;
    columnVisibility.disc = document.getElementById('toggleDisc')?.checked ?? true;
    columnVisibility.cgst = document.getElementById('toggleCgst')?.checked ?? true;
    columnVisibility.sgst = document.getElementById('toggleSgst')?.checked ?? true;
    columnVisibility.igst = document.getElementById('toggleIgst')?.checked ?? true;
    columnVisibility.mrp = document.getElementById('toggleMrp')?.checked ?? true;
    columnVisibility.expiryDate = document.getElementById('toggleExpiryDate')?.checked ?? true;

    // Apply visibility to table columns
    const columns = {
        'column-project': columnVisibility.project,
        'column-disc': columnVisibility.disc,
        'column-cgst': columnVisibility.cgst,
        'column-sgst': columnVisibility.sgst,
        'column-igst': columnVisibility.igst,
        'column-mrp': columnVisibility.mrp,
        'column-expiryDate': columnVisibility.expiryDate
    };

    Object.entries(columns).forEach(([className, isVisible]) => {
        const elements = document.querySelectorAll(`.${className}`);
        elements.forEach(el => {
            el.style.display = isVisible ? '' : 'none';
        });
    });

    // Close modal
    closeColumnVisibilityModal();

    // Save to localStorage
    localStorage.setItem('inventoryColumnSettings', JSON.stringify(columnVisibility));
}

/**
 * Show keyboard shortcuts modal
 */
function showKeyboardShortcutsModal() {
    const modal = document.getElementById('keyboardShortcutsModal');
    if (modal) {
        modal.style.display = 'flex';
    }
}

/**
 * Map state jurisdiction to state name for dropdown selection
 */
function mapStateJurisdictionToState(stateJurisdiction) {
    if (!stateJurisdiction) return '';

    // Common state jurisdiction to state name mappings
    const stateMapping = {
        // Andhra Pradesh
        'Visakhapatnam': 'Andhra Pradesh',
        'Vijayawada': 'Andhra Pradesh',
        'Guntur': 'Andhra Pradesh',
        'Tirupati': 'Andhra Pradesh',

        // Assam
        'Guwahati': 'Assam',
        'Dibrugarh': 'Assam',
        'Silchar': 'Assam',

        // Bihar
        'Patna': 'Bihar',
        'Patna West': 'Bihar',
        'Patna East': 'Bihar',
        'Muzaffarpur': 'Bihar',
        'Bhagalpur': 'Bihar',
        'Gaya': 'Bihar',

        // Delhi
        'New Delhi': 'Delhi',
        'Delhi North': 'Delhi',
        'Delhi South': 'Delhi',
        'Delhi East': 'Delhi',
        'Delhi West': 'Delhi',

        // Gujarat
        'Ahmedabad': 'Gujarat',
        'Surat': 'Gujarat',
        'Vadodara': 'Gujarat',
        'Rajkot': 'Gujarat',
        'Gandhinagar': 'Gujarat',

        // Haryana
        'Gurgaon': 'Haryana',
        'Faridabad': 'Haryana',
        'Panipat': 'Haryana',
        'Ambala': 'Haryana',

        // Karnataka
        'Bangalore': 'Karnataka',
        'Bengaluru': 'Karnataka',
        'Mysore': 'Karnataka',
        'Hubli': 'Karnataka',
        'Mangalore': 'Karnataka',

        // Kerala
        'Kochi': 'Kerala',
        'Thiruvananthapuram': 'Kerala',
        'Kozhikode': 'Kerala',
        'Thrissur': 'Kerala',

        // Maharashtra
        'Mumbai': 'Maharashtra',
        'Pune': 'Maharashtra',
        'Nagpur': 'Maharashtra',
        'Nashik': 'Maharashtra',
        'Aurangabad': 'Maharashtra',
        'Thane': 'Maharashtra',

        // Punjab
        'Chandigarh': 'Punjab',
        'Ludhiana': 'Punjab',
        'Amritsar': 'Punjab',
        'Jalandhar': 'Punjab',

        // Rajasthan
        'Jaipur': 'Rajasthan',
        'Jodhpur': 'Rajasthan',
        'Udaipur': 'Rajasthan',
        'Kota': 'Rajasthan',

        // Tamil Nadu
        'Chennai': 'Tamil Nadu',
        'Coimbatore': 'Tamil Nadu',
        'Madurai': 'Tamil Nadu',
        'Salem': 'Tamil Nadu',
        'Tiruchirapalli': 'Tamil Nadu',

        // Telangana
        'Hyderabad': 'Telangana',
        'Warangal': 'Telangana',
        'Nizamabad': 'Telangana',

        // Uttar Pradesh
        'Lucknow': 'Uttar Pradesh',
        'Kanpur': 'Uttar Pradesh',
        'Agra': 'Uttar Pradesh',
        'Varanasi': 'Uttar Pradesh',
        'Meerut': 'Uttar Pradesh',
        'Allahabad': 'Uttar Pradesh',
        'Prayagraj': 'Uttar Pradesh',

        // West Bengal
        'Kolkata': 'West Bengal',
        'Howrah': 'West Bengal',
        'Durgapur': 'West Bengal',
        'Siliguri': 'West Bengal'
    };

    // Direct mapping
    if (stateMapping[stateJurisdiction]) {
        return stateMapping[stateJurisdiction];
    }

    // Try to extract state name from jurisdiction
    // Remove common suffixes like "East", "West", "North", "South", "Central"
    const cleanJurisdiction = stateJurisdiction
        .replace(/\s+(East|West|North|South|Central|Range|Division|Circle)$/i, '')
        .trim();

    if (stateMapping[cleanJurisdiction]) {
        return stateMapping[cleanJurisdiction];
    }

    // If no mapping found, return the original jurisdiction
    return stateJurisdiction;
}

/**
 * Fetch party details by GST number for modal
 */
async function fetchPartyByGSTModal(buttonElement) {
    const gstinInput = document.getElementById('modalPartyGstin');
    const gstin = gstinInput?.value?.trim();

    if (!gstin) {
        alert('Please enter a GSTIN number first');
        return;
    }

    // Validate GSTIN format (15 characters)
    if (gstin.length !== 15) {
        alert('GSTIN must be 15 characters long');
        return;
    }

    // Show loading state
    const fetchButton = buttonElement;
    const originalText = fetchButton.innerHTML;
    fetchButton.innerHTML = '⏳';
    fetchButton.disabled = true;

    try {
        console.log('Fetching party details for GSTIN:', gstin);

        // RapidAPI Configuration
        const RAPIDAPI_KEY = GST_API_CONFIG.rapidApiKey;

        // Check if RapidAPI key is configured
        if (RAPIDAPI_KEY === 'YOUR_RAPIDAPI_KEY_HERE') {
            alert('GST API requires RapidAPI key configuration.\n\nPlease configure your RapidAPI key in the GST_API_CONFIG section.');
            return;
        }

        // Try multiple RapidAPI endpoints (same as main page)
        const rapidApiEndpoints = [
            {
                url: `https://powerful-gstin-tool.p.rapidapi.com/v1/gstin/${gstin}/details`,
                headers: {
                    'x-rapidapi-key': RAPIDAPI_KEY,
                    'x-rapidapi-host': 'powerful-gstin-tool.p.rapidapi.com'
                },
                parser: (data) => data
            }
        ];

        let apiSuccess = false;

        for (const endpoint of rapidApiEndpoints) {
            try {
                console.log('Trying RapidAPI endpoint:', endpoint.url);

                const fetchOptions = {
                    method: endpoint.method || 'GET',
                    headers: endpoint.headers
                };

                if (endpoint.body) {
                    fetchOptions.body = endpoint.body;
                }

                const response = await fetch(endpoint.url, fetchOptions);

                if (response.ok) {
                    const data = await response.json();
                    if (GST_API_CONFIG.enableLogging) {
                        console.log('RapidAPI Response:', data);
                    }

                    // Parse the response based on the endpoint
                    const parsedData = endpoint.parser(data);

                    // Check for successful response in various formats (same as main page)
                    const isSuccess = parsedData && (
                        parsedData.status === 'success' ||
                        parsedData.flag === true ||
                        parsedData.success === true ||
                        parsedData.data ||
                        parsedData.result ||
                        (parsedData.gstin && parsedData.legal_name) || // Powerful GSTIN Tool format
                        (parsedData.gstin && parsedData.trade_name) || // Powerful GSTIN Tool format
                        (parsedData.legal_name || parsedData.trade_name) // Direct data format
                    );

                    if (isSuccess) {
                        // Extract party data from various possible locations
                        const partyData = parsedData.data || parsedData.result || parsedData;
                        // Add small delay to ensure modal is fully rendered
                        setTimeout(() => {
                            populateModalFromGSTData(partyData, gstin);
                            alert('Party details fetched successfully from GST database!');
                        }, 100);
                        apiSuccess = true;
                        break;
                    } else {
                        if (GST_API_CONFIG.enableLogging) {
                            console.log('API response not recognized as success:', parsedData);
                        }
                    }
                }
            } catch (endpointError) {
                console.log('RapidAPI endpoint failed:', endpoint.url, endpointError.message);
                continue;
            }
        }

        if (!apiSuccess) {
            alert('No valid data found for this GSTIN. Please check the number and try again.');
        }

    } catch (error) {
        console.error('Error fetching party details:', error);
        alert('Failed to fetch party details. Please check your internet connection and API configuration.');
    } finally {
        // Restore button state
        fetchButton.innerHTML = originalText;
        fetchButton.disabled = false;
    }
}

/**
 * Fetch party details by GST number using RPID API
 */
async function fetchPartyByGST(buttonElement) {
    const gstinInput = document.getElementById('partyGstin');
    const gstin = gstinInput?.value?.trim();

    if (!gstin) {
        alert('Please enter a GSTIN number first');
        return;
    }

    // Validate GSTIN format (15 characters)
    if (gstin.length !== 15) {
        alert('GSTIN must be 15 characters long');
        return;
    }

    // Show loading state
    const fetchButton = buttonElement;
    const originalText = fetchButton.innerHTML;
    fetchButton.innerHTML = '⏳';
    fetchButton.disabled = true;

    try {
        console.log('Fetching party details for GSTIN:', gstin);

        // RapidAPI Configuration
        const RAPIDAPI_KEY = GST_API_CONFIG.rapidApiKey;

        // Try multiple RapidAPI endpoints
        const rapidApiEndpoints = [
            {
                url: `https://powerful-gstin-tool.p.rapidapi.com/v1/gstin/${gstin}/details`,
                headers: {
                    'x-rapidapi-key': RAPIDAPI_KEY,
                    'x-rapidapi-host': 'powerful-gstin-tool.p.rapidapi.com'
                },
                parser: (data) => data // Adjust based on actual response format
            },
            {
                url: `https://verification-solutions.p.rapidapi.com/v1/gst/verify`,
                headers: {
                    'X-RapidAPI-Key': RAPIDAPI_KEY,
                    'X-RapidAPI-Host': 'verification-solutions.p.rapidapi.com',
                    'Content-Type': 'application/json'
                },
                method: 'POST',
                body: JSON.stringify({ gstin: gstin }),
                parser: (data) => data.result || data // Adjust based on actual response format
            }
        ];

        let apiSuccess = false;

        // Check if RapidAPI key is configured
        if (RAPIDAPI_KEY === 'YOUR_RAPIDAPI_KEY_HERE') {
            alert(
                'GST API requires RapidAPI key configuration.\n\n' +
                'To use real GST verification:\n' +
                '1. Sign up at rapidapi.com\n' +
                '2. Subscribe to a GST verification API\n' +
                '3. Replace YOUR_RAPIDAPI_KEY_HERE in the code\n\n' +
                'Please configure your API key to use GST verification.'
            );
            return;
        }

        for (const endpoint of rapidApiEndpoints) {
            try {
                console.log('Trying RapidAPI endpoint:', endpoint.url);

                const fetchOptions = {
                    method: endpoint.method || 'GET',
                    headers: endpoint.headers
                };

                if (endpoint.body) {
                    fetchOptions.body = endpoint.body;
                }

                const response = await fetch(endpoint.url, fetchOptions);

                if (response.ok) {
                    const data = await response.json();
                    if (GST_API_CONFIG.enableLogging) {
                        console.log('RapidAPI Response:', data);
                    }

                    // Parse the response based on the endpoint
                    const parsedData = endpoint.parser(data);

                    // Check for successful response in various formats
                    const isSuccess = parsedData && (
                        parsedData.status === 'success' ||
                        parsedData.flag === true ||
                        parsedData.success === true ||
                        parsedData.data ||
                        parsedData.result ||
                        (parsedData.gstin && parsedData.legal_name) || // Powerful GSTIN Tool format
                        (parsedData.gstin && parsedData.trade_name) || // Powerful GSTIN Tool format
                        (parsedData.legal_name || parsedData.trade_name) // Direct data format
                    );

                    if (isSuccess) {
                        // Extract party data from various possible locations
                        const partyData = parsedData.data || parsedData.result || parsedData;
                        populatePartyFromRapidAPI(partyData, gstin);
                        alert('Party details fetched successfully from RapidAPI!');
                        apiSuccess = true;
                        break;
                    } else {
                        if (GST_API_CONFIG.enableLogging) {
                            console.log('API response not recognized as success:', parsedData);
                        }
                    }
                }
            } catch (endpointError) {
                console.log('RapidAPI endpoint failed:', endpoint.url, endpointError.message);
                continue;
            }
        }

        if (!apiSuccess) {
            alert('GST API is currently unavailable. Please enter party details manually.');
        }

    } catch (error) {
        console.error('Error fetching party details:', error);
        alert('Failed to fetch party details from GST API. Please enter party details manually.');

    } finally {
        // Restore button state
        fetchButton.innerHTML = originalText;
        fetchButton.disabled = false;
    }
}

/**
 * Format address from RPID API response
 */
function formatAddress(partyData) {
    if (!partyData) return '';

    // Try to get address from pradr (principal address) first, then adadr (additional address)
    const address = partyData.pradr || partyData.adadr;
    if (!address) return '';

    // Format address components
    const addressParts = [];

    if (address.bno) addressParts.push(address.bno);
    if (address.bnm) addressParts.push(address.bnm);
    if (address.st) addressParts.push(address.st);
    if (address.loc) addressParts.push(address.loc);
    if (address.dst) addressParts.push(address.dst);
    if (address.stcd && address.stcd !== partyData.stj) addressParts.push(address.stcd);
    if (address.pncd) addressParts.push(address.pncd);

    return addressParts.join(', ');
}

/**
 * Extract PIN code from address
 */
function extractPinFromAddress(partyData) {
    if (!partyData) return '';

    // Try to get PIN from pradr first, then adadr
    const address = partyData.pradr || partyData.adadr;
    return address?.pncd || '';
}



/**
 * Populate party form from API data
 */
function populatePartyFromData(partyData, gstin) {
    // Update form fields with fetched data
    updateFormField('partyName', partyData.tradeNam || partyData.lgnm || '');
    updateFormField('partyAddress', formatAddress(partyData) || '');
    updateFormField('partyState', partyData.stj || '');
    updateFormField('partyPin', extractPinFromAddress(partyData) || '');

    // Update billForm object
    billForm.partyName = partyData.tradeNam || partyData.lgnm || '';
    billForm.partyAddress = formatAddress(partyData) || '';
    billForm.partyGstin = gstin;
    billForm.partyState = partyData.stj || '';
    billForm.partyPin = extractPinFromAddress(partyData) || '';

    // Add to inventory data if not already exists
    const existingParty = inventoryData.parties.find(p => p.gstin === gstin);
    if (!existingParty) {
        const newParty = {
            supply: billForm.partyName,
            addr: billForm.partyAddress,
            gstin: gstin,
            state: billForm.partyState,
            pin: billForm.partyPin,
            // Additional data from API
            lgnm: partyData.lgnm,
            tradeNam: partyData.tradeNam,
            sts: partyData.sts,
            rgdt: partyData.rgdt,
            ctb: partyData.ctb,
            pradr: partyData.pradr,
            adadr: partyData.adadr
        };

        inventoryData.parties.push(newParty);
        populatePartyList(); // Refresh the party dropdown
    }

    // Recalculate totals as GST depends on state
    calculateBillTotal();
}

/**
 * Populate party form from RapidAPI data
 */
function populatePartyFromRapidAPI(partyData, gstin) {
    if (GST_API_CONFIG.enableLogging) {
        console.log('Processing RapidAPI data:', partyData);
    }

    // Extract data based on actual Powerful GSTIN Tool API response structure
    const tradeName = partyData.trade_name || '';
    const legalName = partyData.legal_name || '';

    // Use trade name first, then legal name
    const displayName = tradeName || legalName;

    // Extract and map state from state_jurisdiction
    const stateJurisdiction = partyData.state_jurisdiction || '';
    const state = mapStateJurisdictionToState(stateJurisdiction);

    // Format address from place_of_business_principal
    const address = formatPowerfulGSTINAddress(partyData) || '';

    // Extract PIN from principal place of business
    const pinCode = extractPowerfulGSTINPinCode(partyData) || '';

    // Validate that we have at least a name
    if (!displayName) {
        alert('No valid company name found in API response. Please enter details manually.');
        return;
    }

    // Update form fields with fetched data
    updateFormField('partyName', displayName);
    updateFormField('partyAddress', address);
    updateFormField('partyState', state);
    updateFormField('partyPin', pinCode);

    // Update billForm object
    billForm.partyName = displayName;
    billForm.partyAddress = address;
    billForm.partyGstin = gstin;
    billForm.partyState = state;
    billForm.partyPin = pinCode;

    // Add to inventory data if not already exists
    const existingParty = inventoryData.parties.find(p => p.gstin === gstin);
    if (!existingParty) {
        const newParty = {
            supply: billForm.partyName,
            addr: billForm.partyAddress,
            gstin: gstin,
            state: billForm.partyState,
            pin: billForm.partyPin,
            // Store additional API data
            apiData: partyData,
            tradeName: tradeName,
            legalName: legalName,
            status: partyData.status || '',
            registrationDate: partyData.registration_date || '',
            businessConstitution: partyData.business_constitution || '',
            businessNature: partyData.business_activity_nature || []
        };

        inventoryData.parties.push(newParty);
        populatePartyList(); // Refresh the party dropdown
    }

    // Recalculate totals as GST depends on state
    calculateBillTotal();
}

/**
 * Format address from Powerful GSTIN Tool API response
 */
function formatPowerfulGSTINAddress(partyData) {
    if (!partyData || !partyData.place_of_business_principal) return '';

    const addr = partyData.place_of_business_principal.address;
    if (!addr) return '';

    const parts = [];

    // Building details
    if (addr.door_num) parts.push(addr.door_num);
    if (addr.building_name) parts.push(addr.building_name);
    if (addr.floor_num) parts.push(addr.floor_num);

    // Street and location
    if (addr.street) parts.push(addr.street);
    if (addr.location) parts.push(addr.location);

    // City and district
    if (addr.city) parts.push(addr.city);
    if (addr.district) parts.push(addr.district);
    if (addr.state) parts.push(addr.state);

    // Don't include PIN in address as it has separate field

    return parts.filter(p => p && p.toString().trim()).join(', ');
}

/**
 * Format address from RapidAPI response (fallback for other APIs)
 */
function formatRapidAPIAddress(partyData) {
    if (!partyData) return '';

    // Try different possible address field names from RapidAPI
    const addressFields = [
        partyData.address,
        partyData.principalPlaceAddress,
        partyData.principal_place_address,
        partyData.pradr,
        partyData.businessAddress,
        partyData.business_address,
        partyData.registeredAddress,
        partyData.registered_address,
        partyData.addr
    ];

    for (const addr of addressFields) {
        if (addr) {
            if (typeof addr === 'string') {
                return addr.trim();
            } else if (typeof addr === 'object') {
                // If address is an object, format it
                const parts = [];

                // Building details
                if (addr.buildingNo || addr.bno || addr.building_no) {
                    parts.push(addr.buildingNo || addr.bno || addr.building_no);
                }
                if (addr.buildingName || addr.bnm || addr.building_name) {
                    parts.push(addr.buildingName || addr.bnm || addr.building_name);
                }
                if (addr.floorNo || addr.floor_no) {
                    parts.push(addr.floorNo || addr.floor_no);
                }

                // Street and location
                if (addr.street || addr.st) parts.push(addr.street || addr.st);
                if (addr.location || addr.loc) parts.push(addr.location || addr.loc);
                if (addr.locality) parts.push(addr.locality);
                if (addr.area) parts.push(addr.area);

                // City and district
                if (addr.city) parts.push(addr.city);
                if (addr.district || addr.dst) parts.push(addr.district || addr.dst);

                // Don't include PIN in address as it has separate field

                return parts.filter(p => p && p.toString().trim()).join(', ');
            }
        }
    }

    // Try to build address from individual fields if no complete address found
    const parts = [];
    if (partyData.buildingNo) parts.push(partyData.buildingNo);
    if (partyData.buildingName) parts.push(partyData.buildingName);
    if (partyData.street) parts.push(partyData.street);
    if (partyData.city) parts.push(partyData.city);
    if (partyData.district) parts.push(partyData.district);

    return parts.filter(p => p && p.toString().trim()).join(', ');
}

/**
 * Extract PIN code from Powerful GSTIN Tool API response
 */
function extractPowerfulGSTINPinCode(partyData) {
    if (!partyData || !partyData.place_of_business_principal) return '';

    const addr = partyData.place_of_business_principal.address;
    if (!addr || !addr.pin_code) return '';

    const pinStr = addr.pin_code.toString().trim();
    // Validate PIN format (6 digits)
    if (/^\d{6}$/.test(pinStr)) {
        return pinStr;
    }

    return '';
}

/**
 * Extract PIN code from RapidAPI response (fallback for other APIs)
 */
function extractRapidAPIPinCode(partyData) {
    if (!partyData) return '';

    // Try different possible PIN field names
    const pinFields = [
        partyData.pincode,
        partyData.pin,
        partyData.postalCode,
        partyData.postal_code,
        partyData.principalPlacePincode,
        partyData.principal_place_pincode,
        partyData.zipCode,
        partyData.zip_code,
        partyData.pncd
    ];

    for (const pin of pinFields) {
        if (pin) {
            const pinStr = pin.toString().trim();
            // Validate PIN format (6 digits)
            if (/^\d{6}$/.test(pinStr)) {
                return pinStr;
            }
        }
    }

    // Try to extract from address object
    const addressFields = [
        partyData.address,
        partyData.principalPlaceAddress,
        partyData.principal_place_address,
        partyData.pradr,
        partyData.businessAddress,
        partyData.business_address,
        partyData.registeredAddress,
        partyData.registered_address
    ];

    for (const address of addressFields) {
        if (address && typeof address === 'object') {
            const addressPin = address.pincode || address.pncd || address.pin ||
                              address.postalCode || address.postal_code || address.zipCode;
            if (addressPin) {
                const pinStr = addressPin.toString().trim();
                if (/^\d{6}$/.test(pinStr)) {
                    return pinStr;
                }
            }
        }
    }

    return '';
}

/**
 * Center modal dialog on screen
 */
function centerModal(form) {
    try {
        console.log('Centering modal...');

        // Find the modal dialog container
        let modalDialog = form.closest('.ui-dialog');
        if (!modalDialog.length) {
            // Try alternative selectors
            modalDialog = form.closest('[role="dialog"]');
        }
        if (!modalDialog.length) {
            modalDialog = form.closest('.ui-jqdialog');
        }

        if (modalDialog.length > 0) {
            console.log('Found modal dialog, centering...');

            // Get window dimensions
            const windowWidth = $(window).width();
            const windowHeight = $(window).height();

            // Get modal dimensions
            const modalWidth = modalDialog.outerWidth();
            const modalHeight = modalDialog.outerHeight();

            // Calculate center position
            const left = Math.max(0, (windowWidth - modalWidth) / 2);
            const top = Math.max(0, (windowHeight - modalHeight) / 2);

            // Apply centering
            modalDialog.css({
                'position': 'fixed',
                'left': left + 'px',
                'top': top + 'px',
                'margin': '0'
            });

            // Optimize modal content spacing
            const modalContent = modalDialog.find('.ui-dialog-content, .ui-jqdialog-content');
            if (modalContent.length > 0) {
                modalContent.css({
                    'padding': '12px',
                    'overflow': 'hidden',
                    'max-height': 'none'
                });

                // Optimize form table spacing
                const formTable = modalContent.find('table.EditTable, table.FormTable');
                if (formTable.length > 0) {
                    formTable.css({
                        'margin': '0',
                        'border-spacing': '4px 2px',
                        'border-collapse': 'separate'
                    });

                    // Optimize input field spacing
                    formTable.find('td').css({
                        'padding': '1px 6px',
                        'vertical-align': 'middle',
                        'line-height': '1.2'
                    });

                    // Optimize input fields
                    formTable.find('input, select, textarea').css({
                        'margin': '1px 0',
                        'padding': '4px 6px',
                        'line-height': '1.3'
                    });
                }
            }

            // Ensure button area is visible and properly styled
            const buttonPane = modalDialog.find('.ui-dialog-buttonpane, .ui-jqdialog-buttonpane');
            if (buttonPane.length > 0) {
                buttonPane.css({
                    'padding': '12px 16px',
                    'margin': '0',
                    'border-top': '1px solid #ddd',
                    'min-height': 'auto',
                    'display': 'block',
                    'visibility': 'visible',
                    'height': 'auto'
                });

                // Ensure buttons are visible
                const buttons = buttonPane.find('.ui-button, button');
                if (buttons.length > 0) {
                    buttons.css({
                        'display': 'inline-block',
                        'visibility': 'visible',
                        'margin': '0 4px',
                        'padding': '6px 12px',
                        'min-width': '70px',
                        'height': 'auto'
                    });
                }

                // Ensure button container is visible
                const buttonSet = buttonPane.find('.ui-dialog-buttonset, .ui-jqdialog-buttonset');
                if (buttonSet.length > 0) {
                    buttonSet.css({
                        'display': 'block',
                        'visibility': 'visible',
                        'text-align': 'right',
                        'margin': '0'
                    });
                }
            }

            console.log(`Modal centered at: left=${left}px, top=${top}px`);
        } else {
            console.log('Modal dialog container not found');
        }
    } catch (error) {
        console.error('Error centering modal:', error);
    }
}

/**
 * Populate modal form fields from GST API data
 */
function populateModalFromGSTData(partyData, gstin) {
    if (GST_API_CONFIG.enableLogging) {
        console.log('Populating modal with GST data:', partyData);
    }

    // Extract data based on Powerful GSTIN Tool API response structure
    const tradeName = partyData.trade_name || '';
    const legalName = partyData.legal_name || '';

    // Use trade name first, then legal name
    const displayName = tradeName || legalName;

    // Extract and map state from state_jurisdiction
    const stateJurisdiction = partyData.state_jurisdiction || '';
    const state = mapStateJurisdictionToState(stateJurisdiction);

    console.log('GST API Response - State Jurisdiction:', stateJurisdiction);
    console.log('Mapped State:', state);

    // Format address from place_of_business_principal
    const address = formatPowerfulGSTINAddress(partyData) || '';

    // Extract PIN from principal place of business
    const pinCode = extractPowerfulGSTINPinCode(partyData) || '';

    // Update modal form fields
    const modalPartyName = document.getElementById('modalPartyName');
    const modalPartyAddress = document.getElementById('modalPartyAddress');
    const modalPartyState = document.getElementById('modalPartyState');
    const modalPartyPin = document.getElementById('modalPartyPin');

    if (modalPartyName) modalPartyName.value = displayName;
    if (modalPartyAddress) modalPartyAddress.value = address;
    if (modalPartyPin) modalPartyPin.value = pinCode;

    // Set state dropdown
    if (modalPartyState && state) {
        console.log('Attempting to select state:', state);
        console.log('Modal state dropdown element:', modalPartyState);
        console.log('Available options:', Array.from(modalPartyState.options).map(opt => `"${opt.value}"`));

        // Find and select the matching state option
        let stateSelected = false;
        const options = modalPartyState.options;

        for (let i = 0; i < options.length; i++) {
            console.log(`Checking option ${i}: "${options[i].value}" vs "${state}"`);
            if (options[i].value === state || options[i].text === state) {
                modalPartyState.selectedIndex = i;
                stateSelected = true;
                console.log('State selected successfully:', state, 'at index', i);
                break;
            }
        }

        if (!stateSelected) {
            console.log('State not found in dropdown:', state);
            console.log('Trying case-insensitive match...');

            // Try case-insensitive match
            for (let i = 0; i < options.length; i++) {
                if (options[i].value.toLowerCase() === state.toLowerCase() ||
                    options[i].text.toLowerCase() === state.toLowerCase()) {
                    modalPartyState.selectedIndex = i;
                    stateSelected = true;
                    console.log('State selected with case-insensitive match:', state, 'at index', i);
                    break;
                }
            }
        }

        if (!stateSelected) {
            console.log('Final attempt: State still not found:', state);
        }
    } else {
        console.log('Modal state dropdown or state value missing:', {
            modalPartyState: !!modalPartyState,
            state: state
        });
    }
}

/**
 * Close keyboard shortcuts modal
 */
function closeKeyboardShortcutsModal() {
    const modal = document.getElementById('keyboardShortcutsModal');
    if (modal) {
        modal.style.display = 'none';
    }
}

/**
 * Load column settings from localStorage
 */
function loadColumnSettings() {
    try {
        const savedSettings = localStorage.getItem('inventoryColumnSettings');
        if (savedSettings) {
            const parsedSettings = JSON.parse(savedSettings);
            columnVisibility = {
                ...columnVisibility,
                ...parsedSettings
            };
        }
    } catch (error) {
        console.error('Error loading column settings:', error);
    }
}

/**
 * Close modal when clicking outside and setup auto-calculation
 */
function setupModalCloseHandlers() {
    const modals = ['partyModal', 'stockItemModal', 'otherChargesModal', 'columnVisibilityModal', 'keyboardShortcutsModal'];

    modals.forEach(modalId => {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';

                    // Trigger auto-calculation for data-related modals
                    if (modalId === 'stockItemModal' || modalId === 'otherChargesModal') {
                        console.log('Modal closed, triggering auto-calculation...');
                        setTimeout(() => {
                            calculateBillTotal();
                        }, 100);
                    }
                }
            });
        }
    });

    // Also setup auto-calculation for jqGrid modal dialogs
    setupJqGridModalHandlers();
}

/**
 * Setup auto-calculation handlers for jqGrid modal dialogs
 */
function setupJqGridModalHandlers() {
    console.log('Setting up jqGrid modal handlers for auto-calculation...');

    // Override jQuery UI dialog close event for jqGrid modals
    $(document).on('dialogclose', '.ui-dialog', function(event, ui) {
        console.log('jqGrid modal dialog closed, triggering auto-calculation...');

        // Use the same approach as manual calculation
        setTimeout(() => {
            console.log('Auto-calculation: Syncing grid data and calculating after modal close...');

            // Sync jqGrid data to billForm (same as manual)
            syncGridDataToBillForm();

            // Calculate totals (same as manual)
            calculateBillTotal();

            console.log('Auto-calculation completed after modal close');
        }, 200);
    });

    // Also listen for form submissions in jqGrid modals
    $(document).on('click', '.ui-dialog .ui-button:contains("Submit")', function() {
        console.log('jqGrid modal submit button clicked, will trigger auto-calculation...');

        // Use the same approach as manual calculation
        setTimeout(() => {
            console.log('Auto-calculation: Syncing grid data and calculating after submit...');

            // Sync jqGrid data to billForm (same as manual)
            syncGridDataToBillForm();

            // Calculate totals (same as manual)
            calculateBillTotal();

            console.log('Auto-calculation completed after submit');
        }, 300);
    });

    console.log('jqGrid modal handlers setup complete');
}

// Global functions for HTML onclick handlers
window.updateStockItem = updateStockItem;
window.removeStockItem = removeStockItem;
window.removeOtherCharge = removeOtherCharge;
window.showPartyModal = showPartyModal;
window.closePartyModal = closePartyModal;
window.showStockItemModal = showStockItemModal;
window.closeStockItemModal = closeStockItemModal;
window.showOtherChargesModal = showOtherChargesModal;
window.closeOtherChargesModal = closeOtherChargesModal;
window.testOtherChargesAutocomplete = testOtherChargesAutocomplete;
window.showColumnVisibilityModal = showColumnVisibilityModal;
window.closeColumnVisibilityModal = closeColumnVisibilityModal;
window.applyColumnVisibility = applyColumnVisibility;
window.showKeyboardShortcutsModal = showKeyboardShortcutsModal;
window.closeKeyboardShortcutsModal = closeKeyboardShortcutsModal;


window.fetchPartyByGST = fetchPartyByGST;
window.fetchPartyByGSTModal = fetchPartyByGSTModal;

// Make functions globally available for the tab system
window.addRecordFunctions = {
    initialize: initializeAddRecord
};

// Initialize when this script loads
document.addEventListener('DOMContentLoaded', function() {
    // Load column settings
    loadColumnSettings();

    // Setup modal close handlers
    setTimeout(() => {
        setupModalCloseHandlers();
    }, 500);
});

// Cleanup auto-save on page unload
window.addEventListener('beforeunload', function(event) {
    if (AUTO_SAVE_CONFIG.enabled) {
        // Perform final auto-save
        autoSaveFormData();

        // Stop auto-save timer
        stopAutoSave();

        if (AUTO_SAVE_CONFIG.enableLogging) {
            console.log('Page unloading, auto-save cleanup completed');
        }
    }
});

// Add visual indicator for auto-save status
function addAutoSaveIndicator() {
    // Auto-save indicator creation disabled to prevent UI clutter
    // Auto-save functionality still works silently in the background
    return;
}

// Show auto-save indicator briefly - DISABLED for better UX
function showAutoSaveIndicator() {
    // Auto-save indicator disabled to prevent UI interruption
    // Auto-save still works silently in the background
    return;
}


