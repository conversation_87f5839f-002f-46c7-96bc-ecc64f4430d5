// server/api/auth/forgot-password.post.ts
import { define<PERSON><PERSON><PERSON><PERSON><PERSON>, readBody, createError, getRequestURL } from 'h3';
import crypto from 'crypto';
import User from '../../models/User';
import { emailService } from '../../utils/emailService';

export default defineEventHandler(async (event) => {
  try {
    const body = await readBody(event);
    const { email } = body;

    if (!email) {
      throw createError({ 
        statusCode: 400, 
        statusMessage: 'Email address is required' 
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw createError({ 
        statusCode: 400, 
        statusMessage: 'Please provide a valid email address' 
      });
    }

    // Find user by email
    const user = await User.findOne({ email: email.toLowerCase() });
    
    // Always return success to prevent email enumeration attacks
    // But only send email if user exists
    if (user) {
      // Check if user account is approved
      if (user.status !== 1) {
        console.log(`Password reset attempted for unapproved user: ${email}`);
        // Still return success to prevent enumeration
        return { 
          message: 'If an account with this email exists, a password reset link has been sent.' 
        };
      }

      // Check if there's already a recent reset token (prevent spam)
      const now = new Date();
      if (user.passwordResetExpires && user.passwordResetExpires > now) {
        const timeLeft = Math.ceil((user.passwordResetExpires.getTime() - now.getTime()) / (1000 * 60));
        console.log(`Password reset rate limited for user: ${email}, ${timeLeft} minutes left`);
        
        // Return success but don't send another email
        return { 
          message: 'If an account with this email exists, a password reset link has been sent.' 
        };
      }

      // Generate secure reset token
      const resetToken = crypto.randomBytes(32).toString('hex');
      const hashedToken = crypto.createHash('sha256').update(resetToken).digest('hex');
      
      // Set token expiration (1 hour)
      const expirationTime = new Date(Date.now() + 60 * 60 * 1000);

      // Save hashed token to database
      user.passwordResetToken = hashedToken;
      user.passwordResetExpires = expirationTime;
      await user.save();

      // Create reset URL
      const resetUrl = `${getRequestURL(event).origin}/reset-password?token=${resetToken}`;

      // Send password reset email
      try {
        await emailService.sendPasswordResetEmail(user.email, {
          fullname: user.fullname,
          resetUrl,
          expiresIn: '1 hour'
        });

        console.log(`Password reset email sent to: ${email}`);
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError);
        
        // Clear the reset token if email fails
        user.passwordResetToken = undefined;
        user.passwordResetExpires = undefined;
        await user.save();

        throw createError({
          statusCode: 500,
          statusMessage: 'Failed to send password reset email. Please try again later.'
        });
      }
    } else {
      console.log(`Password reset attempted for non-existent email: ${email}`);
    }

    // Always return the same message to prevent email enumeration
    return { 
      message: 'If an account with this email exists, a password reset link has been sent to your email address. Please check your inbox and follow the instructions to reset your password.' 
    };

  } catch (error: any) {
    console.error('Password reset request error:', error);
    
    if (error.statusCode) {
      throw error;
    }

    throw createError({
      statusCode: 500,
      statusMessage: 'An error occurred while processing your password reset request. Please try again later.'
    });
  }
});
