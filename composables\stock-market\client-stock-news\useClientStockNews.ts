import { ref, computed, readonly } from 'vue'
import { useUniversalAIClient } from '~/composables/ai/useUniversalAIClient'

export interface ClientStockNewsResult {
  searchSummary: string
  totalFound: string
  recentNews: any[]
  keyDevelopments: string
  marketImpact: string
  analystViews: string
  riskFactors?: string
  opportunities?: string
  sectorOutlook?: string
  searchTimestamp: string
}

export const useClientStockNews = () => {
  // State
  const loading = ref(false)
  const error = ref('')
  const analysis = ref<ClientStockNewsResult | null>(null)
  const progress = ref(0)
  const statusMessage = ref('')
  
  // Enhanced progress tracking
  const dataPointsProcessed = ref(0)
  const currentOperation = ref('')
  const operationStartTime = ref<Date | null>(null)

  // Universal AI Client - NO MORE HARDCODED PROVIDERS!
  const { callAIForJSON, isConfigured, getAIInfo } = useUniversalAIClient()

  // Generate AI analysis prompt - REAL DATA ONLY
  const generateAIAnalysisPrompt = (stockData: any) => {
    const currentTime = new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })
    
    return `You are an expert financial news analyst specializing in Indian stock markets. Provide comprehensive stock news research and analysis for the following stock:

STOCK INFORMATION:
- Symbol: ${stockData.symbol}
- Company: ${stockData.companyName}
- Current Price: ₹${stockData.currentPrice}
- Price Change: ${stockData.change} (${stockData.pChange}%)
- Analysis Date: ${currentTime}

ANALYSIS REQUIREMENTS:
1. Search for and analyze recent news, announcements, and developments
2. Focus on earnings reports, corporate actions, regulatory updates
3. Include analyst recommendations and price target changes
4. Assess market sentiment and investor reactions
5. Identify key risk factors and growth opportunities
6. Provide sector-specific outlook and competitive positioning

Return your analysis in the following JSON format:
{
  "searchSummary": "Brief summary of news search and key findings",
  "totalFound": "Number or description of news items analyzed",
  "recentNews": [
    {
      "headline": "News headline",
      "date": "Date of news",
      "source": "News source",
      "summary": "Brief summary of the news",
      "impact": "Positive/Negative/Neutral",
      "relevance": "High/Medium/Low"
    }
  ],
  "keyDevelopments": "Major recent developments and their significance",
  "marketImpact": "Analysis of how news affects stock price and market position",
  "analystViews": "Current analyst opinions, ratings, and price targets",
  "riskFactors": "Key risks identified from recent news",
  "opportunities": "Growth opportunities and positive catalysts",
  "sectorOutlook": "Sector-specific trends and competitive landscape"
}

Focus on Indian market context and provide actionable insights based on recent news and developments. Only include real, factual information - no speculation or generic content.`
  }

  // Universal AI Provider Call - NO MORE HARDCODED LOGIC!
  const callAIProviderDirectly = async (prompt: string): Promise<any> => {
    const aiInfo = getAIInfo()
    console.log(`🤖 Making universal AI call to ${aiInfo.provider} with model ${aiInfo.model}`)

    try {
      // Use universal AI client - works with ANY provider dynamically
      const systemPrompt = 'You are a professional financial news analyst. Provide accurate, data-driven news analysis based on recent market developments. Return only valid JSON format matching the exact structure requested.'
      return await callAIForJSON(prompt, systemPrompt)
    } catch (error: any) {
      console.error(`❌ Universal AI call failed:`, error)
      throw new Error(`AI analysis failed: ${error.message}`)
    }
  }

  // Dynamic OpenAI API call - supports any OpenAI-compatible model
  const callOpenAIDirect = async (prompt: string, config: any): Promise<string> => {
    const baseUrl = config.baseUrl || 'https://api.openai.com/v1'
    const response = await fetch(`${baseUrl}/chat/completions`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional financial news analyst. Provide accurate, data-driven news analysis based on recent market developments. Return only valid JSON format matching the exact structure requested.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 8192
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`OpenAI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || ''
  }

  // Dynamic Google AI API call
  const callGoogleAIDirect = async (prompt: string, config: any): Promise<string> => {
    const baseUrl = config.baseUrl || 'https://generativelanguage.googleapis.com/v1beta'
    const response = await fetch(`${baseUrl}/models/${config.model}:generateContent?key=${config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{
            text: `You are a professional financial news analyst. ${prompt}`
          }]
        }],
        generationConfig: {
          temperature: config.temperature || 0.7,
          maxOutputTokens: config.maxTokens || 8192
        }
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Google AI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.candidates?.[0]?.content?.parts?.[0]?.text || ''
  }

  // Dynamic Anthropic API call
  const callAnthropicDirect = async (prompt: string, config: any): Promise<string> => {
    const baseUrl = config.baseUrl || 'https://api.anthropic.com/v1'
    const response = await fetch(`${baseUrl}/messages`, {
      method: 'POST',
      headers: {
        'x-api-key': config.apiKey,
        'Content-Type': 'application/json',
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: config.model,
        max_tokens: config.maxTokens || 8192,
        temperature: config.temperature || 0.7,
        messages: [
          {
            role: 'user',
            content: `You are a professional financial news analyst. ${prompt}`
          }
        ]
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Anthropic API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.content?.[0]?.text || ''
  }

  // Dynamic OpenRouter API call
  const callOpenRouterDirect = async (prompt: string, config: any): Promise<string> => {
    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': window.location.origin,
        'X-Title': 'BusinessPro Suite - Stock News Analysis'
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional financial news analyst. Provide accurate, data-driven news analysis based on recent market developments. Return only valid JSON format matching the exact structure requested.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 8192
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`OpenRouter API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || ''
  }

  // Dynamic Custom AI API call
  const callCustomAIDirect = async (prompt: string, config: any): Promise<string> => {
    const response = await fetch(config.baseUrl, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
        ...config.headers
      },
      body: JSON.stringify({
        model: config.model,
        messages: [
          {
            role: 'system',
            content: 'You are a professional financial news analyst. Provide accurate, data-driven news analysis based on recent market developments. Return only valid JSON format matching the exact structure requested.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: config.temperature || 0.7,
        max_tokens: config.maxTokens || 8192
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Custom AI API error: ${response.status} ${response.statusText} - ${errorText}`)
    }

    const data = await response.json()

    // Dynamic response parsing based on config
    if (config.responsePath) {
      return config.responsePath.split('.').reduce((obj: any, key: string) => obj?.[key], data) || ''
    }

    // Default OpenAI-compatible response format
    return data.choices?.[0]?.message?.content || data.content || ''
  }

  // Perform client-side stock news analysis with REAL-TIME PROGRESS
  const performStockNewsAnalysis = async (stockData: any) => {
    if (!isConfigured.value) {
      throw new Error('AI configuration is not complete. Please configure your AI settings.')
    }

    loading.value = true
    error.value = ''
    analysis.value = null
    progress.value = 0
    dataPointsProcessed.value = 0
    currentOperation.value = 'Initializing'
    operationStartTime.value = new Date()
    statusMessage.value = `Starting stock news analysis for ${stockData.symbol}...`

    try {
      // Step 1: Prepare analysis
      progress.value = 10
      currentOperation.value = 'Preparing Analysis'
      statusMessage.value = `Preparing news analysis for ${stockData.symbol}...`

      // Step 2: Generate AI analysis prompt
      progress.value = 20
      statusMessage.value = 'Preparing AI analysis prompt with stock data...'

      const aiPrompt = generateAIAnalysisPrompt(stockData)

      progress.value = 30
      const aiInfo = getAIInfo()
      statusMessage.value = `Sending news analysis request to ${aiInfo.provider} (${aiInfo.model})...`

      // Make UNIVERSAL AI provider call - COMPLETELY DYNAMIC!
      const stockNewsAnalysis = await callAIProviderDirectly(aiPrompt)

      // Step 3: Validate AI response with detailed progress
      progress.value = 70
      statusMessage.value = `Received AI response, validating structure...`

      if (!stockNewsAnalysis || typeof stockNewsAnalysis !== 'object') {
        throw new Error('Invalid response from AI provider - expected JSON object')
      }

      console.log('✅ Received parsed stock news analysis result:', stockNewsAnalysis)

      progress.value = 85
      statusMessage.value = 'Validating stock news analysis data...'

      // Validate required fields - NO FALLBACK DATA
      if (!stockNewsAnalysis.searchSummary) {
        throw new Error('AI response missing required searchSummary field')
      }

      // Step 4: Prepare final response with detailed completion
      progress.value = 95
      statusMessage.value = 'Preparing final news analysis result...'

      const finalAnalysis: ClientStockNewsResult = {
        searchSummary: stockNewsAnalysis.searchSummary,
        totalFound: stockNewsAnalysis.totalFound,
        recentNews: Array.isArray(stockNewsAnalysis.recentNews) ? stockNewsAnalysis.recentNews.slice(0, 5) : [],
        keyDevelopments: stockNewsAnalysis.keyDevelopments,
        marketImpact: stockNewsAnalysis.marketImpact,
        analystViews: stockNewsAnalysis.analystViews,
        riskFactors: stockNewsAnalysis.riskFactors,
        opportunities: stockNewsAnalysis.opportunities,
        sectorOutlook: stockNewsAnalysis.sectorOutlook,
        searchTimestamp: new Date().toISOString()
      }

      progress.value = 100
      statusMessage.value = `News analysis complete! Found ${finalAnalysis.recentNews.length} news items`

      analysis.value = finalAnalysis
      loading.value = false

      console.log('✅ Client-side stock news analysis completed:', finalAnalysis)
      return finalAnalysis

    } catch (err: any) {
      console.error('❌ Client-side stock news analysis failed:', err)
      error.value = err.message || 'Stock news analysis failed'
      loading.value = false
      throw err
    }
  }

  // Reset function
  const reset = () => {
    loading.value = false
    error.value = ''
    analysis.value = null
    progress.value = 0
    statusMessage.value = ''
    dataPointsProcessed.value = 0
    currentOperation.value = ''
    operationStartTime.value = null
  }

  return {
    // State (readonly)
    loading: readonly(loading),
    error: readonly(error),
    analysis: readonly(analysis),
    progress: readonly(progress),
    statusMessage: readonly(statusMessage),
    currentOperation: readonly(currentOperation),
    dataPointsProcessed: readonly(dataPointsProcessed),

    // Computed
    isConfigured,

    // Methods
    performStockNewsAnalysis,
    reset
  }
}
