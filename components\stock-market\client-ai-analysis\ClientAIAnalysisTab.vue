<template>
  <div class="tab-panel">
    <!-- Enhanced AI Analysis Loading -->
    <div v-if="clientAnalysisLoading">
      <EnhancedProgressDisplay
        title="🤖 AI Comprehensive Analysis (CLIENT-SIDE)"
        :progress="enhancedProgress.overallProgress.value"
        :current-message="enhancedProgress.currentMessage.value"
        :elapsed-time="enhancedProgress.elapsedTime.value"
        :provider="aiConfig.provider || 'unknown'"
        :model="aiConfig.model || 'unknown'"
        analysis-type="ai-analysis"
        :current-stage="enhancedProgress.currentStage.value"
        :stages="enhancedProgress.stages.value"
        :estimated-remaining="enhancedProgress.estimatedRemaining.value"
        :show-advanced-options="true"
        :allow-cancel="true"
        :show-debug-info="false"
        @cancel="cancelAnalysis"
        @background-toggle="handleBackgroundToggle"
      />
    </div>

    <!-- AI Analysis Error -->
    <div v-else-if="clientAnalysisError" class="bg-red-50 border border-red-200 rounded-lg p-6 text-center">
      <div class="text-red-600 mb-2">⚠️ AI Analysis Failed</div>
      <p class="text-red-700 mb-4">{{ clientAnalysisError }}</p>
      <button @click="startAIAnalysis"
        class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
        🔄 Retry Analysis
      </button>
    </div>

    <!-- AI Analysis Results -->
    <div v-else-if="clientAnalysis" class="space-y-6">
      <!-- Overall Recommendation -->
      <div class="bg-gradient-to-r from-green-50 to-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">📊 AI Recommendation</h4>
        <div class="flex items-center space-x-4">
          <div class="text-2xl font-bold" :class="getRecommendationColor(clientAnalysis.recommendation)">
            {{ clientAnalysis.recommendation }}
          </div>
          <div class="text-sm text-gray-600">
            {{ clientAnalysis.confidence ? `Confidence: ${clientAnalysis.confidence}` : '' }}
          </div>
        </div>
        <p class="text-gray-700 mt-3">{{ clientAnalysis.summary }}</p>
      </div>

      <!-- Technical Analysis -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">📈 Technical Analysis</h4>
        <div class="prose prose-sm max-w-none text-gray-700">
          <div v-html="formatAnalysisText(clientAnalysis.technicalAnalysis)"></div>
        </div>
      </div>

      <!-- Fundamental Analysis -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">🏢 Fundamental Analysis</h4>
        <div class="prose prose-sm max-w-none text-gray-700">
          <div v-html="formatAnalysisText(clientAnalysis.fundamentalAnalysis)"></div>
        </div>
      </div>

      <!-- Market Trends -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">📊 Market Trends & Outlook</h4>
        <div class="prose prose-sm max-w-none text-gray-700">
          <div v-html="formatAnalysisText(clientAnalysis.marketTrends)"></div>
        </div>
      </div>

      <!-- Risk Assessment -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="text-lg font-semibold text-gray-900 mb-3">⚠️ Risk Assessment</h4>
        <div class="prose prose-sm max-w-none text-gray-700">
          <div v-html="formatAnalysisText(clientAnalysis.riskAssessment)"></div>
        </div>
      </div>

      <!-- Analysis Timestamp -->
      <div class="text-xs text-gray-500 text-center">
        <span v-if="clientAnalysis.analysisTimestamp">
          Analysis completed: {{ formatTimestamp(clientAnalysis.analysisTimestamp) }}
        </span>
      </div>
    </div>

    <!-- AI Configuration Check -->
    <div v-else-if="!isConfigured" class="space-y-4">
      <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd"
                d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-yellow-800">AI Configuration Required</h3>
            <div class="mt-2 text-sm text-yellow-700">
              <p>Please configure your AI settings to use comprehensive stock analysis.</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Quick AI Configuration -->
      <div class="bg-white border border-gray-200 rounded-lg p-6">
        <h4 class="text-lg font-medium text-gray-900 mb-4">Quick AI Setup</h4>

        <!-- Provider Selection -->
        <div class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">AI Provider</label>
          <div class="grid grid-cols-3 gap-3">
            <button v-for="provider in aiProviders" :key="provider.id" @click="selectedProvider = provider.id"
              :class="[
                'p-3 border rounded-lg text-center transition-colors',
                selectedProvider === provider.id
                  ? 'border-blue-500 bg-blue-50 text-blue-700'
                  : 'border-gray-300 hover:border-gray-400'
              ]">
              <div class="font-medium">{{ provider.name }}</div>
            </button>
          </div>
        </div>

        <!-- Model Selection -->
        <div v-if="selectedProvider" class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">Model</label>
          <select v-model="selectedModel"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option value="">Select a model</option>
            <option v-for="model in getProviderModels(selectedProvider)" :key="model.id" :value="model.id">
              {{ model.name }}
            </option>
          </select>
        </div>

        <!-- API Key Input -->
        <div v-if="selectedModel" class="mb-4">
          <label class="block text-sm font-medium text-gray-700 mb-2">API Key</label>
          <div class="relative">
            <input v-model="tempApiKey" :type="showApiKey ? 'text' : 'password'"
              :placeholder="`Enter your ${getProviderName(selectedProvider)} API key`"
              class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
            <button @click="showApiKey = !showApiKey" type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center">
              <svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path v-if="!showApiKey" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path v-if="!showApiKey" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                <path v-if="showApiKey" stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div v-if="tempApiKey" class="flex space-x-3">
          <button @click="testAndSaveAIConfig" :disabled="testingApiKey"
            class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors">
            <span v-if="testingApiKey" class="flex items-center justify-center">
              <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
              Testing...
            </span>
            <span v-else>💾 Save & Test Configuration</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Initial State for AI Analysis (when configured) -->
    <div v-else class="bg-white border border-gray-200 rounded-lg p-6 text-center">
      <div class="flex items-center justify-center mb-4">
        <div class="flex items-center space-x-2 text-sm text-green-600 bg-green-50 px-3 py-1 rounded-full">
          <svg class="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          <span>AI Configured</span>
        </div>
      </div>
      <p class="text-gray-600 mb-4">Click the button below to start comprehensive AI analysis for {{ stock.symbol }} (CLIENT-SIDE)</p>
      <p class="text-gray-500 text-sm mb-6">AI will analyze technical indicators, fundamentals, market trends, and provide investment recommendations</p>
      <button @click="startAIAnalysis"
        class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium">
        🚀 Start AI Analysis
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useClientAIAnalysis } from '~/composables/stock-market/client-ai-analysis/useClientAIAnalysis'
import { useAIConfig } from '~/composables/ai/useAIConfig'
import { useEnhancedProgress } from '~/composables/stock-market/shared/useEnhancedProgress'
import EnhancedProgressDisplay from '~/components/stock-market/shared/EnhancedProgressDisplay.vue'

// Props
const props = defineProps<{
  stock: any
}>()

// Client-side AI analysis composable
const {
  loading: clientAnalysisLoading,
  error: clientAnalysisError,
  analysis: clientAnalysis,
  progress: clientAnalysisProgress,
  statusMessage: clientAnalysisStatusMessage,
  currentOperation,
  isConfigured,
  performAIAnalysis,
  reset: clientAnalysisReset
} = useClientAIAnalysis()

// AI Configuration
const { aiConfig, aiProviders, getProviderModels, saveAIConfig, testAIConfig } = useAIConfig()

// Enhanced Progress System
const enhancedProgress = useEnhancedProgress('ai-analysis')

// Local state for AI configuration
const selectedProvider = ref('')
const selectedModel = ref('')
const tempApiKey = ref('')
const showApiKey = ref(false)
const testingApiKey = ref(false)

// Elapsed time tracking (legacy - now handled by enhanced progress)
const elapsedTime = ref(0)
const elapsedTimeInterval = ref<NodeJS.Timeout | null>(null)

// Start AI analysis with enhanced progress
const startAIAnalysis = async () => {
  if (!isConfigured.value) {
    alert('Please configure your AI settings first.')
    return
  }

  // Start enhanced progress system
  enhancedProgress.start()
  enhancedProgress.autoUpdateEstimates(aiConfig.value.provider, aiConfig.value.model)

  // Start legacy elapsed time tracking (for compatibility)
  elapsedTime.value = 0
  elapsedTimeInterval.value = setInterval(() => {
    elapsedTime.value++
  }, 1000)

  try {
    await performAIAnalysisWithProgress({
      symbol: props.stock.symbol,
      companyName: props.stock.meta?.companyName || props.stock.symbol,
      currentPrice: props.stock.lastPrice,
      change: props.stock.change,
      pChange: props.stock.pChange,
      volume: props.stock.totalTradedVolume,
      dayHigh: props.stock.dayHigh,
      dayLow: props.stock.dayLow,
      previousClose: props.stock.previousClose
    })
  } catch (error) {
    console.error('AI analysis failed:', error)
    enhancedProgress.reset()
  } finally {
    // Stop elapsed time tracking
    if (elapsedTimeInterval.value) {
      clearInterval(elapsedTimeInterval.value)
      elapsedTimeInterval.value = null
    }
  }
}

// Enhanced progress-aware AI analysis
const performAIAnalysisWithProgress = async (stockData: any) => {
  // Stage 1: Initialize
  enhancedProgress.setStage('init', 0)
  await new Promise(resolve => setTimeout(resolve, 500)) // Brief pause for UX

  // Stage 2: Prepare data
  enhancedProgress.setStage('prepare', 0)
  enhancedProgress.setStageProgress(50)
  await new Promise(resolve => setTimeout(resolve, 300))
  enhancedProgress.setStageProgress(100)

  // Stage 3: Generate prompt
  enhancedProgress.setStage('prompt', 0)
  enhancedProgress.setStageProgress(50)
  await new Promise(resolve => setTimeout(resolve, 200))
  enhancedProgress.setStageProgress(100)

  // Stage 4: Send request
  enhancedProgress.setStage('send', 0)
  enhancedProgress.setStageProgress(100)

  // Stage 5: AI Processing (main work)
  enhancedProgress.setStage('process', 0)

  try {
    // Call the actual AI analysis with progress updates
    await performAIAnalysis(stockData, (stage, stageProgress) => {
      if (stage === 'process') {
        enhancedProgress.setStageProgress(stageProgress)
      }
    })

    // Stage 6: Parse response
    enhancedProgress.setStage('parse', 0)
    enhancedProgress.setStageProgress(50)
    await new Promise(resolve => setTimeout(resolve, 300))
    enhancedProgress.setStageProgress(100)

    // Stage 7: Validate
    enhancedProgress.setStage('validate', 0)
    enhancedProgress.setStageProgress(100)

    // Stage 8: Finalize
    enhancedProgress.setStage('finalize', 0)
    enhancedProgress.setStageProgress(100)

    // Complete
    enhancedProgress.complete()

  } catch (error) {
    enhancedProgress.reset()
    throw error
  }
}

// Cancel analysis
const cancelAnalysis = () => {
  enhancedProgress.reset()
  if (elapsedTimeInterval.value) {
    clearInterval(elapsedTimeInterval.value)
    elapsedTimeInterval.value = null
  }
  // Note: We can't actually cancel the AI API call once it's sent
  console.log('Analysis cancellation requested (AI call cannot be stopped once started)')
}

// Handle background toggle
const handleBackgroundToggle = (enabled: boolean) => {
  console.log('Background processing:', enabled ? 'enabled' : 'disabled')
  // Could implement background processing logic here
}

// Test and save AI configuration
const testAndSaveAIConfig = async () => {
  if (!selectedProvider.value || !selectedModel.value || !tempApiKey.value) {
    alert('Please fill in all required fields')
    return
  }

  testingApiKey.value = true
  try {
    const config = {
      provider: selectedProvider.value,
      model: selectedModel.value,
      apiKey: tempApiKey.value
    }

    const testResult = await testAIConfig(config)
    if (testResult.success) {
      await saveAIConfig(config)
      alert('✅ AI configuration saved successfully!')
      tempApiKey.value = ''
    } else {
      alert(`❌ Configuration test failed: ${testResult.error}`)
    }
  } catch (error: any) {
    alert(`❌ Configuration test failed: ${error.message}`)
  } finally {
    testingApiKey.value = false
  }
}

// Get provider name
const getProviderName = (providerId: string) => {
  const provider = aiProviders.find(p => p.id === providerId)
  return provider?.name || providerId
}

// Format analysis text (convert newlines to HTML)
const formatAnalysisText = (text: string) => {
  if (!text) return ''
  return text.replace(/\n/g, '<br>')
}

// Format timestamp
const formatTimestamp = (timestamp: string) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleString()
}

// Get recommendation color
const getRecommendationColor = (recommendation: string) => {
  if (!recommendation) return 'text-gray-600'
  const rec = recommendation.toLowerCase()
  if (rec.includes('buy') || rec.includes('strong buy')) return 'text-green-600'
  if (rec.includes('sell') || rec.includes('strong sell')) return 'text-red-600'
  if (rec.includes('hold')) return 'text-yellow-600'
  return 'text-blue-600'
}

// Cleanup on unmount
onUnmounted(() => {
  if (elapsedTimeInterval.value) {
    clearInterval(elapsedTimeInterval.value)
  }
})
</script>
