// server/plugins/models.ts
import { defineNitroPlugin } from 'nitropack/runtime/plugin';

// Import all models to ensure they are registered with Mongoose
import '../models/User';
import { NSE } from '../models/NSE';
import { Folio } from '../models/Folio';
import { CNNote } from '../models/CNNote';
import NSEDocumentModel from '../models/NSEDocument';


export default defineNitroPlugin(() => {
  // Models registered with Mongoose
});
