<template>
  <div class="bg-white rounded-lg shadow p-6">
    <!-- Note for sub-contractors -->
    <div v-if="isSubContractorUser" class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
      <p class="text-sm text-blue-800">
        <strong>Note:</strong> You can add transactions to your account. Enter the name of the person or company you paid in the "Paid To/From" field.
      </p>
    </div>
    <form ref="transactionForm" @submit.prevent="handleSubmit">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <!-- Date Field -->
        <div>
          <label for="date" class="block text-sm font-medium text-gray-700 mb-1">Date *</label>
          <input
            type="date"
            id="date"
            v-model="formData.date"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Amount Field -->
        <div>
          <label for="amount" class="block text-sm font-medium text-gray-700 mb-1">Amount *</label>
          <input
            type="number"
            id="amount"
            v-model="formData.amount"
            step="0.01"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
          />
        </div>

        <!-- Paid To Field with Datalist -->
        <div>
          <label for="paidTo" class="block text-sm font-medium text-gray-700 mb-1">Paid To/From *</label>
          <div class="relative">
            <div class="flex items-center">
              <input
                type="text"
                id="paidTo"
                v-model="formData.paidTo"
                list="paidToList"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                placeholder="Enter recipient name"
                autocomplete="off"
                required
              />
            </div>
            <datalist id="paidToList">
              <option v-for="paidTo in uniquePaidToValues" :key="paidTo" :value="paidTo"></option>
            </datalist>
            <p v-if="!formData.paidTo" class="mt-1 text-sm text-red-600">
              Please enter who the payment is to/from
            </p>
          </div>
        </div>

        <!-- Category Field with Datalist -->
        <div>
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <div class="relative">
            <input
              type="text"
              id="category"
              v-model="formData.category"
              list="categoryList"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              autocomplete="off"
            />
            <datalist id="categoryList">
              <option value="PAYMENT"></option>
              <option value="RECEIPT"></option>
              <option v-for="category in uniqueCategories" :key="category" :value="category"></option>
            </datalist>
          </div>
        </div>

        <!-- Project Field with Datalist -->
        <div>
          <label for="project" class="block text-sm font-medium text-gray-700 mb-1">Project *</label>
          <div class="relative">
            <input
              type="text"
              id="project"
              v-model="formData.project"
              list="projectList"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              autocomplete="off"
              required
            />
            <datalist id="projectList">
              <option v-for="project in uniqueProjects" :key="project" :value="project"></option>
            </datalist>
            <p v-if="!formData.project" class="mt-1 text-sm text-red-600">
              Please enter a project name
            </p>
          </div>
        </div>

        <!-- Description Field -->
        <div class="md:col-span-2">
          <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
          <textarea
            id="description"
            v-model="formData.description"
            rows="3"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          ></textarea>
        </div>
      </div>

      <!-- Form Actions (only shown if showFooterButtons is true) -->
      <div v-if="showFooterButtons" class="mt-8 flex justify-end space-x-4">
        <button
          type="button"
          @click="$emit('cancel')"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          :disabled="isLoading"
        >
          Cancel
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :disabled="isLoading || !formData.paidTo || !formData.project"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving...
          </span>
          <span v-else>{{ transaction ? 'Update' : 'Save' }}</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useSubs } from '~/composables/expenses/useSubs';
import useUserRole from '~/composables/auth/useUserRole';
import useToast from '~/composables/ui/useToast';

export default {
  name: 'SubsTransactionForm',

  props: {
    transaction: {
      type: Object,
      default: null
    },
    subsModel: {
      type: Object,
      default: null
    },
    isLoading: {
      type: Boolean,
      default: false
    },
    showFooterButtons: {
      type: Boolean,
      default: true
    }
  },

  emits: ['submit', 'cancel'],

  setup(props, { emit }) {
    // Get user role information
    const { isSubContractor } = useUserRole();
    const isSubContractorUser = computed(() => isSubContractor());

    // Get composables
    const {
      getUniqueCategories,
      getUniqueProjects
    } = useExpenses();

    const {
      subsModels,
      fetchSubsModels,
      fetchSubs,
      getUniquePaidToValues,
      getUniqueSubProjects
    } = useSubs();

    // Default form data
    const defaultFormData = {
      date: new Date().toISOString().split('T')[0],
      paidTo: '',
      amount: '',
      category: 'PAYMENT',
      project: '',
      description: ''
    };

    // Initialize form data
    const formData = ref({ ...defaultFormData });

    // Method to reset the form with default values
    const resetForm = (customData) => {
      // Reset to default values
      formData.value = { ...defaultFormData };

      // If custom data is provided, merge it with the default form data
      if (customData) {
        formData.value = {
          ...defaultFormData,
          ...customData
        };
      }

      // Force update the category radio buttons if needed
      setTimeout(() => {
        // If you have radio buttons for category, ensure they're properly set
        const categoryInput = document.querySelector(`input[name="category"][value="${formData.value.category}"]`);
        if (categoryInput) categoryInput.checked = true;
      }, 0);
    };

    // Computed properties
    const uniqueCategories = computed(() => getUniqueCategories.value);

    // Combine projects from both expenses and subs
    const uniqueProjects = computed(() => {
      const expenseProjects = getUniqueProjects.value || [];
      const subProjects = getUniqueSubProjects.value || [];

      // Combine both arrays and remove duplicates
      const allProjects = new Set([...expenseProjects, ...subProjects]);
      return Array.from(allProjects).sort();
    });

    const uniquePaidToValues = computed(() => getUniquePaidToValues.value);

    // Get toast notifications
    const toast = useToast();

    // Methods
    const submitForm = () => {
      handleSubmit();
    };

    const handleSubmit = () => {
      // Validate paidTo field
      if (!formData.value.paidTo) {
        toast.error('Please enter who the payment is to/from', 'Validation Error');
        return;
      }

      // Validate project field
      if (!formData.value.project) {
        console.log(`❌ [${debugId}] Validation failed: project is empty`);
        toast.error('Please enter a project name', 'Validation Error');
        return;
      }

      // Validate amount field with enhanced checks
      const amount = Number(formData.value.amount);
      if (!amount || amount <= 0 || isNaN(amount)) {
        console.log(`❌ [${debugId}] Validation failed: invalid amount:`, amount);
        toast.error('Please enter a valid amount greater than 0', 'Validation Error');
        return;
      }

      // Additional validation for very large amounts
      if (amount > 10000000) { // 1 crore limit
        console.log(`❌ [${debugId}] Validation failed: amount too large:`, amount);
        toast.error('Amount cannot exceed ₹1,00,00,000', 'Validation Error');
        return;
      }

      // Validate amount precision (max 2 decimal places)
      if (amount.toString().includes('.') && amount.toString().split('.')[1].length > 2) {
        toast.error('Amount cannot have more than 2 decimal places', 'Validation Error');
        return;
      }

      // Adjust the amount based on the category
      const formDataToSubmit = { ...formData.value };

      // For PAYMENT: amount should be negative (red)
      // For RECEIPT: amount should be positive (green)
      if (formDataToSubmit.category === 'PAYMENT') {
        formDataToSubmit.amount = -Math.abs(amount);
      } else if (formDataToSubmit.category === 'RECEIPT') {
        formDataToSubmit.amount = Math.abs(amount);
      }

      emit('submit', formDataToSubmit);
    };

    // Get expenses functions
    const { fetchExpenses } = useExpenses();

    // Initialize
    onMounted(async () => {
      try {
        // Fetch data for dropdowns
        await Promise.all([
          fetchSubsModels(),
          fetchSubs(), // Fetch subs data to get paidTo values
          fetchExpenses() // Fetch expenses data to get project values
        ]);

        // Add event listener for form reset
        const form = document.querySelector('form');
        const resetFormHandler = (event) => {
          resetForm(event.detail);
        };

        if (form) {
          form.addEventListener('reset-form', resetFormHandler);

          // Clean up event listener when component is unmounted
          onUnmounted(() => {
            form.removeEventListener('reset-form', resetFormHandler);
          });
        }

        // If editing an existing transaction, populate the form
        if (props.transaction) {
          // For editing, we need to show the absolute value of the amount
          const absoluteAmount = Math.abs(Number(props.transaction.amount) || 0);

          // Determine category based on the sign of the original amount
          let category = props.transaction.category;
          if (!category) {
            category = Number(props.transaction.amount) < 0 ? 'PAYMENT' : 'RECEIPT';
          }

          formData.value = {
            date: new Date(props.transaction.date).toISOString().split('T')[0],
            paidTo: props.transaction.paidTo,
            amount: absoluteAmount, // Always show positive amount
            category: category,
            project: props.transaction.project || '',
            description: props.transaction.description || ''
          };


        }
      } catch (error) {
        // Error handling
      }
    });

    // Watch for changes to transaction prop
    watch(() => props.transaction, (newTransaction) => {
      if (newTransaction) {
        // For editing, we need to show the absolute value of the amount
        // The category determines the sign, not the amount itself
        const absoluteAmount = Math.abs(Number(newTransaction.amount) || 0);

        // Determine category based on the sign of the original amount
        let category = newTransaction.category;
        if (!category) {
          // If no category is provided, determine from amount sign
          category = Number(newTransaction.amount) < 0 ? 'PAYMENT' : 'RECEIPT';
        }

        formData.value = {
          date: new Date(newTransaction.date).toISOString().split('T')[0],
          paidTo: newTransaction.paidTo,
          amount: absoluteAmount, // Always show positive amount
          category: category,
          project: newTransaction.project || '',
          description: newTransaction.description || ''
        };


      }
    });

    // Watch for changes to subsModel prop
    // We no longer set the paidTo field when a subsModel changes
    watch(() => props.subsModel, () => {
      // The user should be able to enter any recipient
      // No action needed here
    });

    // Watch for changes to category - but keep amount always positive in form
    watch(() => formData.value.category, (newCategory, oldCategory) => {
      if (newCategory !== oldCategory && formData.value.amount) {
        const amount = Number(formData.value.amount);

        // Always keep the amount positive in the form
        // The sign will be applied during submission based on category
        formData.value.amount = Math.abs(amount);

        console.log('Category changed:', {
          from: oldCategory,
          to: newCategory,
          amount: formData.value.amount
        });
      }
    });

    // Create a ref for the form element
    const transactionForm = ref(null);

    return {
      formData,
      subsModels,
      uniqueCategories,
      uniqueProjects,
      uniquePaidToValues,
      handleSubmit,
      submitForm,
      resetForm,
      isSubContractorUser,
      transactionForm
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
