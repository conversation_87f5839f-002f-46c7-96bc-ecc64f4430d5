# Labor Payment System Test Scenarios

This document outlines comprehensive test scenarios to validate the new payment logic implementation.

## Test Data Setup

### Group: Test Labor Group
- Group ID: `test-group-001`
- Firm ID: `test-firm-001`

### Labor Profiles in Group:
1. Worker A - Daily Rate: ₹500
2. Worker B - Daily Rate: ₹600
3. Worker C - Daily Rate: ₹550

## Scenario 1: Settled Period with Final Payment

### Setup Data:
**Attendance Period:** 04-04-2025 to 26-04-2025 (23 days)

**Attendance Records:**
- Worker A: 20 days worked = ₹10,000
- Worker B: 22 days worked = ₹13,200  
- Worker C: 21 days worked = ₹11,550
- **Total Labor Earnings:** ₹34,750

**Site Expenses:**
- 10-04-2025: ₹2,000
- 18-04-2025: ₹1,500
- **Total Site Expenses:** ₹3,500

**Total Period Earnings:** ₹38,250

**Payments Made:**
1. 06-04-2025: Advance Payment = ₹2,000
2. 09-04-2025: Advance Payment = ₹1,500
3. 14-04-2025: Site Expense Payment = ₹2,000
4. 20-04-2025: Advance Payment = ₹3,000
5. 28-04-2025: Final Payment = ₹29,750

**Total Payments:** ₹38,250

### Expected Results:
- **Period Display:** 04-04-2025 - 26-04-2025
- **Total Payments:** ₹38,250
- **Unpaid Amount:** ₹0
- **Type:** settled

### Test Validation:
```javascript
// API Call
GET /api/labor/payments/unpaid-v2?groupId=test-group-001&firmId=test-firm-001

// Expected Response
{
  "success": true,
  "data": [
    {
      "period": {
        "period_start": "2025-04-04",
        "period_end": "2025-04-26"
      },
      "totalPayments": 38250,
      "unpaidAmount": 0,
      "type": "settled",
      "totalEarnings": 38250
    }
  ]
}
```

## Scenario 2: Post-Final Payment Period

### Setup Data:
**Continuing from Scenario 1...**

**Additional Payments (after final settlement):**
1. 03-05-2025: Advance Payment = ₹3,000
2. 06-05-2025: Advance Payment = ₹4,000
3. 10-05-2025: Site Expense Payment = ₹3,000

**Total Post-Final Payments:** ₹10,000

### Expected Results:
- **Period Display:** 29-04-2025 - 10-05-2025 (virtual period)
- **Total Payments:** ₹10,000
- **Unpaid Amount:** ₹10,000 (all post-final payments are unpaid)
- **Type:** post-final

### Test Validation:
```javascript
// Expected Response (should include both periods)
{
  "success": true,
  "data": [
    {
      "period": {
        "period_start": "2025-04-04",
        "period_end": "2025-04-26"
      },
      "totalPayments": 38250,
      "unpaidAmount": 0,
      "type": "settled",
      "totalEarnings": 38250
    },
    {
      "period": {
        "period_start": "2025-04-29",
        "period_end": "2025-05-10"
      },
      "totalPayments": 10000,
      "unpaidAmount": 10000,
      "type": "post-final",
      "totalEarnings": 0
    }
  ]
}
```

## Scenario 3: Ongoing Period Without Final Settlement

### Setup Data:
**New Attendance Period:** 15-05-2025 to 05-06-2025 (22 days)

**Attendance Records:**
- Worker A: 18 days worked = ₹9,000
- Worker B: 20 days worked = ₹12,000
- Worker C: 19 days worked = ₹10,450
- **Total Labor Earnings:** ₹31,450

**Site Expenses:**
- 20-05-2025: ₹1,800
- 28-05-2025: ₹2,200
- **Total Site Expenses:** ₹4,000

**Total Period Earnings:** ₹35,450

**Payments Made (no final payment yet):**
1. 18-05-2025: Advance Payment = ₹3,000
2. 25-05-2025: Advance Payment = ₹4,000
3. 30-05-2025: Site Expense Payment = ₹3,000

**Total Payments:** ₹10,000
**Due Amount:** ₹25,450

### Expected Results:
- **Period Display:** 15-05-2025 - 05-06-2025
- **Total Payments:** ₹10,000
- **Unpaid Amount:** ₹25,450
- **Type:** ongoing

### Test Validation:
```javascript
// Expected Response (should include all three periods)
{
  "success": true,
  "data": [
    {
      "period": {
        "period_start": "2025-04-04",
        "period_end": "2025-04-26"
      },
      "totalPayments": 38250,
      "unpaidAmount": 0,
      "type": "settled",
      "totalEarnings": 38250
    },
    {
      "period": {
        "period_start": "2025-04-29",
        "period_end": "2025-05-10"
      },
      "totalPayments": 10000,
      "unpaidAmount": 10000,
      "type": "post-final",
      "totalEarnings": 0
    },
    {
      "period": {
        "period_start": "2025-05-15",
        "period_end": "2025-06-05"
      },
      "totalPayments": 10000,
      "unpaidAmount": 25450,
      "type": "ongoing",
      "totalEarnings": 35450
    }
  ]
}
```

## Edge Case Tests

### Test 4: Multiple Final Payments
- Verify warning when multiple final payments exist
- Ensure latest final payment is used for calculations

### Test 5: Payments Without Attendance
- Verify orphaned payments are flagged in validation
- Ensure they're handled gracefully

### Test 6: Negative Scenarios
- Invalid date formats
- Missing required fields
- Non-existent group IDs

## Validation Checklist

- [ ] Scenario 1: Settled period shows 0 unpaid amount
- [ ] Scenario 2: Post-final payments show as fully unpaid
- [ ] Scenario 3: Ongoing period shows correct due amount
- [ ] All payment types are correctly categorized
- [ ] Validation warnings appear for data inconsistencies
- [ ] Frontend toggle switches between old and new logic
- [ ] Loading states work correctly
- [ ] Error handling works for invalid inputs

## Performance Tests

- [ ] API response time < 2 seconds for 100 periods
- [ ] Memory usage remains stable with large datasets
- [ ] Database queries are optimized (check query plans)

## Integration Tests

- [ ] Payment creation updates unpaid amounts correctly
- [ ] Final payment properly settles periods
- [ ] Frontend displays match API responses
- [ ] Validation results are shown to users
