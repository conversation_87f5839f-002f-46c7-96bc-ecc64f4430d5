<template>
  <div class="bg-white rounded-lg shadow p-6">
    <form @submit.prevent="handleSubmit">
      <!-- Main filters grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Report Type -->
        <div>
          <label for="reportType" class="block text-sm font-medium text-gray-700 mb-1">Report Type *</label>
          <select
            id="reportType"
            v-model="filters.type"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            required
            @change="handleReportTypeChange"
          >
            <option value="daily">Daily</option>
            <option value="weekly">Weekly</option>
            <option value="monthly">Monthly</option>
            <option value="yearly">Yearly</option>
            <option value="financial-year">Financial Year</option>
            <option value="date-range">Date Range</option>
            <option value="paidTo">By Paid To/From</option>
            <option value="category">By Category</option>
            <option value="project">By Project</option>
            <option value="subs">Subs Only</option>
          </select>
        </div>

      </div>

      <!-- Time Period Selector (outside the main grid for better visibility) -->
      <div v-if="showTimePeriodSelector" class="mt-4 bg-blue-50 p-4 rounded-md border border-blue-200">
        <div class="text-sm font-medium text-blue-800 mb-2">Select Time Period for {{ filters.type.charAt(0).toUpperCase() + filters.type.slice(1) }} Report</div>
        <TimePeriodSelector
          :key="filters.type"
          :type="filters.type"
          v-model="filters.timePeriod"
        />
      </div>

      <!-- Continue with the main grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-4">

        <!-- Date Range -->
        <div v-if="showDateRange" class="md:col-span-2">
          <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-1">Date Range</label>
          <div class="flex space-x-2">
            <input
              type="date"
              id="startDate"
              v-model="filters.startDate"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <span class="self-center">to</span>
            <input
              type="date"
              id="endDate"
              v-model="filters.endDate"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
          </div>
        </div>

        <!-- Paid To Filter -->
        <div v-if="filters.type === 'paidTo'">
          <label for="paidTo" class="block text-sm font-medium text-gray-700 mb-1">Paid To/From</label>
          <div class="relative">
            <input
              type="text"
              id="paidTo"
              v-model="filters.paidTo"
              list="paidToList"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="paidToList">
              <option v-for="paidTo in uniquePaidTo" :key="paidTo" :value="paidTo"></option>
            </datalist>
          </div>
        </div>

        <!-- Category Filter -->
        <div v-if="filters.type === 'category'">
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1">Category</label>
          <div class="relative">
            <input
              type="text"
              id="category"
              v-model="filters.category"
              list="categoryList"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="categoryList">
              <option value="PAYMENT"></option>
              <option value="RECEIPT"></option>
              <option value="TRANSFER"></option>
              <option v-for="category in uniqueCategories" :key="category" :value="category"></option>
            </datalist>
          </div>
        </div>

        <!-- Project Filter -->
        <div v-if="filters.type === 'project'">
          <label for="project" class="block text-sm font-medium text-gray-700 mb-1">Project</label>
          <div class="relative">
            <input
              type="text"
              id="project"
              v-model="filters.project"
              list="projectList"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
            />
            <datalist id="projectList">
              <option v-for="project in uniqueProjects" :key="project" :value="project"></option>
            </datalist>
          </div>
        </div>

        <!-- Advanced Filters (Collapsible) -->
        <div class="md:col-span-3">
          <button
            type="button"
            @click="showAdvancedFilters = !showAdvancedFilters"
            class="text-indigo-600 hover:text-indigo-800 text-sm flex items-center"
          >
            <span v-if="showAdvancedFilters">Hide Advanced Filters</span>
            <span v-else>Show Advanced Filters</span>
            <svg
              :class="{ 'transform rotate-180': showAdvancedFilters }"
              class="ml-1 h-5 w-5 transition-transform duration-200"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>

          <div v-if="showAdvancedFilters" class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
            <!-- Payment Mode Filter -->
            <div>
              <label for="paymentMode" class="block text-sm font-medium text-gray-700 mb-1">Payment Mode</label>
              <select
                id="paymentMode"
                v-model="filters.paymentMode"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">All Payment Modes</option>
                <option value="cash">Cash</option>
                <option value="bank">Bank</option>
              </select>
            </div>

            <!-- Paid To Group Filter -->
            <div>
              <label for="paidToGroup" class="block text-sm font-medium text-gray-700 mb-1">Paid To Group</label>
              <div class="relative">
                <input
                  type="text"
                  id="paidToGroup"
                  v-model="filters.paidToGroup"
                  list="paidToGroupList"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                />
                <datalist id="paidToGroupList">
                  <option v-for="group in uniquePaidToGroups" :key="group" :value="group"></option>
                </datalist>
              </div>
            </div>

            <!-- Transfer Filter -->
            <div>
              <label for="isTransfer" class="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
              <select
                id="isTransfer"
                v-model="filters.isTransfer"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              >
                <option value="">All Transactions</option>
                <option value="false">Regular Expenses/Receipts</option>
                <option value="true">Transfers Only</option>
              </select>
            </div>
          </div>
        </div>
      </div>

      <!-- Form Actions -->
      <div class="mt-6 flex justify-end space-x-4">
        <button
          type="button"
          @click="resetFilters"
          class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          :disabled="isLoading"
        >
          Reset
        </button>
        <button
          type="submit"
          class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          :disabled="isLoading"
        >
          <span v-if="isLoading">
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Generating...
          </span>
          <span v-else>Generate Report</span>
        </button>
      </div>
    </form>
  </div>
</template>

<script>
import { ref, computed, onMounted, watch } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { usePaidToGroups } from '~/composables/expenses/usePaidToGroups';
import TimePeriodSelector from '~/components/expenses/TimePeriodSelector.vue';

export default {
  name: 'ReportFilters',

  components: {
    TimePeriodSelector
  },

  props: {
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['generate', 'reset'],

  setup(props, { emit }) {
    // Get composables
    const {
      getUniquePaidTo,
      getUniqueCategories,
      getUniqueProjects,
      fetchExpenses
    } = useExpenses();

    const {
      getUniquePaidToGroupNames,
      fetchPaidToGroups
    } = usePaidToGroups();

    // State
    const filters = ref({
      type: 'monthly',
      timePeriod: '',
      startDate: '',
      endDate: '',
      paidTo: '',
      category: '',
      project: '',
      paymentMode: '',
      paidToGroup: '',
      isTransfer: ''
    });

    const showAdvancedFilters = ref(false);

    // Computed properties
    const uniquePaidTo = computed(() => getUniquePaidTo.value);
    const uniqueCategories = computed(() => getUniqueCategories.value);
    const uniqueProjects = computed(() => getUniqueProjects.value);
    const uniquePaidToGroups = computed(() => getUniquePaidToGroupNames.value);

    const showTimePeriodSelector = computed(() => {
      return ['weekly', 'monthly', 'yearly', 'financial-year'].includes(filters.value.type);
    });

    const showDateRange = computed(() => {
      return filters.value.type === 'date-range' || filters.value.type === 'daily';
    });

    // Methods
    const handleReportTypeChange = () => {
      // Reset timePeriod when report type changes
      filters.value.timePeriod = '';

      // Reset date range if not needed
      if (!showDateRange.value) {
        filters.value.startDate = '';
        filters.value.endDate = '';
      } else {
        // Set default date range if needed
        filters.value.startDate = getDefaultStartDate();
        filters.value.endDate = getDefaultEndDate();
      }
    };

    const handleSubmit = () => {
      emit('generate', { ...filters.value });
    };

    const resetFilters = () => {
      filters.value = {
        type: 'monthly',
        timePeriod: '',
        startDate: getDefaultStartDate(),
        endDate: getDefaultEndDate(),
        paidTo: '',
        category: '',
        project: '',
        paymentMode: '',
        paidToGroup: '',
        isTransfer: ''
      };

      emit('reset');
    };

    const getDefaultStartDate = () => {
      const date = new Date();
      date.setMonth(date.getMonth() - 3); // Default to 3 months ago
      return date.toISOString().split('T')[0];
    };

    const getDefaultEndDate = () => {
      return new Date().toISOString().split('T')[0];
    };

    // Initialize
    onMounted(async () => {
      try {
        // Set default date range
        filters.value.startDate = getDefaultStartDate();
        filters.value.endDate = getDefaultEndDate();

        // Fetch data for dropdowns
        await Promise.all([
          fetchExpenses(),
          fetchPaidToGroups()
        ]);
      } catch (error) {
        console.error('Error initializing report filters:', error);
      }
    });

    // Watch for changes to report type
    watch(() => filters.value.type, (newType) => {
      // Reset specific filters when report type changes
      if (newType === 'paidTo') {
        filters.value.category = '';
        filters.value.project = '';
      } else if (newType === 'category') {
        filters.value.paidTo = '';
        filters.value.project = '';
      } else if (newType === 'project') {
        filters.value.paidTo = '';
        filters.value.category = '';
      } else if (newType === 'subs') {
        filters.value.paidToGroup = 'subs';
      }
    });

    return {
      filters,
      showAdvancedFilters,
      uniquePaidTo,
      uniqueCategories,
      uniqueProjects,
      uniquePaidToGroups,
      showDateRange,
      showTimePeriodSelector,
      handleSubmit,
      handleReportTypeChange,
      resetFilters
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
