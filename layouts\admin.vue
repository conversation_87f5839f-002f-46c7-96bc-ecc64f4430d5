<template>
  <div class="admin-layout bg-gray-50 min-h-screen">
    <!-- Top Navigation Bar -->
    <div class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg">
      <div class="container mx-auto px-4 py-3">
        <div class="flex justify-between items-center">
          <div class="flex items-center space-x-4">
            <!-- Mobile Menu Toggle -->
            <button @click="toggleSidebar" class="lg:hidden">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              </svg>
            </button>
            
            <!-- Logo -->
            <NuxtLink to="/admin" class="flex items-center">
              <span class="text-xl font-bold tracking-tight">Admin Dashboard</span>
            </NuxtLink>
          </div>
          
          <!-- Right Side Navigation Items -->
          <div class="flex items-center space-x-4">
            <!-- Notifications -->
            <div class="relative">
              <button class="p-1 rounded-full hover:bg-indigo-500 transition-colors">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                </svg>
                <span class="absolute top-0 right-0 bg-red-500 text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
              </button>
            </div>
            
            <!-- User Menu -->
            <div class="relative" ref="userMenuContainer">
              <button @click="toggleUserMenu" class="flex items-center space-x-2 focus:outline-none">
                <div class="w-8 h-8 rounded-full bg-indigo-400 flex items-center justify-center">
                  <span class="font-semibold">{{ userInitials }}</span>
                </div>
                <span class="hidden md:inline-block">{{ userName }}</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                </svg>
              </button>
              
              <!-- User Dropdown Menu -->
              <div v-if="showUserMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-10 animate-fade-in-down">
                <NuxtLink to="/profile" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Your Profile</NuxtLink>
                <NuxtLink to="/settings" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">Settings</NuxtLink>
                <div class="border-t border-gray-100"></div>
                <button @click="logout" class="block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                  Sign out
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="flex">
      <!-- Sidebar -->
      <div :class="['bg-white shadow-lg transition-all duration-300 ease-in-out', 
                   sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0',
                   'fixed lg:static inset-y-0 left-0 z-30 w-64 overflow-y-auto lg:block']">
        <div class="px-4 py-6">
          <div class="mb-8">
            <h2 class="text-xs uppercase tracking-wider text-gray-500 font-semibold mb-3">Main</h2>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/admin" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                  </svg>
                  Dashboard
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/admin?tab=management" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4" />
                  </svg>
                  Management
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/admin?tab=database" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
                  </svg>
                  Database
                </NuxtLink>
              </li>
            </ul>
          </div>
          
          <div class="mb-8">
            <h2 class="text-xs uppercase tracking-wider text-gray-500 font-semibold mb-3">Management</h2>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/admin?tab=management&subtab=firms" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                  Firms
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/admin?tab=management&subtab=users" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                  </svg>
                  Users
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/admin?tab=management&subtab=codes" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V5a2 2 0 114 0v1m-4 0a2 2 0 104 0m-5 8a2 2 0 100-4 2 2 0 000 4zm0 0c1.306 0 2.417.835 2.83 2M9 14a3.001 3.001 0 00-2.83 2M15 11h3m-3 4h2" />
                  </svg>
                  Manager Codes
                </NuxtLink>
              </li>
            </ul>
          </div>
          
          <div class="mb-8">
            <h2 class="text-xs uppercase tracking-wider text-gray-500 font-semibold mb-3">Database</h2>
            <ul class="space-y-2">
              <li>
                <NuxtLink to="/admin?tab=database&subtab=mongodb" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2 1 3 3 3h10c2 0 3-1 3-3V7c0-2-1-3-3-3H7c-2 0-3 1-3 3z" />
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 11h6m-6 4h6" />
                  </svg>
                  MongoDB
                </NuxtLink>
              </li>
              <li>
                <NuxtLink to="/admin?tab=database&subtab=firestore" class="flex items-center px-3 py-2 text-gray-700 hover:bg-indigo-50 hover:text-indigo-600 rounded-md transition-colors group">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400 group-hover:text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 14v6m-3-3h6M6 10h2a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2zm10 0h2a2 2 0 002-2V6a2 2 0 00-2-2h-2a2 2 0 00-2 2v2a2 2 0 002 2zM6 20h2a2 2 0 002-2v-2a2 2 0 00-2-2H6a2 2 0 00-2 2v2a2 2 0 002 2z" />
                  </svg>
                  Firestore
                </NuxtLink>
              </li>
            </ul>
          </div>
          
          <!-- System Status Card -->
          <div class="bg-gradient-to-br from-indigo-50 to-blue-50 rounded-lg p-4 shadow-sm border border-indigo-100">
            <h3 class="text-sm font-medium text-indigo-700 mb-2">System Status</h3>
            <div class="space-y-2">
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Server</span>
                <span class="flex items-center text-xs font-medium text-green-600">
                  <span class="h-2 w-2 rounded-full bg-green-500 mr-1.5"></span>
                  Online
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Database</span>
                <span class="flex items-center text-xs font-medium text-green-600">
                  <span class="h-2 w-2 rounded-full bg-green-500 mr-1.5"></span>
                  Connected
                </span>
              </div>
              <div class="flex justify-between items-center">
                <span class="text-xs text-gray-600">Storage</span>
                <span class="flex items-center text-xs font-medium text-yellow-600">
                  <span class="h-2 w-2 rounded-full bg-yellow-500 mr-1.5"></span>
                  75% Used
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Overlay for mobile sidebar -->
      <div v-if="sidebarOpen" @click="closeSidebar" class="fixed inset-0 z-20 bg-black bg-opacity-50 lg:hidden"></div>
      
      <!-- Main Content -->
      <div class="flex-1 overflow-x-hidden">
        <div class="container mx-auto px-4 py-6">
          <!-- Breadcrumbs -->
          <div class="mb-6 flex items-center text-sm text-gray-500">
            <NuxtLink to="/admin" class="hover:text-indigo-600">Dashboard</NuxtLink>
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mx-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
            <span class="text-gray-700">{{ currentPage }}</span>
          </div>
          
          <!-- Page Content -->
          <div class="animate-fade-in">
            <slot />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useLogout } from '~/composables/auth/useLogout';

const route = useRoute();
const router = useRouter();

// User information
const userName = ref('Admin User');
const userInitials = computed(() => {
  return userName.value
    .split(' ')
    .map(name => name[0])
    .join('')
    .toUpperCase();
});

// Sidebar state
const sidebarOpen = ref(false);

const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value;
};

const closeSidebar = () => {
  sidebarOpen.value = false;
};

// User menu state
const showUserMenu = ref(false);
const userMenuContainer = ref(null);

const toggleUserMenu = () => {
  showUserMenu.value = !showUserMenu.value;
};

// Close user menu when clicking outside
const handleClickOutside = (event) => {
  if (userMenuContainer.value && !userMenuContainer.value.contains(event.target)) {
    showUserMenu.value = false;
  }
};

// Current page for breadcrumbs
const currentPage = computed(() => {
  const tab = route.query.tab || 'dashboard';
  const subtab = route.query.subtab;
  
  let pageName = tab.charAt(0).toUpperCase() + tab.slice(1);
  
  if (subtab) {
    pageName += ' > ' + subtab.charAt(0).toUpperCase() + subtab.slice(1);
  }
  
  return pageName;
});

// Use centralized logout composable
const { performLogout } = useLogout()

// Logout function
const logout = async () => {
  await performLogout()
};

onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});
</script>

<style>
/* Animations */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

.animate-fade-in-down {
  animation: fadeInDown 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
