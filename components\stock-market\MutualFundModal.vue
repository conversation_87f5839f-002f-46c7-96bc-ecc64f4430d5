<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-[60] p-2 sm:p-4">
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full sm:max-w-2xl lg:max-w-5xl xl:max-w-6xl 2xl:max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
      <!-- Modal Header with Gradient -->
      <div class="bg-green-600 p-4 flex justify-between items-center">
        <h3 class="text-lg font-medium text-white">{{ modalTitle }}</h3>
        <button
          @click="$emit('close')"
          class="text-white hover:text-gray-200 focus:outline-none"
        >
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- Modal Body with Scrollable Content -->
      <div class="flex-1 overflow-y-auto p-4">
                <form @submit.prevent="saveMutualFund" class="grid grid-cols-1 lg:grid-cols-3 gap-5 w-full">
                  <!-- Modal Type Selection - Only show if not editing an existing record -->
                  <div v-if="!mutualFund._id" class="mb-6 col-span-1 lg:col-span-3 w-full">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">What would you like to do?</label>
                    <div class="flex flex-col space-y-2 w-full">
                      <div class="flex items-center">
                        <input
                          type="radio"
                          id="addNew"
                          name="modalType"
                          value="new"
                          v-model="modalType"
                          class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <label for="addNew" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Add a new mutual fund
                        </label>
                      </div>
                      <div class="flex items-center">
                        <input
                          type="radio"
                          id="addToExisting"
                          name="modalType"
                          value="addToExisting"
                          v-model="modalType"
                          class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                        />
                        <label for="addToExisting" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          Add to an existing mutual fund
                        </label>
                      </div>
                      <div v-if="modalType === 'addToExisting'" class="w-full mt-4">
                        <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg border border-gray-200 dark:border-gray-600 w-full">
                          <h4 class="text-md font-medium text-gray-800 dark:text-gray-200 mb-3">Add to Existing Mutual Fund</h4>

                          <!-- Main container with horizontal layout on large screens -->
                          <div class="flex flex-col lg:flex-row lg:space-x-4 w-full">
                            <!-- Left column: Search and filter controls -->
                            <div class="lg:w-[20%] mb-4 lg:mb-0">
                              <div class="flex flex-col gap-3">
                                <div>
                                  <label for="fundSearch" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Search Funds</label>
                                  <input
                                    type="text"
                                    id="fundSearch"
                                    v-model="fundSearchQuery"
                                    placeholder="Search by name, fund house, etc."
                                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                  />
                                </div>

                                <div>
                                  <label for="fundTypeFilter" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Fund Type</label>
                                  <select
                                    id="fundTypeFilter"
                                    v-model="fundTypeFilter"
                                    class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                                  >
                                    <option value="all">All Funds</option>
                                    <option value="sip">SIP Only</option>
                                    <option value="non-sip">Non-SIP Only</option>
                                  </select>
                                </div>

                                <div>
                                  <button
                                    @click="refreshExistingSIPs"
                                    class="w-full px-4 py-2 bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 flex items-center justify-center"
                                  >
                                    <span class="mr-1">↻</span> Refresh
                                  </button>
                                </div>
                              </div>
                            </div>

                            <!-- Middle column: Fund selection list -->
                            <div class="lg:w-[45%] mb-4 lg:mb-0">
                              <!-- Loading state -->
                              <div v-if="isLoadingExistingSIPs" class="py-8 text-center">
                                <div class="inline-block animate-spin text-blue-600 text-xl mb-2">⟳</div>
                                <p class="text-blue-600">Loading mutual funds...</p>
                              </div>

                              <!-- Empty state -->
                              <div v-else-if="!localExistingFunds || localExistingFunds.length === 0" class="py-8 text-center">
                                <p class="text-red-500 mb-2">No mutual funds found</p>
                                <p class="text-gray-500 dark:text-gray-400">Please add a new fund first</p>
                              </div>

                              <!-- Fund selection list -->
                              <div v-else class="h-[400px] overflow-y-auto border border-gray-200 dark:border-gray-600 rounded-md">
                                <!-- Group funds by fund house -->
                                <div v-for="(funds, fundHouse) in groupedFundsByHouse" :key="fundHouse" class="border-b border-gray-200 dark:border-gray-600 last:border-b-0">
                                  <!-- Fund house header -->
                                  <div class="bg-gray-100 dark:bg-gray-600 px-3 py-2 font-medium text-gray-700 dark:text-gray-200 sticky top-0">
                                    {{ fundHouse }} ({{ funds.length }})
                                  </div>

                                  <!-- Fund items -->
                                  <div v-for="fund in funds" :key="fund._id"
                                       @click="selectExistingFund(fund)"
                                       class="px-3 py-3 border-t border-gray-200 dark:border-gray-600 first:border-t-0 hover:bg-blue-50 dark:hover:bg-gray-700 cursor-pointer"
                                       :class="{'bg-blue-50 dark:bg-gray-700': selectedExistingFund === fund._id}">
                                    <div class="flex justify-between items-start">
                                      <div>
                                        <div class="font-medium text-gray-800 dark:text-gray-200 flex items-center">
                                          {{ fund.schemeName }}
                                          <span v-if="fund.sipFlag" class="ml-2 px-2 py-0.5 text-xs bg-green-100 text-green-800 rounded-full">SIP</span>
                                        </div>
                                        <div class="text-sm text-gray-500 dark:text-gray-400 mt-1">
                                          {{ fund.category }} | {{ fund.schemeType }}
                                        </div>
                                        <div class="text-xs text-gray-500 dark:text-gray-400 mt-1">
                                          Folio: {{ fund.folioNumber || 'N/A' }} | NAV: ₹{{ formatNumber(fund.currentNAV || fund.purchaseNAV) }}
                                        </div>
                                      </div>
                                      <div class="text-right">
                                        <div class="font-medium text-gray-800 dark:text-gray-200">
                                          ₹{{ formatNumber(fund.currentValue || 0) }}
                                        </div>
                                        <div class="text-sm mt-1" :class="getProfitLossClass(fund.profitLossPercentage || 0)">
                                          {{ formatProfitLoss(fund.profitLossPercentage || 0) }}
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            <!-- Right column: Selected fund details -->
                            <div class="lg:w-[35%]">
                              <div v-if="selectedExistingFund && selectedFundDetails" class="p-3 bg-blue-50 dark:bg-gray-600 rounded-md border border-blue-100 dark:border-gray-500 h-full">
                                <h5 class="font-medium text-gray-800 dark:text-gray-200 mb-3">Selected Fund Details</h5>
                                <div class="flex flex-col text-sm">
                                  <div class="mb-2">
                                    <span class="text-gray-500 dark:text-gray-400 mr-1">Scheme Name:</span>
                                    <span class="text-gray-800 dark:text-gray-200 font-medium">{{ selectedFundDetails.schemeName }}</span>
                                  </div>
                                  <div class="mb-2">
                                    <span class="text-gray-500 dark:text-gray-400 mr-1">Fund House:</span>
                                    <span class="text-gray-800 dark:text-gray-200 font-medium">{{ selectedFundDetails.fundHouse }}</span>
                                  </div>
                                  <div class="mb-2">
                                    <span class="text-gray-500 dark:text-gray-400 mr-1">Current NAV:</span>
                                    <span class="text-gray-800 dark:text-gray-200 font-medium">₹{{ formatNumber(selectedFundDetails.currentNAV || selectedFundDetails.purchaseNAV) }}</span>
                                  </div>
                                  <div class="mb-2">
                                    <span class="text-gray-500 dark:text-gray-400 mr-1">Total Units:</span>
                                    <span class="text-gray-800 dark:text-gray-200 font-medium">{{ formatNumber(selectedFundDetails.units || 0) }}</span>
                                  </div>
                                  <div class="mb-2">
                                    <span class="text-gray-500 dark:text-gray-400 mr-1">Current Value:</span>
                                    <span class="text-gray-800 dark:text-gray-200 font-medium">₹{{ formatNumber(selectedFundDetails.currentValue || 0) }}</span>
                                  </div>
                                  <div class="mb-2">
                                    <span class="text-gray-500 dark:text-gray-400 mr-1">Returns:</span>
                                    <span :class="getProfitLossClass(selectedFundDetails.profitLossPercentage || 0)" class="font-medium">
                                      {{ formatProfitLoss(selectedFundDetails.profitLossPercentage || 0) }}
                                    </span>
                                  </div>
                                </div>
                              </div>
                              <div v-else class="p-3 bg-gray-100 dark:bg-gray-700 rounded-md border border-gray-200 dark:border-gray-600 h-full flex items-center justify-center">
                                <p class="text-gray-500 dark:text-gray-400 text-center">Select a fund to view details</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Scheme Name with Autocomplete -->
                  <div class="mb-4 relative">
                    <label for="schemeName" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Scheme Name*</label>
                    <div class="flex">
                      <input
                        type="text"
                        id="schemeName"
                        v-model="schemeNameSearch"
                        @input="searchSchemeByName"
                        @focus="showSchemeNameResults = true"
                        required
                        autocomplete="off"
                        placeholder="Start typing to search schemes..."
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      />
                      <button
                        type="button"
                        @click="searchSchemeByName"
                        class="ml-2 mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        Search
                      </button>
                    </div>

                    <!-- Autocomplete Results -->
                    <div
                      v-if="showSchemeNameResults && schemeNameResults.length > 0"
                      class="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md border border-gray-300 dark:border-gray-600 max-h-60 overflow-y-auto"
                    >
                      <ul class="py-1">
                        <li
                          v-for="(result, index) in schemeNameResults"
                          :key="index"
                          @click="selectScheme(result)"
                          class="px-3 py-2 cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-sm"
                        >
                          <div class="font-medium">{{ result.schemeName }}</div>
                          <div class="text-xs text-gray-500 dark:text-gray-400">
                            {{ result.fundHouse }} | Code: {{ result.schemeCode }}
                          </div>
                        </li>
                      </ul>
                    </div>

                    <!-- Hidden input for the actual scheme name value -->
                    <input
                      type="hidden"
                      v-model="mutualFund.schemeName"
                      required
                    />
                  </div>

                  <!-- Scheme Code -->
                  <div class="mb-4">
                    <label for="schemeCode" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Scheme Code*</label>
                    <div class="flex">
                      <input
                        type="text"
                        id="schemeCode"
                        v-model="mutualFund.schemeCode"
                        required
                        class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        placeholder="e.g. 120503"
                      />
                      <button
                        type="button"
                        @click="searchScheme"
                        class="ml-2 mt-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
                      >
                        Search
                      </button>
                    </div>
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      You can find scheme codes on the <a href="https://www.amfiindia.com/nav-history-download" target="_blank" class="text-indigo-600 dark:text-indigo-400 hover:underline">AMFI website</a>
                    </p>
                  </div>

                  <!-- Fund House -->
                  <div class="mb-4">
                    <label for="fundHouse" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Fund House*</label>
                    <input
                      type="text"
                      id="fundHouse"
                      v-model="mutualFund.fundHouse"
                      required
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="e.g. HDFC Mutual Fund"
                    />
                  </div>

                  <!-- Category -->
                  <div class="mb-4">
                    <label for="category" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Category*</label>
                    <input
                      type="text"
                      id="category"
                      v-model="mutualFund.category"
                      required
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="e.g. Large Cap"
                    />
                  </div>

                  <!-- Purchase NAV -->
                  <div class="mb-4">
                    <label for="purchaseNAV" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Purchase NAV*</label>
                    <input
                      type="number"
                      id="purchaseNAV"
                      v-model="mutualFund.purchaseNAV"
                      required
                      step="0.0001"
                      min="0"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <!-- Current NAV -->
                  <div class="mb-4">
                    <label for="currentNAV" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Current NAV</label>
                    <input
                      type="number"
                      id="currentNAV"
                      v-model="mutualFund.currentNAV"
                      step="0.0001"
                      min="0"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    />
                    <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
                      Leave blank to fetch the latest NAV automatically
                    </p>
                  </div>

                  <!-- Investment Method Selection -->
                  <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">Investment Method*</label>
                    <div class="mt-2 flex">
                      <button
                        type="button"
                        @click="investmentMethod = 'amount'"
                        :class="[
                          'px-4 py-2 text-sm font-medium rounded-l-md',
                          investmentMethod === 'amount'
                            ? 'bg-indigo-600 text-white'
                            : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600'
                        ]"
                      >
                        By Amount
                      </button>
                      <button
                        type="button"
                        @click="investmentMethod = 'units'"
                        :class="[
                          'px-4 py-2 text-sm font-medium rounded-r-md',
                          investmentMethod === 'units'
                            ? 'bg-indigo-600 text-white'
                            : 'bg-white dark:bg-gray-700 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 border-l-0'
                        ]"
                      >
                        By Units
                      </button>
                    </div>
                  </div>

                  <!-- Investment Amount (if by amount) -->
                  <div v-if="investmentMethod === 'amount'" class="mb-4">
                    <label for="investmentAmount" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Investment Amount*</label>
                    <div class="mt-1 relative rounded-md shadow-sm">
                      <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <span class="text-gray-500 dark:text-gray-400 sm:text-sm">₹</span>
                      </div>
                      <input
                        type="number"
                        id="investmentAmount"
                        v-model="mutualFund.investmentAmount"
                        required
                        min="0"
                        step="0.01"
                        class="pl-7 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        @input="calculateUnits"
                      />
                    </div>
                  </div>

                  <!-- Units (if by units) -->
                  <div v-if="investmentMethod === 'units'" class="mb-4">
                    <label for="units" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Units*</label>
                    <input
                      type="number"
                      id="units"
                      v-model="mutualFund.units"
                      required
                      min="0"
                      step="0.001"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      @input="calculateAmount"
                    />
                  </div>

                  <!-- Purchase Date -->
                  <div class="mb-4">
                    <label for="purchaseDate" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Purchase Date*</label>
                    <input
                      type="date"
                      id="purchaseDate"
                      v-model="purchaseDateFormatted"
                      required
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <!-- Folio Number -->
                  <div class="mb-4">
                    <label for="folioNumber" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Folio Number</label>
                    <input
                      type="text"
                      id="folioNumber"
                      v-model="mutualFund.folioNumber"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <!-- Broker -->
                  <div class="mb-4">
                    <label for="broker" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Broker</label>
                    <input
                      type="text"
                      id="broker"
                      v-model="mutualFund.broker"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                      placeholder="e.g. Groww, Zerodha, Direct"
                    />
                  </div>

                  <!-- Expense Ratio -->
                  <div class="mb-4">
                    <label for="expense" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Expense Ratio (%)</label>
                    <input
                      type="number"
                      id="expense"
                      v-model="mutualFund.expense"
                      min="0"
                      max="5"
                      step="0.01"
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>

                  <!-- Scheme Type -->
                  <div class="mb-4">
                    <label for="schemeType" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Scheme Type*</label>
                    <select
                      id="schemeType"
                      v-model="mutualFund.schemeType"
                      required
                      class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="Regular">Regular</option>
                      <option value="Direct">Direct</option>
                    </select>
                  </div>

                  <!-- Dividend Option and SIP Flag in a horizontal layout -->
                  <div class="mb-4 col-span-1 lg:col-span-2">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <!-- Dividend Option -->
                      <div>
                        <label for="dividendOption" class="block text-sm font-medium text-gray-700 dark:text-gray-300">Dividend Option</label>
                        <select
                          id="dividendOption"
                          v-model="mutualFund.dividendOption"
                          class="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                        >
                          <option value="Growth">Growth</option>
                          <option value="Dividend">Dividend</option>
                          <option value="Dividend Reinvestment">Dividend Reinvestment</option>
                        </select>
                      </div>

                      <!-- SIP Details -->
                      <div class="flex items-center h-full pt-8">
                        <input
                          type="checkbox"
                          id="sipFlag"
                          v-model="mutualFund.sipFlag"
                          class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                        />
                        <label for="sipFlag" class="ml-2 block text-sm text-gray-700 dark:text-gray-300">
                          This is a SIP (Systematic Investment Plan)
                        </label>
                      </div>
                    </div>

                    <div v-if="mutualFund.sipFlag" class="mt-4 p-5 bg-blue-50 dark:bg-gray-700 rounded-lg border border-blue-100 dark:border-gray-600">
                      <div class="flex items-center mb-4">
                        <div class="bg-blue-100 dark:bg-blue-900 p-2 rounded-full mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-600 dark:text-blue-300" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd" />
                          </svg>
                        </div>
                        <h4 class="text-md font-medium text-gray-900 dark:text-gray-100">Systematic Investment Plan (SIP) Details</h4>
                      </div>

                      <!-- SIP fields in horizontal layout on large screens -->
                      <div class="flex flex-col lg:flex-row lg:space-x-5 space-y-4 lg:space-y-0">
                        <!-- SIP Amount -->
                        <div class="flex-1">
                          <label for="sipAmount" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">SIP Amount*</label>
                          <div class="relative rounded-md shadow-sm">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                              <span class="text-gray-500 dark:text-gray-400 sm:text-sm">₹</span>
                            </div>
                            <input
                              type="number"
                              id="sipAmount"
                              v-model="mutualFund.sipAmount"
                              required
                              min="0"
                              class="pl-7 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                            />
                          </div>
                          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Regular investment amount each period</p>
                        </div>

                        <!-- SIP Frequency -->
                        <div class="flex-1">
                          <label for="sipFrequency" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">SIP Frequency*</label>
                          <select
                            id="sipFrequency"
                            v-model="mutualFund.sipFrequency"
                            required
                            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                          >
                            <option value="Daily">Daily</option>
                            <option value="Weekly">Weekly</option>
                            <option value="Monthly">Monthly</option>
                            <option value="Quarterly">Quarterly</option>
                            <option value="Yearly">Yearly</option>
                          </select>
                          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">How often the SIP will be processed</p>
                        </div>

                        <!-- SIP Day -->
                        <div class="flex-1">
                          <label for="sipDay" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">SIP Day*</label>
                          <input
                            type="number"
                            id="sipDay"
                            v-model="mutualFund.sipDay"
                            required
                            min="1"
                            max="31"
                            class="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:text-white"
                            placeholder="Day of month/week"
                          />
                          <p class="mt-1 text-xs text-gray-500 dark:text-gray-400">Day of the month when SIP will be processed</p>
                        </div>
                      </div>

                      <div class="mt-4 p-3 bg-blue-100 dark:bg-gray-600 rounded text-sm text-blue-800 dark:text-blue-200">
                        <div class="flex">
                          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                          </svg>
                          <span>
                            SIPs are a great way to invest regularly and benefit from rupee cost averaging. Your investment will be automatically processed on the specified day each period.
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Error message -->
                  <div v-if="error" class="mb-4 text-sm text-red-600 dark:text-red-400">
                    {{ error }}
                  </div>
                </form>
      </div>

      <!-- Modal Footer with Green Background -->
      <div class="bg-green-600 p-4 flex justify-end space-x-3">
        <button
          type="button"
          @click="$emit('close')"
          class="px-4 py-2 bg-white text-gray-800 rounded-md hover:bg-gray-100 focus:outline-none whitespace-nowrap"
        >
          Close
        </button>
        <button
          type="button"
          @click="resetForm"
          class="px-4 py-2 bg-white text-gray-800 rounded-md hover:bg-gray-100 focus:outline-none whitespace-nowrap"
        >
          Reset Form
        </button>
        <button
          type="button"
          @click="saveMutualFund"
          class="px-4 py-2 bg-white text-green-600 rounded-md hover:bg-green-50 focus:outline-none whitespace-nowrap"
          :disabled="isLoading"
        >
          <span v-if="isLoading" class="animate-spin h-4 w-4 mr-2 border-2 border-green-600 border-t-transparent rounded-full inline-block"></span>
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import { useRouter } from '#app';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';

// Initialize router
const router = useRouter();

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  initialData: {
    type: Object,
    default: () => ({})
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  existingSIPs: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['close', 'save', 'fetch-existing-sips']);

// Modal type: 'new' for adding new mutual fund, 'addToExisting' for adding to existing SIP
const modalType = ref('new');
const selectedExistingFund = ref('');

// Computed property for modal title
const modalTitle = computed(() => {
  if (mutualFund.value._id) {
    return 'Edit Mutual Fund';
  }

  return modalType.value === 'addToExisting'
    ? 'Add to Existing Mutual Fund'
    : 'Add Mutual Fund';
});

// Computed property for existing SIP options
const existingSIPOptions = computed(() => {
  return props.existingSIPs || [];
});

// Format number with commas for Indian currency
const formatNumber = (num) => {
  if (num === null || num === undefined) return '0';

  // Convert to number if it's a string
  const numValue = typeof num === 'string' ? parseFloat(num) : num;

  // Check if it's a valid number
  if (isNaN(numValue)) return '0';

  // Format with 2 decimal places and commas for thousands
  return numValue.toLocaleString('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
};

// Format profit/loss percentage
const formatProfitLoss = (percentage) => {
  if (percentage === null || percentage === undefined) return '0%';

  // Convert to number if it's a string
  const numValue = typeof percentage === 'string' ? parseFloat(percentage) : percentage;

  // Check if it's a valid number
  if (isNaN(numValue)) return '0%';

  // Format with sign and 2 decimal places
  const sign = numValue >= 0 ? '+' : '';
  return `${sign}${numValue.toFixed(2)}%`;
};

// Get CSS class for profit/loss display
const getProfitLossClass = (percentage) => {
  if (percentage > 0) {
    return 'text-green-600 dark:text-green-400';
  } else if (percentage < 0) {
    return 'text-red-600 dark:text-red-400';
  }
  return 'text-gray-600 dark:text-gray-400';
};

// Select an existing fund
const selectExistingFund = (fund) => {
  selectedExistingFund.value = fund._id;
  selectedFundDetails.value = fund;

  // Pre-fill the form with the selected fund's data
  // But keep the current date and empty the investment amount/units
  // We're adding a new entry to the existing fund
  mutualFund.value = {
    schemeCode: fund.schemeCode,
    schemeName: fund.schemeName,
    fundHouse: fund.fundHouse,
    category: fund.category,
    purchaseNAV: fund.currentNAV || fund.purchaseNAV, // Use current NAV if available
    currentNAV: fund.currentNAV,
    investmentAmount: null,
    units: null,
    purchaseDate: new Date(),
    folioNumber: fund.folioNumber,
    broker: fund.broker,
    expense: fund.expense,
    dividendOption: fund.dividendOption,
    schemeType: fund.schemeType || 'Direct',
    sipFlag: fund.sipFlag,
    sipAmount: fund.sipAmount,
    sipFrequency: fund.sipFrequency,
    sipDay: fund.sipDay
  };

  // Set investment method based on what's commonly used
  if (fund.sipFlag) {
    investmentMethod.value = 'amount'; // SIPs typically use amount
  }

  // Search for scheme details to get the latest NAV
  if (mutualFund.value.schemeCode) {
    searchScheme();
  }
};

// Handle selection of existing fund (for backward compatibility)
const handleExistingFundChange = () => {
  if (!selectedExistingFund.value) {
    // Reset form if no fund selected
    resetForm();
    selectedFundDetails.value = null;
    return;
  }

  // Find the selected fund
  const fund = localExistingFunds.value.find(f => f._id === selectedExistingFund.value) ||
               props.existingSIPs.find(f => f._id === selectedExistingFund.value);

  if (!fund) return;

  // Use the selectExistingFund method to handle the selection
  selectExistingFund(fund);
};

// Default mutual fund data
const defaultMutualFund = {
  schemeName: '',
  schemeCode: '',
  fundHouse: '',
  category: '',
  purchaseNAV: null,
  units: null,
  investmentAmount: null,
  purchaseDate: new Date(),
  folioNumber: '',
  currentNAV: null,
  sipFlag: false,
  sipAmount: null,
  sipFrequency: 'Monthly',
  sipDay: 1,
  broker: '',
  expense: null,
  dividendOption: 'Growth',
  schemeType: 'Direct'
};

// Mutual fund data
const mutualFund = ref({ ...defaultMutualFund });

// Investment method (by amount or by units)
const investmentMethod = ref('amount');

// Error message
const error = ref('');

// Scheme name search
const schemeNameSearch = ref('');
const schemeNameResults = ref([]);
const showSchemeNameResults = ref(false);
const searchTimeout = ref(null);

// Add loading state for existing SIPs
const isLoadingExistingSIPs = ref(false);

// Add local ref for existing funds
const localExistingFunds = ref([]);

// Add search and filter functionality
const fundSearchQuery = ref('');
const fundTypeFilter = ref('all');
const selectedFundDetails = ref(null);

// Add fetchExistingFunds method
const fetchExistingFunds = async () => {
  isLoadingExistingSIPs.value = true;
  try {
    const api = useApiWithAuth();
    console.log('Fetching existing mutual funds from API...');
    const response = await api.get('/api/stock-market/mutual-funds/raw');
    console.log('Received response from API:', response ? response.length : 0, 'funds');
    localExistingFunds.value = response || [];
  } catch (err) {
    localExistingFunds.value = [];
    console.error('Error fetching existing funds:', err);
  } finally {
    // Always reset loading state in finally block
    isLoadingExistingSIPs.value = false;
    console.log('Loading state reset to false');
  }
};

// Reset form to default values
const resetForm = () => {
  mutualFund.value = { ...defaultMutualFund, purchaseDate: new Date() };
  investmentMethod.value = 'amount';
  error.value = '';
  schemeNameSearch.value = '';
  schemeNameResults.value = [];
  showSchemeNameResults.value = false;
};

// Close autocomplete results when clicking outside
const handleClickOutside = (event) => {
  if (event.target.id !== 'schemeName' && !event.target.closest('.scheme-name-results')) {
    showSchemeNameResults.value = false;
  }
};

// Add and remove event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  console.log('MutualFundModal mounted, show:', props.show, 'modalType:', modalType.value);

  // Debug props
  if (props.show && props.existingSIPs) {
    console.log('Existing SIPs on mount:', props.existingSIPs.length);
  }
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// Format purchase date for input field
const purchaseDateFormatted = computed({
  get() {
    if (!mutualFund.value.purchaseDate) return '';
    const date = new Date(mutualFund.value.purchaseDate);
    return date.toISOString().split('T')[0];
  },
  set(value) {
    mutualFund.value.purchaseDate = value ? new Date(value) : null;
  }
});

// Watch for changes in initialData
watch(() => props.initialData, (newValue) => {
  if (newValue && Object.keys(newValue).length > 0) {
    // Clone the object to avoid modifying the original
    mutualFund.value = JSON.parse(JSON.stringify(newValue));

    // Update the scheme name search field to show the current scheme name
    schemeNameSearch.value = mutualFund.value.schemeName || '';

    // Set investment method based on whether units or amount is provided
    if (mutualFund.value.units) {
      investmentMethod.value = 'units';
    } else {
      investmentMethod.value = 'amount';
    }
  } else {
    // Reset to default values
    mutualFund.value = { ...defaultMutualFund };
    schemeNameSearch.value = '';
    investmentMethod.value = 'amount';
  }
}, { immediate: true });

// Calculate units based on investment amount and purchase NAV
const calculateUnits = () => {
  if (mutualFund.value.investmentAmount && mutualFund.value.purchaseNAV) {
    mutualFund.value.units = Number(mutualFund.value.investmentAmount) / Number(mutualFund.value.purchaseNAV);
  }
};

// Calculate investment amount based on units and purchase NAV
const calculateAmount = () => {
  if (mutualFund.value.units && mutualFund.value.purchaseNAV) {
    mutualFund.value.investmentAmount = Number(mutualFund.value.units) * Number(mutualFund.value.purchaseNAV);
  }
};

// Search for scheme details using AMFI API
const searchScheme = async () => {
  if (!mutualFund.value.schemeCode) {
    error.value = 'Please enter a scheme code to search';
    return;
  }

  try {
    error.value = '';

    // Fetch scheme details from AMFI API
    const response = await fetch(`https://api.mfapi.in/mf/${mutualFund.value.schemeCode}`);

    if (!response.ok) {
      throw new Error(`Failed to fetch scheme details: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (data.error) {
      throw new Error(data.error);
    }

    // Update mutual fund data with scheme details
    mutualFund.value.schemeName = data.meta.scheme_name || '';
    schemeNameSearch.value = data.meta.scheme_name || '';
    mutualFund.value.fundHouse = data.meta.fund_house || '';
    mutualFund.value.category = data.meta.scheme_category || '';

    // Set current NAV if available
    if (data.data && data.data.length > 0) {
      mutualFund.value.currentNAV = parseFloat(data.data[0].nav);

      // If purchase NAV is not set, use current NAV as default
      if (!mutualFund.value.purchaseNAV) {
        mutualFund.value.purchaseNAV = mutualFund.value.currentNAV;
      }
    }

  } catch (err) {
    console.error('Error searching for scheme:', err);
    error.value = err.message || 'Failed to fetch scheme details';
  }
};

// Search for schemes by name
const searchSchemeByName = async () => {
  // Clear any existing timeout
  if (searchTimeout.value) {
    clearTimeout(searchTimeout.value);
  }

  // Don't search if input is too short
  if (!schemeNameSearch.value || schemeNameSearch.value.length < 3) {
    schemeNameResults.value = [];
    return;
  }

  // Set a timeout to avoid too many API calls
  searchTimeout.value = setTimeout(async () => {
    try {
      error.value = '';

      // Fetch schemes from AMFI API
      const response = await fetch(`https://api.mfapi.in/mf/search?q=${encodeURIComponent(schemeNameSearch.value)}`);

      if (!response.ok) {
        throw new Error(`Failed to search schemes: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Format the results
      schemeNameResults.value = data.slice(0, 10).map(scheme => ({
        schemeCode: scheme.schemeCode,
        schemeName: scheme.schemeName,
        fundHouse: scheme.fundHouse || 'Unknown',
        category: scheme.schemeCategory || ''
      }));

      showSchemeNameResults.value = schemeNameResults.value.length > 0;

    } catch (err) {
      console.error('Error searching for schemes by name:', err);
      error.value = err.message || 'Failed to search schemes';
      schemeNameResults.value = [];
    }
  }, 300); // 300ms debounce
};

// Select a scheme from the autocomplete results
const selectScheme = (scheme) => {
  mutualFund.value.schemeCode = scheme.schemeCode;
  mutualFund.value.schemeName = scheme.schemeName;
  schemeNameSearch.value = scheme.schemeName;
  mutualFund.value.fundHouse = scheme.fundHouse;
  mutualFund.value.category = scheme.category;

  // Hide the results
  showSchemeNameResults.value = false;

  // Fetch additional details like NAV
  searchScheme();
};

// Refresh existing SIPs
const refreshExistingSIPs = async () => {
  console.log("Manual refresh of mutual funds requested");

  // Clear the selected fund
  selectedExistingFund.value = '';
  selectedFundDetails.value = null;

  // Reset search and filter
  fundSearchQuery.value = '';
  fundTypeFilter.value = 'all';

  // Set loading state
  isLoadingExistingSIPs.value = true;

  try {
    // Use our local fetch method
    await fetchExistingFunds();
  } catch (err) {
    console.error('Error refreshing funds:', err);
  }

  // Also emit event to ensure parent component data is updated
  emit('fetch-existing-sips');

  // Set a safety timeout to reset loading state after 5 seconds
  setTimeout(() => {
    if (isLoadingExistingSIPs.value) {
      console.log('Safety timeout: resetting loading state after manual refresh');
      isLoadingExistingSIPs.value = false;
    }
  }, 5000);
};

// Watch modalType changes
watch(modalType, (newType) => {
  if (newType === 'addToExisting') {
    console.log('Switching to addToExisting mode, triggering fetch');
    // First try to use local data if available
    if (localExistingFunds.value && localExistingFunds.value.length > 0) {
      console.log('Using locally cached mutual funds:', localExistingFunds.value.length);
      isLoadingExistingSIPs.value = false;
    } else {
      // If no local data, fetch from API
      console.log('No local data available, fetching from API');
      isLoadingExistingSIPs.value = true;
      // Use our local fetch method instead of emitting event
      fetchExistingFunds();
      // Also emit event to ensure parent component data is updated
      emit('fetch-existing-sips');

      // Set a safety timeout to reset loading state after 5 seconds
      // in case the API call or watch doesn't reset it
      setTimeout(() => {
        if (isLoadingExistingSIPs.value) {
          console.log('Safety timeout: resetting loading state');
          isLoadingExistingSIPs.value = false;
        }
      }, 5000);
    }
  }
});

// Watch for existingSIPs changes
watch(() => props.existingSIPs, (newValue) => {
  console.log('Received existingSIPs in modal:', newValue ? newValue.length : 0);
  // Always set loading to false when we receive a response, even if it's empty
  isLoadingExistingSIPs.value = false;
}, { immediate: true });

// Watch for show prop to reset form
watch(() => props.show, (newValue) => {
  if (newValue) {
    // Reset modalType when modal is opened
    modalType.value = 'new';
    selectedExistingFund.value = '';

    // Log data for debugging when modal is opened
    console.log('Modal opened, existingSIPs:', props.existingSIPs?.length);

    // Always reset loading state when modal is opened
    isLoadingExistingSIPs.value = false;

    // Fetch existing funds in the background
    fetchExistingFunds();
  } else {
    // Reset loading state when modal is closed
    isLoadingExistingSIPs.value = false;
  }
});

// Save mutual fund
const saveMutualFund = () => {
  try {
    error.value = '';

    // Common validation code
    if (!mutualFund.value.schemeName) {
      error.value = 'Scheme name is required';
      return;
    }

    if (!mutualFund.value.schemeCode) {
      error.value = 'Scheme code is required';
      return;
    }

    if (!mutualFund.value.fundHouse) {
      error.value = 'Fund house is required';
      return;
    }

    if (!mutualFund.value.category) {
      error.value = 'Category is required';
      return;
    }

    if (!mutualFund.value.purchaseNAV) {
      error.value = 'Purchase NAV is required';
      return;
    }

    if (investmentMethod.value === 'amount' && !mutualFund.value.investmentAmount) {
      error.value = 'Investment amount is required';
      return;
    }

    if (investmentMethod.value === 'units' && !mutualFund.value.units) {
      error.value = 'Units are required';
      return;
    }

    if (!mutualFund.value.purchaseDate) {
      error.value = 'Purchase date is required';
      return;
    }

    // Calculate units or amount if not provided
    if (investmentMethod.value === 'amount') {
      calculateUnits();
    } else {
      calculateAmount();
    }

    // Validate SIP details if SIP is enabled
    if (mutualFund.value.sipFlag) {
      if (!mutualFund.value.sipAmount) {
        error.value = 'SIP amount is required';
        return;
      }

      if (!mutualFund.value.sipFrequency) {
        error.value = 'SIP frequency is required';
        return;
      }

      if (!mutualFund.value.sipDay || mutualFund.value.sipDay < 1 || mutualFund.value.sipDay > 31) {
        error.value = 'SIP day must be between 1 and 31';
        return;
      }
    }

    // Special processing for adding to existing SIP
    if (!mutualFund.value._id && modalType.value === 'addToExisting' && selectedExistingFund.value) {
      // Make sure we're creating a new entry but maintaining the link to existing fund
      const entryData = { ...mutualFund.value };

      // Clear _id so it creates a new record
      delete entryData._id;

      // Flag this as an addition to existing fund
      entryData.isAddingToExisting = true;
      entryData.existingFundId = selectedExistingFund.value;

      // Emit save event with entry data
      emit('save', entryData);
      return;
    }

    // Regular save for new or edit
    emit('save', { ...mutualFund.value });

  } catch (err) {
    console.error('Error saving mutual fund:', err);
    error.value = err.message || 'Failed to save mutual fund';
  }
};

// Add this function to format dates in a shorter format
const formatDateShort = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN', { day: '2-digit', month: 'short', year: 'numeric' });
};

// Add computed property for filtered funds
const filteredFunds = computed(() => {
  if (!localExistingFunds.value || localExistingFunds.value.length === 0) {
    return [];
  }

  return localExistingFunds.value.filter(fund => {
    // Apply search filter
    const searchMatch = !fundSearchQuery.value ||
      fund.schemeName.toLowerCase().includes(fundSearchQuery.value.toLowerCase()) ||
      fund.fundHouse.toLowerCase().includes(fundSearchQuery.value.toLowerCase()) ||
      fund.category.toLowerCase().includes(fundSearchQuery.value.toLowerCase()) ||
      fund.schemeCode.toLowerCase().includes(fundSearchQuery.value.toLowerCase());

    // Apply fund type filter
    let typeMatch = true;
    if (fundTypeFilter.value === 'sip') {
      typeMatch = fund.sipFlag === true;
    } else if (fundTypeFilter.value === 'non-sip') {
      typeMatch = fund.sipFlag !== true;
    }

    return searchMatch && typeMatch;
  });
});

// Add computed property for grouped funds (for backward compatibility)
const groupedFunds = computed(() => {
  // Group by schemeCode + schemeName
  const map = new Map();
  for (const fund of filteredFunds.value) {
    const key = `${fund.schemeCode}::${fund.schemeName}`;
    if (!map.has(key)) {
      map.set(key, fund);
    }
  }
  return Array.from(map.values());
});

// Add computed property for funds grouped by fund house
const groupedFundsByHouse = computed(() => {
  const result = {};

  // First group by fund house
  filteredFunds.value.forEach(fund => {
    const fundHouse = fund.fundHouse || 'Other';
    if (!result[fundHouse]) {
      result[fundHouse] = [];
    }

    // Check if we already have this scheme in the group
    const existingFund = result[fundHouse].find(f =>
      f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName
    );

    if (!existingFund) {
      result[fundHouse].push(fund);
    }
  });

  // Sort fund houses alphabetically
  const sortedResult = {};
  Object.keys(result).sort().forEach(key => {
    sortedResult[key] = result[key];
  });

  return sortedResult;
});
</script>
