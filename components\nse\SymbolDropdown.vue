<template>
  <div class="relative w-full">
    <!-- Input field -->
    <input
      ref="inputRef"
      type="text"
      :value="modelValue"
      @input="onInput"
      @focus="onFocus"
      @blur="onBlur"
      @keydown="onKeyDown"
      class="w-full p-1 border rounded"
      placeholder="Type to search symbols"
    />

    <!-- Dropdown menu -->
    <Teleport to="body">
      <div
        v-if="showDropdown && filteredOptions.length > 0"
        class="fixed z-[9999] bg-white border border-gray-300 rounded-md shadow-xl max-h-60 overflow-y-auto"
        :style="dropdownStyle"
      >
        <div
          v-for="(option, index) in filteredOptions"
          :key="option"
          :class="[
            'p-2 cursor-pointer hover:bg-gray-100 flex items-center',
            index === selectedIndex ? 'bg-red-100 font-bold border-l-4 border-red-500' : ''
          ]"
          @mousedown.prevent="selectOption(option)"
          @mouseover="selectedIndex = index"
        >
          <div class="flex-1 flex justify-between items-center">
            <span class="font-medium">{{ option }}</span>
            <span v-if="symbolPrices[option] !== undefined" class="ml-2 text-sm font-semibold"
                  :class="{'text-green-600': symbolPrices[option] > 0, 'text-red-600': symbolPrices[option] < 0, 'text-gray-600': symbolPrices[option] === 0}">
              ₹{{ formatPrice(symbolPrices[option]) }}
            </span>
            <span v-else class="ml-2 text-xs text-gray-400 italic">
              Loading price...
            </span>
          </div>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import PouchDB from 'pouchdb';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';

const props = defineProps<{
  modelValue: string;
  options: string[];
}>();

const emit = defineEmits(['update:modelValue', 'price-updated']);

const inputRef = ref<HTMLInputElement | null>(null);
const showDropdown = ref(false);
const searchQuery = ref('');
const selectedIndex = ref(-1);
const symbolPrices = ref<Record<string, number>>({});
const inputPosition = ref({ top: 0, left: 0, width: 0 });

// Filter options based on search query
const filteredOptions = computed(() => {
  if (!searchQuery.value) {
    return props.options.slice(0, 10); // Show first 10 options when empty
  }

  const query = searchQuery.value.toLowerCase();
  return props.options
    .filter(option => option.toLowerCase().includes(query))
    .slice(0, 10); // Limit to 10 results for performance
});

// Compute dropdown position and style
const dropdownStyle = computed(() => {
  return {
    top: `${inputPosition.value.top + 40}px`,
    left: `${inputPosition.value.left}px`,
    width: `${Math.max(inputPosition.value.width, 300)}px`,
    maxWidth: '500px',
    minWidth: '300px',
    boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
  };
});

// Format price with commas for thousands
function formatPrice(price: number): string {
  // Check if price is undefined, null, or NaN
  if (price === undefined || price === null || isNaN(price)) {
    return '0.00';
  }

  // Handle zero or actual price values
  return price.toLocaleString('en-IN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// Fetch symbol prices from PouchDB
async function fetchSymbolPrices() {
  try {
    console.log('Fetching symbol prices...');
    // Use the same prefix as in pouchDBUtils.ts
    const DB_PREFIX = 'nse_app_';
    const gsDb = new PouchDB(`${DB_PREFIX}gs_records`);

    // First, let's check what's in the database
    const result = await gsDb.allDocs({ include_docs: true });
    console.log(`Found ${result.rows.length} records in gs_records PouchDB`);

    // Debug: Log the first document to see its structure
    if (result.rows.length > 0) {
      console.log('First document structure:', JSON.stringify(result.rows[0].doc, null, 2));

      const priceMap: Record<string, number> = {};

      // Process each document
      for (const row of result.rows) {
        const doc = row.doc as any;

        // Debug: Log each document's symbol and history
        console.log(`Processing doc with id: ${doc._id}`);
        console.log(`Symbol: ${doc.symbol}, Has history: ${!!(doc.history && doc.history.length)}`);

        if (doc.symbol) {
          // Remove NSE: prefix if it exists
          const symbol = doc.symbol.replace('NSE:', '');

          // Use the last price from history if available
          if (doc.history && doc.history.length > 0) {
            const lastEntry = doc.history[doc.history.length - 1];
            console.log(`Last entry for ${symbol}:`, lastEntry);

            if (lastEntry && lastEntry.close !== undefined) {
              // Ensure we have a valid number
              const price = parseFloat(String(lastEntry.close));
              if (!isNaN(price)) {
                priceMap[symbol] = price;
                console.log(`✅ Price for ${symbol}: ${price}`);
              } else {
                console.warn(`⚠️ Invalid price for ${symbol}: ${lastEntry.close}`);
              }
            } else {
              console.warn(`⚠️ No close price in last entry for ${symbol}`);
            }
          } else {
            // If no history, try to use a direct price property if it exists
            if (doc.price !== undefined) {
              const price = parseFloat(String(doc.price));
              if (!isNaN(price)) {
                priceMap[symbol] = price;
                console.log(`✅ Direct price for ${symbol}: ${price}`);
              }
            } else {
              console.warn(`⚠️ No history or price for ${symbol}`);
            }
          }
        }
      }

      // Update the prices
      symbolPrices.value = priceMap;
      console.log('Symbol prices loaded:', Object.keys(priceMap).length);

      // If no prices were loaded, try to fetch from API directly
      if (Object.keys(priceMap).length === 0) {
        await fetchPricesFromAPI();
      }
    } else {
      console.warn('No records found in gs_records PouchDB');
      await fetchPricesFromAPI();
    }
  } catch (error) {
    console.error('Error fetching symbol prices:', error);
    await fetchPricesFromAPI();
  }
}

// Function to fetch all symbols with prices from the new API endpoint
async function fetchPricesFromAPI() {
  try {
    console.log('Fetching all symbols with prices from API...');

    const api = useApiWithAuth();
    const data = await api.get('/api/nse/all_symbols');

    console.log(`Received ${data.length} symbols from all_symbols API`);

    if (data.length > 0) {
      console.log('First API record:', data[0]);

      const priceMap: Record<string, number> = {};

      // Process the data which has direct symbol-price pairs
      for (const item of data) {
        if (item.symbol && item.price !== undefined) {
          const symbol = item.symbol;
          const price = parseFloat(String(item.price));
          if (!isNaN(price)) {
            priceMap[symbol] = price;
          }
        }
      }

      // Update the prices
      if (Object.keys(priceMap).length > 0) {
        symbolPrices.value = priceMap;
        console.log('Prices loaded from API:', Object.keys(priceMap).length);
      } else {
        console.warn('No prices found in all_symbols API response');
        // Try the get_nse endpoint as a fallback
        await fetchPricesFromGetNseAPI();
      }
    } else {
      console.warn('Empty response from all_symbols API');
      // Try the get_nse endpoint as a fallback
      await fetchPricesFromGetNseAPI();
    }
  } catch (error) {
    console.error('Error fetching prices from all_symbols API:', error);
    // Try the get_nse endpoint as a fallback
    await fetchPricesFromGetNseAPI();
  }
}

// Fallback function to fetch prices from the get_nse API
async function fetchPricesFromGetNseAPI() {
  try {
    console.log('Fetching prices from get_nse API...');

    const api = useApiWithAuth();
    const data = await api.get('/api/nse/get_nse');

    console.log(`Received ${data.length} records from get_nse API`);

    if (data.length > 0) {
      const priceMap: Record<string, number> = {};

      for (const item of data) {
        if (item.symbol) {
          const symbol = item.symbol.replace('NSE:', '');

          if (item.cprice !== undefined) {
            const price = parseFloat(String(item.cprice));
            if (!isNaN(price)) {
              priceMap[symbol] = price;
            }
          }
        }
      }

      if (Object.keys(priceMap).length > 0) {
        symbolPrices.value = priceMap;
        console.log('Prices loaded from get_nse API:', Object.keys(priceMap).length);
      } else {
        // Try the folio endpoint as a last resort
        await fetchPricesFromFolioAPI();
      }
    } else {
      // Try the folio endpoint as a last resort
      await fetchPricesFromFolioAPI();
    }
  } catch (error) {
    console.error('Error fetching prices from get_nse API:', error);
    // Try the folio endpoint as a last resort
    await fetchPricesFromFolioAPI();
  }
}

// Additional fallback to fetch prices from the folio API
async function fetchPricesFromFolioAPI() {
  try {
    console.log('Fetching prices from folio API...');

    const api = useApiWithAuth();
    const data = await api.get('/api/nse/folio');

    console.log(`Received ${data.length} records from folio API`);

    if (data.length > 0) {
      const priceMap: Record<string, number> = {};

      for (const item of data) {
        if (item.symbol && item.cprice !== undefined) {
          const symbol = item.symbol;
          const price = parseFloat(String(item.cprice));
          if (!isNaN(price)) {
            priceMap[symbol] = price;
            console.log(`✅ Folio price for ${symbol}: ${price}`);
          }
        }
      }

      if (Object.keys(priceMap).length > 0) {
        symbolPrices.value = priceMap;
        console.log('Prices loaded from folio API:', Object.keys(priceMap).length);
      }
    }
  } catch (error) {
    console.error('Error fetching prices from folio API:', error);
  }
}

// Handle input changes
function onInput(event: Event) {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;
  emit('update:modelValue', target.value);

  // Reset selected index when input changes
  selectedIndex.value = -1;

  // Show dropdown when typing
  showDropdown.value = true;

  // Update position when typing
  if (inputRef.value) {
    const rect = inputRef.value.getBoundingClientRect();
    inputPosition.value = {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width
    };
  }
}

// Handle focus
function onFocus() {
  showDropdown.value = true;
  searchQuery.value = props.modelValue;

  // Calculate position of input for dropdown positioning
  nextTick(() => {
    if (inputRef.value) {
      const rect = inputRef.value.getBoundingClientRect();
      inputPosition.value = {
        top: rect.top + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      };
    }
    // This will trigger the computed property to update
    searchQuery.value = searchQuery.value;
  });
}

// Handle blur
function onBlur() {
  // Delay hiding dropdown to allow click events to complete
  setTimeout(() => {
    showDropdown.value = false;
  }, 300);
}

// Handle keyboard navigation
function onKeyDown(event: KeyboardEvent) {
  if (!showDropdown.value) {
    if (event.key === 'ArrowDown' || event.key === 'ArrowUp') {
      showDropdown.value = true;
      event.preventDefault();
    }
    return;
  }

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      if (selectedIndex.value < filteredOptions.value.length - 1) {
        selectedIndex.value++;
        ensureSelectedVisible();
      }
      break;

    case 'ArrowUp':
      event.preventDefault();
      if (selectedIndex.value > 0) {
        selectedIndex.value--;
        ensureSelectedVisible();
      }
      break;

    case 'Enter':
      event.preventDefault();
      if (selectedIndex.value >= 0 && selectedIndex.value < filteredOptions.value.length) {
        selectOption(filteredOptions.value[selectedIndex.value]);
      }
      break;

    case 'Escape':
      event.preventDefault();
      showDropdown.value = false;
      break;
  }
}

// Ensure the selected item is visible in the dropdown
function ensureSelectedVisible() {
  nextTick(() => {
    // Use a more specific selector to find our dropdown
    const dropdown = document.querySelector('.fixed.z-\\[9999\\].overflow-y-auto');
    if (!dropdown) return;

    // Find all direct children that are div elements
    const dropdownItems = dropdown.querySelectorAll(':scope > div');
    if (!dropdownItems.length) return;

    // Get the selected item
    const selectedItem = dropdownItems[selectedIndex.value] as HTMLElement;
    if (!selectedItem) return;

    const dropdownRect = dropdown.getBoundingClientRect();
    const selectedRect = selectedItem.getBoundingClientRect();

    if (selectedRect.bottom > dropdownRect.bottom) {
      dropdown.scrollTop += selectedRect.bottom - dropdownRect.bottom;
    } else if (selectedRect.top < dropdownRect.top) {
      dropdown.scrollTop -= dropdownRect.top - selectedRect.top;
    }
  });
}

// Select an option from the dropdown
function selectOption(option: string) {
  emit('update:modelValue', option);
  searchQuery.value = option;
  showDropdown.value = false;

  // Emit the price if available
  if (symbolPrices.value[option] !== undefined) {
    emit('price-updated', symbolPrices.value[option]);
  }

  // Focus the input after selection
  nextTick(() => {
    inputRef.value?.focus();
  });
}

// Watch for changes in modelValue prop
watch(() => props.modelValue, (newValue) => {
  if (!showDropdown.value) {
    searchQuery.value = newValue;
  }
});

// Update dropdown position on window resize
function updatePosition() {
  if (showDropdown.value && inputRef.value) {
    const rect = inputRef.value.getBoundingClientRect();
    inputPosition.value = {
      top: rect.top + window.scrollY,
      left: rect.left + window.scrollX,
      width: rect.width
    };
  }
}

// Track intervals for proper cleanup
const apiRefreshInterval = ref<NodeJS.Timeout | null>(null);
const pouchDbRefreshInterval = ref<NodeJS.Timeout | null>(null);

// Fetch symbol prices on component mount and set up resize listener
onMounted(async () => {
  console.log('SymbolDropdown: Component mounted - starting price fetch and timers');

  // First try to fetch prices directly from the API (Sheet1)
  await fetchPricesFromAPI();

  // Then try PouchDB as a fallback
  if (Object.keys(symbolPrices.value).length === 0) {
    await fetchSymbolPrices();
  }

  // Set up event listeners
  window.addEventListener('resize', updatePosition);
  window.addEventListener('scroll', updatePosition, true);

  // Start refresh intervals only when component is mounted and visible
  startRefreshIntervals();
});

// Function to start refresh intervals
function startRefreshIntervals() {
  console.log('SymbolDropdown: Starting refresh intervals');

  // Clear any existing intervals first
  stopRefreshIntervals();

  // Refresh prices every 2 minutes (120000 ms) directly from API
  apiRefreshInterval.value = setInterval(async () => {
    console.log('SymbolDropdown: Auto-refreshing prices from API');
    await fetchPricesFromAPI();
  }, 120000);

  // Add a second interval to fetch from PouchDB every 5 minutes as a fallback
  pouchDbRefreshInterval.value = setInterval(async () => {
    // Only use PouchDB if we don't have prices from API
    if (Object.keys(symbolPrices.value).length === 0) {
      console.log('SymbolDropdown: Auto-refreshing prices from PouchDB (fallback)');
      await fetchSymbolPrices();
    }
  }, 300000);
}

// Function to stop refresh intervals
function stopRefreshIntervals() {
  console.log('SymbolDropdown: Stopping refresh intervals');

  if (apiRefreshInterval.value) {
    clearInterval(apiRefreshInterval.value);
    apiRefreshInterval.value = null;
  }

  if (pouchDbRefreshInterval.value) {
    clearInterval(pouchDbRefreshInterval.value);
    pouchDbRefreshInterval.value = null;
  }
}

// Clean up event listeners and intervals
onUnmounted(() => {
  console.log('SymbolDropdown: Component unmounting - cleaning up');

  // Stop all refresh intervals
  stopRefreshIntervals();

  // Remove event listeners
  window.removeEventListener('resize', updatePosition);
  window.removeEventListener('scroll', updatePosition, true);
});

// Expose symbolPrices to parent components
defineExpose({
  symbolPrices
});
</script>

<style scoped>
.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.overflow-y-auto {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e0 #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f7fafc;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background-color: #cbd5e0;
  border-radius: 3px;
}
</style>
