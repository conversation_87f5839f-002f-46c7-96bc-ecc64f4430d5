<template>
  <div class="max-w-10xl mx-auto py-6 sm:px-6 lg:px-8">
    <!-- Toast notifications are handled globally -->

    <div class="px-4 py-6 sm:px-0">
      <div class="flex flex-col lg:flex-row lg:justify-between lg:items-center mb-4 space-y-2 lg:space-y-0">
        <h1 class="text-2xl md:text-3xl font-bold text-gray-900">Employee Master Roll Management</h1>
        <div class="flex flex-wrap lg:flex-nowrap gap-2 lg:gap-3 lg:items-center">
          <button @click="showAddModal = true"
            class="inline-flex items-center justify-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 w-full sm:w-auto lg:min-w-[140px] whitespace-nowrap">
            <UserPlusIcon class="h-5 w-5 mr-2" />
            Add Employee
          </button>
          <button @click="showBulkEditModal = true"
            class="inline-flex items-center justify-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-orange-600 hover:bg-orange-700 w-full sm:w-auto lg:min-w-[140px] whitespace-nowrap">
            <PencilSquareIcon class="h-5 w-5 mr-2" />
            Bulk Edit
          </button>
          <button @click="downloadTemplate"
            class="inline-flex items-center justify-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 w-full sm:w-auto lg:min-w-[140px] whitespace-nowrap">
            <ExcelIcon class="h-5 w-5 mr-2" />
            Download Template
          </button>
          <button @click="downloadMasterRoll"
            class="inline-flex items-center justify-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-teal-600 hover:bg-teal-700 w-full sm:w-auto lg:min-w-[140px] whitespace-nowrap">
            <ExcelIcon class="h-5 w-5 mr-2" />
            Download Master Roll
          </button>
          <button @click="showExportModal = true"
            class="inline-flex items-center justify-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-purple-600 hover:bg-purple-700 w-full sm:w-auto lg:min-w-[140px] whitespace-nowrap">
            <DocumentArrowDownIcon class="h-5 w-5 mr-2" />
            Filtered Export
          </button>
          <label
            class="inline-flex items-center justify-center px-3 py-2 md:px-4 md:py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 cursor-pointer w-full sm:w-auto lg:min-w-[140px] whitespace-nowrap">
            <ExcelIcon class="h-5 w-5 mr-2" />
            Upload Excel
            <input type="file" @change="handleFileUpload" accept=".xlsx,.xls" class="hidden">
          </label>
        </div>
      </div>

      <!-- Data Table -->
      <div class="mt-8 flex flex-col">
        <!-- Search Box and Filter Controls -->
        <div class="mb-4 px-4">
          <div class="flex flex-col md:flex-row md:justify-between md:items-center space-y-2 md:space-y-0 md:space-x-4">
            <div class="relative flex-grow">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
              </div>
              <input v-model="searchTerm" type="text" placeholder="Search employees..."
                class="pl-10 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" />
            </div>
            <div v-if="Object.values(columnFilters).some(filters => filters.length > 0)" class="flex items-center">
              <span class="text-sm text-gray-600 mr-2">Active filters:</span>
              <button @click="resetAllFilters()"
                class="flex items-center bg-red-100 text-red-700 hover:bg-red-200 px-3 py-1 rounded text-sm">
                <XMarkIcon class="h-4 w-4 mr-1" />
                Reset All Filters
              </button>
            </div>
          </div>
        </div>
        <div class="-my-2 overflow-x-auto">
          <div class="py-2 align-middle inline-block min-w-full px-2">
            <div class="shadow overflow-hidden border-b border-gray-200 sm:rounded-lg">
              <!-- Desktop Table with fixed header and 70vh height for large screens -->
              <div class="hidden md:block h-[70vh] overflow-y-auto relative">
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gradient-to-r from-teal-500 to-indigo-600 sticky top-0 z-10">
                    <tr>
                      <!-- Sl. No. Column -->
                      <th class="px-3 py-3 text-center text-xs font-medium text-white uppercase tracking-wider">
                        Sl. No.
                      </th>
                      <!-- Employee Name Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('employeeName')" class="flex items-center focus:outline-none">
                            <span>Employee Name</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'employeeName' && sortDirection === 'asc'"
                                class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'employeeName' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('employeeName')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.employeeName.length > 0,
                              'text-blue-300': activeFilterColumn === 'employeeName'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'employeeName'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('employeeName')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.employeeName" :key="option"
                                class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-employeeName-${option}`"
                                  :checked="columnFilters.employeeName.includes(option)"
                                  @change="applyFilter('employeeName', option)" class="mr-2">
                                <label :for="`filter-employeeName-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Father/Husband Name Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('fatherHusbandName')" class="flex items-center focus:outline-none">
                            <span>Father/Husband Name</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'fatherHusbandName' && sortDirection === 'asc'"
                                class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'fatherHusbandName' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('fatherHusbandName')"
                            class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.fatherHusbandName.length > 0,
                              'text-blue-300': activeFilterColumn === 'fatherHusbandName'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'fatherHusbandName'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('fatherHusbandName')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.fatherHusbandName" :key="option"
                                class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-fatherHusbandName-${option}`"
                                  :checked="columnFilters.fatherHusbandName.includes(option)"
                                  @change="applyFilter('fatherHusbandName', option)" class="mr-2">
                                <label :for="`filter-fatherHusbandName-${option}`"
                                  class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Phone Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('phoneNo')" class="flex items-center focus:outline-none">
                            <span>Phone</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'phoneNo' && sortDirection === 'asc'"
                                class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'phoneNo' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('phoneNo')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.phoneNo.length > 0,
                              'text-blue-300': activeFilterColumn === 'phoneNo'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'phoneNo'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('phoneNo')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.phoneNo" :key="option" class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-phoneNo-${option}`"
                                  :checked="columnFilters.phoneNo.includes(option)"
                                  @change="applyFilter('phoneNo', option)" class="mr-2">
                                <label :for="`filter-phoneNo-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Date of Joining Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('dateOfJoining')" class="flex items-center focus:outline-none">
                            <span>Date of Joining</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'dateOfJoining' && sortDirection === 'asc'"
                                class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'dateOfJoining' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('dateOfJoining')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.dateOfJoining.length > 0,
                              'text-blue-300': activeFilterColumn === 'dateOfJoining'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'dateOfJoining'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('dateOfJoining')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.dateOfJoining" :key="option"
                                class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-dateOfJoining-${option}`"
                                  :checked="columnFilters.dateOfJoining.includes(option)"
                                  @change="applyFilter('dateOfJoining', option)" class="mr-2">
                                <label :for="`filter-dateOfJoining-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Status Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('status')" class="flex items-center focus:outline-none">
                            <span>Status</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'status' && sortDirection === 'asc'" class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'status' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('status')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.status.length > 0,
                              'text-blue-300': activeFilterColumn === 'status'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'status'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('status')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.status" :key="option" class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-status-${option}`"
                                  :checked="columnFilters.status.includes(option)"
                                  @change="applyFilter('status', option)" class="mr-2">
                                <label :for="`filter-status-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Category Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('category')" class="flex items-center focus:outline-none">
                            <span>Category</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'category' && sortDirection === 'asc'"
                                class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'category' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('category')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.category.length > 0,
                              'text-blue-300': activeFilterColumn === 'category'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'category'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('category')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.category" :key="option"
                                class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-category-${option}`"
                                  :checked="columnFilters.category.includes(option)"
                                  @change="applyFilter('category', option)" class="mr-2">
                                <label :for="`filter-category-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Project Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('project')" class="flex items-center focus:outline-none">
                            <span>Project</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'project' && sortDirection === 'asc'"
                                class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'project' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('project')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.project.length > 0,
                              'text-blue-300': activeFilterColumn === 'project'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'project'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('project')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.project" :key="option" class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-project-${option}`"
                                  :checked="columnFilters.project.includes(option)"
                                  @change="applyFilter('project', option)" class="mr-2">
                                <label :for="`filter-project-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Site Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                        <div class="flex items-center relative">
                          <button @click="toggleSort('site')" class="flex items-center focus:outline-none">
                            <span>Site</span>
                            <span class="ml-1">
                              <ChevronUpIcon v-if="sortField === 'site' && sortDirection === 'asc'" class="h-4 w-4" />
                              <ChevronDownIcon v-else-if="sortField === 'site' && sortDirection === 'desc'"
                                class="h-4 w-4" />
                              <span v-else class="h-4 w-4"></span>
                            </span>
                          </button>
                          <button @click.stop="toggleFilterDropdown('site')" class="ml-2 focus:outline-none">
                            <FunnelIcon class="h-4 w-4" :class="{
                              'text-yellow-300': columnFilters.site.length > 0,
                              'text-blue-300': activeFilterColumn === 'site'
                            }" />
                          </button>

                          <!-- Filter Dropdown -->
                          <div v-if="activeFilterColumn === 'site'"
                            class="absolute mt-1 bg-white rounded-md shadow-lg z-50 top-8 left-0 w-48 max-h-60 overflow-y-auto filter-dropdown-container border border-gray-300"
                            @click.stop>
                            <div class="p-2 border-b flex justify-between items-center">
                              <button @click="clearColumnFilter('site')"
                                class="text-xs text-blue-600 hover:text-blue-800">
                                Clear Column Filter
                              </button>
                              <button @click="resetAllFilters()"
                                class="text-xs bg-red-100 text-red-700 hover:bg-red-200 px-2 py-1 rounded">
                                Reset All
                              </button>
                            </div>
                            <div class="p-2 max-h-40 overflow-y-auto">
                              <div v-for="option in filterOptions.site" :key="option" class="flex items-center mb-1">
                                <input type="checkbox" :id="`filter-site-${option}`"
                                  :checked="columnFilters.site.includes(option)" @change="applyFilter('site', option)"
                                  class="mr-2">
                                <label :for="`filter-site-${option}`" class="text-xs text-gray-700 truncate">
                                  {{ option }}
                                </label>
                              </div>
                            </div>
                          </div>
                        </div>
                      </th>

                      <!-- Actions Column -->
                      <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="(employee, index) in filteredEmployees" :key="employee._id" class="hover:bg-green-200">
                      <td class="px-3 py-4 whitespace-nowrap text-center font-medium">{{ index + 1 }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ employee.employeeName }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ employee.fatherHusbandName }}</td>
                      <td class="px-6 py-4 whitespace-nowrap"
                        :class="{ 'text-red-600 font-semibold': !isValidPhoneNumber(employee.phoneNo) }">{{
                          employee.phoneNo }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ formatDate(employee.dateOfJoining) }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ employee.status || 'Active' }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ employee.category }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ employee.project || '-' }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">{{ employee.site || '-' }}</td>
                      <td class="px-6 py-4 whitespace-nowrap">
                        <button @click="editEmployee(employee)"
                          class="text-indigo-600 hover:text-indigo-900 mr-3 inline-flex items-center">
                          <PencilIcon class="h-4 w-4 mr-1" />
                          Edit
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>

            <!-- Mobile View (visible only on small screens) -->
            <div class="md:hidden">
              <div v-for="(employee, index) in filteredEmployees" :key="employee._id"
                class="bg-white border-b border-gray-200 p-4 hover:bg-green-50">
                <div class="flex justify-between items-center mb-2">
                  <h3 class="text-lg font-medium text-gray-900">
                    <span class="inline-block bg-teal-100 text-teal-800 text-xs font-semibold mr-2 px-2 py-1 rounded">
                      #{{ index + 1 }}
                    </span>
                    {{ employee.employeeName }}
                  </h3>
                  <button @click="editEmployee(employee)"
                    class="text-indigo-600 hover:text-indigo-900 inline-flex items-center">
                    <PencilIcon class="h-4 w-4 mr-1" />
                    Edit
                  </button>
                </div>
                <div class="grid grid-cols-2 gap-2 text-sm">
                  <div>
                    <span class="text-gray-500">Father/Husband:</span>
                    <span class="ml-1">{{ employee.fatherHusbandName }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Phone:</span>
                    <span class="ml-1"
                      :class="{ 'text-red-600 font-semibold': !isValidPhoneNumber(employee.phoneNo) }">{{
                        employee.phoneNo }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Joined:</span>
                    <span class="ml-1">{{ formatDate(employee.dateOfJoining) }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Status:</span>
                    <span class="ml-1">{{ employee.status || 'Active' }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Category:</span>
                    <span class="ml-1">{{ employee.category }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Project:</span>
                    <span class="ml-1">{{ employee.project || '-' }}</span>
                  </div>
                  <div>
                    <span class="text-gray-500">Site:</span>
                    <span class="ml-1">{{ employee.site || '-' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Modal -->
    <div v-if="showEditModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
      <div class="bg-white rounded-lg w-full lg:w-4/5 max-w-2xl lg:max-w-none my-4">
        <!-- Modal Header -->
        <div
          class="bg-gradient-to-r from-teal-500 to-indigo-600 p-4 rounded-t-lg flex justify-between items-center sticky top-0">
          <h2 class="text-xl font-bold text-white">Edit Employee</h2>
          <button @click="showEditModal = false" class="text-white hover:text-red-200 transition-colors">
            <XMarkIcon class="h-6 w-6" />
          </button>
        </div>

        <!-- Modal Body -->
        <div class="p-4 md:p-6 overflow-y-auto max-h-[70vh]">
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium text-gray-700">Employee Name</label>
              <input v-model="editingEmployee.employeeName"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Father/Husband Name</label>
              <input v-model="editingEmployee.fatherHusbandName"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Date of Birth</label>
              <input type="date" :value="formatDateForInput(editingEmployee.dateOfBirth)"
                @input="e => editingEmployee.dateOfBirth = (e.target as HTMLInputElement).value"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Date of Joining</label>
              <input type="date" :value="formatDateForInput(editingEmployee.dateOfJoining)"
                @input="e => editingEmployee.dateOfJoining = (e.target as HTMLInputElement).value"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Aadhar</label>
              <input v-model="editingEmployee.aadhar"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">PAN</label>
              <input v-model="editingEmployee.pan"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Address</label>
              <input v-model="editingEmployee.address"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Phone Number</label>
              <input v-model="editingEmployee.phoneNo"
                class="mt-1 block w-full rounded-md shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus"
                :class="{
                  'border-red-500 text-red-600': !isValidPhoneNumber(editingEmployee.phoneNo) && editingEmployee.phoneNo,
                  'border-gray-300': isValidPhoneNumber(editingEmployee.phoneNo) || !editingEmployee.phoneNo
                }">
              <div v-if="!isValidPhoneNumber(editingEmployee.phoneNo) && editingEmployee.phoneNo"
                class="text-red-500 text-xs mt-1">
                ⚠️ Invalid phone number format
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Bank</label>
              <input v-model="editingEmployee.bank"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Branch</label>
              <input v-model="editingEmployee.branch"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Account Number</label>
              <input v-model="editingEmployee.accountNo"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">IFSC</label>
              <input v-model="editingEmployee.ifsc"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">UAN</label>
              <input v-model="editingEmployee.uan"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">ESIC Number</label>
              <input v-model="editingEmployee.esicNo"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">S Kalyan Number</label>
              <input v-model="editingEmployee.sKalyanNo"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Per Day Wage</label>
              <input type="number" v-model="editingEmployee.pDayWage"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Project</label>
              <input v-model="editingEmployee.project"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Site</label>
              <input v-model="editingEmployee.site"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Category</label>
              <select v-model="editingEmployee.category"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
                <option value="HELPER">HELPER</option>
                <option value="TECHNICIAN">TECHNICIAN</option>
                <option value="ELECTRICIAN">ELECTRICIAN</option>
                <option value="SEMI-SKILLED">SEMI-SKILLED</option>
                <option value="HIGHLY-SKILLED">HIGHLY-SKILLED</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Status</label>
              <select v-model="editingEmployee.status"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="on Leave">On Leave</option>
                <option value="terminated">Terminated</option>
                <option value="left">Left Service</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Date of Exit</label>
              <input type="date" :value="formatDateForInput(editingEmployee.dateOfExit)"
                @input="e => editingEmployee.dateOfExit = (e.target as HTMLInputElement).value"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700">Exit Remarks</label>
              <input v-model="editingEmployee.doeRem"
                class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 blink-focus">
            </div>
          </div>
        </div>

        <!-- Wage History Section -->
        <transition name="slide-fade">
          <div v-if="showWageHistory" class="p-4 md:p-6 bg-gray-50 border-t border-gray-200">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Wage History</h3>
            <EmployeeWageHistory :wage-history="wageHistory" :loading="loadingWageHistory" />
          </div>
        </transition>

        <!-- Modal Footer -->
        <div class="bg-gradient-to-r from-indigo-600 to-teal-500 p-4 rounded-b-lg flex justify-end space-x-3">
          <div class="relative inline-block letter-format-dropdown">
            <button @click="showLetterFormatOptions = !showLetterFormatOptions"
              class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-yellow-400 to-orange-500 text-white font-bold shadow-md hover:from-yellow-500 hover:to-orange-600 focus:outline-none transition duration-300"
              :disabled="isGeneratingDocument" data-dropdown-toggle="letterFormatDropdown">
              <div v-if="isGeneratingDocument"
                class="animate-spin h-5 w-5 mr-2 border-2 border-white border-t-transparent rounded-full"></div>
              <DocumentTextIcon v-else class="h-5 w-5 mr-2" />
              {{ isGeneratingDocument ? 'Generating Document...' : 'Generate Appointment Letter' }}
            </button>
            <!-- Dropdown menu -->
            <div v-if="showLetterFormatOptions"
              class="absolute z-50 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none letter-format-dropdown">
              <div class="py-1">
                <button @click="generateAppointmentLetter(editingEmployee, 'pdf')"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  PDF Format
                </button>
                <button @click="generateAppointmentLetter(editingEmployee, 'docx')"
                  class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                  DOCX Format
                </button>
              </div>
            </div>
          </div>

          <!-- Wage History Button -->
          <button @click="toggleWageHistory"
            class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-purple-400 to-purple-600 text-white font-bold shadow-md hover:from-purple-500 hover:to-purple-700 focus:outline-none transition duration-300">
            <ChartBarIcon class="h-5 w-5 mr-2" />
            {{ showWageHistory ? 'Hide Wage History' : 'Show Wage History' }}
          </button>

          <button @click="showEditModal = false"
            class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-red-400 to-red-600 text-white font-bold shadow-md hover:from-red-500 hover:to-red-700 focus:outline-none transition duration-300">
            <XMarkIcon class="h-5 w-5 mr-2" />
            Cancel
          </button>

          <!-- Save Button -->
          <button @click="updateEmployee"
            class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-teal-400 to-blue-500 text-white font-bold shadow-lg hover:from-teal-500 hover:to-blue-600 focus:outline-none transform hover:scale-105 transition duration-300">
            <CheckIcon class="h-5 w-5 mr-2" />
            Save
          </button>
        </div>
      </div>
    </div>

    <!-- Add Employee Modal -->
    <div v-if="showAddModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50 overflow-y-auto">
      <div class="bg-white rounded-lg w-full lg:w-4/5 max-w-2xl lg:max-w-none my-4">
        <!-- Modal Header -->
        <div
          class="bg-gradient-to-r from-teal-500 to-indigo-600 p-4 rounded-t-lg flex justify-between items-center sticky top-0">
          <h2 class="text-xl font-bold text-white">Add New Employee</h2>
          <button @click="showAddModal = false" class="text-white hover:text-red-200 transition-colors">
            <XMarkIcon class="h-6 w-6" />
          </button>
        </div>

        <!-- Modal Body -->
        <div class="p-4 md:p-6 overflow-y-auto max-h-[70vh]">
          <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Employee Name <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.employeeName" class="mt-1 block w-full blink-focus"
                placeholder="Enter employee name">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Father/Husband Name <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.fatherHusbandName" class="mt-1 block w-full blink-focus"
                placeholder="Enter father/husband name">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Date of Birth <span class="text-red-500">*</span>
              </label>
              <input type="date" v-model="newEmployee.dateOfBirth" class="mt-1 block w-full blink-focus">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Date of Joining <span class="text-red-500">*</span>
              </label>
              <input type="date" v-model="newEmployee.dateOfJoining" class="mt-1 block w-full blink-focus">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Aadhar <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.aadhar" class="mt-1 block w-full blink-focus"
                placeholder="Enter Aadhar number">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">PAN</label>
              <input v-model="newEmployee.pan" class="mt-1 block w-full blink-focus" placeholder="Enter PAN number">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Address <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.address" class="mt-1 block w-full blink-focus" placeholder="Enter address">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Phone Number <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.phoneNo" class="mt-1 block w-full blink-focus"
                placeholder="Enter phone number" :class="{
                  'border-red-500 text-red-600': !isValidPhoneNumber(newEmployee.phoneNo) && newEmployee.phoneNo,
                  'border-green-500': isValidPhoneNumber(newEmployee.phoneNo) && newEmployee.phoneNo
                }">
              <div v-if="!isValidPhoneNumber(newEmployee.phoneNo) && newEmployee.phoneNo"
                class="text-red-500 text-xs mt-1">
                ⚠️ Invalid phone number format
              </div>
              <div v-if="isValidPhoneNumber(newEmployee.phoneNo) && newEmployee.phoneNo"
                class="text-green-500 text-xs mt-1">
                ✓ Valid phone number
              </div>
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                IFSC Code <span class="text-red-500">*</span>
              </label>
              <div class="relative">
                <input v-model="newEmployee.ifsc" @input="handleIfscInput" @blur="validateIfsc"
                  class="mt-1 block w-full blink-focus" placeholder="Enter IFSC code (e.g., SBIN0000001)"
                  :class="{ 'border-red-500': ifscError, 'border-green-500': ifscValid }">
                <div v-if="fetchingBankDetails" class="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500"></div>
                </div>
              </div>
              <div v-if="ifscError" class="text-red-500 text-xs mt-1">{{ ifscError }}</div>
              <div v-if="ifscValid && !fetchingBankDetails" class="text-green-500 text-xs mt-1">✓ Valid IFSC - Bank
                details auto-filled</div>
            </div>

            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Bank Name <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.bank" class="mt-1 block w-full blink-focus"
                placeholder="Enter bank name or use IFSC auto-fill" list="bankOptions">
              <datalist id="bankOptions">
                <option v-for="bank in uniqueBanks" :key="bank" :value="bank"></option>
              </datalist>
            </div>

            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Branch Name</label>
              <input v-model="newEmployee.branch" class="mt-1 block w-full blink-focus"
                placeholder="Enter branch name or use IFSC auto-fill">
            </div>

            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">
                Account Number <span class="text-red-500">*</span>
              </label>
              <input v-model="newEmployee.accountNo" class="mt-1 block w-full blink-focus"
                placeholder="Enter account number">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">UAN</label>
              <input v-model="newEmployee.uan" class="mt-1 block w-full blink-focus" placeholder="Enter UAN number">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">ESIC Number</label>
              <input v-model="newEmployee.esicNo" class="mt-1 block w-full blink-focus" placeholder="Enter ESIC number">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">S Kalyan Number</label>
              <input v-model="newEmployee.sKalyanNo" class="mt-1 block w-full blink-focus"
                placeholder="Enter S Kalyan number">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Per Day Wage</label>
              <input type="number" v-model="newEmployee.pDayWage" class="mt-1 block w-full blink-focus"
                placeholder="Enter daily wage amount">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Project</label>
              <input v-model="newEmployee.project" class="mt-1 block w-full blink-focus"
                placeholder="Enter project name" list="projectOptions">
              <datalist id="projectOptions">
                <option v-for="project in uniqueProjects" :key="project" :value="project"></option>
              </datalist>
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Site</label>
              <input v-model="newEmployee.site" class="mt-1 block w-full blink-focus" placeholder="Enter site location"
                list="siteOptions">
              <datalist id="siteOptions">
                <option v-for="site in uniqueSites" :key="site" :value="site"></option>
              </datalist>
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Category</label>
              <select v-model="newEmployee.category" class="mt-1 block w-full blink-focus">
                <option value="HELPER">HELPER</option>
                <option value="TECHNICIAN">TECHNICIAN</option>
                <option value="ELECTRICIAN">ELECTRICIAN</option>
                <option value="SEMI-SKILLED">SEMI-SKILLED</option>
                <option value="HIGHLY-SKILLED">HIGHLY-SKILLED</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Status</label>
              <select v-model="newEmployee.status" class="mt-1 block w-full blink-focus">
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
                <option value="on Leave">On Leave</option>
                <option value="terminated">Terminated</option>
                <option value="left">Left Service</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Date of Exit</label>
              <input type="date" v-model="newEmployee.dateOfExit" class="mt-1 block w-full blink-focus">
            </div>
            <div>
              <label class="block text-sm font-semibold text-gray-800 mb-2">Exit Remarks</label>
              <input v-model="newEmployee.doeRem" class="mt-1 block w-full blink-focus"
                placeholder="Enter exit remarks">
            </div>
          </div>
        </div>

        <!-- Footer Notification -->
        <div v-if="showFooterNotification"
          class="bg-gradient-to-r from-blue-500 to-purple-600 p-3 text-white text-center font-medium animate-pulse">
          {{ footerNotification }}
        </div>

        <!-- Modal Footer -->
        <div class="bg-gradient-to-r from-indigo-600 to-teal-500 p-4 rounded-b-lg flex justify-end space-x-3">
          <button @click="showAddModal = false"
            class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-red-400 to-red-600 text-white font-bold shadow-md hover:from-red-500 hover:to-red-700 focus:outline-none transition duration-300">
            <XMarkIcon class="h-5 w-5 mr-2" />
            Cancel
          </button>

          <!-- Save Button -->
          <button @click="addEmployee"
            class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-teal-400 to-blue-500 text-white font-bold shadow-lg hover:from-teal-500 hover:to-blue-600 focus:outline-none transform hover:scale-105 transition duration-300">
            <CheckIcon class="h-5 w-5 mr-2" />
            Save
          </button>
        </div>
      </div>
    </div>

    <!-- Export Modal -->
    <MasterRollExportModal :show="showExportModal" :employees="employees" :current-filters="columnFilters"
      :search-term="searchTerm" :unique-projects="uniqueProjects" :unique-sites="uniqueSites"
      @close="showExportModal = false" @export-complete="handleExportComplete" />

    <!-- Bulk Edit Modal -->
    <BulkEditModal :show="showBulkEditModal" :employees="filteredEmployees" :unique-categories="uniqueCategories"
      :unique-projects="uniqueProjects" :unique-sites="uniqueSites" @close="showBulkEditModal = false"
      @success="handleBulkEditSuccess" />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { usePageTitle } from '~/composables/ui/usePageTitle'
import { PencilIcon, UserPlusIcon, XMarkIcon, CheckIcon, MagnifyingGlassIcon, ChevronUpIcon, ChevronDownIcon, FunnelIcon, DocumentTextIcon, ChartBarIcon, DocumentArrowDownIcon, PencilSquareIcon } from '@heroicons/vue/24/outline' // Import icons
import ExcelIcon from '~/components/icons/ExcelIcon.vue'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'
import EmployeeWageHistory from '~/components/wages/EmployeeWageHistory.vue'
import MasterRollExportModal from '~/components/wages/MasterRollExportModal.vue'
import BulkEditModal from '~/components/wages/BulkEditModal.vue'

// Set page title
usePageTitle('Master Roll', 'Employee master roll management');

// Set page meta
if (typeof definePageMeta === 'function') {
  definePageMeta({
    requiresAuth: true
  });
}

// We're using the notify function for notifications instead of useToast
// Create a simple notification function
const notify = (message: string, type: 'success' | 'error' = 'success') => {
  alert(type === 'success' ? `✅ ${message}` : `❌ ${message}`);
}

// Define employee interface
interface Employee {
  _id: string;
  employeeName: string;
  fatherHusbandName: string;
  dateOfBirth: string;
  dateOfJoining: string;
  dateOfExit: string;
  doeRem: string;
  aadhar: string;
  phoneNo: string;
  address: string;
  bank: string;
  branch: string;
  accountNo: string;
  ifsc: string;
  category: string;
  project: string;
  site: string;
  sKalyanNo: string;
  pan: string;
  uan: string;
  esicNo: string;
  pDayWage: string | number;
  status: string;
}

const employees = ref<Employee[]>([])
const showEditModal = ref(false)
const editingEmployee = reactive({
  _id: '',
  employeeName: '',
  fatherHusbandName: '',
  dateOfBirth: '',
  dateOfJoining: '',
  dateOfExit: '',
  doeRem: '',
  aadhar: '',
  phoneNo: '',
  address: '',
  bank: '',
  branch: '',
  accountNo: '',
  ifsc: '',
  category: 'UNSKILLED',
  project: '',
  site: '',
  sKalyanNo: '',
  pan: '',
  uan: '',
  esicNo: '',
  pDayWage: '',
  status: 'Active'
})

// Add new state variables for adding new employee
const showAddModal = ref(false)
const showExportModal = ref(false)
const showBulkEditModal = ref(false)
const searchTerm = ref('')

// IFSC validation and bank fetching state
const fetchingBankDetails = ref(false)
const ifscError = ref('')
const ifscValid = ref(false)
const ifscTimeout = ref(null)

// Footer notification state
const footerNotification = ref('')
const showFooterNotification = ref(false)
const footerNotificationTimeout = ref(null)

// Sorting state
const sortField = ref('dateOfJoining') // Default sort by Date of Joining
const sortDirection = ref('desc') // Default sort direction

// Column filters
const columnFilters = ref({
  employeeName: [],
  fatherHusbandName: [],
  phoneNo: [],
  dateOfJoining: [],
  status: [],
  category: [],
  project: [],
  site: []
})

// Filter options for each column
const filterOptions = ref({
  employeeName: [],
  fatherHusbandName: [],
  phoneNo: [],
  dateOfJoining: [],
  status: [],
  category: [],
  project: [],
  site: []
})

// Active filter dropdown
const activeFilterColumn = ref('')

// Toggle sort for a column
const toggleSort = (field) => {
  if (sortField.value === field) {
    // Toggle direction if already sorting by this field
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc'
  } else {
    // Set new sort field and default to ascending
    sortField.value = field
    sortDirection.value = 'asc'
  }
}

// Toggle filter dropdown
const toggleFilterDropdown = (column) => {
  activeFilterColumn.value = activeFilterColumn.value === column ? '' : column
}

// Handler for closing filter dropdown when clicking outside
const handleClickOutside = (event) => {
  // Check if click is outside any filter dropdown
  if (activeFilterColumn.value && !event.target.closest('.filter-dropdown-container')) {
    activeFilterColumn.value = ''
  }

  // Check if click is outside the letter format options dropdown
  if (showLetterFormatOptions.value && !event.target.closest('.letter-format-dropdown') &&
    !event.target.closest('button[data-dropdown-toggle="letterFormatDropdown"]')) {
    showLetterFormatOptions.value = false
  }
}

// Apply filter for a column
const applyFilter = (column, value) => {
  // If value is already in filters, remove it, otherwise add it
  const index = columnFilters.value[column].indexOf(value)
  if (index > -1) {
    columnFilters.value[column].splice(index, 1)
  } else {
    columnFilters.value[column].push(value)
  }
}

// Clear all filters for a column
const clearColumnFilter = (column) => {
  columnFilters.value[column] = []
}

// Reset all filters
const resetAllFilters = () => {
  Object.keys(columnFilters.value).forEach(key => {
    columnFilters.value[key] = []
  })
  // Close the active filter dropdown
  activeFilterColumn.value = ''
}

// Computed property for filtered employees
const filteredEmployees = computed(() => {
  // First apply search filter
  let filtered = employees.value

  if (searchTerm.value) {
    const search = searchTerm.value.toLowerCase()
    filtered = filtered.filter(employee => {
      return (
        employee.employeeName?.toLowerCase().includes(search) ||
        employee.fatherHusbandName?.toLowerCase().includes(search) ||
        employee.phoneNo?.toLowerCase().includes(search) ||
        employee.category?.toLowerCase().includes(search) ||
        employee.status?.toLowerCase().includes(search) ||
        employee.project?.toLowerCase().includes(search) ||
        employee.site?.toLowerCase().includes(search)
      )
    })
  }

  // Then apply column filters
  Object.keys(columnFilters.value).forEach(column => {
    if (columnFilters.value[column].length > 0) {
      filtered = filtered.filter(employee => {
        // Special handling for date fields
        if (column === 'dateOfJoining') {
          const formattedDate = formatDate(employee[column])
          return columnFilters.value[column].includes(formattedDate)
        }
        return columnFilters.value[column].includes(employee[column])
      })
    }
  })

  // Finally sort the results
  return [...filtered].sort((a, b) => {
    // Special handling for date fields
    if (sortField.value === 'dateOfJoining') {
      const dateA = new Date(a[sortField.value] || 0)
      const dateB = new Date(b[sortField.value] || 0)

      return sortDirection.value === 'asc'
        ? dateA - dateB
        : dateB - dateA
    }

    // Regular string comparison for other fields
    const valueA = (a[sortField.value] || '').toString().toLowerCase()
    const valueB = (b[sortField.value] || '').toString().toLowerCase()

    if (sortDirection.value === 'asc') {
      return valueA.localeCompare(valueB)
    } else {
      return valueB.localeCompare(valueA)
    }
  })
})

// Computed properties for dropdown options
const uniqueBanks = computed(() => {
  const banks = employees.value.map(emp => emp.bank).filter(Boolean)
  return [...new Set(banks)].sort()
})

const uniqueCategories = computed(() => {
  const categories = employees.value.map(emp => emp.category).filter(Boolean)
  return [...new Set(categories)].sort()
})

const uniqueProjects = computed(() => {
  const projects = employees.value.map(emp => emp.project).filter(Boolean)
  return [...new Set(projects)].sort()
})

const uniqueSites = computed(() => {
  const sites = employees.value.map(emp => emp.site).filter(Boolean)
  return [...new Set(sites)].sort()
})

const newEmployee = ref({
  employeeName: '',
  fatherHusbandName: '',
  dateOfBirth: '',
  dateOfJoining: '',
  aadhar: '',
  pan: '',
  phoneNo: '',
  address: '',
  bank: '',
  branch: '',
  accountNo: '',
  ifsc: '',
  uan: '',
  esicNo: '',
  sKalyanNo: '',
  category: 'HELPER',
  pDayWage: 0,
  project: '',
  site: '',
  dateOfExit: '',
  doeRem: '',
  status: 'active'
})

// Fetch employees function
const fetchEmployees = async () => {
  try {
    // Use our new API helper to fetch employees
    const api = useApiWithAuth();
    const response = await api.get('/api/master-roll');

    employees.value = response.employees as Employee[]

    // Populate filter options
    updateFilterOptions()
  } catch (error) {
    console.error('Error in Master Roll page:', error)
    // Use window.alert for consistent error handling
    window.alert('❌ Failed to load employees. Please refresh the page.')
  }
};

// Setup on component mount
onMounted(async () => {
  // Fetch initial data
  fetchEmployees();

  // Add click outside event listener
  document.addEventListener('click', handleClickOutside)
})

// Clean up event listeners on unmount
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  // Clear IFSC timeout
  if (ifscTimeout.value) {
    clearTimeout(ifscTimeout.value)
  }
  // Clear footer notification timeout
  if (footerNotificationTimeout.value) {
    clearTimeout(footerNotificationTimeout.value)
  }
})

// Function to update filter options based on current data
const updateFilterOptions = () => {
  // Get unique values for each filterable column
  const uniqueValues = {
    employeeName: [...new Set(employees.value.map(e => e.employeeName || '').filter(Boolean))],
    fatherHusbandName: [...new Set(employees.value.map(e => e.fatherHusbandName || '').filter(Boolean))],
    phoneNo: [...new Set(employees.value.map(e => e.phoneNo || '').filter(Boolean))],
    dateOfJoining: [...new Set(employees.value.map(e => formatDate(e.dateOfJoining)).filter(Boolean))],
    status: [...new Set(employees.value.map(e => e.status || 'Active').filter(Boolean))],
    category: [...new Set(employees.value.map(e => e.category || '').filter(Boolean))],
    project: [...new Set(employees.value.map(e => e.project || '').filter(Boolean))],
    site: [...new Set(employees.value.map(e => e.site || '').filter(Boolean))]
  }

  // Sort the options alphabetically
  Object.keys(uniqueValues).forEach(key => {
    if (key === 'dateOfJoining') {
      // For dates, we want to sort chronologically
      uniqueValues[key].sort((a, b) => {
        const dateA = new Date(a.split('-').reverse().join('-'))
        const dateB = new Date(b.split('-').reverse().join('-'))
        return dateA - dateB
      })
    } else {
      uniqueValues[key].sort((a, b) => a.toString().localeCompare(b.toString()))
    }
  })

  // Update the filter options
  filterOptions.value = uniqueValues
}

// No middleware needed

const downloadTemplate = async () => {
  // Alert the user about the color scheme
  alert('Template color scheme:\n\n- Red headers: Required fields\n- Orange headers: Optional fields');

  try {
    const api = useApiWithAuth();
    // Use the fetchWithAuth for binary data download
    const response = await api.fetchWithAuth('/api/master-roll/template', {
      method: 'GET',
    });

    // Create a blob from the response
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);

    // Trigger file download
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'MasterRollTemplate.xlsx');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading template:', error);
    alert('Failed to download template. Please try again.');
  }
}

const handleFileUpload = async (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (!file) return

  try {
    // Create FormData and append the file
    const formData = new FormData()
    formData.append('file', file)

    // Show loading notification using direct alert
    window.alert('✅ Uploading file, please wait...')

    // Use our new API helper for uploading
    const api = useApiWithAuth();
    const response = await api.fetchWithAuth('/api/master-roll/upload', {
      method: 'POST',
      body: formData
    })

    try {
      // Refresh the employee list
      const updatedData = await api.get('/api/master-roll')
      employees.value = updatedData.employees as Employee[]

      // Update filter options
      updateFilterOptions()

      // Show success notification using direct alert
      window.alert('✅ Employees uploaded successfully')

      // Reset the file input
      if (event.target) {
        (event.target as HTMLInputElement).value = ''
      }
    } catch (refreshError) {
      console.error('Error refreshing employee list:', refreshError)
      window.alert('✅ Employees uploaded successfully, but there was an error refreshing the list. Please reload the page.')
    }
  } catch (error: any) {
    console.error('Error uploading employees:', error)

    // Show detailed error message
    let errorMessage = 'Failed to upload employees'

    // Extract the specific error message if available
    if (error.response && error.response.data && error.response.data.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    // Show error notification with direct alert
    window.alert('❌ ' + errorMessage)

    // Reset the file input safely
    if (event.target) {
      try {
        (event.target as HTMLInputElement).value = ''
      } catch (resetError) {
        console.error('Error resetting file input:', resetError)
      }
    }
  }
}

const editEmployee = (employee: any) => {
  Object.assign(editingEmployee, employee)
  showEditModal.value = true

  // Reset wage history state
  showWageHistory.value = false
  wageHistory.value = []
}

const updateEmployee = async () => {
  try {
    // Create the API instance
    const api = useApiWithAuth();

    // Make the request - CSRF token is automatically handled by useApiWithAuth
    await api.put(`/api/master-roll/${editingEmployee._id}`, editingEmployee)

    showEditModal.value = false

    // Refresh the employee list
    const response = await api.get('/api/master-roll')
    employees.value = response.employees as Employee[]

    // Update filter options
    updateFilterOptions()

    // Show success notification
    notify('Employee updated successfully', 'success')
  } catch (error1) {
    console.error('Error updating employee:', error1)
    // Show error notification
    notify('Failed to update employee', 'error')
  }
}

const deleteEmployee = async (id: string) => {
  if (!confirm('Are you sure you want to delete this employee?')) return

  try {
    const api = useApiWithAuth();
    await api.delete(`/api/master-roll/${id}`)

    // Refresh the employee list
    const response = await api.get('/api/master-roll')
    employees.value = response.employees as Employee[]

    // Update filter options
    updateFilterOptions()
  } catch (error) {
    console.error('Error deleting employee:', error)
  }
}

// Update the date formatting functions
const formatDate = (date: string) => {
  if (!date) return ''
  const dateObj = new Date(date)
  const day = dateObj.getDate().toString().padStart(2, '0')
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
  const year = dateObj.getFullYear()
  return `${day}-${month}-${year}`
}

const formatDateForInput = (date: string) => {
  if (!date) return ''
  return new Date(date).toISOString().split('T')[0]
}

// Phone number validation
const isValidPhoneNumber = (phoneNo: string) => {
  if (!phoneNo) return false

  // Remove any spaces, dashes, or other non-digit characters
  const cleanPhone = phoneNo.toString().replace(/\D/g, '')

  // Check for invalid patterns
  const invalidPatterns = [
    /^0+$/, // All zeros (0000000000)
    /^1+$/, // All ones (1111111111)
    /^2+$/, // All twos (2222222222)
    /^3+$/, // All threes (3333333333)
    /^4+$/, // All fours (4444444444)
    /^5+$/, // All fives (5555555555)
    /^6+$/, // All sixes (6666666666)
    /^7+$/, // All sevens (7777777777)
    /^8+$/, // All eights (8888888888)
    /^9+$/, // All nines (9999999999)
    /^123456789[0-9]$/, // Sequential numbers like 1234567890
    /^987654321[0-9]$/, // Reverse sequential numbers
  ]

  // Check if phone number matches any invalid pattern
  for (const pattern of invalidPatterns) {
    if (pattern.test(cleanPhone)) {
      return false
    }
  }

  // Check if it's a valid Indian mobile number (10 digits starting with 6-9)
  // or a valid landline number (10-11 digits)
  if (cleanPhone.length === 10) {
    // Mobile number should start with 6, 7, 8, or 9
    return /^[6-9]/.test(cleanPhone)
  } else if (cleanPhone.length === 11) {
    // Landline with STD code
    return /^[0-9]/.test(cleanPhone)
  }

  return false
}

const downloadMasterRoll = async () => {
  try {
    const api = useApiWithAuth();
    // Use the fetchWithAuth for binary data download
    const response = await api.fetchWithAuth('/api/master-roll/download', {
      method: 'GET',
      responseType: 'blob'
    });

    // Create a blob from the response
    const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    const url = window.URL.createObjectURL(blob);

    // Trigger file download
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', 'master_roll.xlsx');
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(url);
  } catch (error) {
    console.error('Error downloading master roll:', error)
  }
}

// notify function is defined at the top of the file

// Function to add a new employee
const addEmployee = async () => {
  try {
    const api = useApiWithAuth();
    const response = await api.post('/api/master-roll', newEmployee.value);

    if (response.success) {
      // Add the new employee to the list
      employees.value.push(response.data)

      // Update filter options
      updateFilterOptions()

      // Close the modal and reset the form
      showAddModal.value = false
      newEmployee.value = {
        employeeName: '',
        fatherHusbandName: '',
        dateOfBirth: '',
        dateOfJoining: '',
        aadhar: '',
        pan: '',
        phoneNo: '',
        address: '',
        bank: '',
        branch: '',
        accountNo: '',
        ifsc: '',
        uan: '',
        esicNo: '',
        sKalyanNo: '',
        category: 'UNSKILLED',
        pDayWage: 0,
        project: '',
        site: '',
        dateOfExit: '',
        doeRem: '',
        status: 'active'
      }

      // Show success notification
      notify('Employee added successfully', 'success');
    } else {
      notify(response.message || 'Failed to add employee', 'error');
    }
  } catch (error1: any) {
    notify(error1.message || 'An error occurred', 'error');
  }
}

// Wage history state
const showWageHistory = ref(false);
const wageHistory = ref([]);
const loadingWageHistory = ref(false);

// Function to toggle wage history visibility
const toggleWageHistory = async () => {
  showWageHistory.value = !showWageHistory.value;

  // If showing wage history and we haven't loaded it yet, fetch it
  if (showWageHistory.value && wageHistory.value.length === 0 && !loadingWageHistory.value) {
    await fetchWageHistory();
  }
};

// Function to fetch wage history
const fetchWageHistory = async () => {
  if (!editingEmployee._id) return;

  loadingWageHistory.value = true;
  try {
    const api = useApiWithAuth();
    const response = await api.get(`/api/wages/employee-history/${editingEmployee._id}`);

    if (response.success) {
      wageHistory.value = response.wageHistory;
    } else {
      notify('Failed to load wage history', 'error');
    }
  } catch (error) {
    console.error('Error fetching wage history:', error);
    notify('Failed to load wage history', 'error');
  } finally {
    loadingWageHistory.value = false;
  }
};

// Footer notification function
const showFooterMessage = (message, duration = 10000) => {
  // Clear any existing timeout
  if (footerNotificationTimeout.value) {
    clearTimeout(footerNotificationTimeout.value)
  }

  footerNotification.value = message
  showFooterNotification.value = true

  // Hide after specified duration
  footerNotificationTimeout.value = setTimeout(() => {
    showFooterNotification.value = false
    footerNotification.value = ''
  }, duration)
}

// IFSC validation and bank details fetching functions
const handleIfscInput = (event) => {
  const ifsc = event.target.value.toUpperCase()
  newEmployee.value.ifsc = ifsc

  // Reset states
  ifscError.value = ''
  ifscValid.value = false

  // Clear previous timeout
  if (ifscTimeout.value) {
    clearTimeout(ifscTimeout.value)
  }

  // Only validate if IFSC has 11 characters
  if (ifsc.length === 11) {
    // Debounce the API call
    ifscTimeout.value = setTimeout(() => {
      fetchBankDetailsByIfsc(ifsc)
    }, 500)
  } else if (ifsc.length > 11) {
    ifscError.value = 'IFSC code should be exactly 11 characters'
  }
}

const validateIfsc = () => {
  const ifsc = newEmployee.value.ifsc
  if (ifsc && ifsc.length !== 11) {
    ifscError.value = 'IFSC code should be exactly 11 characters'
  }
}

const fetchBankDetailsByIfsc = async (ifsc) => {
  if (!ifsc || ifsc.length !== 11) return

  fetchingBankDetails.value = true
  ifscError.value = ''

  try {
    const response = await fetch(`https://ifsc.razorpay.com/${ifsc}`)

    if (response.ok) {
      const bankData = await response.json()

      // Auto-fill bank and branch details
      newEmployee.value.bank = bankData.BANK || ''
      newEmployee.value.branch = bankData.BRANCH || ''

      ifscValid.value = true
      ifscError.value = ''

      // Show footer notification instead of alert
      showFooterMessage(`✅ Bank details auto-filled: ${bankData.BANK} - ${bankData.BRANCH}`)
    } else {
      throw new Error('Invalid IFSC code')
    }
  } catch (error) {
    ifscError.value = 'Invalid IFSC code or unable to fetch bank details'
    ifscValid.value = false
    showFooterMessage(`❌ Error: ${error.message}`, 5000)
    console.error('Error fetching bank details:', error)
  } finally {
    fetchingBankDetails.value = false
  }
}

// Loading state for document generation
const isGeneratingDocument = ref(false);
const showLetterFormatOptions = ref(false);

// Handle export completion
const handleExportComplete = (result: { success: boolean; error?: string }) => {
  if (result.success) {
    notify('Data exported successfully', 'success')
  } else {
    notify(result.error || 'Export failed', 'error')
  }
}

// Handle bulk edit completion
const handleBulkEditSuccess = async (result: { updatedCount: number; fieldsUpdated: string[] }) => {
  try {
    // Refresh the employee list to show updated data
    await fetchEmployees()

    // Show success notification
    notify(`Successfully updated ${result.updatedCount} employees`, 'success')

    // Close the modal
    showBulkEditModal.value = false

  } catch (error) {
    console.error('Error refreshing data after bulk edit:', error)
    notify('Bulk edit completed but failed to refresh data. Please reload the page.', 'warning')
  }
}

// Function to generate appointment letter
const generateAppointmentLetter = async (employee: any, format = 'pdf') => {
  // Set loading state
  isGeneratingDocument.value = true;
  // Hide the format options dropdown
  showLetterFormatOptions.value = false;

  try {
    const api = useApiWithAuth();

    // Generate the URL based on the format
    const url = format === 'docx'
      ? `/api/master-roll/generate-letter-docx?employeeId=${employee._id}`
      : `/api/master-roll/generate-letter?employeeId=${employee._id}`;

    // Create a direct download link (don't open in new tab)
    const link = document.createElement('a');
    link.href = url;
    const fileExtension = format === 'docx' ? 'docx' : 'pdf';
    link.setAttribute('download', `Appointment_Letter_${employee.employeeName.replace(/\s+/g, '_')}.${fileExtension}`);
    document.body.appendChild(link);
    link.click();

    // Clean up
    setTimeout(() => {
      link.remove();
    }, 100);

    notify(`Appointment letter generated successfully in ${format.toUpperCase()} format`);
  } catch (error: any) {
    console.error('Error generating appointment letter:', error);
    notify('Failed to generate appointment letter: ' + (error.message || 'Unknown error'), 'error');
  } finally {
    // Reset loading state
    isGeneratingDocument.value = false;
  }
}
</script>

<style scoped>
.modal-enter-active,
.modal-leave-active {
  transition: opacity 0.3s ease;
}

.modal-enter-from,
.modal-leave-to {
  opacity: 0;
}

.blink-focus {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid #e5e7eb;
  background: linear-gradient(145deg, #ffffff, #f8fafc);
  border-radius: 12px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(8px);
}

.blink-focus:hover {
  border-color: #d1d5db;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  transform: translateY(-1px);
}

.blink-focus:focus {
  outline: none;
  animation: borderBlink 1.5s infinite;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

/* Slide fade transition for wage history */
.slide-fade-enter-active,
.slide-fade-leave-active {
  transition: all 0.3s ease;
  max-height: 1000px;
  overflow: hidden;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  max-height: 0;
  overflow: hidden;
}

@keyframes borderBlink {
  0% {
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.3);
  }

  25% {
    border-color: #8b5cf6;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(139, 92, 246, 0.3);
  }

  50% {
    border-color: #10b981;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(16, 185, 129, 0.3);
  }

  75% {
    border-color: #f59e0b;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(245, 158, 11, 0.3);
  }

  100% {
    border-color: #3b82f6;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1), 0 0 0 3px rgba(59, 130, 246, 0.3);
  }
}

/* IFSC validation styles */
.border-red-500 {
  border-color: #ef4444 !important;
}

.border-green-500 {
  border-color: #10b981 !important;
}

/* Loading spinner */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.animate-spin {
  animation: spin 1s linear infinite;
}
</style>
