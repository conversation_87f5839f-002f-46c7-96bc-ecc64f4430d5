// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2024-11-01',
  devtools: { enabled: true },
  modules: ['@nuxtjs/tailwindcss', '@nuxt/icon'],
  css: [
    '~/assets/css/inventory-datalist.css',
    '~/assets/css/main.css',
    '~/assets/css/admin.css'
  ],
  app: {
    head: {
      title: 'BusinessPro Suite',
      titleTemplate: '%s | BusinessPro Suite',
      meta: [
        { charset: 'utf-8' },
        { name: 'viewport', content: 'width=device-width, initial-scale=1' },
        { key: 'description', name: 'description', content: 'BusinessPro Suite - Comprehensive Business Management Solution' }
      ]
    }
  },
  plugins: [
    '~/plugins/api-error-handler.js',
    '~/plugins/auth-interceptor.js',
    '~/plugins/chart.client.js',
    '~/plugins/expenses-components.js',
    '~/plugins/csrf.client.js'
  ],
  runtimeConfig: {
    emailHost: process.env.EMAIL_HOST,
    emailPort: process.env.EMAIL_PORT,
    emailUser: process.env.EMAIL_USER,
    emailPass: process.env.EMAIL_PASS,
    emailFrom: process.env.EMAIL_FROM,
    MONGO_URI: process.env.MONGO_URI,
    jwtSecret: process.env.JWT_SECRET,
    // Firebase Admin config
    projectId: process.env.fb_projectId,
    clientEmail: process.env.fb_email,
    privateKey: process.env.fb_pvt_key?.replace(/\\n/g, '\n'),
    // Firebase API key for client auth
    firebaseApiKey: process.env.FIREBASE_API_KEY,
    // Google AI API key
    googleAiApiKey: process.env.GOOGLE_AI_API_KEY,
    // Google Drive config
    googleDriveEmail: process.env.gs_email,
    googleDriveKey: process.env.gs_key?.replace(/\\n/g, '\n'),
***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    // Upstash Redis config
    UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,
    UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,

    // Public runtime config (accessible from client)
    public: {
      // Environment information
      nodeEnv: process.env.NODE_ENV || 'development',

      // CSRF configuration
      csrf: {
        cookieName: 'csrf_token_client',
        headerName: 'X-CSRF-Token',
        paramName: '_csrf',
        // Cookie expiration in seconds (4 hours)
        maxAge: 4 * 60 * 60
      }
    }
  },

  serverDir: './server',

  // Register global middleware
  router: {
    middleware: ['sub-contractor-access']
  },

  // Define route rules for additional security
  routeRules: {
    '/admin/**': { middleware: ['admin-only'] },
    '/manager/**': { middleware: ['manager-only'] }
  },

  // Initialize database connections on server startup
  nitro: {
    plugins: ['~/server/utils/dbConnect', '~/server/plugins/firebase', '~/server/plugins/pdfkit'],
    // Register server middleware
    serverMiddleware: [
      // Logger middleware should be first to log all requests
      { path: '/', handler: './server/middleware/logger' },
      // CSRF middleware should come before auth middleware
      { path: '/', handler: './server/middleware/csrf' },
      // Auth middleware for all protected routes
      { path: '/', handler: './server/middleware/auth' },
      // Manager-specific auth middleware for manager routes
      { path: '/', handler: './server/middleware/manager-auth' },
      // Admin-specific auth middleware for additional protection (must come after manager-auth)
      { path: '/', handler: './server/middleware/admin-auth' },
      // API logs auto-backup middleware (Excel only, 5-min interval, admin-only)
      { path: '/', handler: './server/middleware/api-logs-check' }
    ]
  },

  // Configure external dependencies
  vite: {
    optimizeDeps: {
      exclude: ['pdfkit']
    }
  }
})
