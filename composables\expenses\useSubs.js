import { ref, computed } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';

/**
 * Composable for managing subs
 *
 * Provides functions and state for working with subs
 */
export function useSubs() {
  const api = useApiWithAuth();

  // State
  const subs = ref([]);
  const subsModels = ref([]);
  const currentSub = ref(null);
  const currentSubsModel = ref(null);
  const isLoading = ref(false);
  const error = ref(null);

  // Computed properties
  const activeSubs = computed(() => {
    return subsModels.value.filter(sub => sub.isActive);
  });

  const inactiveSubs = computed(() => {
    return subsModels.value.filter(sub => !sub.isActive);
  });

  const totalSubsBalance = computed(() => {
    return subsModels.value.reduce((sum, sub) => sum + sub.balance, 0);
  });

  const getUniqueSubNames = computed(() => {
    return subsModels.value.map(sub => sub.name).sort();
  });

  // Get unique paidTo values from subs transactions
  const getUniquePaidToValues = computed(() => {
    // Extract all paidTo values from all transactions in all subs
    const paidToValues = new Set();

    // First add all sub names as they are common recipients
    subsModels.value.forEach(sub => {
      if (sub.name) paidToValues.add(sub.name);
    });

    // Then add all paidTo values from transactions
    subs.value.forEach(sub => {
      if (sub.paidTo) paidToValues.add(sub.paidTo);
    });

    return Array.from(paidToValues).sort();
  });

  // Get unique project values from subs transactions
  const getUniqueSubProjects = computed(() => {
    // Extract all project values from all transactions in all subs
    const projectSet = new Set();


    // Add projects from subs transactions
    subsModels.value.forEach(sub => {
      if (sub.transactions && Array.isArray(sub.transactions)) {
        sub.transactions.forEach(tx => {
          if (tx.project) projectSet.add(tx.project);
        });
      }
    });

    // Also add projects from individual subs
    subs.value.forEach(sub => {
      if (sub.project) projectSet.add(sub.project);
    });

    return Array.from(projectSet).sort();
  });

  // Methods
  const fetchSubs = async (filters = {}) => {
    try {
      isLoading.value = true;
      error.value = null;


      // Build query parameters
      const queryParams = new URLSearchParams();

      if (filters.startDate) {
        queryParams.append('startDate', new Date(filters.startDate).toISOString());
      }

      if (filters.endDate) {
        queryParams.append('endDate', new Date(filters.endDate).toISOString());
      }

      if (filters.paidTo) {
        queryParams.append('paidTo', filters.paidTo);
      }

      if (filters.category) {
        queryParams.append('category', filters.category);
      }

      if (filters.project) {
        queryParams.append('project', filters.project);
      }

      // Make API request
      const url = `/api/subs${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await api.get(url);

      subs.value = response;

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to fetch subs';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSubById = async (id) => {
    try {
      isLoading.value = true;
      error.value = null;


      const response = await api.get(`/api/subs/${id}`);
      currentSub.value = response;

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to fetch sub';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createSub = async (subData) => {
    try {
      isLoading.value = true;
      error.value = null;


      const response = await api.post('/api/subs', subData);

      // Refresh subs list
      await fetchSubs();

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to create sub';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateSub = async (id, subData) => {
    try {
      isLoading.value = true;
      error.value = null;


      const response = await api.put(`/api/subs/${id}`, subData);

      // Refresh subs list
      await fetchSubs();

      // Update current sub if it's the one being edited
      if (currentSub.value && currentSub.value.id === id) {
        currentSub.value = response;
      }

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to update sub';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteSub = async (id) => {
    try {
      isLoading.value = true;
      error.value = null;


      const response = await api.delete(`/api/subs/${id}`);

      // Refresh subs list
      await fetchSubs();

      // Clear current sub if it's the one being deleted
      if (currentSub.value && currentSub.value.id === id) {
        currentSub.value = null;
      }

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to delete sub';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // Subs Models methods
  const fetchSubsModels = async (filters = {}) => {
    try {
      isLoading.value = true;
      error.value = null;


      // Build query parameters
      const queryParams = new URLSearchParams();

      // Support for backward compatibility
      if (typeof filters === 'boolean') {
        queryParams.append('isActive', filters);
      } else {
        // Handle filters as an object
        if (filters.isActive !== undefined) {
          queryParams.append('isActive', filters.isActive);
        }

        if (filters.firmId) {
          queryParams.append('firmId', filters.firmId);
        }
      }

      // Make API request
      const url = `/api/subs/models${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await api.get(url);

      subsModels.value = response;

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to fetch subs models';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const fetchSubsModelById = async (id) => {
    try {
      isLoading.value = true;
      error.value = null;

      console.log('=== FETCHING SUBS MODEL BY ID ===');
      console.log('Subs model ID:', id);

      // Use the subs models API that reads from separate subs collection
      const response = await api.get(`/api/subs/models/${id}`);

      console.log('Subs model response:', response);
      console.log('Transactions found:', response?.transactions?.length || 0);

      // Ensure transactions array exists
      if (response && !response.transactions) {
        console.log('No transactions array found, creating empty array');
        response.transactions = [];
      }

      // Log transaction details if they exist
      if (response && response.transactions && response.transactions.length > 0) {
        console.log('Transactions loaded:', response.transactions);
      } else {
        console.log('No transactions found for this subs model');
      }

      currentSubsModel.value = response;
      return response;
    } catch (err) {
      console.error('Error fetching subs model by ID:', err);
      error.value = err.message || 'Failed to fetch subs model';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const createSubsModel = async (subsModelData) => {
    try {
      isLoading.value = true;
      error.value = null;


      const response = await api.post('/api/subs/models', subsModelData);

      // Refresh subs models list
      await fetchSubsModels();

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to create subs model';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const updateSubsModel = async (id, subsModelData) => {
    try {
      isLoading.value = true;
      error.value = null;


      const response = await api.put(`/api/subs/models/${id}`, subsModelData);

      // Refresh subs models list
      await fetchSubsModels();

      // Update current subs model if it's the one being edited
      if (currentSubsModel.value && currentSubsModel.value.id === id) {
        currentSubsModel.value = response;
      }

      return response;
    } catch (err) {

      error.value = err.message || 'Failed to update subs model';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  const deleteSubsModel = async (id) => {
    try {
      isLoading.value = true;
      error.value = null;

      console.log('Deleting subs model:', id);

      const response = await api.delete(`/api/subs/models/${id}`);

      // Refresh subs models list
      await fetchSubsModels();

      // Clear current subs model if it's the one being deleted
      if (currentSubsModel.value && currentSubsModel.value.id === id) {
        currentSubsModel.value = null;
      }

      return response;
    } catch (err) {
      console.error('Error deleting subs model:', err);
      error.value = err.message || 'Failed to delete subs model';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // NEW METHOD: Delete a specific transaction from subs model
  const deleteSubsTransaction = async (transactionId) => {
    try {
      isLoading.value = true;
      error.value = null;

      console.log('Deleting subs transaction:', transactionId);

      const response = await api.delete(`/api/expenses/subs/transactions/${transactionId}`);

      // Refresh subs models list to get updated balances
      await fetchSubsModels();

      return response;
    } catch (err) {
      console.error('Error deleting subs transaction:', err);
      error.value = err.message || 'Failed to delete subs transaction';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // NEW METHOD: Update a specific transaction in subs model
  const updateSubsTransaction = async (transactionId, transactionData) => {
    try {
      isLoading.value = true;
      error.value = null;

      console.log('Updating subs transaction:', transactionId, transactionData);

      const response = await api.put(`/api/expenses/subs/transactions/${transactionId}`, transactionData);

      // Refresh subs models list to get updated balances
      await fetchSubsModels();

      return response;
    } catch (err) {
      console.error('Error updating subs transaction:', err);
      error.value = err.message || 'Failed to update subs transaction';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };

  // NEW METHOD: Create a new transaction in subs model
  const createSubsTransaction = async (transactionData) => {
    const debugId = `createSubsTransaction_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;

    try {
      console.log(`🔥 [${debugId}] ===== FRONTEND TRANSACTION CREATION STARTED =====`);
      console.log(`🔥 [${debugId}] Call stack:`, new Error().stack);
      console.log(`🔥 [${debugId}] isLoading.value BEFORE:`, isLoading.value);

      isLoading.value = true;
      error.value = null;

      console.log(`🔥 [${debugId}] === FRONTEND TRANSACTION CREATION ===`);
      console.log(`🔥 [${debugId}] Transaction data being sent:`, JSON.stringify(transactionData, null, 2));
      console.log(`🔥 [${debugId}] API endpoint: /api/expenses/subs/transactions`);
      console.log(`🔥 [${debugId}] About to make API call...`);

      const response = await api.post('/api/expenses/subs/transactions', transactionData);

      console.log(`✅ [${debugId}] Transaction creation response:`, response);
      console.log(`🔄 [${debugId}] About to refresh subs models...`);

      // Refresh subs models list to get updated balances
      await fetchSubsModels();

      console.log(`✅ [${debugId}] Subs models refreshed`);
      console.log(`🎉 [${debugId}] ===== FRONTEND TRANSACTION CREATION COMPLETED =====`);

      return response;
    } catch (err) {
      console.error(`❌ [${debugId}] === TRANSACTION CREATION ERROR ===`);
      console.error(`❌ [${debugId}] Error details:`, err);
      console.error(`❌ [${debugId}] Error message:`, err.message);
      console.error(`❌ [${debugId}] Error response:`, err.response?.data);
      console.error(`❌ [${debugId}] Error stack:`, err.stack);

      error.value = err.message || 'Failed to create subs transaction';
      throw err;
    } finally {
      console.log(`🏁 [${debugId}] Setting isLoading to FALSE`);
      isLoading.value = false;
      console.log(`🏁 [${debugId}] ===== FRONTEND TRANSACTION CREATION ENDED =====`);
    }
  };

  return {
    // State
    subs,
    subsModels,
    currentSub,
    currentSubsModel,
    isLoading,
    error,

    // Computed
    activeSubs,
    inactiveSubs,
    totalSubsBalance,
    getUniqueSubNames,
    getUniquePaidToValues,
    getUniqueSubProjects,

    // Subs methods
    fetchSubs,
    fetchSubById,
    createSub,
    updateSub,
    deleteSub,

    // Subs Models methods
    fetchSubsModels,
    fetchSubsModelById,
    createSubsModel,
    updateSubsModel,
    deleteSubsModel,

    // Transaction methods
    createSubsTransaction,
    deleteSubsTransaction,
    updateSubsTransaction
  };
}
