import { ref, computed, readonly } from 'vue'
import { useUniversalAIClient } from '~/composables/ai/useUniversalAIClient'

export interface ClientAIAnalysisResult {
  recommendation: string
  confidence: string
  summary: string
  technicalAnalysis: string
  fundamentalAnalysis: string
  marketTrends: string
  riskAssessment: string
  analysisTimestamp: string
}

export const useClientAIAnalysis = () => {
  // State
  const loading = ref(false)
  const error = ref('')
  const analysis = ref<ClientAIAnalysisResult | null>(null)
  const progress = ref(0)
  const statusMessage = ref('')
  
  // Enhanced progress tracking
  const dataPointsProcessed = ref(0)
  const currentOperation = ref('')
  const operationStartTime = ref<Date | null>(null)

  // Universal AI Client - NO MORE HARDCODED PROVIDERS!
  const { callAIForJSON, isConfigured, getAIInfo } = useUniversalAIClient()

  // Generate AI analysis prompt - COMPREHENSIVE STOCK ANALYSIS
  const generateAIAnalysisPrompt = (stockData: any) => {
    const currentTime = new Date().toLocaleString('en-IN', { timeZone: 'Asia/Kolkata' })
    
    return `You are an expert financial analyst specializing in Indian stock markets. Provide comprehensive investment analysis for the following stock:

STOCK INFORMATION:
- Symbol: ${stockData.symbol}
- Company: ${stockData.companyName}
- Current Price: ₹${stockData.currentPrice}
- Price Change: ${stockData.change} (${stockData.pChange}%)
- Volume: ${stockData.volume}
- Day High: ₹${stockData.dayHigh}
- Day Low: ₹${stockData.dayLow}
- Previous Close: ₹${stockData.previousClose}
- Analysis Date: ${currentTime}

ANALYSIS REQUIREMENTS:
Provide a comprehensive investment analysis covering:

1. **Technical Analysis**: Chart patterns, support/resistance levels, moving averages, RSI, MACD, volume analysis
2. **Fundamental Analysis**: Financial health, P/E ratio, debt levels, revenue growth, profitability, competitive position
3. **Market Trends**: Sector outlook, market sentiment, economic factors affecting the stock
4. **Risk Assessment**: Key risks, volatility analysis, downside scenarios

Return your analysis in the following JSON format:
{
  "recommendation": "Strong Buy/Buy/Hold/Sell/Strong Sell",
  "confidence": "High/Medium/Low with percentage if possible",
  "summary": "Brief overall investment summary and rationale",
  "technicalAnalysis": "Detailed technical analysis with specific indicators and levels",
  "fundamentalAnalysis": "Comprehensive fundamental analysis including financial metrics",
  "marketTrends": "Market trends, sector outlook, and macroeconomic factors",
  "riskAssessment": "Key risks, volatility assessment, and risk mitigation strategies"
}

Focus on Indian market context and provide actionable insights. Be specific with price levels, ratios, and timeframes. Only include real, factual analysis - no speculation or generic content.`
  }

  // Universal AI Provider Call - NO MORE HARDCODED LOGIC!
  const callAIProviderDirectly = async (prompt: string): Promise<any> => {
    const aiInfo = getAIInfo()
    console.log(`🤖 Making universal AI call to ${aiInfo.provider} with model ${aiInfo.model}`)

    try {
      // Use universal AI client - works with ANY provider dynamically
      const systemPrompt = 'You are a professional financial analyst. Provide accurate, data-driven investment analysis based on current market conditions. Return only valid JSON format matching the exact structure requested.'
      // Return the parsed JSON object directly
      return await callAIForJSON(prompt, systemPrompt)
    } catch (error: any) {
      console.error(`❌ Universal AI call failed:`, error)
      throw new Error(`AI analysis failed: ${error.message}`)
    }
  }

  // ALL HARDCODED PROVIDER FUNCTIONS REMOVED!
  // Now using Universal AI Client for complete dynamic support



  // Perform client-side comprehensive AI analysis with ENHANCED PROGRESS
  const performAIAnalysis = async (stockData: any, progressCallback?: (stage: string, progress: number) => void) => {
    if (!isConfigured.value) {
      throw new Error('AI configuration is not complete. Please configure your AI settings.')
    }

    loading.value = true
    error.value = ''
    analysis.value = null
    progress.value = 0
    dataPointsProcessed.value = 0
    currentOperation.value = 'Initializing'
    operationStartTime.value = new Date()
    statusMessage.value = `Starting comprehensive AI analysis for ${stockData.symbol}...`

    try {
      // Step 1: Prepare comprehensive analysis
      progress.value = 10
      currentOperation.value = 'Preparing Analysis'
      statusMessage.value = `Preparing comprehensive analysis for ${stockData.symbol}...`

      // Step 2: Generate AI analysis prompt
      progress.value = 20
      statusMessage.value = 'Preparing AI analysis prompt with stock data...'

      const aiPrompt = generateAIAnalysisPrompt(stockData)

      progress.value = 30
      const aiInfo = getAIInfo()
      statusMessage.value = `Sending comprehensive analysis request to ${aiInfo.provider} (${aiInfo.model})...`
      progressCallback?.('send', 100)

      // Check for reasoning models
      if (aiInfo.model?.toLowerCase().includes('r1') ||
          aiInfo.model?.toLowerCase().includes('reasoning') ||
          aiInfo.model?.toLowerCase().includes('think')) {
        statusMessage.value = `🧠 Reasoning model detected - AI is thinking through the analysis step-by-step...`
      }

      // Notify progress callback that AI processing is starting
      progressCallback?.('process', 0)

      // Make UNIVERSAL AI provider call - COMPLETELY DYNAMIC!
      const aiAnalysisResult = await callAIProviderDirectly(aiPrompt)

      // AI processing complete
      progressCallback?.('process', 100)

      // Step 3: Validate AI response with detailed progress
      progress.value = 70
      statusMessage.value = `Received AI response, validating structure...`

      if (!aiAnalysisResult || typeof aiAnalysisResult !== 'object') {
        throw new Error('Invalid response from AI provider - expected JSON object')
      }

      console.log('✅ Received parsed AI analysis result:', aiAnalysisResult)

      progress.value = 85
      statusMessage.value = 'Validating comprehensive analysis data...'

      // Validate required fields - NO FALLBACK DATA
      if (!aiAnalysisResult.recommendation) {
        throw new Error('AI response missing required recommendation field')
      }
      if (!aiAnalysisResult.summary) {
        throw new Error('AI response missing required summary field')
      }

      // Step 4: Prepare final response with detailed completion
      progress.value = 95
      statusMessage.value = 'Preparing final comprehensive analysis result...'

      const finalAnalysis: ClientAIAnalysisResult = {
        recommendation: aiAnalysisResult.recommendation,
        confidence: aiAnalysisResult.confidence,
        summary: aiAnalysisResult.summary,
        technicalAnalysis: aiAnalysisResult.technicalAnalysis,
        fundamentalAnalysis: aiAnalysisResult.fundamentalAnalysis,
        marketTrends: aiAnalysisResult.marketTrends,
        riskAssessment: aiAnalysisResult.riskAssessment,
        analysisTimestamp: new Date().toISOString()
      }

      progress.value = 100
      statusMessage.value = `Comprehensive analysis complete! Generated ${Object.keys(finalAnalysis).length} analysis sections`

      analysis.value = finalAnalysis
      loading.value = false

      console.log('✅ Client-side comprehensive AI analysis completed:', finalAnalysis)
      return finalAnalysis

    } catch (err: any) {
      console.error('❌ Client-side AI analysis failed:', err)
      error.value = err.message || 'Comprehensive AI analysis failed'
      loading.value = false
      throw err
    }
  }

  // Reset function
  const reset = () => {
    loading.value = false
    error.value = ''
    analysis.value = null
    progress.value = 0
    statusMessage.value = ''
    dataPointsProcessed.value = 0
    currentOperation.value = ''
    operationStartTime.value = null
  }

  return {
    // State (readonly)
    loading: readonly(loading),
    error: readonly(error),
    analysis: readonly(analysis),
    progress: readonly(progress),
    statusMessage: readonly(statusMessage),
    currentOperation: readonly(currentOperation),
    dataPointsProcessed: readonly(dataPointsProcessed),

    // Computed
    isConfigured,

    // Methods
    performAIAnalysis,
    reset
  }
}
