<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-full p-4">
      <div class="bg-white rounded-lg w-[95%] max-w-[90vw] lg:max-w-[95vw] xl:max-w-[90vw] my-4 max-h-[95vh] flex flex-col shadow-2xl">
        <!-- Modal Header -->
        <div
          class="bg-gradient-to-r from-orange-500 to-red-600 p-4 rounded-t-lg flex justify-between items-center sticky top-0">
          <h2 class="text-xl font-bold text-white">Bulk Edit Employees</h2>
          <button @click="$emit('close')" class="text-white hover:text-red-200 transition-colors">
            <XMarkIcon class="h-6 w-6" />
          </button>
        </div>

        <!-- Modal Content -->
        <div class="flex-1 p-6 modal-content">
          <!-- Step Indicator -->
          <div class="mb-6">
            <div class="flex items-center justify-center space-x-4">
              <div class="flex items-center">
                <div :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  currentStep >= 1 ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-600'
                ]">1</div>
                <span class="ml-2 text-sm font-medium">Select Employees</span>
              </div>
              <div class="w-8 h-0.5 bg-gray-300"></div>
              <div class="flex items-center">
                <div :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  currentStep >= 2 ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-600'
                ]">2</div>
                <span class="ml-2 text-sm font-medium">Choose Edit Type</span>
              </div>
              <div class="w-8 h-0.5 bg-gray-300"></div>
              <div class="flex items-center">
                <div :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  currentStep >= 3 ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-600'
                ]">3</div>
                <span class="ml-2 text-sm font-medium">Configure Fields</span>
              </div>
              <div class="w-8 h-0.5 bg-gray-300"></div>
              <div class="flex items-center">
                <div :class="[
                  'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium',
                  currentStep >= 4 ? 'bg-orange-500 text-white' : 'bg-gray-200 text-gray-600'
                ]">4</div>
                <span class="ml-2 text-sm font-medium">Preview & Execute</span>
              </div>
            </div>
          </div>

          <!-- Step 1: Employee Selection -->
          <div v-if="currentStep === 1" class="space-y-6">
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-blue-800 mb-3">Select Employees to Edit</h3>

              <!-- Selection Methods -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Selection Method</label>
                  <select v-model="selectionMethod"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                    <option value="individual">Select Individual Employees</option>
                    <option value="criteria">Select by Criteria</option>
                    <option value="all">Select All Employees</option>
                  </select>
                </div>
                <div v-if="selectionMethod === 'individual'">
                  <label class="block text-sm font-medium text-gray-700 mb-2">Search Employees</label>
                  <input v-model="employeeSearchTerm" type="text" placeholder="Search by name..."
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500" />
                </div>
              </div>

              <!-- Criteria Selection -->
              <div v-if="selectionMethod === 'criteria'" class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Status</label>
                  <select v-model="criteriaFilters.status"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                    <option value="">All Statuses</option>
                    <option value="Active">Active</option>
                    <option value="Inactive">Inactive</option>
                    <option value="On Leave">On Leave</option>
                    <option value="Terminated">Terminated</option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                  <select v-model="criteriaFilters.category"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                    <option value="">All Categories</option>
                    <option v-for="category in uniqueCategories" :key="category" :value="category">{{ category }}
                    </option>
                  </select>
                </div>
                <div>
                  <label class="block text-sm font-medium text-gray-700 mb-2">Project</label>
                  <select v-model="criteriaFilters.project"
                    class="w-full rounded-md border-gray-300 shadow-sm focus:border-orange-500 focus:ring-orange-500">
                    <option value="">All Projects</option>
                    <option v-for="project in uniqueProjects" :key="project" :value="project">{{ project }}</option>
                  </select>
                </div>
              </div>

              <!-- Employee List -->
              <div v-if="selectionMethod === 'individual'"
                class="max-h-80 overflow-y-auto border border-gray-200 rounded-lg scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-gray-100">
                <div class="p-2 bg-gray-50 border-b">
                  <label class="flex items-center">
                    <input type="checkbox" :checked="isAllFilteredSelected" @change="toggleAllFiltered"
                      class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-500 focus:ring-orange-500" />
                    <span class="ml-2 text-sm font-medium">Select All Filtered ({{ filteredEmployeesForSelection.length
                      }})</span>
                  </label>
                </div>
                <div class="divide-y divide-gray-200">
                  <label v-for="employee in filteredEmployeesForSelection" :key="employee._id"
                    class="flex items-center p-3 hover:bg-gray-50">
                    <input type="checkbox" :value="employee._id" v-model="selectedEmployeeIds"
                      class="rounded border-gray-300 text-orange-600 shadow-sm focus:border-orange-500 focus:ring-orange-500" />
                    <div class="ml-3 flex-1">
                      <div class="text-sm font-medium text-gray-900">{{ employee.employeeName }}</div>
                      <div class="text-sm text-gray-500">{{ employee.category }} | {{ employee.status || 'Active' }}
                      </div>
                    </div>
                  </label>
                </div>
              </div>

              <!-- Selection Summary -->
              <div class="mt-4 p-3 bg-orange-50 border border-orange-200 rounded-lg">
                <div class="text-sm font-medium text-orange-800">
                  Selected: {{ selectedEmployees.length }} employees
                  <span v-if="selectedEmployees.length > 50" class="text-red-600 font-semibold">
                    (Will be processed in chunks of 50)
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Step 2: Operation Type Selection -->
          <div v-if="currentStep === 2" class="space-y-6">
            <OperationTypeSelector :selected-employees-count="selectedEmployees.length"
              @update:editType="handleEditTypeChange" />
          </div>

          <!-- Step 3: Edit Interface Based on Type -->
          <div v-if="currentStep === 3" class="space-y-6">
            <!-- Personal Details Edit -->
            <PersonalDetailsTable v-if="editType === 'individual'" :employees="selectedEmployees"
              @update:changes="handlePersonalChanges" />

            <!-- Employment Bulk Update -->
            <EmploymentBulkForm v-if="editType === 'bulk'" :selected-employees-count="selectedEmployees.length"
              :unique-categories="uniqueCategories" :unique-projects="uniqueProjects" :unique-sites="uniqueSites"
              @update:changes="handleBulkChanges" />

            <!-- Mixed Edit Interface -->
            <div v-if="editType === 'mixed'" class="space-y-6">
              <PersonalDetailsTable :employees="selectedEmployees" @update:changes="handlePersonalChanges" />
              <EmploymentBulkForm :selected-employees-count="selectedEmployees.length"
                :unique-categories="uniqueCategories" :unique-projects="uniqueProjects" :unique-sites="uniqueSites"
                @update:changes="handleBulkChanges" />
            </div>

            <!-- Fallback message if no edit type selected -->
            <div v-if="!editType" class="bg-gray-100 border border-gray-300 rounded p-4 text-center text-gray-600">
              Please select an edit type from the previous step.
            </div>
          </div>

          <!-- Step 4: Preview & Execute -->
          <div v-if="currentStep === 4" class="space-y-6">
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
              <h3 class="text-lg font-semibold text-purple-800 mb-3">Preview Changes</h3>

              <!-- Summary -->
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="bg-white p-3 rounded border">
                  <div class="text-sm text-gray-600">Employees Selected</div>
                  <div class="text-2xl font-bold text-purple-600">{{ selectedEmployees.length }}</div>
                </div>
                <div class="bg-white p-3 rounded border">
                  <div class="text-sm text-gray-600">Edit Type</div>
                  <div class="text-sm font-medium text-purple-600 capitalize">{{ editType }}</div>
                </div>
                <div class="bg-white p-3 rounded border">
                  <div class="text-sm text-gray-600">Total Changes</div>
                  <div class="text-2xl font-bold text-purple-600">{{ selectedFieldsCount }}</div>
                </div>
              </div>

              <!-- Changes Preview -->
              <div class="bg-white border rounded-lg overflow-hidden">
                <div class="bg-gray-50 px-4 py-2 border-b">
                  <h4 class="font-medium text-gray-900">Changes Summary</h4>
                </div>
                <div class="p-4 space-y-4">
                  <!-- Personal Changes -->
                  <div v-if="personalChanges.length > 0">
                    <h5 class="font-medium text-gray-800 mb-2">Personal Details Changes</h5>
                    <div class="text-sm text-gray-600">
                      {{ personalChanges.length }} individual field updates across multiple employees
                    </div>
                  </div>

                  <!-- Bulk Changes -->
                  <div v-if="Object.keys(bulkChanges.selectedFields || {}).length > 0">
                    <h5 class="font-medium text-gray-800 mb-2">Bulk Employment Updates</h5>
                    <div class="space-y-1">
                      <div v-for="(value, field) in bulkChanges.selectedFields" :key="field"
                        class="flex justify-between items-center text-sm">
                        <span class="text-gray-700 capitalize">{{ field }}:</span>
                        <span class="text-gray-900 bg-gray-100 px-2 py-1 rounded">{{ value || 'Not set' }}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Progress Section -->
              <div v-if="isProcessing" class="mt-6">
                <div class="bg-white border rounded-lg p-4">
                  <h4 class="font-medium text-gray-900 mb-3">Processing Progress</h4>

                  <!-- Overall Progress -->
                  <div class="mb-4">
                    <div class="flex justify-between text-sm text-gray-600 mb-1">
                      <span>Overall Progress</span>
                      <span>{{ Math.round(overallProgress) }}%</span>
                    </div>
                    <div class="w-full bg-gray-200 rounded-full h-2">
                      <div class="bg-purple-600 h-2 rounded-full transition-all duration-300"
                        :style="{ width: overallProgress + '%' }"></div>
                    </div>
                  </div>

                  <!-- Current Status -->
                  <div class="text-sm text-gray-600 mb-2">
                    <span class="font-medium">Status:</span> {{ currentStatus }}
                  </div>

                  <!-- Records Progress -->
                  <div class="text-sm text-gray-600">
                    <span class="font-medium">Records:</span> {{ processedRecords }} / {{ totalRecords }}
                    <span v-if="failedRecords > 0" class="text-red-600 ml-2">({{ failedRecords }} failed)</span>
                  </div>

                  <!-- Cancel Button -->
                  <div class="mt-4">
                    <button @click="cancelProcessing" :disabled="!canCancel"
                      class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed">
                      Cancel Processing
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal Footer -->
        <div class="bg-gradient-to-r from-orange-600 to-red-500 p-4 rounded-b-lg flex justify-between">
          <div>
            <button v-if="currentStep > 1" @click="previousStep" :disabled="isProcessing"
              class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-gray-400 to-gray-600 text-white font-bold shadow-md hover:from-gray-500 hover:to-gray-700 focus:outline-none transition duration-300 disabled:opacity-50">
              <ChevronLeftIcon class="h-5 w-5 mr-2" />
              Previous
            </button>
          </div>

          <div class="flex space-x-3">
            <button @click="$emit('close')" :disabled="isProcessing"
              class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-red-400 to-red-600 text-white font-bold shadow-md hover:from-red-500 hover:to-red-700 focus:outline-none transition duration-300 disabled:opacity-50">
              <XMarkIcon class="h-5 w-5 mr-2" />
              Cancel
            </button>

            <button v-if="currentStep < 4" @click="nextStep" :disabled="!canProceedToNextStep"
              class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-orange-400 to-orange-600 text-white font-bold shadow-lg hover:from-orange-500 hover:to-orange-700 focus:outline-none transform hover:scale-105 transition duration-300 disabled:opacity-50">
              Next
              <ChevronRightIcon class="h-5 w-5 ml-2" />
            </button>

            <button v-if="currentStep === 4 && !isProcessing" @click="executeChanges" :disabled="!canExecute"
              class="inline-flex items-center px-4 py-2 rounded-lg bg-gradient-to-r from-green-400 to-green-600 text-white font-bold shadow-lg hover:from-green-500 hover:to-green-700 focus:outline-none transform hover:scale-105 transition duration-300 disabled:opacity-50">
              <CheckIcon class="h-5 w-5 mr-2" />
              Execute Changes
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import {
  XMarkIcon,
  CheckIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'
import { useBulkEdit } from '~/composables/business/useBulkEdit'
import OperationTypeSelector from './BulkEdit/OperationTypeSelector.vue'
import PersonalDetailsTable from './BulkEdit/PersonalDetailsTable.vue'
import EmploymentBulkForm from './BulkEdit/EmploymentBulkForm.vue'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  employees: {
    type: Array,
    default: () => []
  },
  uniqueCategories: {
    type: Array,
    default: () => []
  },
  uniqueProjects: {
    type: Array,
    default: () => []
  },
  uniqueSites: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['close', 'success'])

// Composable for bulk edit functionality
const {
  executeChanges: executeBulkChanges,
  isProcessing,
  overallProgress,
  currentStatus,
  processedRecords,
  totalRecords,
  failedRecords,
  canCancel,
  cancelProcessing
} = useBulkEdit()

// Step management
const currentStep = ref(1)

// Employee selection
const selectionMethod = ref('individual')
const selectedEmployeeIds = ref([])
const employeeSearchTerm = ref('')
const criteriaFilters = ref({
  status: '',
  category: '',
  project: '',
  site: ''
})

// Edit type and changes tracking
const editType = ref('')
const personalChanges = ref([])
const bulkChanges = ref({})

// Handle edit type change
const handleEditTypeChange = (type) => {
  editType.value = type
}

// Handle personal details changes
const handlePersonalChanges = (changes) => {
  personalChanges.value = changes
}

// Handle bulk changes
const handleBulkChanges = (changes) => {
  bulkChanges.value = changes
}

// Computed properties
const filteredEmployeesForSelection = computed(() => {
  let filtered = props.employees

  if (employeeSearchTerm.value) {
    const searchTerm = employeeSearchTerm.value.toLowerCase()
    filtered = filtered.filter(emp =>
      emp.employeeName.toLowerCase().includes(searchTerm)
    )
  }

  return filtered
})

const selectedEmployees = computed(() => {
  if (selectionMethod.value === 'all') {
    return props.employees
  } else if (selectionMethod.value === 'criteria') {
    return props.employees.filter(emp => {
      const statusMatch = !criteriaFilters.value.status || emp.status === criteriaFilters.value.status
      const categoryMatch = !criteriaFilters.value.category || emp.category === criteriaFilters.value.category
      const projectMatch = !criteriaFilters.value.project || emp.project === criteriaFilters.value.project
      const siteMatch = !criteriaFilters.value.site || emp.site === criteriaFilters.value.site

      return statusMatch && categoryMatch && projectMatch && siteMatch
    })
  } else {
    return props.employees.filter(emp => selectedEmployeeIds.value.includes(emp._id))
  }
})

const isAllFilteredSelected = computed(() => {
  return filteredEmployeesForSelection.value.length > 0 &&
    filteredEmployeesForSelection.value.every(emp => selectedEmployeeIds.value.includes(emp._id))
})

const selectedFieldsCount = computed(() => {
  if (editType.value === 'individual' || editType.value === 'mixed') {
    return personalChanges.value.length
  } else if (editType.value === 'bulk') {
    return Object.keys(bulkChanges.value.selectedFields || {}).length
  }
  return 0
})

const canProceedToNextStep = computed(() => {
  if (currentStep.value === 1) {
    return selectedEmployees.value.length > 0
  } else if (currentStep.value === 2) {
    return editType.value !== ''
  } else if (currentStep.value === 3) {
    return selectedFieldsCount.value > 0
  }
  return true
})

const canExecute = computed(() => {
  return selectedEmployees.value.length > 0 && selectedFieldsCount.value > 0 && !isProcessing.value
})

// Methods
const toggleAllFiltered = () => {
  if (isAllFilteredSelected.value) {
    // Deselect all filtered
    selectedEmployeeIds.value = selectedEmployeeIds.value.filter(id =>
      !filteredEmployeesForSelection.value.some(emp => emp._id === id)
    )
  } else {
    // Select all filtered
    const filteredIds = filteredEmployeesForSelection.value.map(emp => emp._id)
    selectedEmployeeIds.value = [...new Set([...selectedEmployeeIds.value, ...filteredIds])]
  }
}

const nextStep = () => {
  if (canProceedToNextStep.value && currentStep.value < 4) {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 1) {
    currentStep.value--
  }
}



const executeChanges = async () => {
  try {
    let payload = {}

    if (editType.value === 'individual') {
      // Individual edit mode
      payload = {
        type: 'individual',
        updates: personalChanges.value
      }
    } else if (editType.value === 'bulk') {
      // Bulk edit mode
      payload = {
        type: 'bulk',
        employeeIds: selectedEmployees.value.map(emp => emp._id),
        fieldsToUpdate: bulkChanges.value.selectedFields || {}
      }
    } else if (editType.value === 'mixed') {
      // Mixed edit mode
      payload = {
        type: 'mixed',
        individualUpdates: personalChanges.value,
        bulkUpdates: bulkChanges.value.selectedFields || {},
        employeeIds: selectedEmployees.value.map(emp => emp._id)
      }
    }

    await executeBulkChanges(payload)

    // Emit success event to parent
    emit('success', {
      updatedCount: selectedEmployees.value.length,
      editType: editType.value,
      personalChanges: personalChanges.value.length,
      bulkChanges: Object.keys(bulkChanges.value.selectedFields || {}).length
    })

    // Close modal after successful completion
    setTimeout(() => {
      emit('close')
    }, 2000)

  } catch (error) {
    console.error('Bulk edit failed:', error)
    // Error handling is done in the composable
  }
}

// Watch for selection method changes
watch(selectionMethod, (newMethod) => {
  if (newMethod === 'all') {
    selectedEmployeeIds.value = []
  } else if (newMethod === 'criteria') {
    selectedEmployeeIds.value = []
    criteriaFilters.value = {
      status: '',
      category: '',
      project: '',
      site: ''
    }
  }
})

// Reset modal state when closed
watch(() => props.show, (newShow) => {
  if (!newShow) {
    currentStep.value = 1
    selectedEmployeeIds.value = []
    selectionMethod.value = 'individual'
    employeeSearchTerm.value = ''
    criteriaFilters.value = {
      status: '',
      category: '',
      project: '',
      site: ''
    }
    editType.value = ''
    personalChanges.value = []
    bulkChanges.value = {}
  }
})
</script>

<style scoped>
/* Custom scrollbar styles */
.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: #9CA3AF #F3F4F6;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: #F3F4F6;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: #9CA3AF;
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #6B7280;
}

/* Ensure modal content scrolls properly */
.modal-content {
  max-height: calc(95vh - 140px);
  overflow-y: auto;
}

/* Better scrollbar for main modal content */
.modal-content::-webkit-scrollbar {
  width: 12px;
}

.modal-content::-webkit-scrollbar-track {
  background: #F9FAFB;
  border-radius: 6px;
}

.modal-content::-webkit-scrollbar-thumb {
  background: #D1D5DB;
  border-radius: 6px;
  border: 2px solid #F9FAFB;
}

.modal-content::-webkit-scrollbar-thumb:hover {
  background: #9CA3AF;
}
</style>
