<template>
  <div class="bg-white rounded-lg shadow p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Attendance Summary</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div>
        <p class="text-sm font-medium text-gray-500">Total Present Days</p>
        <p class="mt-1 text-3xl font-semibold text-green-600">{{ summary.presentDays }}</p>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-500">Total Absent Days</p>
        <p class="mt-1 text-3xl font-semibold text-red-600">{{ summary.absentDays }}</p>
      </div>
      <div>
        <p class="text-sm font-medium text-gray-500">Total Earnings</p>
        <p class="mt-1 text-3xl font-semibold text-blue-600">₹{{ summary.totalEarnings.toFixed(2) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  attendanceData: {
    type: Array,
    default: () => []
  }
})

const summary = computed(() => {
  let presentDays = 0
  let absentDays = 0
  let totalEarnings = 0

  props.attendanceData.forEach(row => {
    if (row.attendance) {
        Object.values(row.attendance).forEach(days => {
          if (days > 0) {
            presentDays += days
          } else {
            absentDays += 1
          }
          totalEarnings += (days || 0) * row.daily_rate
        })
    }
  })

  return {
    presentDays,
    absentDays,
    totalEarnings
  }
})
</script>