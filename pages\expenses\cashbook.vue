<template>
  <div class="container mx-auto px-2 sm:px-4 py-4 sm:py-8 mt-0">
    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-8 space-y-4 sm:space-y-0">
      <h1 class="text-xl sm:text-2xl font-bold text-gray-900">Cash Book</h1>

      <div class="flex flex-wrap gap-2 sm:gap-4">
        <!-- Export Buttons -->
        <a
          :href="`/api/expenses/export/pdf?type=cashbook&paymentMode=cash`"
          target="_blank"
          class="px-3 py-2 sm:px-4 sm:py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 inline-flex items-center text-xs sm:text-sm flex-1 sm:flex-none justify-center"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
            Export to PDF
          </span>
        </a>

        <a
          :href="`/api/expenses/export/excel?type=cashbook&paymentMode=cash`"
          target="_blank"
          class="px-3 py-2 sm:px-4 sm:py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 inline-flex items-center text-xs sm:text-sm flex-1 sm:flex-none justify-center"
        >
          <span class="flex items-center">
            <svg class="h-5 w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export to Excel
          </span>
        </a>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading cash book...</span>
    </div>

    <!-- Cash Book Summary -->
    <div v-else class="bg-white rounded-lg shadow-md p-6 mb-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- Total Payments Card -->
        <div class="bg-white p-4 rounded-lg shadow border-l-4 border-red-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Total Payments</p>
              <p class="text-xl font-bold text-red-600">{{ formatCurrency(totalPayments) }}</p>
            </div>
            <div class="bg-red-100 p-2 rounded-full">
              <svg class="h-6 w-6 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Total Receipts Card -->
        <div class="bg-white p-4 rounded-lg shadow border-l-4 border-green-500">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Total Receipts</p>
              <p class="text-xl font-bold text-green-600">{{ formatCurrency(totalReceipts) }}</p>
            </div>
            <div class="bg-green-100 p-2 rounded-full">
              <svg class="h-6 w-6 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
          </div>
        </div>

        <!-- Current Balance Card -->
        <div class="bg-white p-4 rounded-lg shadow border-l-4" :class="currentBalance >= 0 ? 'border-blue-500' : 'border-orange-500'">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500">Current Balance</p>
              <p class="text-xl font-bold" :class="currentBalance >= 0 ? 'text-blue-600' : 'text-orange-600'">{{ formatCurrency(currentBalance) }}</p>
            </div>
            <div :class="currentBalance >= 0 ? 'bg-blue-100' : 'bg-orange-100'" class="p-2 rounded-full">
              <svg class="h-6 w-6" :class="currentBalance >= 0 ? 'text-blue-500' : 'text-orange-500'" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 6l3 1m0 0l-3 9a5.002 5.002 0 006.001 0M6 7l3 9M6 7l6-2m6 2l3-1m-3 1l-3 9a5.002 5.002 0 006.001 0M18 7l3 9m-3-9l-6-2m0-2v2m0 16V5m0 16H9m3 0h3" />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Date Filter -->
    <div class="bg-white rounded-lg shadow-md p-4 sm:p-6 mb-4 sm:mb-8">
      <h2 class="text-lg font-medium text-gray-900 mb-4">Filter Transactions</h2>
      <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4 sm:gap-6">
        <div>
          <label for="startDate" class="block text-sm font-medium text-gray-700">Start Date</label>
          <input
            type="date"
            id="startDate"
            v-model="filters.startDate"
            class="mt-1 block w-full px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-xs sm:text-sm"
          />
        </div>

        <div>
          <label for="endDate" class="block text-sm font-medium text-gray-700">End Date</label>
          <input
            type="date"
            id="endDate"
            v-model="filters.endDate"
            class="mt-1 block w-full px-2 sm:px-3 py-1.5 sm:py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-xs sm:text-sm"
          />
        </div>

        <div class="flex items-end space-x-2 w-full sm:w-auto">
          <button
            @click="applyFilters"
            class="flex-1 sm:flex-none px-3 sm:px-4 py-1.5 sm:py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-xs sm:text-sm"
          >
            Apply Filters
          </button>

          <button
            @click="resetFilters"
            class="flex-1 sm:flex-none px-3 sm:px-4 py-1.5 sm:py-2 border border-gray-300 rounded-md shadow-sm text-xs sm:text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Reset
          </button>
        </div>
      </div>
    </div>

    <!-- Transactions Table -->
    <div class="bg-white rounded-lg shadow-md overflow-hidden mt-4 sm:mt-6">
      <div v-if="transactions.length === 0" class="text-center py-12 text-gray-500">
        No cash transactions found.
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Date
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Paid To/From
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider hidden sm:table-cell">
                Category
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider hidden md:table-cell">
                Description
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Balance
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(transaction, index) in transactions" :key="transaction.id" class="hover:bg-gray-50">
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">
                {{ formatDate(transaction.date) }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                {{ transaction.paidTo || '-' }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 hidden sm:table-cell">
                {{ transaction.category || '-' }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 hidden md:table-cell">
                {{ transaction.description || '-' }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium" :class="getAmountClass(transaction.amount)">
                {{ formatCurrency(transaction.amount) }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium" :class="getBalanceClass(getRunningBalance(index))">
                {{ formatCurrency(getRunningBalance(index)) }}
              </td>
            </tr>
          </tbody>
          <!-- Summary Footer -->
          <tfoot class="bg-gray-50">
            <tr>
              <td colspan="4" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                Total:
              </td>
              <td class="px-6 py-3 text-left text-sm font-medium" :class="getBalanceClass(currentBalance)">
                {{ formatCurrency(currentBalance) }}
              </td>
              <td></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true
});

import { ref, computed, onMounted } from 'vue';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { usePageTitle } from '~/composables/ui/usePageTitle';

// Set page title
usePageTitle('Cash Book', 'Track and manage cash transactions and balances');

// State
const isLoading = ref(true);
const transactions = ref([]);
const filters = ref({
  startDate: '',
  endDate: ''
});

// Computed properties
const totalPayments = computed(() => {
  return transactions.value
    .filter(tx => tx.amount < 0)
    .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
});

const totalReceipts = computed(() => {
  return transactions.value
    .filter(tx => tx.amount > 0)
    .reduce((sum, tx) => sum + tx.amount, 0);
});

const currentBalance = computed(() => {
  return totalReceipts.value - totalPayments.value;
});

// Methods
const fetchCashTransactions = async () => {
  try {
    isLoading.value = true;

    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('paymentMode', 'cash');

    if (filters.value.startDate) {
      queryParams.append('startDate', filters.value.startDate);
    }

    if (filters.value.endDate) {
      queryParams.append('endDate', filters.value.endDate);
    }

    // Fetch transactions
    const api = useApiWithAuth();
    const response = await api.get(`/api/expenses?${queryParams.toString()}`);

    if (response && Array.isArray(response)) {
      // Sort by date (newest first)
      transactions.value = response.sort((a, b) => new Date(b.date) - new Date(a.date));

    } else {
      transactions.value = [];
    }
  } catch (error) {
    console.error('Error fetching cash transactions:', error);
    alert('Failed to load cash transactions. Please try again.');
    transactions.value = [];
  } finally {
    isLoading.value = false;
  }
};

const applyFilters = () => {
  fetchCashTransactions();
};

const resetFilters = () => {
  filters.value = {
    startDate: '',
    endDate: ''
  };
  fetchCashTransactions();
};

const getRunningBalance = (index) => {
  // Calculate running balance up to this transaction
  let balance = 0;
  for (let i = transactions.value.length - 1; i >= index; i--) {
    balance += transactions.value[i].amount;
  }
  return balance;
};

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

const formatDate = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-IN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const getAmountClass = (amount) => {
  return amount >= 0 ? 'text-green-600' : 'text-red-600';
};

const getBalanceClass = (balance) => {
  return balance >= 0 ? 'text-blue-600' : 'text-orange-600';
};

// Initialize
onMounted(() => {
  fetchCashTransactions();
});
</script>
