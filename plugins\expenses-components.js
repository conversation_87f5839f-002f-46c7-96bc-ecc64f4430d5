import { defineNuxtPlugin } from '#app';
import ExpenseForm from '~/components/expenses/ExpenseForm.vue';
import TransferForm from '~/components/expenses/TransferForm.vue';
import ExpensesDashboard from '~/components/expenses/Dashboard.vue';
import ExpenseList from '~/components/expenses/ExpenseList.vue';

export default defineNuxtPlugin((nuxtApp) => {
  // Register global components
  nuxtApp.vueApp.component('ExpenseForm', ExpenseForm);
  nuxtApp.vueApp.component('TransferForm', TransferForm);
  nuxtApp.vueApp.component('ExpensesDashboard', ExpensesDashboard);
  nuxtApp.vueApp.component('ExpenseList', ExpenseList);
});
