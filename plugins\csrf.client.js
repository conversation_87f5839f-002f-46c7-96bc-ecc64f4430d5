/**
 * Plugin to initialize CSRF token on application startup
 * This ensures a CSRF token is available for all requests
 *
 * This plugin implements the following CSRF protection strategy:
 * 1. Fetch a CSRF token on application startup
 * 2. Refresh the token periodically
 * 3. Ensure all state-changing requests include the token
 */
import useCsrf from '~/composables/auth/useCsrf';

export default defineNuxtPlugin(async (nuxtApp) => {
  // Only run on client side
  if (process.server) {
    return;
  }

  // Initialize CSRF token
  const { ensureToken, needsRefresh, getCsrfToken } = useCsrf();

  // First check if we already have a token in cookies
  const existingToken = getCsrfToken();

  // Only try to fetch a new token if we don't have one
  if (!existingToken) {
    try {
      console.log('Initializing CSRF token...');
      await ensureToken();
      console.log('CSRF token initialized successfully');
    } catch (error) {
      console.error('Failed to initialize CSRF token:', error);
      // Don't throw the error to prevent app initialization failure
      // The app should still work for public pages without a CSRF token
    }
  } else {
    console.log('Using existing CSRF token from cookie');
  }

  // Set up a global navigation hook to refresh the token when needed
  nuxtApp.hook('page:finish', async () => {
    if (needsRefresh.value) {
      try {
        await ensureToken();
      } catch (error) {
        console.error('Failed to refresh CSRF token:', error);
      }
    }
  });

  // Add a global error handler for CSRF errors
  nuxtApp.hook('vue:error', (error) => {
    if (error?.message?.includes('CSRF') ||
        error?.statusCode === 403 ||
        error?.response?.status === 403) {
      console.error('CSRF error detected:', error);

      // Try to refresh the token
      ensureToken(true).catch(err => {
        console.error('Failed to refresh CSRF token after error:', err);
      });
    }
  });
});
