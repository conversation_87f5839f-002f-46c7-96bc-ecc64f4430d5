/**
 * Plugin to intercept API calls and handle token refresh
 * This ensures all fetch calls throughout the application properly handle authentication
 */
import useAuthRefresh from '~/composables/auth/useAuthRefresh';
import useCsrf from '~/composables/auth/useCsrf';
import { useLogout } from '~/composables/auth/useLogout';
import { navigateTo } from '#app';

export default defineNuxtPlugin((nuxtApp) => {
  // Only run this interceptor on the client side
  if (process.server) {
    return;
  }

  // Original fetch function
  const originalFetch = globalThis.$fetch;

  // Intercepted fetch function
  globalThis.$fetch = async (url, options = {}) => {
    // Special case for firms/signup endpoint - always skip interception
    if (url.includes('/api/firms/signup')) {
      return originalFetch(url, options);
    }

    // Skip interception for auth-related endpoints to avoid circular dependencies
    // Also skip for non-API URLs to prevent initial errors on page load
    const skipAuthPaths = [
      '/api/auth/login',
      '/api/auth/register',
      '/api/auth/refresh',
      '/api/auth/logout',
      '/api/auth/forgot-password', // Password reset request - must be public
      '/api/auth/reset-password',  // Password reset confirmation - must be public
      '/api/auth/validate-password', // Password validation - used on public pages
      '/signup',
      '/api/csrf/token', // Add CSRF token endpoint to prevent circular dependency
      '/api/tools/languages', // Public language list API
      '/api/tools/translate' // Public translation API
    ];

    // Skip auth check for public endpoints or non-API URLs
    const isPublicPath = skipAuthPaths.some(path => url.includes(path));
    const isNonApiUrl = !url.includes('/api/');


    if (isPublicPath || isNonApiUrl) {
      return originalFetch(url, options);
    }

    // For all other API calls, ensure valid token first
    try {
      // Get auth functions from the composable
      const { ensureValidToken, getToken, clearToken } = useAuthRefresh();
      const { ensureToken } = useCsrf();

      // Try to ensure we have a valid token
      const hasValidToken = await ensureValidToken();

      if (!hasValidToken) {
        // If we couldn't get a valid token, redirect to login but don't throw here
        console.warn(`Authentication check failed for URL: ${url}`);

        // For debugging in production
        console.log('URL requiring auth:', url);
        console.log('Is public path:', isPublicPath);
        console.log('Is non-API URL:', isNonApiUrl);

        // Use emergency logout to clear all user data
        const { emergencyLogout } = useLogout();
        emergencyLogout();

        // Return a rejected promise to prevent the original request
        return Promise.reject(new Error(`Authentication failed for ${url}. Please log in again.`));
      }

      // Add auth header with the current token
      const token = getToken();

      if (!options.headers) {
        options.headers = {};
      }

      options.headers.Authorization = `Bearer ${token}`;

      // Add CSRF token for state-changing operations
      if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(options.method?.toUpperCase())) {
        try {
          // Ensure we have a valid CSRF token
          const csrfToken = await ensureToken();

          if (csrfToken) {

            // Add the token to the header
            options.headers['X-CSRF-Token'] = csrfToken;

            // If there's a body, add the token there too
            if (options.body && typeof options.body === 'string') {
              try {
                const body = JSON.parse(options.body);
                body._csrf = csrfToken;
                options.body = JSON.stringify(body);
              } catch (e) {
                console.error('Error adding CSRF token to request body:', e);
              }
            } else if (options.body && typeof options.body === 'object' && !(options.body instanceof FormData)) {
              // Handle case where body is already an object
              options.body._csrf = csrfToken;
            } else if (options.body instanceof FormData) {
              // Handle FormData
              options.body.append('_csrf', csrfToken);
            } else if (!options.body) {
              // If no body exists, create one with the CSRF token
              options.body = { _csrf: csrfToken };

              // Ensure content type is set for JSON
              if (!options.headers['Content-Type']) {
                options.headers['Content-Type'] = 'application/json';
              }

              // Stringify if needed
              if (options.headers['Content-Type'] === 'application/json') {
                options.body = JSON.stringify(options.body);
              }
            }
          } else {
          }
        } catch (error) {
          console.error('Error handling CSRF token:', error);
        }
      }

      // Make the actual API call
      return originalFetch(url, options);
    } catch (error) {
      // Log the error
      console.error('Error in auth interceptor:', error);

      // Don't throw here, just log the error and proceed with the original request
      // This helps prevent errors during initial page load
      return originalFetch(url, options);
    }
  };
});