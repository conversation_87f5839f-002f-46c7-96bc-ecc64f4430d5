<template>
  <div>
    <!-- Subs Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Name
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Balance
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Contact
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="isLoading" class="animate-pulse">
              <td colspan="5" class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center justify-center">
                  <svg class="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="ml-2 text-sm text-gray-500">Loading subs...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="subsModels.length === 0">
              <td colspan="5" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                No subs found. Create your first sub to get started.
              </td>
            </tr>
            <tr
              v-for="sub in subsModels"
              :key="sub.id"
              class="hover:bg-gray-50 transition-colors duration-150 ease-in-out"
              :class="{ 'bg-yellow-50': selectedSubId === sub.id }"
              tabindex="0"
              @click="selectSub(sub.id)"
              @keydown.enter="viewSub(sub.id)"
              @keydown.arrow-up="navigateSub('up', sub.id)"
              @keydown.arrow-down="navigateSub('down', sub.id)"
            >
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ sub.name }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getBalanceClass(sub.balance)">
                {{ formatCurrency(sub.balance) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div v-if="sub.contactInfo?.phone">
                  {{ sub.contactInfo.phone }}
                </div>
                <div v-if="sub.contactInfo?.email" class="text-xs text-gray-400">
                  {{ sub.contactInfo.email }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="sub.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                >
                  {{ sub.isActive ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button
                  @click.stop="viewSub(sub.id)"
                  class="text-indigo-600 hover:text-indigo-900 mr-2"
                >
                  View
                </button>
                <button
                  @click.stop="editSub(sub.id)"
                  class="text-green-600 hover:text-green-900 mr-2"
                >
                  Edit
                </button>
                <button
                  @click.stop="confirmDelete(sub.id)"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
          <!-- Summary Footer -->
          <tfoot class="bg-gray-50">
            <tr>
              <td colspan="1" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                Total Balance:
              </td>
              <td class="px-6 py-3 text-left text-sm font-medium" :class="getBalanceClass(totalBalance)">
                {{ formatCurrency(totalBalance) }}
              </td>
              <td colspan="3"></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Confirm Delete</h3>
        </div>
        <div class="p-4">
          <p class="text-gray-700">Are you sure you want to delete this sub? This action cannot be undone.</p>
        </div>
        <div class="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="deleteSub(subToDelete)"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue';

export default {
  name: 'SubsList',

  props: {
    subsModels: {
      type: Array,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['view', 'edit', 'delete'],

  setup(props, { emit }) {
    // State
    const showDeleteModal = ref(false);
    const subToDelete = ref(null);
    const selectedSubId = ref(null);

    // Computed properties
    const totalBalance = computed(() => {
      return props.subsModels.reduce((sum, sub) => sum + (sub.balance || 0), 0);
    });

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount || 0);
    };

    const getBalanceClass = (balance) => {
      return balance < 0 ? 'text-red-600' : 'text-green-600';
    };

    const viewSub = (id) => {

      emit('view', id);
    };

    const editSub = (id) => {

      emit('edit', id);
    };

    const confirmDelete = (id) => {
      subToDelete.value = id;
      showDeleteModal.value = true;
    };

    const deleteSub = (id) => {

      emit('delete', id);
      showDeleteModal.value = false;
      subToDelete.value = null;
    };

    const selectSub = (id) => {
      selectedSubId.value = id;
    };

    const navigateSub = (direction, currentId) => {
      const currentIndex = props.subsModels.findIndex(sub => sub.id === currentId);

      if (direction === 'up' && currentIndex > 0) {
        selectedSubId.value = props.subsModels[currentIndex - 1].id;
      } else if (direction === 'down' && currentIndex < props.subsModels.length - 1) {
        selectedSubId.value = props.subsModels[currentIndex + 1].id;
      }
    };

    return {
      showDeleteModal,
      subToDelete,
      selectedSubId,
      totalBalance,
      formatCurrency,
      getBalanceClass,
      viewSub,
      editSub,
      confirmDelete,
      deleteSub,
      selectSub,
      navigateSub
    };
  }
};
</script>

<style scoped>
/* Add focus styles for keyboard navigation */
tr:focus {
  outline: 2px solid #4f46e5;
  outline-offset: -2px;
}
</style>
