<template>
  <div v-if="isOpen" class="fixed inset-0 z-10 overflow-y-auto">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <div class="fixed inset-0 transition-opacity" aria-hidden="true">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-4xl sm:w-full">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <h3 class="text-lg leading-6 font-medium text-gray-900">
                {{ isEditing ? 'Edit Transaction' : 'Add New Transaction' }}
              </h3>

              <div class="mt-4">
                <form @submit.prevent="submitForm">
                  <!-- CN Note Form -->
                  <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                    <div>
                      <label for="cnNumber" class="block text-sm font-medium text-gray-700">CN Number</label>
                      <input type="text" id="cnNumber" v-model="formData.cn_no" required
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                      <label for="cnDate" class="block text-sm font-medium text-gray-700">CN Date</label>
                      <input type="date" id="cnDate" v-model="formData.cn_date" required
                        class="mt-1 focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                    </div>

                    <div>
                      <label for="tradeType" class="block text-sm font-medium text-gray-700">Trade Type</label>
                      <select id="tradeType" v-model="formData.trade_type" required
                        class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md">
                        <option value="BUY">BUY</option>
                        <option value="SELL">SELL</option>
                      </select>
                    </div>
                  </div>

                  <!-- Records Table -->
                  <div class="mb-4">
                    <div class="flex justify-between items-center mb-2">
                      <h4 class="text-md font-medium text-gray-700">Transaction Records</h4>
                      <button type="button" @click="addRecord" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md shadow-sm text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Add Record
                      </button>
                    </div>

                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Purchase Date</th>
                            <th scope="col" class="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="(record, index) in recordsData" :key="index">
                            <td class="px-3 py-2">
                              <input type="text" v-model="record.symbol" required
                                class="focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </td>
                            <td class="px-3 py-2">
                              <input type="number" v-model.number="record.qnty" required min="1"
                                class="focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </td>
                            <td class="px-3 py-2">
                              <input type="number" v-model.number="record.price" required min="0" step="0.01"
                                class="focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </td>
                            <td class="px-3 py-2">
                              <input type="date" v-model="record.pdate" required
                                class="focus:ring-indigo-500 focus:border-indigo-500 block w-full shadow-sm sm:text-sm border-gray-300 rounded-md">
                            </td>
                            <td class="px-3 py-2">
                              <button type="button" @click="removeRecord(index)" class="text-red-600 hover:text-red-900">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                              </button>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>

                  <div class="mt-6 flex justify-end">
                    <button type="button" @click="$emit('close')" class="mr-3 inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                      Cancel
                    </button>
                    <button type="submit" :disabled="isSubmitting" class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                      <span v-if="isSubmitting">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                      </span>
                      <span v-else>{{ isEditing ? 'Update' : 'Submit' }}</span>
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { useNSEService } from '~/composables/nse/useNSEService';

const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  },
  editData: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'submitted']);

const { submitData, updateData } = useNSEService();

const isSubmitting = ref(false);
const isEditing = ref(false);

// Form data for CN Note
const formData = reactive({
  cn_no: '',
  cn_date: new Date().toISOString().substr(0, 10),
  trade_type: 'BUY'
});

// Records data for Folio entries
const recordsData = ref([]);

// Initialize with empty record
function addRecord() {
  recordsData.value.push({
    symbol: '',
    qnty: 1,
    price: 0,
    pdate: new Date().toISOString().substr(0, 10),
    cprice: 0, // Current price will be updated by the server
    cval: 0,   // Current value will be calculated by the server
    pl: 0      // Profit/Loss will be calculated by the server
  });
}

// Remove record at index
function removeRecord(index) {
  recordsData.value.splice(index, 1);
}

// Reset form
function resetForm() {
  formData.cn_number = '';
  formData.cn_date = new Date().toISOString().substr(0, 10);
  formData.trade_type = 'BUY';
  recordsData.value = [];
  addRecord(); // Add one empty record
}

// Submit form
async function submitForm() {
  if (recordsData.value.length === 0) {
    alert('Please add at least one record');
    return;
  }

  isSubmitting.value = true;

  try {
    if (isEditing.value) {
      await updateData(formData, recordsData.value);
    } else {
      await submitData(formData, recordsData.value);
    }

    emit('submitted');
    emit('close');
    resetForm();
  } catch (error) {
    console.error('Error submitting form:', error);
    alert('Failed to submit data. Please try again.');
  } finally {
    isSubmitting.value = false;
  }
}

// Watch for edit data changes
watch(() => props.editData, (newVal) => {
  if (newVal) {
    isEditing.value = true;

    // Populate form data
    formData.id = newVal._id;
    formData.cn_no = newVal.cn_no;
    formData.cn_date = new Date(newVal.cn_date).toISOString().substr(0, 10);
    formData.trade_type = newVal.trade_type;

    // Populate records data
    recordsData.value = newVal.Folio_rec.map(record => ({
      symbol: record.symbol,
      qnty: record.qnty,
      price: record.price,
      pdate: new Date(record.pdate).toISOString().substr(0, 10),
      cprice: record.cprice,
      cval: record.cval,
      pl: record.pl
    }));
  } else {
    isEditing.value = false;
    resetForm();
  }
}, { immediate: true });

// Initialize with one empty record
if (recordsData.value.length === 0 && !props.editData) {
  addRecord();
}
</script>
