import { navigateTo } from '#app'
import useUserRole from '~/composables/auth/useUserRole';
import { showAccessDenied } from '~/utils/accessDenied';

/**
 * Manager-only middleware
 * 
 * This middleware restricts access to manager routes to users with manager or admin roles only.
 * If a regular user tries to access a manager route, they will be redirected to the dashboard
 * with an error message.
 */
export default defineNuxtRouteMiddleware((to) => {
  // Skip middleware on server-side
  if (process.server) return;

  // Get user role
  const { hasRolePrivilege, ROLES } = useUserRole();

  // If user has manager or higher privileges, allow access
  if (hasRolePrivilege(ROLES.MANAGER)) return;

  // User is not a manager or admin, show error and redirect to dashboard
  showAccessDenied('Access denied: Manager privileges required');
  console.error(`Unauthorized access attempt to manager page: ${to.path}`);
  
  // Redirect to dashboard
  return navigateTo('/dashboard');
});
