@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for the expenses tracker */
html, body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
}

/* Add responsive table styles */
@media (max-width: 640px) {
  .responsive-table {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }
}

/* Add custom focus styles for accessibility */
*:focus-visible {
  outline: 2px solid #4f46e5;
  outline-offset: 2px;
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c7d2fe;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #818cf8;
}

/* Custom animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* Custom form styles */
.form-input:focus {
  box-shadow: 0 0 0 2px rgba(79, 70, 229, 0.2);
}

/* Custom button styles */
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
}

.btn-primary {
  @apply bg-indigo-600 text-white hover:bg-indigo-700 focus:ring-indigo-500;
}

.btn-secondary {
  @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-indigo-500;
}

.btn-danger {
  @apply bg-red-600 text-white hover:bg-red-700 focus:ring-red-500;
}

.btn-success {
  @apply bg-green-600 text-white hover:bg-green-700 focus:ring-green-500;
}

/* Custom card styles */
.card {
  @apply bg-white rounded-lg shadow overflow-hidden;
}

.card-header {
  @apply px-6 py-4 border-b border-gray-200;
}

.card-body {
  @apply p-6;
}

.card-footer {
  @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
}

/* Custom table styles */
.table-header {
  @apply bg-indigo-600 text-white;
}

.table-row-hover {
  @apply hover:bg-gray-50 transition-colors duration-150 ease-in-out;
}

.table-row-selected {
  @apply bg-yellow-50;
}

/* Custom modal styles */
.modal-overlay {
  @apply fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50;
}

.modal-container {
  @apply bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-screen overflow-y-auto;
}

.modal-header {
  @apply p-4 border-b border-gray-200 flex justify-between items-center;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply p-4 border-t border-gray-200 flex justify-end space-x-3;
}

/* Custom badge styles */
.badge {
  @apply px-2 inline-flex text-xs leading-5 font-semibold rounded-full;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-blue-100 text-blue-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

/* Custom tooltip styles */
.tooltip {
  @apply relative inline-block;
}

.tooltip .tooltip-text {
  @apply invisible absolute z-10 p-2 text-sm text-white bg-gray-900 rounded-md opacity-0 transition-opacity duration-300;
  width: 120px;
  bottom: 100%;
  left: 50%;
  margin-left: -60px;
  margin-bottom: 5px;
}

.tooltip:hover .tooltip-text {
  @apply visible opacity-100;
}

/* Custom chart styles */
.chart-container {
  @apply h-64;
}

/* Custom datalist styles */
datalist {
  display: none;
}

/* Custom responsive styles */
@media (max-width: 768px) {
  .hide-on-mobile {
    display: none;
  }
}

@media (min-width: 769px) {
  .show-on-mobile {
    display: none;
  }
}
