<template>
  <div class="container mx-auto px-4 py-8 mt-0">
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">Stock Items Report</h1>
      <div class="flex flex-col sm:flex-row items-center gap-2 mt-2 sm:mt-0">
        <div class="flex items-center space-x-2">
          <div class="relative">
            <input
              v-model="searchTerm"
              type="text"
              placeholder="Search items..."
              class="pl-10 pr-4 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              @keyup.enter="fetchStockReport"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
            </div>
          </div>
          <button
            @click="fetchStockReport"
            class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
          >
            Search
          </button>
        </div>

        <div class="flex items-center space-x-2 mt-2 sm:mt-0">
          <label for="limit-select" class="text-sm text-gray-700">Show:</label>
          <select
            id="limit-select"
            v-model.number="pagination.limit"
            @change="changeLimit"
            class="border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 text-sm py-2"
          >
            <option :value="25">25</option>
            <option :value="50">50</option>
            <option :value="100">100</option>
            <option :value="200">200</option>
          </select>
        </div>

        <div class="flex flex-col sm:flex-row items-center gap-2 mt-2 sm:mt-0">
          <div class="flex items-center space-x-2">
            <input
              id="include-history"
              type="checkbox"
              v-model="includeHistory"
              class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
            />
            <label for="include-history" class="text-sm text-gray-700">Include history</label>
          </div>

          <button
            @click="exportToExcel"
            :disabled="isExporting || !stockItems.length"
            class="flex items-center space-x-1 bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md shadow-sm text-sm focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ExcelIcon width="20" height="20" />
            <span>{{ isExporting ? 'Exporting...' : 'Export to Excel' }}</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md mb-6">
      <p>{{ error }}</p>
    </div>

    <!-- No results -->
    <div v-else-if="!stockItems.length" class="bg-gray-50 border border-gray-200 text-gray-700 px-4 py-12 rounded-md mb-6 text-center">
      <p class="text-lg">No stock items found.</p>
      <p class="text-sm text-gray-500 mt-2">Try adjusting your search criteria.</p>
    </div>

    <!-- Results -->
    <div v-else class="bg-white shadow-md rounded-lg overflow-hidden mb-6">
      <!-- Stock items table -->
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part No</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Batch</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">HSN</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">UOM</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Transaction</th>
              <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in stockItems" :key="item._id" class="hover:bg-gray-50">
              <td class="px-3 py-2 whitespace-nowrap">
                <div class="font-medium text-gray-900">{{ item.item }}</div>
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                {{ item.pno || 'N/A' }}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                {{ item.batch || 'N/A' }}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                {{ item.hsn }}
              </td>
              <td class="px-3 py-2 whitespace-nowrap">
                <span
                  :class="{
                    'text-red-600': item.qty <= 5,
                    'text-yellow-600': item.qty > 5 && item.qty <= 20,
                    'text-green-600': item.qty > 20
                  }"
                  class="font-medium"
                >
                  {{ item.qty }}
                  <span class="text-xs ml-1">({{ item.qty <= 5 ? 'Low' : item.qty <= 20 ? 'Medium' : 'High' }})</span>
                </span>
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                {{ item.uom }}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                ₹{{ item.rate.toFixed(2) }}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                ₹{{ (item.qty * item.rate).toFixed(2) }}
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm">
                <div v-if="item.history && item.history.length > 0">
                  <span
                    class="px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full"
                    :class="{
                      'bg-green-100 text-green-800': item.history[0].type === 'PURCHASE' || item.history[0].type === 'CREDIT NOTE',
                      'bg-red-100 text-red-800': item.history[0].type === 'SALES' || item.history[0].type === 'DEBIT NOTE'
                    }"
                  >
                    {{ item.history[0].type }}
                  </span>
                  <div class="text-xs mt-1">
                    {{ formatDate(item.history[0].bdate) }} | {{ item.history[0].bno }}
                  </div>
                </div>
                <div v-else class="text-xs text-gray-400">No transactions</div>
              </td>
              <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-500">
                <button
                  v-if="item.history && item.history.length > 0"
                  @click="showAllTransactions(item)"
                  class="text-indigo-600 hover:text-indigo-900 focus:outline-none text-sm"
                >
                  View History
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div v-if="pagination.pages > 1" class="px-4 py-3 bg-gray-50 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="flex-1 flex justify-between sm:hidden">
            <button
              @click="prevPage"
              :disabled="pagination.page === 1"
              :class="[
                pagination.page === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50',
                'relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md'
              ]"
            >
              Previous
            </button>
            <button
              @click="nextPage"
              :disabled="pagination.page === pagination.pages"
              :class="[
                pagination.page === pagination.pages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-700 hover:bg-gray-50',
                'ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md'
              ]"
            >
              Next
            </button>
          </div>
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing
                <span class="font-medium">{{ (pagination.page - 1) * pagination.limit + 1 }}</span>
                to
                <span class="font-medium">
                  {{ Math.min(pagination.page * pagination.limit, pagination.total) }}
                </span>
                of
                <span class="font-medium">{{ pagination.total }}</span>
                results
              </p>
            </div>
            <div>
              <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <button
                  @click="prevPage"
                  :disabled="pagination.page === 1"
                  :class="[
                    pagination.page === 1 ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 text-sm font-medium'
                  ]"
                >
                  <span class="sr-only">Previous</span>
                  <ChevronLeftIcon class="h-5 w-5" aria-hidden="true" />
                </button>
                <button
                  v-for="pageNum in paginationRange"
                  :key="pageNum"
                  @click="goToPage(pageNum)"
                  :class="[
                    pageNum === pagination.page
                      ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-4 py-2 border text-sm font-medium'
                  ]"
                >
                  {{ pageNum }}
                </button>
                <button
                  @click="nextPage"
                  :disabled="pagination.page === pagination.pages"
                  :class="[
                    pagination.page === pagination.pages ? 'bg-gray-100 text-gray-400 cursor-not-allowed' : 'bg-white text-gray-500 hover:bg-gray-50',
                    'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 text-sm font-medium'
                  ]"
                >
                  <span class="sr-only">Next</span>
                  <ChevronRightIcon class="h-5 w-5" aria-hidden="true" />
                </button>
              </nav>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Transaction History Modal -->
    <div v-if="showTransactionModal" class="fixed inset-0 z-50 overflow-y-auto">
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="showTransactionModal = false"></div>

      <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-4xl">
          <div class="flex justify-between items-center bg-indigo-600 px-4 py-2">
            <h3 class="text-base font-semibold text-white">
              Transaction History for {{ selectedItem?.item }}
            </h3>
            <button
              @click="showTransactionModal = false"
              class="text-white hover:text-gray-200 focus:outline-none"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
              </svg>
            </button>
          </div>

          <div class="bg-white p-2">
            <div class="overflow-x-auto max-h-[70vh]">
              <table class="min-w-full divide-y divide-gray-200 text-sm">
                <thead class="bg-gray-50 sticky top-0">
                  <tr>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Bill No</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Party</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Qty</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total</th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(transaction, index) in selectedItem?.history" :key="transaction._id"
                      :class="{'bg-gray-50': index % 2 === 0, 'hover:bg-indigo-50': true}">
                    <td class="px-2 py-1 whitespace-nowrap">
                      <span
                        class="px-2 py-0.5 inline-flex text-xs leading-5 font-semibold rounded-full"
                        :class="{
                          'bg-green-100 text-green-800': transaction.type === 'PURCHASE' || transaction.type === 'CREDIT NOTE',
                          'bg-red-100 text-red-800': transaction.type === 'SALES' || transaction.type === 'DEBIT NOTE'
                        }"
                      >
                        {{ transaction.type }}
                      </span>
                    </td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs">{{ formatDate(transaction.bdate) }}</td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs">{{ transaction.bno }}</td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs">{{ transaction.supply }}</td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs">
                      <span
                        :class="{
                          'text-green-600': transaction.type === 'PURCHASE' || transaction.type === 'CREDIT NOTE',
                          'text-red-600': transaction.type === 'SALES' || transaction.type === 'DEBIT NOTE'
                        }"
                      >
                        {{ transaction.type === 'PURCHASE' || transaction.type === 'CREDIT NOTE' ? '+' : '-' }}{{ transaction.qty }}
                      </span>
                    </td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs">₹{{ transaction.rate.toFixed(2) }}</td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs">₹{{ transaction.total.toFixed(2) }}</td>
                    <td class="px-2 py-1 whitespace-nowrap text-xs font-medium">
                      {{ transaction.qtyh }} {{ selectedItem?.uom }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <div class="bg-gray-50 px-4 py-2 flex justify-between items-center">
            <div class="text-xs text-gray-500">
              Showing {{ selectedItem?.history?.length || 0 }} transactions
            </div>
            <button
              type="button"
              class="inline-flex justify-center rounded-md bg-indigo-600 px-3 py-1.5 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus:outline-none"
              @click="showTransactionModal = false"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { MagnifyingGlassIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/vue/24/outline';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import ExcelIcon from '~/components/icons/ExcelIcon.vue';

// Set page title
usePageTitle('Stock Items Report', 'View detailed stock items report with transaction history');

// State
const isLoading = ref(false);
const isExporting = ref(false);
const error = ref(null);
const stockItems = ref([]);
const searchTerm = ref('');
const pagination = ref({
  total: 0,
  page: 1,
  limit: 50, // Increased from 24 to 50 for table view
  pages: 0
});
const showTransactionModal = ref(false);
const selectedItem = ref(null);
const includeHistory = ref(true);

// Computed
const paginationRange = computed(() => {
  const range = [];
  const maxPages = 5;
  const currentPage = pagination.value.page;
  const totalPages = pagination.value.pages;

  if (totalPages <= maxPages) {
    // Show all pages if there are fewer than maxPages
    for (let i = 1; i <= totalPages; i++) {
      range.push(i);
    }
  } else {
    // Always include first page
    range.push(1);

    // Calculate start and end of range
    let start = Math.max(2, currentPage - 1);
    let end = Math.min(totalPages - 1, currentPage + 1);

    // Adjust if at the beginning
    if (currentPage <= 2) {
      end = 4;
    }

    // Adjust if at the end
    if (currentPage >= totalPages - 1) {
      start = totalPages - 3;
    }

    // Add ellipsis if needed
    if (start > 2) {
      range.push('...');
    }

    // Add middle pages
    for (let i = start; i <= end; i++) {
      range.push(i);
    }

    // Add ellipsis if needed
    if (end < totalPages - 1) {
      range.push('...');
    }

    // Always include last page
    range.push(totalPages);
  }

  return range;
});

// Methods
const fetchStockReport = async () => {
  try {
    isLoading.value = true;
    error.value = null;

    const api = useApiWithAuth();
    const params = {
      page: pagination.value.page,
      limit: pagination.value.limit
    };

    if (searchTerm.value) {
      params.search = searchTerm.value;
    }

    const response = await api.get('/api/inventory/stock-report', { params });

    stockItems.value = response.stocks;
    pagination.value = response.pagination;
  } catch (err) {
    console.error('Error fetching stock report:', err);
    error.value = err.message || 'Failed to fetch stock report';
  } finally {
    isLoading.value = false;
  }
};

const prevPage = () => {
  if (pagination.value.page > 1) {
    pagination.value.page--;
    fetchStockReport();
  }
};

const nextPage = () => {
  if (pagination.value.page < pagination.value.pages) {
    pagination.value.page++;
    fetchStockReport();
  }
};

const goToPage = (page) => {
  if (typeof page === 'number' && page !== pagination.value.page) {
    pagination.value.page = page;
    fetchStockReport();
  }
};

const changeLimit = () => {
  pagination.value.page = 1; // Reset to first page when changing limit
  fetchStockReport();
};

const formatDate = (date) => {
  if (!date) return 'N/A';
  return new Date(date).toLocaleDateString();
};

const showAllTransactions = (item) => {
  selectedItem.value = item;
  showTransactionModal.value = true;
};

const exportToExcel = async () => {
  if (isExporting.value) return;

  try {
    isExporting.value = true;

    const api = useApiWithAuth();
    const params = {
      search: searchTerm.value || '',
      includeHistory: includeHistory.value
    };

    // Use fetchWithAuth for binary data download
    const response = await api.fetchWithAuth('/api/inventory/export/excel', {
      method: 'GET',
      params,
      responseType: 'blob'
    });

    // Create a blob from the response
    const blob = new Blob([response], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    });

    // Create a URL for the blob
    const url = window.URL.createObjectURL(blob);

    // Create a temporary link and trigger download
    const link = document.createElement('a');
    link.href = url;
    link.setAttribute('download', `Stock_Items_Report_${new Date().toISOString().split('T')[0]}.xlsx`);
    document.body.appendChild(link);
    link.click();

    // Clean up
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

  } catch (error) {
    console.error('Error exporting to Excel:', error);
    alert('Failed to export data. Please try again.');
  } finally {
    isExporting.value = false;
  }
};

// Lifecycle hooks
onMounted(() => {
  fetchStockReport();
});
</script>

<style scoped>
/* Add any component-specific styles here */
</style>
