<template>
  <div v-if="isOpen" class="fixed inset-0 z-[9999] overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <!-- Background overlay -->
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal"></div>
    
    <!-- Modal panel -->
    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
      <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all w-full mx-auto" 
           :class="{ 
             'max-w-sm md:max-w-md lg:max-w-lg xl:max-w-2xl': isMobile, 
             'max-w-2xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl': !isMobile 
           }"
           @click.stop>
        
        <!-- Modal header -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 px-4 py-3 sm:px-6">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium leading-6 text-white" id="modal-title">
              <CogIcon class="inline-block w-6 h-6 mr-2" />
              Bill Configuration Settings
            </h3>
            <button
              type="button"
              class="rounded-md bg-white bg-opacity-20 p-2 text-white hover:bg-opacity-30 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-blue-600"
              @click="closeModal"
            >
              <span class="sr-only">Close</span>
              <XMarkIcon class="h-6 w-6" aria-hidden="true" />
            </button>
          </div>
        </div>

        <!-- Modal body -->
        <div class="bg-white px-4 py-5 sm:p-6" :class="{ 'max-h-[70vh] overflow-y-auto': isMobile, 'max-h-[80vh] overflow-y-auto': !isMobile }">
          <!-- Tab navigation -->
          <div class="border-b border-gray-200 mb-6">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
              <button
                v-for="tab in tabs"
                :key="tab.id"
                @click="activeTab = tab.id"
                :class="[
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300',
                  'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm'
                ]"
              >
                <component :is="tab.icon" class="w-5 h-5 inline-block mr-2" />
                <span v-html="formatTabName(tab.name, tab.shortcut)"></span>
              </button>
            </nav>
          </div>

          <!-- Tab content -->
          <div class="space-y-6">
            <!-- Features Tab -->
            <div v-if="activeTab === 'features'" class="space-y-6">
              <div class="grid grid-cols-1 gap-6">
                <!-- Item Narration Modal Features -->
                <div class="bg-blue-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-blue-700 mb-4 flex items-center">
                    <DocumentTextIcon class="inline-block w-5 h-5 mr-2" />
                    Item Narration Modal
                  </h4>

                  <div class="space-y-4">
                    <!-- Enable/Disable Item Narration Feature -->
                    <div>
                      <h5 class="text-sm font-medium text-gray-700 mb-2">Enable Item Narration Feature</h5>
                      <div class="flex space-x-4">
                        <label class="flex items-center">
                          <input
                            v-model="config.features.itemNarration.enabled"
                            type="radio"
                            :value="true"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          >
                          <span class="ml-2 text-sm text-gray-700">Yes</span>
                        </label>
                        <label class="flex items-center">
                          <input
                            v-model="config.features.itemNarration.enabled"
                            type="radio"
                            :value="false"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          >
                          <span class="ml-2 text-sm text-gray-700">No</span>
                        </label>
                      </div>
                      <p class="text-xs text-gray-500 mt-1">Allow adding detailed narration/description for individual items</p>
                    </div>

                    <!-- Show Item Narration Modal -->
                    <div :class="{ 'opacity-50': !config.features.itemNarration.enabled }">
                      <h5 class="text-sm font-medium text-gray-700 mb-2">Show Item Narration Modal</h5>
                      <div class="flex space-x-4">
                        <label class="flex items-center">
                          <input
                            v-model="config.features.itemNarration.showModal"
                            type="radio"
                            :value="true"
                            :disabled="!config.features.itemNarration.enabled"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          >
                          <span class="ml-2 text-sm text-gray-700">Yes</span>
                        </label>
                        <label class="flex items-center">
                          <input
                            v-model="config.features.itemNarration.showModal"
                            type="radio"
                            :value="false"
                            :disabled="!config.features.itemNarration.enabled"
                            class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                          >
                          <span class="ml-2 text-sm text-gray-700">No</span>
                        </label>
                      </div>
                      <p class="text-xs text-gray-500 mt-1">Display modal dialog for entering item narration</p>
                    </div>
                  </div>
                </div>

                <!-- Order & Dispatch Details Modal Features -->
                <div class="bg-green-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-green-700 mb-4 flex items-center">
                    <TruckIcon class="inline-block w-5 h-5 mr-2" />
                    Order & Dispatch Details Modal
                  </h4>

                  <div class="space-y-4">
                    <!-- Enable/Disable Order & Dispatch Feature -->
                    <div>
                      <h5 class="text-sm font-medium text-gray-700 mb-2">Enable Order & Dispatch Feature</h5>
                      <div class="flex space-x-4">
                        <label class="flex items-center">
                          <input
                            v-model="config.features.orderDispatch.enabled"
                            type="radio"
                            :value="true"
                            class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                          >
                          <span class="ml-2 text-sm text-gray-700">Yes</span>
                        </label>
                        <label class="flex items-center">
                          <input
                            v-model="config.features.orderDispatch.enabled"
                            type="radio"
                            :value="false"
                            class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                          >
                          <span class="ml-2 text-sm text-gray-700">No</span>
                        </label>
                      </div>
                      <p class="text-xs text-gray-500 mt-1">Enable order and dispatch tracking functionality</p>
                    </div>

                    <!-- Order & Dispatch Sub-features -->
                    <div :class="{ 'opacity-50': !config.features.orderDispatch.enabled }" class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <!-- Show Order Information -->
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-2">Show Order Information</h5>
                        <div class="flex space-x-4">
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showOrderInfo"
                              type="radio"
                              :value="true"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">Yes</span>
                          </label>
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showOrderInfo"
                              type="radio"
                              :value="false"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">No</span>
                          </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Order number and date fields</p>
                      </div>

                      <!-- Show Dispatch Information -->
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-2">Show Dispatch Information</h5>
                        <div class="flex space-x-4">
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showDispatchInfo"
                              type="radio"
                              :value="true"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">Yes</span>
                          </label>
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showDispatchInfo"
                              type="radio"
                              :value="false"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">No</span>
                          </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Dispatch through and docket number</p>
                      </div>

                      <!-- Show Vehicle Number -->
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-2">Show Vehicle Number</h5>
                        <div class="flex space-x-4">
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showVehicleNumber"
                              type="radio"
                              :value="true"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">Yes</span>
                          </label>
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showVehicleNumber"
                              type="radio"
                              :value="false"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">No</span>
                          </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Vehicle registration number field</p>
                      </div>

                      <!-- Show Consignee Details -->
                      <div>
                        <h5 class="text-sm font-medium text-gray-700 mb-2">Show Consignee Details</h5>
                        <div class="flex space-x-4">
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showConsigneeDetails"
                              type="radio"
                              :value="true"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">Yes</span>
                          </label>
                          <label class="flex items-center">
                            <input
                              v-model="config.features.orderDispatch.showConsigneeDetails"
                              type="radio"
                              :value="false"
                              :disabled="!config.features.orderDispatch.enabled"
                              class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300"
                            >
                            <span class="ml-2 text-sm text-gray-700">No</span>
                          </label>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Consignee name and address fields</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Display Settings Tab -->
            <div v-if="activeTab === 'display'" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Column Visibility -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-900 mb-3">
                    <EyeIcon class="inline-block w-5 h-5 mr-2" />
                    Column Visibility
                  </h4>
                  <div class="grid grid-cols-2 gap-3">
                    <!-- Optional Columns -->
                    <div class="space-y-3">
                      <h5 class="text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">Optional Columns</h5>
                      <div class="flex items-center">
                        <input v-model="config.display.showMRP" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">MRP</label>
                      </div>
                      <div class="flex items-center">
                        <input v-model="config.display.showExpiryDate" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">Expiry Date</label>
                      </div>
                      <div class="flex items-center">
                        <input v-model="config.display.showProject" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">Project</label>
                      </div>
                      <div class="flex items-center">
                        <input v-model="config.display.showDiscount" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">Discount (%)</label>
                      </div>
                    </div>

                    <!-- Tax Columns -->
                    <div class="space-y-3">
                      <h5 class="text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">Tax Columns</h5>
                      <div class="flex items-center">
                        <input v-model="config.display.showCGST" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">CGST</label>
                      </div>
                      <div class="flex items-center">
                        <input v-model="config.display.showSGST" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">SGST</label>
                      </div>
                      <div class="flex items-center">
                        <input v-model="config.display.showIGST" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <label class="ml-2 block text-sm text-gray-900">IGST</label>
                      </div>
                    </div>
                  </div>

                  <!-- Required Columns (Read-only) -->
                  <div class="mt-4 pt-3 border-t border-gray-300">
                    <h5 class="text-sm font-medium text-gray-500 mb-2">Required Columns (Always Visible)</h5>
                    <div class="grid grid-cols-3 gap-2 text-xs text-gray-500">
                      <span class="flex items-center">
                        <input type="checkbox" checked disabled class="h-3 w-3 text-gray-400 border-gray-300 rounded opacity-50 mr-1">
                        Item
                      </span>
                      <span class="flex items-center">
                        <input type="checkbox" checked disabled class="h-3 w-3 text-gray-400 border-gray-300 rounded opacity-50 mr-1">
                        Rate
                      </span>
                      <span class="flex items-center">
                        <input type="checkbox" checked disabled class="h-3 w-3 text-gray-400 border-gray-300 rounded opacity-50 mr-1">
                        Qty
                      </span>
                      <span class="flex items-center">
                        <input type="checkbox" checked disabled class="h-3 w-3 text-gray-400 border-gray-300 rounded opacity-50 mr-1">
                        UOM
                      </span>
                      <span class="flex items-center">
                        <input type="checkbox" checked disabled class="h-3 w-3 text-gray-400 border-gray-300 rounded opacity-50 mr-1">
                        GST (%)
                      </span>
                      <span class="flex items-center">
                        <input type="checkbox" checked disabled class="h-3 w-3 text-gray-400 border-gray-300 rounded opacity-50 mr-1">
                        Total
                      </span>
                    </div>
                  </div>
                </div>

                <!-- Table Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-900 mb-3">
                    <TableCellsIcon class="inline-block w-5 h-5 mr-2" />
                    Table Settings
                  </h4>
                  <div class="space-y-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Rows per page</label>
                      <select v-model="config.display.rowsPerPage" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="10">10</option>
                        <option value="25">25</option>
                        <option value="50">50</option>
                        <option value="100">100</option>
                      </select>
                    </div>
                    <div class="flex items-center">
                      <input v-model="config.display.compactMode" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                      <label class="ml-2 block text-sm text-gray-900">Compact table mode</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Behavior Tab -->
            <div v-if="activeTab === 'behavior'" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Auto-save Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-900 mb-3">
                    <DocumentArrowDownIcon class="inline-block w-5 h-5 mr-2" />
                    Auto-save Settings
                  </h4>
                  <div class="space-y-3">
                    <div class="flex items-center">
                      <input v-model="config.behavior.autoSave" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                      <label class="ml-2 block text-sm text-gray-900">Enable auto-save</label>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Auto-save interval (seconds)</label>
                      <input v-model.number="config.behavior.autoSaveInterval" type="number" min="30" max="600" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                  </div>
                </div>

                <!-- Validation Settings -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-900 mb-3">
                    <ShieldCheckIcon class="inline-block w-5 h-5 mr-2" />
                    Validation Settings
                  </h4>
                  <div class="space-y-3">
                    <div class="flex items-center">
                      <input v-model="config.behavior.strictValidation" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                      <label class="ml-2 block text-sm text-gray-900">Strict validation</label>
                    </div>
                    <div class="flex items-center">
                      <input v-model="config.behavior.showWarnings" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                      <label class="ml-2 block text-sm text-gray-900">Show validation warnings</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Shortcuts Tab -->
            <div v-if="activeTab === 'shortcuts'" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Section Navigation -->
                <div class="bg-blue-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-blue-700 mb-3 flex items-center">
                    <CommandLineIcon class="inline-block w-5 h-5 mr-2" />
                    Section Navigation
                  </h4>
                  <ul class="space-y-2">
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Jump to Bill Info</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + 1</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Jump to Party Details</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + 2</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Jump to Amount Details</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + 3</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Jump to Stock Items</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + 4</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Jump to Other Charges</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + 5</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Jump to Action Buttons</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + 6</kbd>
                    </li>
                  </ul>
                </div>

                <!-- Form Actions -->
                <div class="bg-green-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-green-700 mb-3 flex items-center">
                    <DocumentArrowDownIcon class="inline-block w-5 h-5 mr-2" />
                    Form Actions
                  </h4>
                  <ul class="space-y-2">
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Submit Form</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + S</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Reset Form</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + R</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Print Invoice</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + P</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Create New Invoice</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + N</kbd>
                    </li>
                  </ul>
                </div>

                <!-- Table Operations -->
                <div class="bg-purple-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-purple-700 mb-3 flex items-center">
                    <TableCellsIcon class="inline-block w-5 h-5 mr-2" />
                    Table Operations
                  </h4>
                  <ul class="space-y-2">
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Add New Stock Item</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + A</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Add Other Charges</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + O</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Delete Current Row</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + D</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Edit Current Row</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + E</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Navigate Table Cells</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">↑ ↓ ← →</kbd>
                    </li>
                  </ul>
                </div>

                <!-- Special Shortcuts -->
                <div class="bg-amber-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-amber-700 mb-3 flex items-center">
                    <CogIcon class="inline-block w-5 h-5 mr-2" />
                    Special Shortcuts
                  </h4>
                  <ul class="space-y-2">
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Create New Party/Item</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + C</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">View Stock History</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Alt + V</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Edit Selected Party/Item</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">Ctrl + Enter</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700">Show Help (Legacy)</span>
                      <kbd class="bg-gray-200 px-2 py-1 rounded text-xs">F1</kbd>
                    </li>
                    <li class="flex items-center justify-between">
                      <span class="text-sm text-gray-700 font-medium">Configuration</span>
                      <kbd class="bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium">F11</kbd>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <!-- Printing Tab -->
            <div v-if="activeTab === 'printing'" class="space-y-6">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <!-- Page Setup -->
                <div class="bg-blue-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-blue-700 mb-4 flex items-center">
                    <PrinterIcon class="inline-block w-5 h-5 mr-2" />
                    Page Setup
                  </h4>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Paper Size</label>
                      <select v-model="config.printing.paperSize" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="A4">A4 (210 × 297 mm)</option>
                        <option value="A3">A3 (297 × 420 mm)</option>
                        <option value="A5">A5 (148 × 210 mm)</option>
                        <option value="Letter">Letter (8.5 × 11 in)</option>
                        <option value="Legal">Legal (8.5 × 14 in)</option>
                        <option value="Tabloid">Tabloid (11 × 17 in)</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Orientation</label>
                      <select v-model="config.printing.orientation" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="portrait">Portrait</option>
                        <option value="landscape">Landscape</option>
                      </select>
                    </div>
                    <div class="grid grid-cols-2 gap-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Scale (%)</label>
                        <input v-model.number="config.printing.scale" type="number" min="25" max="200" step="5" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Copies</label>
                        <input v-model.number="config.printing.copies" type="number" min="1" max="99" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      </div>
                    </div>
                    <div class="flex items-center space-x-4">
                      <label class="flex items-center">
                        <input v-model="config.printing.fitToPage" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-900">Fit to page</span>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- Margins -->
                <div class="bg-green-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-green-700 mb-4 flex items-center">
                    <DocumentArrowDownIcon class="inline-block w-5 h-5 mr-2" />
                    Margins (mm)
                  </h4>
                  <div class="grid grid-cols-2 gap-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Top</label>
                      <input v-model.number="config.printing.margins.top" type="number" min="0" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Bottom</label>
                      <input v-model.number="config.printing.margins.bottom" type="number" min="0" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Left</label>
                      <input v-model.number="config.printing.margins.left" type="number" min="0" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Right</label>
                      <input v-model.number="config.printing.margins.right" type="number" min="0" max="50" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                    </div>
                  </div>
                </div>

                <!-- Print Quality & Options -->
                <div class="bg-purple-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-purple-700 mb-4 flex items-center">
                    <CogIcon class="inline-block w-5 h-5 mr-2" />
                    Quality & Options
                  </h4>
                  <div class="space-y-4">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Print Quality</label>
                      <select v-model="config.printing.quality" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="draft">Draft (Fast)</option>
                        <option value="normal">Normal</option>
                        <option value="high">High Quality</option>
                        <option value="best">Best Quality</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Color Mode</label>
                      <select v-model="config.printing.colorMode" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="color">Color</option>
                        <option value="grayscale">Grayscale</option>
                        <option value="blackwhite">Black & White</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Duplex Printing</label>
                      <select v-model="config.printing.duplex" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500">
                        <option value="none">Single-sided</option>
                        <option value="long">Double-sided (Long edge)</option>
                        <option value="short">Double-sided (Short edge)</option>
                      </select>
                    </div>
                    <div class="space-y-2">
                      <label class="flex items-center">
                        <input v-model="config.printing.printBackground" type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-900">Print background colors/images</span>
                      </label>
                      <label class="flex items-center">
                        <input v-model="config.printing.printHeaders" type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-900">Print headers</span>
                      </label>
                      <label class="flex items-center">
                        <input v-model="config.printing.printFooters" type="checkbox" class="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded">
                        <span class="ml-2 text-sm text-gray-900">Print footers</span>
                      </label>
                    </div>
                  </div>
                </div>

                <!-- Watermark Settings -->
                <div class="bg-orange-50 p-4 rounded-lg">
                  <h4 class="text-lg font-semibold text-orange-700 mb-4 flex items-center">
                    <ShieldCheckIcon class="inline-block w-5 h-5 mr-2" />
                    Watermark
                  </h4>
                  <div class="space-y-4">
                    <label class="flex items-center">
                      <input v-model="config.printing.watermark.enabled" type="checkbox" class="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded">
                      <span class="ml-2 text-sm font-medium text-gray-900">Enable watermark</span>
                    </label>
                    <div v-if="config.printing.watermark.enabled" class="space-y-3">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Watermark Text</label>
                        <input v-model="config.printing.watermark.text" type="text" placeholder="e.g., DRAFT, CONFIDENTIAL" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500">
                      </div>
                      <div class="grid grid-cols-2 gap-3">
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-1">Opacity</label>
                          <input v-model.number="config.printing.watermark.opacity" type="range" min="0.1" max="1" step="0.1" class="w-full">
                          <span class="text-xs text-gray-500">{{ Math.round(config.printing.watermark.opacity * 100) }}%</span>
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-1">Font Size</label>
                          <input v-model.number="config.printing.watermark.fontSize" type="number" min="12" max="72" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500">
                        </div>
                      </div>
                      <div class="grid grid-cols-2 gap-3">
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-1">Color</label>
                          <input v-model="config.printing.watermark.color" type="color" class="w-full h-10 border border-gray-300 rounded-md">
                        </div>
                        <div>
                          <label class="block text-sm font-medium text-gray-700 mb-1">Rotation (degrees)</label>
                          <input v-model.number="config.printing.watermark.rotation" type="number" min="-90" max="90" step="15" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500">
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Page Break Settings -->
              <div class="bg-gray-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-gray-700 mb-4 flex items-center">
                  <DocumentTextIcon class="inline-block w-5 h-5 mr-2" />
                  Page Break Settings
                </h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <label class="flex items-center">
                    <input v-model="config.printing.pageBreaks.avoidBreakingItems" type="checkbox" class="h-4 w-4 text-gray-600 focus:ring-gray-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-900">Avoid breaking items across pages</span>
                  </label>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Minimum lines per page</label>
                    <input v-model.number="config.printing.pageBreaks.minimumLinesPerPage" type="number" min="1" max="20" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-500 focus:border-gray-500">
                  </div>
                </div>
              </div>

              <!-- Print Template Selection -->
              <div class="bg-purple-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-purple-700 mb-4 flex items-center">
                  <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-5L9 9a2 2 0 00-2 2v10z"></path>
                  </svg>
                  Print Template
                </h4>
                <div class="space-y-4">
                  <div class="p-4 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                      <svg class="w-5 h-5 text-green-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                      </svg>
                      <div>
                        <div class="text-sm font-medium text-green-900">Using Your Original PDF System</div>
                        <div class="text-xs text-green-700">Your professional invoice template is active and working perfectly</div>
                      </div>
                    </div>
                  </div>

                  <!-- Template Customization -->
                  <div class="border-t pt-4">
                    <h5 class="text-sm font-medium text-gray-700 mb-3">Template Colors</h5>
                    <div class="grid grid-cols-3 gap-4">
                      <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">Primary Color</label>
                        <input v-model="config.printing.template.customization.colors.primary" type="color" class="w-full h-8 border border-gray-300 rounded">
                      </div>
                      <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">Secondary Color</label>
                        <input v-model="config.printing.template.customization.colors.secondary" type="color" class="w-full h-8 border border-gray-300 rounded">
                      </div>
                      <div>
                        <label class="block text-xs font-medium text-gray-600 mb-1">Text Color</label>
                        <input v-model="config.printing.template.customization.colors.text" type="color" class="w-full h-8 border border-gray-300 rounded">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Custom Headers/Footers -->
              <div class="bg-indigo-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-indigo-700 mb-4 flex items-center">
                  <DocumentTextIcon class="inline-block w-5 h-5 mr-2" />
                  Custom Headers & Footers
                </h4>
                <div class="space-y-4">
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom Header</label>
                    <textarea v-model="config.printing.customHeader" rows="2" placeholder="Custom header text (supports variables: {date}, {time}, {page})" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                  </div>
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Custom Footer</label>
                    <textarea v-model="config.printing.customFooter" rows="2" placeholder="Custom footer text (supports variables: {date}, {time}, {page})" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"></textarea>
                  </div>
                  <div class="text-xs text-gray-500">
                    <strong>Available variables:</strong> {date}, {time}, {page}, {totalPages}, {company}, {user}
                  </div>
                </div>
              </div>

              <!-- Bank Details Configuration -->
              <div class="bg-green-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-green-700 mb-4 flex items-center">
                  <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                  </svg>
                  Bank Details
                </h4>
                <div class="space-y-4">
                  <label class="flex items-center">
                    <input v-model="config.printing.bankDetails.enabled" type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-900">Include bank details on invoice</span>
                  </label>

                  <div v-if="config.printing.bankDetails.enabled" class="space-y-4 pl-6 border-l-2 border-green-200">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Select Bank Account</label>
                      <select v-model="config.printing.bankDetails.selectedBankId" @change="handleBankSelection" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        <option value="">Select a bank account</option>
                        <option v-for="bank in bankLedgers" :key="bank.id" :value="bank.id">
                          {{ bank.name }} ({{ bank.bankDetails?.accountNumber || 'No Account Number' }})
                        </option>
                      </select>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Bank Name</label>
                        <input v-model="config.printing.bankDetails.bankName" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Account Number</label>
                        <input v-model="config.printing.bankDetails.accountNumber" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                      </div>
                      <div class="relative">
                        <label class="block text-sm font-medium text-gray-700 mb-1">IFSC Code</label>
                        <input v-model="config.printing.bankDetails.ifscCode" @input="handleIfscInput" type="text" maxlength="11" placeholder="e.g., SBIN0001234" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                        <div v-if="fetchingBankDetails" class="absolute right-3 top-8 transform">
                          <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-green-500"></div>
                        </div>
                        <div v-if="ifscError" class="text-red-500 text-xs mt-1">{{ ifscError }}</div>
                        <div v-if="ifscValid && !fetchingBankDetails" class="text-green-500 text-xs mt-1">✓ Valid IFSC - Bank details auto-filled</div>
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Branch</label>
                        <input v-model="config.printing.bankDetails.branch" type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500">
                      </div>
                    </div>

                    <label class="flex items-center">
                      <input v-model="config.printing.bankDetails.showOnInvoice" type="checkbox" class="h-4 w-4 text-green-600 focus:ring-green-500 border-gray-300 rounded">
                      <span class="ml-2 text-sm text-gray-900">Show bank details on printed invoice</span>
                    </label>
                  </div>
                </div>
              </div>

              <!-- Jurisdiction Configuration -->
              <div class="bg-blue-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-blue-700 mb-4 flex items-center">
                  <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                  </svg>
                  Jurisdiction
                </h4>
                <div class="space-y-4">
                  <label class="flex items-center">
                    <input v-model="config.printing.jurisdiction.enabled" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-900">Include jurisdiction information on invoice</span>
                  </label>

                  <div v-if="config.printing.jurisdiction.enabled" class="space-y-4 pl-6 border-l-2 border-blue-200">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">State</label>
                        <input v-model="config.printing.jurisdiction.state" type="text" placeholder="e.g., Maharashtra" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">District</label>
                        <input v-model="config.printing.jurisdiction.district" type="text" placeholder="e.g., Mumbai" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Court</label>
                        <input v-model="config.printing.jurisdiction.court" type="text" placeholder="e.g., Mumbai City Civil Court" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                      </div>
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Custom Jurisdiction Text</label>
                        <textarea v-model="config.printing.jurisdiction.customText" rows="2" placeholder="Custom jurisdiction clause..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"></textarea>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Inventory Fields Selection -->
              <div class="bg-teal-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-teal-700 mb-4 flex items-center">
                  <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                  </svg>
                  Inventory Fields to Print
                </h4>
                <div class="space-y-4">
                  <p class="text-sm text-gray-600 mb-4">Select which inventory fields should be included in the printed invoice</p>

                  <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <!-- Required Fields (Always Visible) -->
                    <div class="space-y-3">
                      <h5 class="text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">Required Fields</h5>
                      <div class="space-y-2">
                        <div class="flex items-center opacity-50">
                          <input type="checkbox" checked disabled class="h-4 w-4 text-gray-400 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-500">Item Name</label>
                        </div>
                        <div class="flex items-center opacity-50">
                          <input type="checkbox" checked disabled class="h-4 w-4 text-gray-400 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-500">Rate</label>
                        </div>
                        <div class="flex items-center opacity-50">
                          <input type="checkbox" checked disabled class="h-4 w-4 text-gray-400 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-500">Quantity</label>
                        </div>
                        <div class="flex items-center opacity-50">
                          <input type="checkbox" checked disabled class="h-4 w-4 text-gray-400 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-500">UOM</label>
                        </div>
                        <div class="flex items-center opacity-50">
                          <input type="checkbox" checked disabled class="h-4 w-4 text-gray-400 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-500">Total</label>
                        </div>
                      </div>
                    </div>

                    <!-- Optional Fields -->
                    <div class="space-y-3">
                      <h5 class="text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">Optional Fields</h5>
                      <div class="space-y-2">
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showHSN" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">HSN Code</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showBatch" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">Batch Number</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showMRP" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">MRP</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showExpiryDate" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">Expiry Date</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showDiscount" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">Discount (%)</label>
                        </div>
                      </div>
                    </div>

                    <!-- Tax & Additional Fields -->
                    <div class="space-y-3">
                      <h5 class="text-sm font-medium text-gray-700 border-b border-gray-300 pb-1">Tax & Additional</h5>
                      <div class="space-y-2">
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showGSTRate" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">GST Rate (%)</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showCGST" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">CGST Amount</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showSGST" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">SGST Amount</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showIGST" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">IGST Amount</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showProject" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">Project</label>
                        </div>
                        <div class="flex items-center">
                          <input v-model="config.printing.inventoryFields.showNarration" type="checkbox" class="h-4 w-4 text-teal-600 focus:ring-teal-500 border-gray-300 rounded">
                          <label class="ml-2 text-sm text-gray-900">Item Narration</label>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Quick Actions -->
                  <div class="flex flex-wrap gap-2 pt-4 border-t border-gray-200">
                    <button type="button" @click="selectAllInventoryFields" class="px-3 py-1 text-xs bg-teal-100 text-teal-700 rounded-md hover:bg-teal-200 focus:outline-none focus:ring-2 focus:ring-teal-500">
                      Select All
                    </button>
                    <button type="button" @click="deselectAllInventoryFields" class="px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500">
                      Deselect All
                    </button>
                    <button type="button" @click="resetInventoryFieldsToDefault" class="px-3 py-1 text-xs bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-blue-500">
                      Reset to Default
                    </button>
                  </div>
                </div>
              </div>

              <!-- Declaration Configuration -->
              <div class="bg-yellow-50 p-4 rounded-lg">
                <h4 class="text-lg font-semibold text-yellow-700 mb-4 flex items-center">
                  <svg class="inline-block w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                  </svg>
                  Declaration
                </h4>
                <div class="space-y-4">
                  <label class="flex items-center">
                    <input v-model="config.printing.declaration.enabled" type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                    <span class="ml-2 text-sm text-gray-900">Include declaration on invoice</span>
                  </label>

                  <div v-if="config.printing.declaration.enabled" class="space-y-4 pl-6 border-l-2 border-yellow-200">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Declaration Text</label>
                      <textarea v-model="config.printing.declaration.text" rows="3" placeholder="Enter your declaration text..." class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-yellow-500 focus:border-yellow-500"></textarea>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Position on Invoice</label>
                        <select v-model="config.printing.declaration.position" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-yellow-500 focus:border-yellow-500">
                          <option value="top">Top of invoice</option>
                          <option value="bottom">Bottom of invoice</option>
                          <option value="custom">Custom position</option>
                        </select>
                      </div>
                      <div class="flex items-center">
                        <label class="flex items-center">
                          <input v-model="config.printing.declaration.showOnInvoice" type="checkbox" class="h-4 w-4 text-yellow-600 focus:ring-yellow-500 border-gray-300 rounded">
                          <span class="ml-2 text-sm text-gray-900">Show on printed invoice</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Defaults Tab -->
            <div v-if="activeTab === 'defaults'" class="space-y-6">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Default Values -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-900 mb-3">
                    <DocumentTextIcon class="inline-block w-5 h-5 mr-2" />
                    Default Values
                  </h4>
                  <div class="space-y-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Default Bill Type</label>
                      <select v-model="config.defaults.billType" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="">Select Bill Type</option>
                        <option value="SALES">SALES</option>
                        <option value="PURCHASE">PURCHASE</option>
                        <option value="DEBIT NOTE">DEBIT NOTE</option>
                        <option value="CREDIT NOTE">CREDIT NOTE</option>
                      </select>
                    </div>
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Default GST Rate (%)</label>
                      <input v-model.number="config.defaults.gstRate" type="number" min="0" max="28" step="0.01" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>

                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Default Firm GST Registration</label>
                      <select v-model="config.defaults.firmGSTIndex" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option v-for="(gst, index) in firmData?.firmGSTs || []"
                                :key="`gst-${index}-${gst.gstNumber || index}`"
                                :value="index">
                          {{ gst.gstNumber || 'Unknown GST' }} - {{ gst.state || 'Unknown State' }} {{ gst.isPrimary ? '(Primary)' : `(${gst.locationName || 'Unknown Location'})` }}
                        </option>
                      </select>
                      <p class="text-xs text-gray-500 mt-1">This GST will be pre-selected when creating new bills</p>
                      <p class="text-xs text-gray-400 mt-1">{{ firmData?.firmGSTs?.length || 0 }} GST registrations available</p>
                      <button type="button"
                              @click="showAddFirmGSTModal = true"
                              class="mt-2 text-xs bg-blue-100 text-blue-700 px-3 py-1 rounded hover:bg-blue-200">
                        + Add New Firm GST
                      </button>
                    </div>
                  </div>
                </div>

                <!-- Number Formats -->
                <div class="bg-gray-50 p-4 rounded-lg">
                  <h4 class="text-lg font-medium text-gray-900 mb-3">
                    <HashtagIcon class="inline-block w-5 h-5 mr-2" />
                    Number Formats
                  </h4>
                  <div class="space-y-3">
                    <div>
                      <label class="block text-sm font-medium text-gray-700 mb-1">Decimal places</label>
                      <select v-model="config.defaults.decimalPlaces" class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="0">0</option>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                      </select>
                    </div>
                    <div class="flex items-center">
                      <input v-model="config.defaults.showCurrencySymbol" type="checkbox" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded">
                      <label class="ml-2 block text-sm text-gray-900">Show currency symbol</label>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Modal footer -->
        <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
          <button
            type="button"
            class="inline-flex w-full justify-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm"
            @click="saveConfiguration"
          >
            <CheckIcon class="w-5 h-5 mr-2" />
            Save Changes
          </button>
          <button
            type="button"
            class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
            @click="resetToDefaults"
          >
            <ArrowUturnLeftIcon class="w-5 h-5 mr-2" />
            Reset to Defaults
          </button>
          <button
            type="button"
            class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm"
            @click="closeModal"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Add Firm GST Modal -->
    <div v-if="showAddFirmGSTModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[10000]">
      <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Add New Firm GST Registration</h3>

        <form @submit.prevent="submitFirmGST">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">GST Number *</label>
              <input v-model="firmGSTForm.gstNumber"
                     type="text"
                     placeholder="27AAAAA0000A1Z5"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md"
                     required />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Location Name *</label>
              <input v-model="firmGSTForm.locationName"
                     type="text"
                     placeholder="Mumbai Branch"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md"
                     required />
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
              <textarea v-model="firmGSTForm.address"
                        placeholder="Complete address with city and pincode"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md"
                        rows="3"
                        required></textarea>
            </div>

            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                <input v-model="firmGSTForm.city"
                       type="text"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
                <input v-model="firmGSTForm.pincode"
                       type="text"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
            </div>

            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Registration Type</label>
              <select v-model="firmGSTForm.registrationType"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <option value="regular">Regular</option>
                <option value="composition">Composition</option>
                <option value="casual">Casual</option>
                <option value="sez">SEZ</option>
              </select>
            </div>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button type="button"
                    @click="showAddFirmGSTModal = false"
                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit"
                    :disabled="isSubmittingGST"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
              {{ isSubmittingGST ? 'Adding...' : 'Add GST Registration' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import {
  CogIcon,
  XMarkIcon,
  EyeIcon,
  TableCellsIcon,
  DocumentArrowDownIcon,
  ShieldCheckIcon,
  CommandLineIcon,
  DocumentTextIcon,
  HashtagIcon,
  CheckIcon,
  ArrowUturnLeftIcon,
  TruckIcon,
  PrinterIcon
} from '@heroicons/vue/24/outline'
import useToast from '~/composables/ui/useToast'
import useApiWithAuth from '~/composables/auth/useApiWithAuth'

// Props
const props = defineProps({
  isOpen: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'configUpdated'])

// Composables
const { success, error } = useToast()

// Reactive data
const activeTab = ref('features')
const isMobile = computed(() => {
  if (process.client) {
    return window.innerWidth < 768
  }
  return false
})

// Bank details related reactive data
const bankLedgers = ref([])
const fetchingBankDetails = ref(false)
const ifscError = ref('')
const ifscValid = ref(false)
const ifscTimeout = ref(null)

// Firm GST related reactive data
const firmData = ref(null)
const showAddFirmGSTModal = ref(false)
const isSubmittingGST = ref(false)
const firmGSTForm = ref({
  gstNumber: '',
  locationName: '',
  address: '',
  city: '',
  pincode: '',
  registrationType: 'regular'
})

// Tab configuration
const tabs = [
  { id: 'features', name: 'Features', icon: CogIcon, shortcut: 'F' },
  { id: 'display', name: 'Display', icon: EyeIcon, shortcut: 'D' },
  { id: 'behavior', name: 'Behavior', icon: CogIcon, shortcut: 'B' },
  { id: 'printing', name: 'Printing', icon: PrinterIcon, shortcut: 'P' },
  { id: 'shortcuts', name: 'Shortcuts', icon: CommandLineIcon, shortcut: 'S' },
  { id: 'defaults', name: 'Defaults', icon: DocumentTextIcon, shortcut: 'T' }
]

// Default configuration
const defaultConfig = {
  display: {
    showMRP: true,
    showExpiryDate: true,
    showProject: true,
    showDiscount: true,
    showCGST: true,
    showSGST: true,
    showIGST: true,
    rowsPerPage: 25,
    compactMode: false
  },
  behavior: {
    autoSave: false,
    autoSaveInterval: 120,
    strictValidation: true,
    showWarnings: true
  },
  features: {
    itemNarration: {
      enabled: true,
      showModal: true
    },
    orderDispatch: {
      enabled: true,
      showOrderInfo: true,
      showDispatchInfo: true,
      showVehicleNumber: true,
      showConsigneeDetails: true
    }
  },
  defaults: {
    billType: 'SALES',
    gstRate: 18,
    decimalPlaces: 2,
    showCurrencySymbol: true,
    firmGSTIndex: 0
  },
  printing: {
    paperSize: 'A4',
    orientation: 'portrait',
    margins: {
      top: 20,
      bottom: 20,
      left: 20,
      right: 20
    },
    quality: 'high',
    colorMode: 'color',
    duplex: 'none',
    copies: 1,
    scale: 100,
    fitToPage: false,
    printBackground: true,
    printHeaders: false,
    printFooters: false,
    customHeader: '',
    customFooter: '',
    watermark: {
      enabled: false,
      text: 'DRAFT',
      opacity: 0.3,
      fontSize: 48,
      color: '#cccccc',
      rotation: 45
    },
    pageBreaks: {
      avoidBreakingItems: true,
      minimumLinesPerPage: 5
    },
    template: {
      selected: 'professional', // 'professional', 'minimal', 'classic', 'modern'
      customization: {
        colors: {
          primary: '#48bb78',
          secondary: '#4299e1',
          text: '#2d3748'
        },
        fonts: {
          header: 'Helvetica-Bold',
          body: 'Helvetica'
        }
      }
    },
    bankDetails: {
      enabled: false,
      selectedBankId: '',
      bankName: '',
      accountNumber: '',
      ifscCode: '',
      branch: '',
      showOnInvoice: true
    },
    jurisdiction: {
      enabled: false,
      state: '',
      district: '',
      court: '',
      customText: ''
    },
    declaration: {
      enabled: false,
      text: 'We declare that this invoice shows the actual price of the goods described and that all particulars are true and correct.',
      showOnInvoice: true,
      position: 'bottom' // 'top', 'bottom', 'custom'
    },
    inventoryFields: {
      // Required fields are always shown
      showHSN: true,
      showBatch: true,
      showMRP: true,
      showExpiryDate: true,
      showDiscount: true,
      showGSTRate: true,
      showCGST: true,
      showSGST: true,
      showIGST: true,
      showProject: true,
      showNarration: false
    }
  }
}

// Configuration state - initialize with defaults for SSR, will be overridden by loadConfiguration() on client
const config = ref({ ...defaultConfig })

// Methods
const closeModal = () => {
  emit('close')
}

const saveConfiguration = () => {
  try {
    // Convert Vue proxy to plain object before saving
    const plainConfig = JSON.parse(JSON.stringify(config.value))
    const configToSave = JSON.stringify(plainConfig, null, 2)
    localStorage.setItem('billConfiguration', configToSave)

    // Verify it was saved correctly
    const verification = localStorage.getItem('billConfiguration')

    success('Configuration saved successfully!')
    emit('configUpdated', config.value)
    closeModal()
  } catch (err) {
    console.error('❌ Failed to save configuration:', err)
    error('Failed to save configuration: ' + err.message)
  }
}

// Firm GST methods
const fetchFirmData = async () => {
  try {
    console.log('🔄 Fetching firm data...')
    const response = await $fetch('/api/inventory')
    firmData.value = response
    console.log('✅ Firm data fetched:', response.firmGSTs?.length, 'GST registrations')

    // Log GST summary for debugging
    response.firmGSTs?.forEach((gst, index) => {
      console.log(`GST ${index}:`, {
        gstNumber: gst.gstNumber,
        state: gst.state,
        locationName: gst.locationName,
        isPrimary: gst.isPrimary
      })
    })
  } catch (error) {
    console.error('❌ Error fetching firm data:', error)
  }
}

const submitFirmGST = async () => {
  if (isSubmittingGST.value) return

  try {
    isSubmittingGST.value = true

    const response = await $fetch('/api/inventory/firm-gst', {
      method: 'POST',
      body: firmGSTForm.value
    })

    if (response.success) {
      success('Firm GST registration added successfully!')
      showAddFirmGSTModal.value = false

      // Reset form
      firmGSTForm.value = {
        gstNumber: '',
        locationName: '',
        address: '',
        city: '',
        pincode: '',
        registrationType: 'regular'
      }

      // Refresh firm data to update dropdown
      await fetchFirmData()

      // Force reactivity update
      await nextTick()
      console.log('🔄 Firm data refreshed, GST count:', firmData.value?.firmGSTs?.length)
    }
  } catch (err) {
    console.error('Error adding firm GST:', err)
    error(err.data?.statusMessage || 'Failed to add firm GST registration')
  } finally {
    isSubmittingGST.value = false
  }
}

// Bank details fetching functions
const fetchBankLedgers = async () => {
  try {
    const api = useApiWithAuth()
    const response = await api.get('/api/ledgers?type=bank')
    bankLedgers.value = response || []
  } catch (err) {
    console.error('Error fetching bank ledgers:', err)
    error('Failed to fetch bank ledgers')
  }
}

const handleBankSelection = () => {
  if (!config.value.printing.bankDetails.selectedBankId) {
    // Clear bank details if no bank selected
    config.value.printing.bankDetails.bankName = ''
    config.value.printing.bankDetails.accountNumber = ''
    config.value.printing.bankDetails.ifscCode = ''
    config.value.printing.bankDetails.branch = ''
    return
  }

  const selectedBank = bankLedgers.value.find(bank => bank.id === config.value.printing.bankDetails.selectedBankId)
  if (selectedBank && selectedBank.bankDetails) {
    config.value.printing.bankDetails.bankName = selectedBank.bankDetails.bankName || ''
    config.value.printing.bankDetails.accountNumber = selectedBank.bankDetails.accountNumber || ''
    config.value.printing.bankDetails.ifscCode = selectedBank.bankDetails.ifscCode || ''
    config.value.printing.bankDetails.branch = selectedBank.bankDetails.branch || ''
  }
}

const handleIfscInput = (event) => {
  const ifsc = event.target.value.toUpperCase()
  config.value.printing.bankDetails.ifscCode = ifsc

  // Reset states
  ifscError.value = ''
  ifscValid.value = false

  // Clear previous timeout
  if (ifscTimeout.value) {
    clearTimeout(ifscTimeout.value)
  }

  // Only validate if IFSC has 11 characters
  if (ifsc.length === 11) {
    // Debounce the API call
    ifscTimeout.value = setTimeout(() => {
      fetchBankDetailsByIfsc(ifsc)
    }, 500)
  } else if (ifsc.length > 11) {
    ifscError.value = 'IFSC code should be exactly 11 characters'
  }
}

const fetchBankDetailsByIfsc = async (ifsc) => {
  if (!ifsc || ifsc.length !== 11) return

  fetchingBankDetails.value = true
  ifscError.value = ''

  try {
    const response = await fetch(`https://ifsc.razorpay.com/${ifsc}`)

    if (response.ok) {
      const bankData = await response.json()

      // Auto-fill bank and branch details
      config.value.printing.bankDetails.bankName = bankData.BANK || ''
      config.value.printing.bankDetails.branch = bankData.BRANCH || ''

      ifscValid.value = true
      ifscError.value = ''

      success(`Bank details auto-filled: ${bankData.BANK} - ${bankData.BRANCH}`)
    } else {
      throw new Error('Invalid IFSC code')
    }
  } catch (error) {
    ifscError.value = 'Invalid IFSC code or unable to fetch bank details'
    ifscValid.value = false
    console.error('Error fetching bank details:', error)
  } finally {
    fetchingBankDetails.value = false
  }
}

const resetToDefaults = () => {
  config.value = { ...defaultConfig }
  success('Configuration reset to defaults')
}

// Inventory fields quick actions
const selectAllInventoryFields = () => {
  config.value.printing.inventoryFields.showHSN = true
  config.value.printing.inventoryFields.showBatch = true
  config.value.printing.inventoryFields.showMRP = true
  config.value.printing.inventoryFields.showExpiryDate = true
  config.value.printing.inventoryFields.showDiscount = true
  config.value.printing.inventoryFields.showGSTRate = true
  config.value.printing.inventoryFields.showCGST = true
  config.value.printing.inventoryFields.showSGST = true
  config.value.printing.inventoryFields.showIGST = true
  config.value.printing.inventoryFields.showProject = true
  config.value.printing.inventoryFields.showNarration = true
  success('All inventory fields selected for printing')
}

const deselectAllInventoryFields = () => {
  config.value.printing.inventoryFields.showHSN = false
  config.value.printing.inventoryFields.showBatch = false
  config.value.printing.inventoryFields.showMRP = false
  config.value.printing.inventoryFields.showExpiryDate = false
  config.value.printing.inventoryFields.showDiscount = false
  config.value.printing.inventoryFields.showGSTRate = false
  config.value.printing.inventoryFields.showCGST = false
  config.value.printing.inventoryFields.showSGST = false
  config.value.printing.inventoryFields.showIGST = false
  config.value.printing.inventoryFields.showProject = false
  config.value.printing.inventoryFields.showNarration = false
  success('All inventory fields deselected from printing')
}

const resetInventoryFieldsToDefault = () => {
  config.value.printing.inventoryFields = { ...defaultConfig.printing.inventoryFields }
  success('Inventory fields reset to default settings')
}

// Deep merge utility function for nested objects
const deepMerge = (target, source) => {
  const result = { ...target }

  for (const key in source) {
    if (source.hasOwnProperty(key)) {
      if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        // If both target and source have the same key and both are objects, merge recursively
        result[key] = target[key] && typeof target[key] === 'object' && !Array.isArray(target[key])
          ? deepMerge(target[key], source[key])
          : { ...source[key] }
      } else {
        // For primitive values, arrays, or null, use source value
        result[key] = source[key]
      }
    }
  }

  return result
}

const loadConfiguration = () => {
  // Skip on server-side rendering
  if (typeof window === 'undefined') {
    config.value = { ...defaultConfig }
    return
  }

  try {
    const saved = localStorage.getItem('billConfiguration')
    console.log('🔍 Loading configuration...')
    console.log('🔍 Raw saved data:', saved)
    console.log('🔍 Data type:', typeof saved)
    console.log('🔍 Data length:', saved ? saved.length : 0)

    if (saved && saved !== 'undefined' && saved !== 'null') {
      // Try to parse the JSON data
      try {
        const parsedConfig = JSON.parse(saved)

        // Use deep merge to properly combine default and saved configurations
        config.value = deepMerge(defaultConfig, parsedConfig)

        console.log('✅ Configuration loaded successfully from localStorage')
        console.log('📊 Merged config:', config.value)
      } catch (e) {
        // If it's not valid JSON, clear it and use defaults
        console.warn('⚠️ Invalid billConfiguration data, clearing and using defaults', e)
        localStorage.removeItem('billConfiguration')
        config.value = { ...defaultConfig }
      }
    } else {
      // No saved configuration, use defaults
      console.log('📋 No saved configuration found, using defaults')
      config.value = { ...defaultConfig }
    }
  } catch (err) {
    console.error('❌ Failed to load configuration:', err)
    // Clear corrupted data and use defaults
    localStorage.removeItem('billConfiguration')
    config.value = { ...defaultConfig }
  }
}

// Format tab name with highlighted shortcut key
const formatTabName = (name, shortcut) => {
  const index = name.toLowerCase().indexOf(shortcut.toLowerCase())
  if (index !== -1) {
    return name.substring(0, index) +
           `<span style="font-weight: bold; color: #dc2626;">${name.charAt(index)}</span>` +
           name.substring(index + 1)
  }
  return name
}

// Handle keyboard shortcuts for tab switching
const handleKeydown = (event) => {
  if (event.altKey) {
    const key = event.key.toLowerCase()
    const tab = tabs.find(t => t.shortcut.toLowerCase() === key)
    if (tab) {
      event.preventDefault()
      activeTab.value = tab.id
    }
  }
}

// Watch for modal open state to reload configuration
watch(() => props.isOpen, async (newValue) => {
  if (newValue) {
    // Wait for DOM to be ready, then reload configuration
    await nextTick()
    loadConfiguration()
    await fetchFirmData()
  }
})

// Load configuration on mount
onMounted(async () => {
  loadConfiguration()

  // Fetch bank ledgers for print configuration
  await fetchBankLedgers()

  // Fetch firm data for GST configuration
  await fetchFirmData()

  // Add global keydown listener for tab shortcuts
  window.addEventListener('keydown', handleKeydown)
})

// Cleanup on unmount
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
