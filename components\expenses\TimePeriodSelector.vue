<template>
  <div>
    <label :for="inputId" class="block text-sm font-medium text-gray-700 mb-1">{{ label }}</label>
    <select
      :id="inputId"
      v-model="selectedValue"
      class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
      @change="emitChange"
    >
      <option v-for="option in options" :key="option.value" :value="option.value">
        {{ option.label }}
      </option>
    </select>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';

export default {
  name: 'TimePeriodSelector',

  props: {
    type: {
      type: String,
      required: true,
      validator: (value) => ['weekly', 'monthly', 'yearly', 'financial-year'].includes(value)
    },
    modelValue: {
      type: String,
      default: ''
    }
  },

  emits: ['update:modelValue'],

  setup(props, { emit }) {
    const selectedValue = ref(props.modelValue);

    // Watch for external changes to modelValue
    watch(() => props.modelValue, (newValue) => {
      selectedValue.value = newValue;
    });

    // Computed properties
    const inputId = computed(() => `${props.type}-selector`);

    const label = computed(() => {
      switch (props.type) {
        case 'weekly':
          return 'Select Week';
        case 'monthly':
          return 'Select Month';
        case 'yearly':
          return 'Select Year';
        case 'financial-year':
          return 'Select Financial Year';
        default:
          return 'Select Period';
      }
    });

    const options = computed(() => {
      const currentDate = new Date();
      const currentYear = currentDate.getFullYear();
      const currentMonth = currentDate.getMonth();

      switch (props.type) {
        case 'weekly': {
          // Generate options for the last 52 weeks (1 year)
          const weekOptions = [];
          const today = new Date();

          for (let i = 0; i < 52; i++) {
            const weekStart = new Date(today);
            weekStart.setDate(today.getDate() - (today.getDay() + 7 * i));

            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);

            const weekLabel = `${weekStart.toLocaleDateString()} - ${weekEnd.toLocaleDateString()}`;
            const weekValue = `${weekStart.toISOString().split('T')[0]}|${weekEnd.toISOString().split('T')[0]}`;

            weekOptions.push({
              label: weekLabel,
              value: weekValue
            });
          }

          return weekOptions;
        }

        case 'monthly': {
          // Generate options for the last 24 months (2 years)
          const monthOptions = [];

          for (let i = 0; i < 24; i++) {
            const year = currentYear - Math.floor((currentMonth + i) / 12);
            const month = (12 + currentMonth - i % 12) % 12;

            const date = new Date(year, month, 1);
            const monthName = date.toLocaleString('default', { month: 'long' });

            monthOptions.push({
              label: `${monthName} ${year}`,
              value: `${year}-${(month + 1).toString().padStart(2, '0')}`
            });
          }

          return monthOptions;
        }

        case 'yearly': {
          // Generate options for the last 5 years
          const yearOptions = [];

          for (let i = 0; i < 5; i++) {
            const year = currentYear - i;

            yearOptions.push({
              label: `${year}`,
              value: `${year}`
            });
          }

          return yearOptions;
        }

        case 'financial-year': {
          // Generate options for the last 5 financial years (April-March)
          const fyOptions = [];

          // Determine the current financial year based on the current month
          // In India, financial year runs from April 1 to March 31
          // If current month is Jan-Mar (0-2), we're in the previous year's FY
          // If current month is Apr-Dec (3-11), we're in the current year's FY

          let currentFYEndYear;
          if (currentMonth >= 0 && currentMonth <= 2) { // Jan-Mar
            currentFYEndYear = currentYear;
          } else { // Apr-Dec
            currentFYEndYear = currentYear + 1;
          }

          for (let i = 0; i < 5; i++) {
            const endYear = currentFYEndYear - i;
            const startYear = endYear - 1;

            fyOptions.push({
              label: `FY ${startYear}-${endYear}`,
              value: `${startYear}-${endYear}`
            });
          }

          return fyOptions;
        }

        default:
          return [];
      }
    });

    // Methods
    const emitChange = () => {
      emit('update:modelValue', selectedValue.value);
    };

    // Watch for changes in the type prop to update the selected value
    watch(() => props.type, (newType) => {
      // Short delay to ensure options are computed with the new type
      setTimeout(() => {
        if (options.value.length > 0) {
          selectedValue.value = options.value[0].value;
          emitChange();
        }
      }, 0);
    }, { immediate: true });

    // Watch for changes in options
    watch(() => options.value, (newOptions) => {
      if (newOptions.length > 0 && !selectedValue.value) {
        selectedValue.value = newOptions[0].value;
        emitChange();
      }
    });

    // Set default value on mount if none provided
    onMounted(() => {
      if (options.value.length > 0) {
        selectedValue.value = options.value[0].value;
        emitChange();
      }
    });

    return {
      selectedValue,
      inputId,
      label,
      options,
      emitChange
    };
  }
};
</script>
