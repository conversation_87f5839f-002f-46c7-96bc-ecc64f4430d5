import { LaborPaymentService } from '~/utils/laborPaymentService.js'

/**
 * Test endpoint to validate Scenario 1 logic
 * Tests the specific scenario described by the user
 */
export default defineEventHandler(async (event) => {
  try {
    // Mock data for Scenario 1
    const mockPayments = [
      {
        id: '1',
        payment_date: '2025-04-06',
        amount: 2000,
        payment_type: 'Advance',
        payment_method: 'cash'
      },
      {
        id: '2', 
        payment_date: '2025-04-09',
        amount: 1500,
        payment_type: 'Advance',
        payment_method: 'cash'
      },
      {
        id: '3',
        payment_date: '2025-04-14', 
        amount: 2000,
        payment_type: 'Site Expense',
        payment_method: 'cash'
      },
      {
        id: '4',
        payment_date: '2025-04-20',
        amount: 3000,
        payment_type: 'Advance', 
        payment_method: 'cash'
      },
      {
        id: '5',
        payment_date: '2025-04-28',
        amount: 6500,
        payment_type: 'Final Payment',
        payment_method: 'cash'
      }
    ]

    const mockPeriods = [
      {
        period_start: '2025-04-04',
        period_end: '2025-04-26'
      }
    ]

    // Create a mock payment service instance
    const paymentService = new LaborPaymentService('mock', 'mock')
    
    // Override methods to use mock data
    paymentService.getGroupPayments = async () => mockPayments
    paymentService.getAttendancePeriods = async () => mockPeriods
    paymentService.calculatePeriodEarnings = async () => 15000 // Mock total earnings

    // Test the logic
    const finalPayments = paymentService.findFinalPayments(mockPayments, mockPeriods)
    const categorized = paymentService.categorizePayments(mockPayments, mockPeriods)
    const unpaidAmounts = await paymentService.calculateUnpaidAmounts('mock-group')

    // Expected results for Scenario 1:
    // Period: 04-04-2025 - 26-04-2025
    // Total Payments: 15000 (8500 in period + 6500 final payment)
    // Unpaid Amount: 0
    
    const expectedResult = {
      period: {
        period_start: '2025-04-04',
        period_end: '2025-04-26'
      },
      totalPayments: 15000,
      unpaidAmount: 0,
      type: 'settled'
    }

    const actualResult = unpaidAmounts[0]
    
    const isCorrect = 
      actualResult &&
      actualResult.period.period_start === expectedResult.period.period_start &&
      actualResult.period.period_end === expectedResult.period.period_end &&
      actualResult.totalPayments === expectedResult.totalPayments &&
      actualResult.unpaidAmount === expectedResult.unpaidAmount &&
      actualResult.type === expectedResult.type

    return {
      success: true,
      scenario: 'Scenario 1: Settled Period with Final Payment Outside Range',
      testData: {
        payments: mockPayments,
        periods: mockPeriods,
        totalEarnings: 15000
      },
      calculations: {
        finalPayments: Array.from(finalPayments.entries()),
        categorized: {
          settledPeriods: categorized.settledPeriods.length,
          ongoingPeriods: categorized.ongoingPeriods.length,
          postFinalPayments: categorized.postFinalPayments.length
        },
        unpaidAmounts
      },
      validation: {
        expected: expectedResult,
        actual: actualResult,
        isCorrect,
        issues: isCorrect ? [] : [
          actualResult?.totalPayments !== expectedResult.totalPayments ? `Total payments mismatch: expected ${expectedResult.totalPayments}, got ${actualResult?.totalPayments}` : null,
          actualResult?.unpaidAmount !== expectedResult.unpaidAmount ? `Unpaid amount mismatch: expected ${expectedResult.unpaidAmount}, got ${actualResult?.unpaidAmount}` : null,
          actualResult?.type !== expectedResult.type ? `Type mismatch: expected ${expectedResult.type}, got ${actualResult?.type}` : null
        ].filter(Boolean)
      }
    }

  } catch (error) {
    console.error('Error in test scenario 1:', error)
    
    return {
      success: false,
      error: error.message,
      stack: error.stack
    }
  }
})
