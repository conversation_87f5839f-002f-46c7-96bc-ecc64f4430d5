<template>
  <div v-if="show" class="fixed inset-x-0 bottom-0 z-50 p-4">
    <div class="max-w-4xl mx-auto bg-yellow-100 border-l-4 border-yellow-500 p-4 rounded shadow-lg">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-6 w-6 text-yellow-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-yellow-800">Database Index Required</h3>
          <div class="mt-2 text-sm text-yellow-700">
            <p>
              A database index is required to properly display ledger transactions. Please follow these steps:
            </p>
            <ol class="list-decimal list-inside mt-2 ml-2">
              <li>Click the link below to open the Firebase console</li>
              <li>Sign in with your administrator account</li>
              <li>Click "Create Index" on the page that opens</li>
              <li>Wait for the index to be created (may take a few minutes)</li>
              <li>Refresh this page after the index is created</li>
            </ol>
            <div class="mt-3">
              <a 
                :href="indexUrl" 
                target="_blank" 
                class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
              >
                Open Firebase Console
              </a>
              <button 
                @click="show = false" 
                class="ml-3 inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
              >
                Dismiss
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'IndexNotification',
  
  props: {
    indexUrl: {
      type: String,
      required: true
    }
  },
  
  data() {
    return {
      show: true
    };
  }
};
</script>
