import { getFirestore, Timestamp } from 'firebase-admin/firestore';
import { initializeApp, cert } from 'firebase-admin/app';
import dotenv from 'dotenv';

/**
 * READ-ONLY Analysis script to identify duplicate transactions
 *
 * This script analyzes the database to understand why duplicate transactions
 * are appearing in the UI without modifying any data.
 *
 * It will check:
 * 1. Transactions in the 'subs' collection (new method)
 * 2. Transactions in the 'transactions' arrays within subsModels (old method)
 * 3. Identify potential duplicates between the two sources
 */

// Load environment variables
dotenv.config();

// Initialize Firebase Admin SDK
const serviceAccount = {
  type: "service_account",
  project_id: process.env.FIREBASE_PROJECT_ID,
  private_key_id: process.env.FIREBASE_PRIVATE_KEY_ID,
  private_key: process.env.FIREBASE_PRIVATE_KEY?.replace(/\\n/g, '\n'),
  client_email: process.env.FIREBASE_CLIENT_EMAIL,
  client_id: process.env.FIREBASE_CLIENT_ID,
  auth_uri: "https://accounts.google.com/o/oauth2/auth",
  token_uri: "https://oauth2.googleapis.com/token",
  auth_provider_x509_cert_url: "https://www.googleapis.com/oauth2/v1/certs",
  client_x509_cert_url: process.env.FIREBASE_CLIENT_CERT_URL
};

// Initialize Firebase
try {
  initializeApp({
    credential: cert(serviceAccount)
  });
  console.log('✅ Firebase initialized successfully');
} catch (error) {
  console.error('❌ Firebase initialization failed:', error.message);
  process.exit(1);
}

const db = getFirestore();

async function analyzeDuplicateTransactions() {
  console.log('🔍 Starting READ-ONLY analysis of duplicate transactions...');
  console.log('📋 This script will NOT modify any data - it only reads and analyzes.\n');
  
  try {
    // 1. Analyze transactions in the 'subs' collection (new method)
    console.log('📊 STEP 1: Analyzing transactions in "subs" collection (new method)...');
    const subsCollection = db.collection('subs');
    const subsSnapshot = await subsCollection.get();
    
    const subsTransactions = [];
    subsSnapshot.forEach(doc => {
      const data = doc.data();
      subsTransactions.push({
        id: data.id,
        docId: doc.id,
        amount: data.amount,
        date: data.date?.toDate(),
        paidTo: data.paidTo,
        subName: data.subName,
        firmId: data.firmId,
        source: 'subs_collection'
      });
    });
    
    console.log(`   Found ${subsTransactions.length} transactions in subs collection`);
    
    // 2. Analyze transactions in subsModels (old method)
    console.log('\n📊 STEP 2: Analyzing transactions in "subsModels" collection (old method)...');
    const subsModelsCollection = db.collection('subsModels');
    const subsModelsSnapshot = await subsModelsCollection.get();
    
    const subsModelsTransactions = [];
    let subsModelsWithTransactions = 0;
    
    subsModelsSnapshot.forEach(doc => {
      const data = doc.data();
      if (data.transactions && Array.isArray(data.transactions) && data.transactions.length > 0) {
        subsModelsWithTransactions++;
        data.transactions.forEach(tx => {
          subsModelsTransactions.push({
            id: tx.id,
            docId: doc.id,
            subsModelName: data.name,
            amount: tx.amount,
            date: tx.date?.toDate(),
            paidTo: tx.paidTo,
            firmId: data.firmId,
            source: 'subsModels_array'
          });
        });
      }
    });
    
    console.log(`   Found ${subsModelsTransactions.length} transactions in ${subsModelsWithTransactions} subsModels`);
    
    // 3. Look for potential duplicates
    console.log('\n🔍 STEP 3: Analyzing for potential duplicates...');
    
    const duplicates = [];
    
    // Check each transaction in subs collection against subsModels transactions
    subsTransactions.forEach(subsTx => {
      const potentialDuplicates = subsModelsTransactions.filter(modelTx => {
        // Consider transactions duplicates if they have:
        // - Same transaction ID, OR
        // - Same amount, date, and paidTo (within same firm)
        return (subsTx.id === modelTx.id) ||
               (subsTx.amount === modelTx.amount &&
                subsTx.date?.getTime() === modelTx.date?.getTime() &&
                subsTx.paidTo === modelTx.paidTo &&
                subsTx.firmId === modelTx.firmId);
      });
      
      if (potentialDuplicates.length > 0) {
        duplicates.push({
          subsTransaction: subsTx,
          duplicatesInSubsModels: potentialDuplicates
        });
      }
    });
    
    console.log(`   Found ${duplicates.length} potential duplicate sets`);
    
    // 4. Detailed analysis
    console.log('\n📋 DETAILED ANALYSIS:');
    console.log('='.repeat(80));
    
    if (duplicates.length > 0) {
      console.log('\n⚠️  DUPLICATE TRANSACTIONS FOUND:');
      
      duplicates.forEach((duplicate, index) => {
        console.log(`\n${index + 1}. Duplicate Set:`);
        console.log(`   📍 Subs Collection Transaction:`);
        console.log(`      - ID: ${duplicate.subsTransaction.id}`);
        console.log(`      - Amount: ₹${duplicate.subsTransaction.amount}`);
        console.log(`      - Date: ${duplicate.subsTransaction.date?.toLocaleDateString()}`);
        console.log(`      - Paid To: ${duplicate.subsTransaction.paidTo}`);
        console.log(`      - Sub Name: ${duplicate.subsTransaction.subName}`);
        
        duplicate.duplicatesInSubsModels.forEach((dup, dupIndex) => {
          console.log(`   📍 SubsModels Array Transaction ${dupIndex + 1}:`);
          console.log(`      - ID: ${dup.id}`);
          console.log(`      - Amount: ₹${dup.amount}`);
          console.log(`      - Date: ${dup.date?.toLocaleDateString()}`);
          console.log(`      - Paid To: ${dup.paidTo}`);
          console.log(`      - SubsModel: ${dup.subsModelName}`);
        });
      });
      
      console.log('\n🎯 SUMMARY:');
      console.log(`   - Total transactions in subs collection: ${subsTransactions.length}`);
      console.log(`   - Total transactions in subsModels arrays: ${subsModelsTransactions.length}`);
      console.log(`   - Duplicate sets found: ${duplicates.length}`);
      console.log(`   - SubsModels with old transactions: ${subsModelsWithTransactions}`);
      
      console.log('\n💡 RECOMMENDATION:');
      console.log('   The duplicates are caused by transactions being stored in BOTH:');
      console.log('   1. The new "subs" collection (correct method)');
      console.log('   2. The old "transactions" arrays in subsModels (old method)');
      console.log('   ');
      console.log('   To fix this, you should:');
      console.log('   1. Ensure the UI only reads from the "subs" collection');
      console.log('   2. Consider removing the old transactions arrays from subsModels');
      console.log('      (but only after confirming all data is safely in subs collection)');
      
    } else {
      console.log('✅ No duplicate transactions found between the two storage methods.');
      console.log('   This suggests the duplication issue might be elsewhere.');
    }
    
    // 5. Check for recent transactions (last 7 days)
    console.log('\n📅 RECENT TRANSACTIONS (Last 7 days):');
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
    
    const recentSubs = subsTransactions.filter(tx => tx.date && tx.date >= sevenDaysAgo);
    const recentModels = subsModelsTransactions.filter(tx => tx.date && tx.date >= sevenDaysAgo);
    
    console.log(`   - Recent transactions in subs collection: ${recentSubs.length}`);
    console.log(`   - Recent transactions in subsModels: ${recentModels.length}`);
    
    if (recentSubs.length > 0) {
      console.log('\n   Recent subs collection transactions:');
      recentSubs.forEach(tx => {
        console.log(`     - ${tx.date?.toLocaleDateString()} | ₹${tx.amount} | ${tx.paidTo}`);
      });
    }
    
    if (recentModels.length > 0) {
      console.log('\n   Recent subsModels transactions:');
      recentModels.forEach(tx => {
        console.log(`     - ${tx.date?.toLocaleDateString()} | ₹${tx.amount} | ${tx.paidTo} | ${tx.subsModelName}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Error during analysis:', error);
    throw error;
  }
}

// Run the analysis
analyzeDuplicateTransactions()
  .then(() => {
    console.log('\n🏁 Analysis completed successfully!');
    console.log('📋 No data was modified during this analysis.');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Analysis failed:', error);
    process.exit(1);
  });
