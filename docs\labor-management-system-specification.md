# Labor Management System - Technical Specification

## Overview
A comprehensive labor management system built with Nuxt.js, Supabase for primary data storage, and Firestore integration for accounting/ledger synchronization.

## System Architecture

### Technology Stack
- **Frontend**: Nuxt.js 3 with Vue.js 3
- **UI Framework**: Tailwind CSS with custom components
- **Primary Database**: Supabase (PostgreSQL)
- **Secondary Database**: Firestore (for accounting integration)
- **Existing Database**: MongoDB (for configuration storage)
- **Authentication**: Existing JWT-based system
- **Icons**: Heroicons Vue

### Database Schema Design

#### Supabase Tables

##### 1. labor_profiles
```sql
CREATE TABLE labor_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL UNIQUE,
  daily_rate DECIMAL(10,2) NOT NULL,
  group_id UUID REFERENCES labor_groups(id),
  phone VARCHAR(20),
  address TEXT,
  aadhar VARCHAR(12),
  bank_details JSONB,
  is_active BOOLEAN DEFAULT true,
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

##### 2. labor_groups
```sql
CREATE TABLE labor_groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  color VARCHAR(7) DEFAULT '#3B82F6',
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

##### 3. attendance_records
```sql
CREATE TABLE attendance_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  labor_id UUID REFERENCES labor_profiles(id),
  attendance_date DATE NOT NULL,
  days_worked DECIMAL(3,1) DEFAULT 0 CHECK (days_worked IN (0, 0.5, 1, 1.5, 2)),
  daily_rate DECIMAL(10,2) NOT NULL,
  amount DECIMAL(10,2) GENERATED ALWAYS AS (days_worked * daily_rate) STORED,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  site_expenses DECIMAL(10,2) DEFAULT 0,
  site_materials DECIMAL(10,2) DEFAULT 0,
  medical_expenses DECIMAL(10,2) DEFAULT 0,
  other_expenses DECIMAL(10,2) DEFAULT 0,
  notes TEXT,
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(labor_id, attendance_date)
);
```

##### 4. payment_records
```sql
CREATE TABLE payment_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_id UUID REFERENCES labor_groups(id),
  payment_date DATE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  project VARCHAR(255),
  payment_method VARCHAR(50) NOT NULL, -- 'bank' or 'cash'
  bank_details JSONB, -- for bank payments
  advance_amount DECIMAL(10,2) DEFAULT 0,
  description TEXT,
  firestore_sync_status VARCHAR(20) DEFAULT 'pending',
  firestore_doc_id VARCHAR(255),
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

##### 5. supabase_config
```sql
CREATE TABLE supabase_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_name VARCHAR(255) NOT NULL,
  supabase_url VARCHAR(500) NOT NULL,
  supabase_anon_key TEXT NOT NULL,
  supabase_service_key TEXT NOT NULL,
  is_active BOOLEAN DEFAULT false,
  firm_id VARCHAR(255) NOT NULL,
  user_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### MongoDB Collections (Configuration Storage)

##### supabase_configurations
```javascript
{
  _id: ObjectId,
  configName: String,
  supabaseUrl: String,
  supabaseAnonKey: String,
  supabaseServiceKey: String, // Encrypted
  isActive: Boolean,
  firmId: ObjectId,
  userId: ObjectId,
  createdAt: Date,
  updatedAt: Date
}
```

## Component Architecture

### 1. Labor Profile Management

#### Components:
- [`LaborProfileModal.vue`](components/labor/LaborProfileModal.vue) - Add/Edit labor modal
- [`LaborProfileTable.vue`](components/labor/LaborProfileTable.vue) - CRUD table view
- [`LaborProfileForm.vue`](components/labor/LaborProfileForm.vue) - Form component

#### Features:
- Name duplicate validation
- Daily rate management
- Group assignment
- Bank details storage
- Contact information

### 2. Attendance System

#### Components:
- [`AttendanceSystem.vue`](components/labor/AttendanceSystem.vue) - Main attendance interface
- [`AttendanceTable.vue`](components/labor/AttendanceTable.vue) - Dynamic attendance grid
- [`AttendanceFilters.vue`](components/labor/AttendanceFilters.vue) - Group and date filters
- [`AttendanceSummary.vue`](components/labor/AttendanceSummary.vue) - Total summary section

#### Keyboard Navigation Logic:
```javascript
// Keyboard event handling
const handleKeyPress = (event, rowIndex, colIndex) => {
  switch(event.key) {
    case ' ': // Space key
      setCellValue(rowIndex, colIndex, 1);
      moveToNextCell(rowIndex, colIndex);
      break;
    case 'Enter':
      setCellValue(rowIndex, colIndex, 0);
      moveToNextCell(rowIndex, colIndex);
      break;
  }
};

// Double-click/right-click cycling
const cycleDayValues = (currentValue) => {
  const values = [0, 0.5, 1, 1.5, 2];
  const currentIndex = values.indexOf(currentValue);
  return values[(currentIndex + 1) % values.length];
};
```

#### Features:
- Group-based filtering
- Date range selection
- Dynamic column generation
- Keyboard navigation (Space/Enter)
- Value cycling (0, 0.5, 1, 1.5, 2)
- Conflict detection for existing records
- Auto-calculation for present days only
- Additional expense inputs

### 3. Payment System

#### Components:
- [`PaymentSystem.vue`](components/labor/PaymentSystem.vue) - Main payment interface
- [`PaymentModal.vue`](components/labor/PaymentModal.vue) - Payment entry modal
- [`UnpaidAmounts.vue`](components/labor/UnpaidAmounts.vue) - Unpaid amounts display
- [`PaymentHistory.vue`](components/labor/PaymentHistory.vue) - Payment history table

#### Features:
- Group-based payments
- Advance payment logic
- Bank/Cash payment methods
- Firestore synchronization
- Unpaid amounts calculation
- Date range filtering

### 4. Dashboard

#### Components:
- [`LaborDashboard.vue`](pages/labor/dashboard.vue) - Main dashboard page
- [`DashboardStats.vue`](components/labor/DashboardStats.vue) - Statistics cards
- [`DashboardCharts.vue`](components/labor/DashboardCharts.vue) - Analytics charts
- [`LaborMovementModal.vue`](components/labor/LaborMovementModal.vue) - Group movement
- [`GroupManagementModal.vue`](components/labor/GroupManagementModal.vue) - Group CRUD

#### Features:
- Various analytics and filters
- Labor group movement
- Group management
- Real-time statistics
- Export functionality

### 5. Configuration

#### Components:
- [`SupabaseConfigModal.vue`](components/labor/SupabaseConfigModal.vue) - Configuration modal
- [`ConfigurationTest.vue`](components/labor/ConfigurationTest.vue) - Connection testing

## API Endpoints

### Labor Profiles
- `GET /api/labor/profiles` - List all labor profiles
- `POST /api/labor/profiles` - Create new labor profile
- `PUT /api/labor/profiles/:id` - Update labor profile
- `DELETE /api/labor/profiles/:id` - Delete labor profile
- `GET /api/labor/profiles/validate-name` - Validate name uniqueness

### Groups
- `GET /api/labor/groups` - List all groups
- `POST /api/labor/groups` - Create new group
- `PUT /api/labor/groups/:id` - Update group
- `DELETE /api/labor/groups/:id` - Delete group
- `POST /api/labor/groups/move-labor` - Move labor between groups

### Attendance
- `GET /api/labor/attendance` - Get attendance records
- `POST /api/labor/attendance` - Create attendance records
- `PUT /api/labor/attendance/:id` - Update attendance record
- `DELETE /api/labor/attendance/:id` - Delete attendance record
- `GET /api/labor/attendance/conflicts` - Check for conflicts
- `POST /api/labor/attendance/bulk` - Bulk attendance operations

### Payments
- `GET /api/labor/payments` - List payments
- `POST /api/labor/payments` - Create payment
- `PUT /api/labor/payments/:id` - Update payment
- `DELETE /api/labor/payments/:id` - Delete payment
- `GET /api/labor/payments/unpaid` - Get unpaid amounts
- `POST /api/labor/payments/sync-firestore` - Sync to Firestore

### Configuration
- `GET /api/labor/config/supabase` - Get Supabase config
- `POST /api/labor/config/supabase` - Save Supabase config
- `POST /api/labor/config/test` - Test Supabase connection

## UI/UX Design System

### Color Palette
```css
:root {
  --labor-primary: #3B82F6;      /* Blue */
  --labor-secondary: #10B981;    /* Green */
  --labor-accent: #F59E0B;       /* Amber */
  --labor-danger: #EF4444;       /* Red */
  --labor-warning: #F59E0B;      /* Orange */
  --labor-success: #10B981;      /* Green */
  --labor-info: #3B82F6;         /* Blue */
  --labor-light: #F8FAFC;        /* Light gray */
  --labor-dark: #1E293B;         /* Dark gray */
}
```

### Component Styling
- Modern card-based layouts
- Consistent spacing and typography
- Responsive design patterns
- Accessible color contrasts
- Professional gradients and shadows
- Interactive hover states
- Loading states and animations

### Table Design
- Sticky headers for large datasets
- Zebra striping for readability
- Hover effects for row selection
- Responsive column management
- Sorting and filtering indicators
- Pagination controls

## Implementation Phases

### Phase 1: Foundation (Days 1-3)
1. Set up Supabase configuration system
2. Create basic database schema
3. Implement configuration modal
4. Set up API structure

### Phase 2: Labor Profiles (Days 4-6)
1. Create labor profile models
2. Build CRUD modal and table
3. Implement name validation
4. Add group management

### Phase 3: Attendance System (Days 7-12)
1. Design attendance UI
2. Implement keyboard navigation
3. Add calculation logic
4. Build conflict detection
5. Create additional expense inputs

### Phase 4: Payment System (Days 13-16)
1. Build payment interface
2. Implement advance payment logic
3. Add Firestore synchronization
4. Create unpaid amounts display

### Phase 5: Dashboard & Analytics (Days 17-20)
1. Create dashboard layout
2. Implement various filters
3. Add analytics and charts
4. Build group movement functionality

### Phase 6: Polish & Testing (Days 21-23)
1. Implement responsive design
2. Add comprehensive error handling
3. Perform testing and bug fixes
4. Create documentation

## Security Considerations

### Data Protection
- Encrypt sensitive configuration data
- Implement proper access controls
- Validate all user inputs
- Use parameterized queries
- Implement rate limiting

### Authentication & Authorization
- Leverage existing JWT system
- Implement role-based access
- Secure API endpoints
- Validate firm/user context

## Performance Optimization

### Database
- Implement proper indexing
- Use connection pooling
- Optimize query performance
- Implement caching strategies

### Frontend
- Lazy load components
- Implement virtual scrolling for large tables
- Use computed properties for calculations
- Optimize bundle size

## Testing Strategy

### Unit Tests
- Component functionality
- Utility functions
- API endpoints
- Database operations

### Integration Tests
- End-to-end workflows
- Database synchronization
- API integration
- User interactions

### Performance Tests
- Load testing for large datasets
- Response time optimization
- Memory usage monitoring
- Database performance

## Deployment Considerations

### Environment Configuration
- Separate configs for dev/staging/production
- Secure environment variable management
- Database migration scripts
- Backup and recovery procedures

### Monitoring
- Error tracking and logging
- Performance monitoring
- Database health checks
- User activity analytics

This specification provides a comprehensive roadmap for implementing the labor management system with all requested features while maintaining high code quality and user experience standards.