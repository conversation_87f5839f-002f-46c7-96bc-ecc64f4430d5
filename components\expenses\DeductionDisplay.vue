<template>
  <div v-if="hasDeductions && deductions && deductions.length > 0" class="mt-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center">
      <svg class="h-5 w-5 text-orange-600 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 0l-3-3m3 3l-3 3M9 17h6m0 0l-3-3m3 3l-3 3" />
      </svg>
      Deductions Applied
    </h3>

    <!-- Deductions Summary -->
    <div class="bg-orange-50 border border-orange-200 rounded-md p-4 mb-4">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div>
          <span class="text-gray-600">{{ transactionType === 'RECEIPT' ? 'Gross Amount Received:' : 'Gross Amount Paid:' }}</span>
          <span class="font-medium ml-2">{{ formatCurrency(grossAmount) }}</span>
        </div>
        <div>
          <span class="text-gray-600">Total Deductions:</span>
          <span class="font-medium text-red-600 ml-2">{{ formatCurrency(totalDeductions) }}</span>
        </div>
        <div>
          <span class="text-gray-600">{{ transactionType === 'RECEIPT' ? 'Net Receipt:' : 'Net Payment:' }}</span>
          <span class="font-semibold text-green-600 ml-2">{{ formatCurrency(Math.abs(netAmount)) }}</span>
        </div>
      </div>
    </div>

    <!-- Deductions Table -->
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Deduction Name
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Amount
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Description
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(deduction, index) in deductions" :key="deduction.id || index">
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
              {{ deduction.name }}
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-red-600 font-medium">
              {{ formatCurrency(deduction.amount) }}
            </td>
            <td class="px-6 py-4 text-sm text-gray-500">
              {{ deduction.description || '-' }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
export default {
  name: 'DeductionDisplay',
  
  props: {
    hasDeductions: {
      type: Boolean,
      default: false
    },
    deductions: {
      type: Array,
      default: () => []
    },
    grossAmount: {
      type: Number,
      required: true
    },
    netAmount: {
      type: Number,
      required: true
    },
    transactionType: {
      type: String,
      default: 'PAYMENT'
    }
  },

  computed: {
    totalDeductions() {
      return this.deductions.reduce((sum, deduction) => {
        return sum + (parseFloat(deduction.amount) || 0);
      }, 0);
    }
  },

  methods: {
    formatCurrency(amount) {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount || 0);
    }
  }
};
</script>

<style scoped>
/* Component-specific styles */
.bg-orange-50 {
  background-color: #fff7ed;
}

.border-orange-200 {
  border-color: #fed7aa;
}

.text-orange-600 {
  color: #ea580c;
}
</style>
