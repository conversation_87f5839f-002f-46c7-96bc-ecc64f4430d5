<template>
  <div>
    <!-- Loading state -->
    <div v-if="isLoading" class="text-center py-4">
      <div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-600"></div>
      <p class="mt-2 text-gray-600">Loading transactions...</p>
    </div>

    <!-- Empty state check -->
    <div v-if="!isLoading && (!transactions || transactions.length === 0)" class="text-center py-4 text-gray-500">
      No transactions found for this sub
    </div>

    <!-- Info message removed as requested -->

    <!-- Transactions Table -->
    <div v-if="transactions && transactions.length > 0" class="bg-white rounded-lg shadow overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Date
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Paid To/From
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Amount
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Category
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Project
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                Description
              </th>
              <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="isLoading" class="animate-pulse">
              <td colspan="7" class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center justify-center">
                  <svg class="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="ml-2 text-sm text-gray-500">Loading transactions...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="transactions.length === 0">
              <td colspan="7" class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 text-center">
                No transactions found.
              </td>
            </tr>
            <tr
              v-for="transaction in transactions"
              :key="transaction.id"
              class="hover:bg-gray-50 transition-colors duration-150 ease-in-out"
              :class="{ 'bg-yellow-50': selectedTransactionId === transaction.id }"
              tabindex="0"
              @click="selectTransaction(transaction.id)"
              @keydown.enter="viewTransaction(transaction.id)"
              @keydown.arrow-up="navigateTransaction('up', transaction.id)"
              @keydown.arrow-down="navigateTransaction('down', transaction.id)"
            >
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(transaction.date) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ transaction.paidTo }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium" :class="getAmountClass(transaction.amount, transaction.category)">
                {{ formatCurrency(transaction.amount) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getCategoryClass(transaction.category)"
                >
                  {{ transaction.category || 'PAYMENT' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ transaction.project || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 truncate max-w-xs">
                {{ transaction.description || '-' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <!-- Show edit/delete buttons for all users (including sub-contractors) -->
                <button
                  @click.stop="editTransaction(transaction.id)"
                  class="text-green-600 hover:text-green-900 mr-2"
                  :disabled="transaction.parentExpenseId"
                  :class="{ 'opacity-50 cursor-not-allowed': transaction.parentExpenseId }"
                >
                  Edit
                </button>
                <button
                  @click.stop="confirmDelete(transaction.id)"
                  class="text-red-600 hover:text-red-900"
                  :disabled="transaction.parentExpenseId"
                  :class="{ 'opacity-50 cursor-not-allowed': transaction.parentExpenseId }"
                >
                  Delete
                </button>
              </td>
            </tr>
          </tbody>
          <!-- Summary Footer -->
          <tfoot class="bg-gray-50">
            <tr>
              <td colspan="2" class="px-6 py-3 text-right text-sm font-medium text-gray-900">
                Total:
              </td>
              <td class="px-6 py-3 text-left text-sm font-medium" :class="getAmountClass(totalAmount)">
                {{ formatCurrency(totalAmount) }}
              </td>
              <td colspan="4"></td>
            </tr>
          </tfoot>
        </table>
      </div>
    </div>

    <!-- Delete Confirmation Modal - Shown for all users -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Confirm Delete</h3>
        </div>
        <div class="p-4">
          <p class="text-gray-700">Are you sure you want to delete this transaction? This action cannot be undone.</p>
        </div>
        <div class="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="deleteTransaction(transactionToDelete)"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch } from 'vue';
import useUserRole from '~/composables/auth/useUserRole';
import useToast from '~/composables/ui/useToast';

export default {
  name: 'SubsTransactionList',

  props: {
    transactions: {
      type: Array,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['view', 'edit', 'delete'],

  setup(props, { emit }) {
    // State
    const showDeleteModal = ref(false);
    const transactionToDelete = ref(null);
    const selectedTransactionId = ref(null);

    // Get user role information
    const { isSubContractor } = useUserRole();
    const isSubContractorUser = computed(() => isSubContractor());

    // Get toast notifications
    const toast = useToast();


    // Watch for changes to transactions
    watch(() => props.transactions, (newTransactions) => {
      // Process transactions when they change
    }, { immediate: true });

    // Computed properties
    const totalAmount = computed(() => {
      if (!props.transactions || props.transactions.length === 0) {
        return 0;
      }
      return props.transactions.reduce((sum, transaction) => {
        // Ensure amount is a number
        const amount = typeof transaction.amount === 'number' ? transaction.amount : parseFloat(transaction.amount) || 0;
        return sum + amount;
      }, 0);
    });

    // Methods
    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount);
    };

    const formatDate = (date) => {
      if (!date) return '-';
      return new Date(date).toLocaleDateString();
    };

    const getAmountClass = (amount, category) => {
      // Use category to determine color, not the amount sign
      if (category === 'PAYMENT') {
        return 'text-red-600'; // PAYMENT should be red
      } else if (category === 'RECEIPT') {
        return 'text-green-600'; // RECEIPT should be green
      } else {
        // Fallback to using amount sign
        return amount < 0 ? 'text-red-600' : 'text-green-600';
      }
    };

    const getCategoryClass = (category) => {
      switch (category) {
        case 'PAYMENT':
          return 'bg-red-100 text-red-800';
        case 'RECEIPT':
          return 'bg-green-100 text-green-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    const viewTransaction = (id) => {

      emit('view', id);
    };

    const editTransaction = (id) => {
      // Check if transaction can be edited (not linked to an expense)
      const transaction = props.transactions.find(t => t.id === id);

      // Allow editing if the user is not a sub-contractor or if they are the creator of the transaction
      if (transaction && !transaction.parentExpenseId) {

        toast.info('Opening transaction for editing', 'Edit Transaction');
        emit('edit', id);
      } else if (transaction && transaction.parentExpenseId) {
        toast.warning('This transaction cannot be edited because it is linked to an expense', 'Cannot Edit');
      } else {
        toast.error('Transaction not found', 'Error');
      }
    };

    const confirmDelete = (id) => {
      // Check if transaction can be deleted (not linked to an expense)
      const transaction = props.transactions.find(t => t.id === id);

      // Allow deletion if the user is not a sub-contractor or if they are the creator of the transaction
      if (transaction && !transaction.parentExpenseId) {
        transactionToDelete.value = id;
        showDeleteModal.value = true;
      } else if (transaction && transaction.parentExpenseId) {
        toast.warning('This transaction cannot be deleted because it is linked to an expense', 'Cannot Delete');
      } else {
        toast.error('Transaction not found', 'Error');
      }
    };

    const deleteTransaction = (id) => {
      toast.info('Deleting transaction...', 'Delete Transaction');
      emit('delete', id);
      showDeleteModal.value = false;
      transactionToDelete.value = null;
    };

    const selectTransaction = (id) => {
      selectedTransactionId.value = id;
    };

    const navigateTransaction = (direction, currentId) => {
      const currentIndex = props.transactions.findIndex(transaction => transaction.id === currentId);

      if (direction === 'up' && currentIndex > 0) {
        selectedTransactionId.value = props.transactions[currentIndex - 1].id;
      } else if (direction === 'down' && currentIndex < props.transactions.length - 1) {
        selectedTransactionId.value = props.transactions[currentIndex + 1].id;
      }
    };

    return {
      showDeleteModal,
      transactionToDelete,
      selectedTransactionId,
      totalAmount,
      formatCurrency,
      formatDate,
      getAmountClass,
      getCategoryClass,
      viewTransaction,
      editTransaction,
      confirmDelete,
      deleteTransaction,
      selectTransaction,
      navigateTransaction,
      isSubContractorUser
    };
  }
};
</script>

<style scoped>
/* Add focus styles for keyboard navigation */
tr:focus {
  outline: 2px solid #4f46e5;
  outline-offset: -2px;
}

.max-w-xs {
  max-width: 20rem;
}
</style>
