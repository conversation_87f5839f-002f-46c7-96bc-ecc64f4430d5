/**
 * Authentication utility for token refresh and management
 * This composable centralizes token refresh functionality for use across the application
 */

export default function useAuthRefresh() {
  // Configure cookies with proper persistence settings
  const accessCookie = useCookie('token', {
    default: () => null,
    maxAge: 60 * 15, // 15 minutes (matches JWT expiry)
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    httpOnly: false // Allow client-side access for token refresh
  });

  const refreshCookie = useCookie('refreshToken', {
    default: () => null,
    maxAge: 60 * 60 * 24 * 7, // 7 days (matches JWT expiry)
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    httpOnly: false // Allow client-side access for token refresh
  });

  /**
   * Get token from cookie with localStorage fallback
   * @param {string} key - The key to retrieve (token or refreshToken)
   * @returns {string|null} - The token value or null
   */
  const getTokenWithFallback = (key) => {
    // First try to get from cookie
    const cookieValue = key === 'token' ? accessCookie.value : refreshCookie.value;
    if (cookieValue) {
      return cookieValue;
    }

    // Fallback to localStorage if available (client-side only)
    if (process.client && typeof localStorage !== 'undefined') {
      try {
        const localValue = localStorage.getItem(key);
        if (localValue) {
          // If found in localStorage, restore to cookie for future use
          if (key === 'token') {
            accessCookie.value = localValue;
          } else {
            refreshCookie.value = localValue;
          }
          return localValue;
        }
      } catch (error) {
        console.error(`Error reading ${key} from localStorage:`, error);
      }
    }

    return null;
  };

  /**
   * Set token in both cookie and localStorage
   * @param {string} key - The key to set (token or refreshToken)
   * @param {string} value - The token value
   */
  const setTokenWithBackup = (key, value) => {
    // Set in cookie
    if (key === 'token') {
      accessCookie.value = value;
    } else {
      refreshCookie.value = value;
    }

    // Backup to localStorage if available (client-side only)
    if (process.client && typeof localStorage !== 'undefined') {
      try {
        localStorage.setItem(key, value);
      } catch (error) {
        console.error(`Error storing ${key} in localStorage:`, error);
      }
    }
  };

  /**
   * Clear token from both cookie and localStorage
   * @param {string} key - The key to clear (token or refreshToken)
   */
  const clearToken = (key) => {
    // Clear from cookie
    if (key === 'token') {
      accessCookie.value = null;
    } else {
      refreshCookie.value = null;
    }

    // Clear from localStorage if available (client-side only)
    if (process.client && typeof localStorage !== 'undefined') {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.error(`Error removing ${key} from localStorage:`, error);
      }
    }
  };

  /**
   * Check if the token is expired
   * @param {string} token - The JWT token to check
   * @returns {boolean} - True if token is expired, false otherwise
   */
  const isTokenExpired = (token) => {
    if (!token) return true;
    
    try {
      const payload = decodeToken(token);
      if (!payload || !payload.exp) return true;
      
      // The exp field is in seconds; convert to milliseconds
      const expTime = payload.exp * 1000;
      return Date.now() > expTime;
    } catch (error) {
      console.error('Error checking token expiration:', error);
      return true;
    }
  };

  /**
   * Decode the JWT payload
   * @param {string} token - The JWT token to decode
   * @returns {object|null} - The decoded token payload or null
   */
  const decodeToken = (token) => {
    if (!token || typeof token !== 'string' || !token.includes('.')) {
      console.error('Invalid token format');
      return null;
    }
    
    try {
      const parts = token.split('.');
      if (parts.length !== 3) {
        console.error('Invalid JWT format: Expected 3 parts');
        return null;
      }
      
      const payloadBase64 = parts[1];
      
      // Server-side decoding
      if (process.server) {
        try {
          return JSON.parse(Buffer.from(payloadBase64, 'base64').toString('utf8'));
        } catch (e) {
          console.error('Server-side decode error:', e);
          return null;
        }
      } 
      // Client-side decoding
      else {
        try {
          const base64 = payloadBase64.replace(/-/g, '+').replace(/_/g, '/');
          const jsonPayload = decodeURIComponent(
            atob(base64)
              .split('')
              .map((c) => '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2))
              .join('')
          );
          return JSON.parse(jsonPayload);
        } catch (e) {
          // Fallback to simpler approach if the above fails
          try {
            return JSON.parse(atob(payloadBase64));
          } catch (e2) {
            console.error('Client-side decode error:', e2);
            return null;
          }
        }
      }
    } catch (error) {
      console.error('Error decoding token:', error);
      return null;
    }
  };

  /**
   * Attempt to refresh the access token using the refresh token
   * @param {string} refreshToken - The refresh token to use
   * @returns {Promise<string|null>} - The new access token or null
   */
  const refreshAccessToken = async (refreshToken) => {
    if (!refreshToken) return null;
    
    try {
      // Use a direct fetch to avoid circular dependencies with our intercepted fetch
      const response = await fetch('/api/auth/refresh', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ refreshToken })
      });
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error('Token refresh failed:', errorData.message || response.statusText);
        return null;
      }
      
      const data = await response.json();
      
      // Update the token using the new storage method
      if (data && data.accessToken) {
        setTokenWithBackup('token', data.accessToken);
        return data.accessToken;
      }
      
      return null;
    } catch (error) {
      console.error('Error refreshing token:', error);
      return null;
    }
  };

  /**
   * Ensure a valid token is available before proceeding with an operation
   * @returns {Promise<boolean>} - True if a valid token is available, false otherwise
   */
  const ensureValidToken = async () => {
    try {
      let accessToken = getTokenWithFallback('token');
      const refreshToken = getTokenWithFallback('refreshToken');

      // Token validation (logging removed to reduce console noise)

      // If no tokens exist, authentication is not possible
      if (!accessToken && !refreshToken) {
        console.log('No tokens available - authentication required');
        return false;
      }

      // If access token is missing but refresh token exists, try to get a new access token
      if (!accessToken && refreshToken) {
        console.log('Access token missing, attempting refresh with refresh token');
        accessToken = await refreshAccessToken(refreshToken);
        if (!accessToken) {
          console.log('Failed to refresh access token');
          return false;
        }
        console.log('Successfully refreshed access token');
      }

      // If access token exists but is expired, try to refresh it
      if (accessToken && isTokenExpired(accessToken) && refreshToken) {
        console.log('Access token expired, attempting refresh');
        accessToken = await refreshAccessToken(refreshToken);
        if (!accessToken) {
          console.log('Failed to refresh expired access token');
          return false;
        }
        console.log('Successfully refreshed expired access token');
      }

      const isValid = !!accessToken && !isTokenExpired(accessToken);
      return isValid;
    } catch (error) {
      console.error('Error ensuring valid token:', error);
      return false;
    }
  };

  return {
    isTokenExpired,
    refreshAccessToken,
    ensureValidToken,
    getToken: () => getTokenWithFallback('token'),
    getRefreshToken: () => getTokenWithFallback('refreshToken'),
    setTokenWithBackup,
    clearToken,
    // Legacy methods for backward compatibility
    getTokenDirect: () => accessCookie.value,
    getRefreshTokenDirect: () => refreshCookie.value
  };
} 