import { getFirestore, Timestamp, FieldValue } from 'firebase-admin/firestore';

/**
 * Creates or updates a ledger transaction for a credit note
 * If the ledger for the party doesn't exist, it will be created
 * For credit notes, we create a credit entry (we owe money to the party)
 *
 * @param {Object} billData - The bill data
 * @param {string} userId - The user ID
 * @param {string} firmId - The firm ID
 * @returns {Promise<Object>} - The result of the transaction
 */
export async function createCreditNoteLedgerTransaction(billData: any, userId: string, firmId: string) {
  try {
    const db = getFirestore();
    const firmIdStr = firmId.toString();
    const userIdStr = userId.toString();
    const now = Timestamp.now();
    const billIdStr = billData._id.toString();

    // Convert bill amount to number to ensure proper calculation
    const billAmount = Number(billData.ntot);

    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // First, check if a transaction already exists for this bill
      const transactionsCollection = db.collection('ledgerTransactions');
      const existingTransactionQuery = transactionsCollection
        .where('billId', '==', billIdStr)
        .where('firmId', '==', firmIdStr)
        .limit(1);

      const existingTransactionSnapshot = await transaction.get(existingTransactionQuery);
      let isUpdate = !existingTransactionSnapshot.empty;
      let oldTransactionData = null;
      let oldLedgerId = null;

      if (isUpdate) {
        oldTransactionData = existingTransactionSnapshot.docs[0].data();
        oldLedgerId = oldTransactionData.ledgerId;
      }

      // Check if a ledger exists for this party
      const ledgersCollection = db.collection('ledgers');
      const ledgerQuery = ledgersCollection
        .where('firmId', '==', firmIdStr)
        .where('name', '==', billData.supply)
        .limit(1);

      const ledgerSnapshot = await transaction.get(ledgerQuery);
      let ledgerRef;
      let ledgerData;
      let newBalance;
      let ledgerCreated = false;

      // If ledger doesn't exist, create it
      if (ledgerSnapshot.empty) {
        // Create a new ledger for this party
        ledgerRef = ledgersCollection.doc();
        ledgerCreated = true;

        const newLedger = {
          name: billData.supply,
          type: 'party',
          openingBalance: 0,
          currentBalance: -billAmount, // Negative for credit note (we owe money)
          address: billData.addr || '',
          state: billData.state || '',
          gstin: billData.gstin || 'UNREGISTERED',
          firmId: firmIdStr,
          userId: userIdStr,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };

        transaction.set(ledgerRef, newLedger);
        newBalance = -billAmount;
      } else {
        // Use existing ledger
        ledgerRef = ledgerSnapshot.docs[0].ref;
        ledgerData = ledgerSnapshot.docs[0].data();

        if (isUpdate && oldLedgerId === ledgerRef.id) {
          // This is an update to the same ledger, adjust the balance by the difference
          const oldAmount = oldTransactionData.amount;
          const amountDifference = billAmount - oldAmount;

          // Calculate new balance based on the difference (subtract for credit note)
          newBalance = ledgerData.currentBalance - amountDifference;
        } else if (isUpdate) {
          // This is an update but the ledger has changed
          // We need to adjust the old ledger and set the new one

          // First, get the old ledger
          const oldLedgerRef = ledgersCollection.doc(oldLedgerId);
          const oldLedgerDoc = await transaction.get(oldLedgerRef);

          if (oldLedgerDoc.exists) {
            const oldLedgerData = oldLedgerDoc.data();
            // Add the old amount back to the old ledger (reverse the credit)
            transaction.update(oldLedgerRef, {
              currentBalance: oldLedgerData.currentBalance + oldTransactionData.amount,
              updatedAt: now
            });
          }

          // Now subtract the new amount from the new ledger
          newBalance = ledgerData.currentBalance - billAmount;
        } else {
          // This is a new transaction for an existing ledger
          newBalance = ledgerData.currentBalance - billAmount;
        }

        // Update ledger balance
        transaction.update(ledgerRef, {
          currentBalance: newBalance,
          updatedAt: now
        });
      }

      // Extract GST information from bill data
      const firmGSTNo = billData.gstSelection?.firmGST?.gstNumber || '';
      const partyGSTNo = billData.gstSelection?.partyGST?.gstNumber || billData.gstin || 'UNREGISTERED';

      // Determine if this is an inter-state transaction
      const firmStateCode = firmGSTNo.substring(0, 2);
      const partyStateCode = partyGSTNo.substring(0, 2);
      const isInterState = firmStateCode !== partyStateCode && partyGSTNo !== 'UNREGISTERED';

      // Prepare transaction data
      const transactionData = {
        ledgerId: ledgerRef.id,
        billId: billIdStr,
        date: billData.bdate instanceof Date ? Timestamp.fromDate(billData.bdate) : now,
        description: `Credit Note #${billData.bno}`,
        amount: billAmount,
        type: 'credit', // Credit for credit note (we owe money to the party)
        balance: newBalance,
        firmId: firmIdStr,
        userId: userIdStr,

        // Multi-GST tracking fields
        firmGSTNo: firmGSTNo,
        partyGSTNo: partyGSTNo,
        firmStateCode: firmStateCode,
        partyStateCode: partyStateCode,
        isInterState: isInterState,

        updatedAt: now
      };

      // If this is an update, update the existing transaction
      // Otherwise, create a new one
      if (isUpdate) {
        const existingTransactionRef = existingTransactionSnapshot.docs[0].ref;
        transaction.update(existingTransactionRef, {
          ...transactionData,
          updatedAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: existingTransactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: true,
          message: `Updated credit entry for ${billData.supply}`
        };
      } else {
        // Create a new transaction entry
        const transactionRef = transactionsCollection.doc();

        transaction.set(transactionRef, {
          ...transactionData,
          createdAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: transactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: false,
          message: ledgerCreated
            ? `Created new ledger for ${billData.supply} and added credit entry`
            : `Added credit entry to existing ledger for ${billData.supply}`
        };
      }
    });
  } catch (error) {
    console.error('Error creating/updating ledger transaction for credit note:', error);
    throw error;
  }
}

/**
 * Creates or updates a ledger transaction for a debit note
 * If the ledger for the party doesn't exist, it will be created
 * For debit notes, we create a debit entry (party owes money)
 *
 * @param {Object} billData - The bill data
 * @param {string} userId - The user ID
 * @param {string} firmId - The firm ID
 * @returns {Promise<Object>} - The result of the transaction
 */
export async function createDebitNoteLedgerTransaction(billData: any, userId: string, firmId: string) {
  try {
    const db = getFirestore();
    const firmIdStr = firmId.toString();
    const userIdStr = userId.toString();
    const now = Timestamp.now();
    const billIdStr = billData._id.toString();

    // Convert bill amount to number to ensure proper calculation
    const billAmount = Number(billData.ntot);

    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // First, check if a transaction already exists for this bill
      const transactionsCollection = db.collection('ledgerTransactions');
      const existingTransactionQuery = transactionsCollection
        .where('billId', '==', billIdStr)
        .where('firmId', '==', firmIdStr)
        .limit(1);

      const existingTransactionSnapshot = await transaction.get(existingTransactionQuery);
      let isUpdate = !existingTransactionSnapshot.empty;
      let oldTransactionData = null;
      let oldLedgerId = null;

      if (isUpdate) {
        oldTransactionData = existingTransactionSnapshot.docs[0].data();
        oldLedgerId = oldTransactionData.ledgerId;
      }

      // Check if a ledger exists for this party
      const ledgersCollection = db.collection('ledgers');
      const ledgerQuery = ledgersCollection
        .where('firmId', '==', firmIdStr)
        .where('name', '==', billData.supply)
        .limit(1);

      const ledgerSnapshot = await transaction.get(ledgerQuery);
      let ledgerRef;
      let ledgerData;
      let newBalance;
      let ledgerCreated = false;

      // If ledger doesn't exist, create it
      if (ledgerSnapshot.empty) {
        // Create a new ledger for this party
        ledgerRef = ledgersCollection.doc();
        ledgerCreated = true;

        const newLedger = {
          name: billData.supply,
          type: 'party',
          openingBalance: 0,
          currentBalance: billAmount, // Positive for debit note (party owes money)
          address: billData.addr || '',
          state: billData.state || '',
          gstin: billData.gstin || 'UNREGISTERED',
          firmId: firmIdStr,
          userId: userIdStr,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };

        transaction.set(ledgerRef, newLedger);
        newBalance = billAmount;
      } else {
        // Use existing ledger
        ledgerRef = ledgerSnapshot.docs[0].ref;
        ledgerData = ledgerSnapshot.docs[0].data();

        if (isUpdate && oldLedgerId === ledgerRef.id) {
          // This is an update to the same ledger, adjust the balance by the difference
          const oldAmount = oldTransactionData.amount;
          const amountDifference = billAmount - oldAmount;

          // Calculate new balance based on the difference
          newBalance = ledgerData.currentBalance + amountDifference;
        } else if (isUpdate) {
          // This is an update but the ledger has changed
          // We need to adjust the old ledger and set the new one

          // First, get the old ledger
          const oldLedgerRef = ledgersCollection.doc(oldLedgerId);
          const oldLedgerDoc = await transaction.get(oldLedgerRef);

          if (oldLedgerDoc.exists) {
            const oldLedgerData = oldLedgerDoc.data();
            // Subtract the old amount from the old ledger (reverse the debit)
            transaction.update(oldLedgerRef, {
              currentBalance: oldLedgerData.currentBalance - oldTransactionData.amount,
              updatedAt: now
            });
          }

          // Now add the new amount to the new ledger
          newBalance = ledgerData.currentBalance + billAmount;
        } else {
          // This is a new transaction for an existing ledger
          newBalance = ledgerData.currentBalance + billAmount;
        }

        // Update ledger balance
        transaction.update(ledgerRef, {
          currentBalance: newBalance,
          updatedAt: now
        });
      }

      // Extract GST information from bill data
      const firmGSTNo = billData.gstSelection?.firmGST?.gstNumber || '';
      const partyGSTNo = billData.gstSelection?.partyGST?.gstNumber || billData.gstin || 'UNREGISTERED';

      // Determine if this is an inter-state transaction
      const firmStateCode = firmGSTNo.substring(0, 2);
      const partyStateCode = partyGSTNo.substring(0, 2);
      const isInterState = firmStateCode !== partyStateCode && partyGSTNo !== 'UNREGISTERED';

      // Prepare transaction data
      const transactionData = {
        ledgerId: ledgerRef.id,
        billId: billIdStr,
        date: billData.bdate instanceof Date ? Timestamp.fromDate(billData.bdate) : now,
        description: `Debit Note #${billData.bno}`,
        amount: billAmount,
        type: 'debit', // Debit for debit note (party owes money)
        balance: newBalance,
        firmId: firmIdStr,
        userId: userIdStr,

        // Multi-GST tracking fields
        firmGSTNo: firmGSTNo,
        partyGSTNo: partyGSTNo,
        firmStateCode: firmStateCode,
        partyStateCode: partyStateCode,
        isInterState: isInterState,

        updatedAt: now
      };

      // If this is an update, update the existing transaction
      // Otherwise, create a new one
      if (isUpdate) {
        const existingTransactionRef = existingTransactionSnapshot.docs[0].ref;
        transaction.update(existingTransactionRef, {
          ...transactionData,
          updatedAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: existingTransactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: true,
          message: `Updated debit entry for ${billData.supply}`
        };
      } else {
        // Create a new transaction entry
        const transactionRef = transactionsCollection.doc();

        transaction.set(transactionRef, {
          ...transactionData,
          createdAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: transactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: false,
          message: ledgerCreated
            ? `Created new ledger for ${billData.supply} and added debit entry`
            : `Added debit entry to existing ledger for ${billData.supply}`
        };
      }
    });
  } catch (error) {
    console.error('Error creating/updating ledger transaction for debit note:', error);
    throw error;
  }
}

/**
 * Creates or updates a ledger transaction for a sales bill
 * If the ledger for the party doesn't exist, it will be created
 * For sales bills, we create a debit entry (party owes money)
 *
 * @param {Object} billData - The bill data
 * @param {string} userId - The user ID
 * @param {string} firmId - The firm ID
 * @returns {Promise<Object>} - The result of the transaction
 */
export async function createSalesBillLedgerTransaction(billData: any, userId: string, firmId: string) {
  try {
    const db = getFirestore();
    const firmIdStr = firmId.toString();
    const userIdStr = userId.toString();
    const now = Timestamp.now();
    const billIdStr = billData._id.toString();

    // Convert bill amount to number to ensure proper calculation
    const billAmount = Number(billData.ntot);

    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // First, check if a transaction already exists for this bill
      const transactionsCollection = db.collection('ledgerTransactions');
      const existingTransactionQuery = transactionsCollection
        .where('billId', '==', billIdStr)
        .where('firmId', '==', firmIdStr)
        .limit(1);

      const existingTransactionSnapshot = await transaction.get(existingTransactionQuery);
      let isUpdate = !existingTransactionSnapshot.empty;
      let oldTransactionData = null;
      let oldLedgerId = null;

      if (isUpdate) {
        oldTransactionData = existingTransactionSnapshot.docs[0].data();
        oldLedgerId = oldTransactionData.ledgerId;
      }

      // Check if a ledger exists for this party
      const ledgersCollection = db.collection('ledgers');
      const ledgerQuery = ledgersCollection
        .where('firmId', '==', firmIdStr)
        .where('name', '==', billData.supply)
        .limit(1);

      const ledgerSnapshot = await transaction.get(ledgerQuery);
      let ledgerRef;
      let ledgerData;
      let newBalance;
      let ledgerCreated = false;

      // If ledger doesn't exist, create it
      if (ledgerSnapshot.empty) {
        // Create a new ledger for this party
        ledgerRef = ledgersCollection.doc();
        ledgerCreated = true;

        const newLedger = {
          name: billData.supply,
          type: 'party',
          openingBalance: 0,
          currentBalance: billAmount, // Start with the bill amount
          address: billData.addr || '',
          state: billData.state || '',
          gstin: billData.gstin || 'UNREGISTERED',
          firmId: firmIdStr,
          userId: userIdStr,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };

        // Set the new ledger in the transaction
        transaction.set(ledgerRef, newLedger);

        // Set ledger data for later use
        ledgerData = newLedger;
        newBalance = billAmount;
      } else {
        // Use existing ledger
        ledgerRef = ledgerSnapshot.docs[0].ref;
        ledgerData = ledgerSnapshot.docs[0].data();

        if (isUpdate && oldLedgerId === ledgerRef.id) {
          // This is an update to the same ledger, adjust the balance by the difference
          const oldAmount = oldTransactionData.amount;
          const amountDifference = billAmount - oldAmount;

          // Calculate new balance based on the difference
          newBalance = ledgerData.currentBalance + amountDifference;
        } else if (isUpdate) {
          // This is an update but the ledger has changed
          // We need to adjust the old ledger and set the new one

          // First, get the old ledger
          const oldLedgerRef = ledgersCollection.doc(oldLedgerId);
          const oldLedgerDoc = await transaction.get(oldLedgerRef);

          if (oldLedgerDoc.exists) {
            const oldLedgerData = oldLedgerDoc.data();
            // Subtract the old amount from the old ledger
            transaction.update(oldLedgerRef, {
              currentBalance: oldLedgerData.currentBalance - oldTransactionData.amount,
              updatedAt: now
            });
          }

          // Now add the new amount to the new ledger
          newBalance = ledgerData.currentBalance + billAmount;
        } else {
          // This is a new transaction for an existing ledger
          newBalance = ledgerData.currentBalance + billAmount;
        }

        // Update ledger balance
        transaction.update(ledgerRef, {
          currentBalance: newBalance,
          updatedAt: now
        });
      }

      // Extract GST information from bill data
      const firmGSTNo = billData.gstSelection?.firmGST?.gstNumber || '';
      const partyGSTNo = billData.gstSelection?.partyGST?.gstNumber || billData.gstin || 'UNREGISTERED';

      // Determine if this is an inter-state transaction
      const firmStateCode = firmGSTNo.substring(0, 2);
      const partyStateCode = partyGSTNo.substring(0, 2);
      const isInterState = firmStateCode !== partyStateCode && partyGSTNo !== 'UNREGISTERED';

      console.log('📊 GST Tracking for Sales Bill:', {
        firmGSTNo,
        partyGSTNo,
        firmStateCode,
        partyStateCode,
        isInterState,
        billNo: billData.bno
      });

      // Prepare transaction data
      const transactionData = {
        ledgerId: ledgerRef.id,
        billId: billIdStr,
        date: billData.bdate instanceof Date ? Timestamp.fromDate(billData.bdate) : now,
        description: `Sales Bill #${billData.bno}`,
        amount: billAmount,
        type: 'debit', // Debit for sales bill (party owes money)
        balance: newBalance,
        firmId: firmIdStr,
        userId: userIdStr,

        // Multi-GST tracking fields
        firmGSTNo: firmGSTNo,
        partyGSTNo: partyGSTNo,
        firmStateCode: firmStateCode,
        partyStateCode: partyStateCode,
        isInterState: isInterState,

        updatedAt: now
      };

      // If this is an update, update the existing transaction
      // Otherwise, create a new one
      if (isUpdate) {
        const existingTransactionRef = existingTransactionSnapshot.docs[0].ref;
        transaction.update(existingTransactionRef, {
          ...transactionData,
          updatedAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: existingTransactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: true,
          message: `Updated debit entry for ${billData.supply}`
        };
      } else {
        // Create a new transaction entry
        const transactionRef = transactionsCollection.doc();

        transaction.set(transactionRef, {
          ...transactionData,
          createdAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: transactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: false,
          message: ledgerCreated
            ? `Created new ledger for ${billData.supply} and added debit entry`
            : `Added debit entry to existing ledger for ${billData.supply}`
        };
      }
    });
  } catch (error) {
    console.error('Error creating/updating ledger transaction for sales bill:', error);
    throw error;
  }
}

/**
 * Creates or updates a ledger transaction for a purchase bill
 * If the ledger for the party doesn't exist, it will be created
 * For purchase bills, we create a credit entry (we owe money to the party)
 *
 * @param {Object} billData - The bill data
 * @param {string} userId - The user ID
 * @param {string} firmId - The firm ID
 * @returns {Promise<Object>} - The result of the transaction
 */
export async function createPurchaseBillLedgerTransaction(billData: any, userId: string, firmId: string) {
  try {
    const db = getFirestore();
    const firmIdStr = firmId.toString();
    const userIdStr = userId.toString();
    const now = Timestamp.now();
    const billIdStr = billData._id.toString();

    // Convert bill amount to number to ensure proper calculation
    const billAmount = Number(billData.ntot);

    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // First, check if a transaction already exists for this bill
      const transactionsCollection = db.collection('ledgerTransactions');
      const existingTransactionQuery = transactionsCollection
        .where('billId', '==', billIdStr)
        .where('firmId', '==', firmIdStr)
        .limit(1);

      const existingTransactionSnapshot = await transaction.get(existingTransactionQuery);
      let isUpdate = !existingTransactionSnapshot.empty;
      let oldTransactionData = null;
      let oldLedgerId = null;

      if (isUpdate) {
        oldTransactionData = existingTransactionSnapshot.docs[0].data();
        oldLedgerId = oldTransactionData.ledgerId;
      }

      // Check if a ledger exists for this party
      const ledgersCollection = db.collection('ledgers');
      const ledgerQuery = ledgersCollection
        .where('firmId', '==', firmIdStr)
        .where('name', '==', billData.supply)
        .limit(1);

      const ledgerSnapshot = await transaction.get(ledgerQuery);
      let ledgerRef;
      let ledgerData;
      let newBalance;
      let ledgerCreated = false;

      // If ledger doesn't exist, create it
      if (ledgerSnapshot.empty) {
        // Create a new ledger for this party
        ledgerRef = ledgersCollection.doc();
        ledgerCreated = true;

        const newLedger = {
          name: billData.supply,
          type: 'party',
          openingBalance: 0,
          currentBalance: -billAmount, // Negative for purchase bill (we owe money)
          address: billData.addr || '',
          state: billData.state || '',
          gstin: billData.gstin || 'UNREGISTERED',
          firmId: firmIdStr,
          userId: userIdStr,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };

        // Set the new ledger in the transaction
        transaction.set(ledgerRef, newLedger);

        // Set ledger data for later use
        ledgerData = newLedger;
        newBalance = -billAmount;
      } else {
        // Use existing ledger
        ledgerRef = ledgerSnapshot.docs[0].ref;
        ledgerData = ledgerSnapshot.docs[0].data();

        if (isUpdate && oldLedgerId === ledgerRef.id) {
          // This is an update to the same ledger, adjust the balance by the difference
          const oldAmount = oldTransactionData.amount;
          const amountDifference = billAmount - oldAmount;

          // Calculate new balance based on the difference (subtract for purchase)
          newBalance = ledgerData.currentBalance - amountDifference;
        } else if (isUpdate) {
          // This is an update but the ledger has changed
          // We need to adjust the old ledger and set the new one

          // First, get the old ledger
          const oldLedgerRef = ledgersCollection.doc(oldLedgerId);
          const oldLedgerDoc = await transaction.get(oldLedgerRef);

          if (oldLedgerDoc.exists) {
            const oldLedgerData = oldLedgerDoc.data();
            // Add the old amount back to the old ledger (reverse the credit)
            transaction.update(oldLedgerRef, {
              currentBalance: oldLedgerData.currentBalance + oldTransactionData.amount,
              updatedAt: now
            });
          }

          // Now subtract the new amount from the new ledger
          newBalance = ledgerData.currentBalance - billAmount;
        } else {
          // This is a new transaction for an existing ledger
          newBalance = ledgerData.currentBalance - billAmount;
        }

        // Update ledger balance
        transaction.update(ledgerRef, {
          currentBalance: newBalance,
          updatedAt: now
        });
      }

      // Extract GST information from bill data
      const firmGSTNo = billData.gstSelection?.firmGST?.gstNumber || '';
      const partyGSTNo = billData.gstSelection?.partyGST?.gstNumber || billData.gstin || 'UNREGISTERED';

      // Determine if this is an inter-state transaction
      const firmStateCode = firmGSTNo.substring(0, 2);
      const partyStateCode = partyGSTNo.substring(0, 2);
      const isInterState = firmStateCode !== partyStateCode && partyGSTNo !== 'UNREGISTERED';

      // Prepare transaction data
      const transactionData = {
        ledgerId: ledgerRef.id,
        billId: billIdStr,
        date: billData.bdate instanceof Date ? Timestamp.fromDate(billData.bdate) : now,
        description: `Purchase Bill #${billData.bno}`,
        amount: billAmount,
        type: 'credit', // Credit for purchase bill (we owe money to the party)
        balance: newBalance,
        firmId: firmIdStr,
        userId: userIdStr,

        // Multi-GST tracking fields
        firmGSTNo: firmGSTNo,
        partyGSTNo: partyGSTNo,
        firmStateCode: firmStateCode,
        partyStateCode: partyStateCode,
        isInterState: isInterState,

        updatedAt: now
      };

      // If this is an update, update the existing transaction
      // Otherwise, create a new one
      if (isUpdate) {
        const existingTransactionRef = existingTransactionSnapshot.docs[0].ref;
        transaction.update(existingTransactionRef, {
          ...transactionData,
          updatedAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: existingTransactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: true,
          message: `Updated credit entry for ${billData.supply}`
        };
      } else {
        // Create a new transaction entry
        const transactionRef = transactionsCollection.doc();

        transaction.set(transactionRef, {
          ...transactionData,
          createdAt: now
        });

        return {
          success: true,
          ledgerId: ledgerRef.id,
          transactionId: transactionRef.id,
          ledgerCreated: ledgerCreated,
          isUpdate: false,
          message: ledgerCreated
            ? `Created new ledger for ${billData.supply} and added credit entry`
            : `Added credit entry to existing ledger for ${billData.supply}`
        };
      }
    });
  } catch (error) {
    console.error('Error creating/updating ledger transaction for purchase bill:', error);
    throw error;
  }
}

/**
 * Creates a ledger transaction for a cancelled bill
 * This function reverses the original transaction by creating an opposite entry
 *
 * @param {Object} billData - The cancelled bill data
 * @param {string} userId - The user ID
 * @param {string} firmId - The firm ID
 * @returns {Promise<Object>} - The result of the transaction
 */
export async function createCancellationLedgerTransaction(billData: any, userId: string, firmId: string) {
  try {
    const db = getFirestore();
    const firmIdStr = firmId.toString();
    const userIdStr = userId.toString();
    const now = Timestamp.now();
    const billIdStr = billData._id.toString();

    // Convert bill amount to number to ensure proper calculation
    const billAmount = Number(billData.ntot);

    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // Check if a ledger exists for this party
      const ledgersCollection = db.collection('ledgers');
      const ledgerQuery = ledgersCollection
        .where('firmId', '==', firmIdStr)
        .where('name', '==', billData.supply)
        .limit(1);

      const ledgerSnapshot = await transaction.get(ledgerQuery);

      // If ledger doesn't exist, create a new one
      let ledgerRef;
      let ledgerData;

      if (ledgerSnapshot.empty) {
        console.log(`Ledger not found for party: ${billData.supply}, creating a new one`);

        // Create a new ledger for this party
        ledgerRef = db.collection('ledgers').doc();
        ledgerData = {
          name: billData.supply,
          type: 'party',
          openingBalance: 0,
          currentBalance: 0,
          address: billData.addr || '',
          state: billData.state || '',
          gstin: billData.gstin || 'UNREGISTERED',
          firmId: firmIdStr,
          userId: userIdStr,
          isActive: true,
          createdAt: now,
          updatedAt: now
        };

        // Set the new ledger data
        transaction.set(ledgerRef, ledgerData);
      } else {
        // Get the ledger reference and data
        ledgerRef = ledgerSnapshot.docs[0].ref;
        ledgerData = ledgerSnapshot.docs[0].data();
      }
      let newBalance;

      // Determine the transaction type based on bill type
      // For cancellation, we do the opposite of the original transaction
      let transactionType;
      let description;

      switch (billData.btype) {
        case 'SALES':
          // Original: Debit (party owes money)
          // Cancellation: Credit (reverse the debit)
          transactionType = 'credit';
          description = `Cancelled Sales Bill #${billData.bno}`;
          newBalance = ledgerData.currentBalance - billAmount; // Subtract from balance
          break;
        case 'PURCHASE':
          // Original: Credit (we owe money)
          // Cancellation: Debit (reverse the credit)
          transactionType = 'debit';
          description = `Cancelled Purchase Bill #${billData.bno}`;
          newBalance = ledgerData.currentBalance + billAmount; // Add to balance
          break;
        case 'CREDIT NOTE':
          // Original: Credit (we owe money)
          // Cancellation: Debit (reverse the credit)
          transactionType = 'debit';
          description = `Cancelled Credit Note #${billData.bno}`;
          newBalance = ledgerData.currentBalance + billAmount; // Add to balance
          break;
        case 'DEBIT NOTE':
          // Original: Debit (party owes money)
          // Cancellation: Credit (reverse the debit)
          transactionType = 'credit';
          description = `Cancelled Debit Note #${billData.bno}`;
          newBalance = ledgerData.currentBalance - billAmount; // Subtract from balance
          break;
        default:
          throw new Error(`Unsupported bill type: ${billData.btype}`);
      }

      // Update ledger balance
      transaction.update(ledgerRef, {
        currentBalance: newBalance,
        updatedAt: now
      });

      // Create a new transaction entry for the cancellation
      const transactionsCollection = db.collection('ledgerTransactions');
      const transactionRef = transactionsCollection.doc();

      const transactionData = {
        ledgerId: ledgerRef.id,
        billId: billIdStr,
        date: now, // Use current time for cancellation
        description,
        amount: billAmount,
        type: transactionType,
        balance: newBalance,
        firmId: firmIdStr,
        userId: userIdStr,
        cancellation: true, // Mark this as a cancellation transaction
        cancelledAt: now,
        createdAt: now,
        updatedAt: now
      };

      transaction.set(transactionRef, transactionData);

      return {
        success: true,
        ledgerId: ledgerRef.id,
        transactionId: transactionRef.id,
        message: `Created cancellation entry for ${billData.btype} #${billData.bno}`
      };
    });
  } catch (error) {
    console.error('Error creating ledger transaction for cancelled bill:', error);
    throw error;
  }
}

/**
 * Creates a ledger transaction to reverse the effect of a deleted bill
 * This function handles all bill types: PURCHASE, SALES, DEBIT NOTE, CREDIT NOTE
 *
 * @param {Object} billData - The bill data that was deleted
 * @param {string} userId - The user ID
 * @param {string} firmId - The firm ID
 * @returns {Promise<Object>} - The result of the transaction
 */
export async function createDeletionLedgerTransaction(billData: any, userId: string, firmId: string) {
  try {
    const db = getFirestore();
    const firmIdStr = firmId.toString();
    const userIdStr = userId.toString();
    const now = Timestamp.now();
    const billIdStr = billData._id.toString();

    // Convert bill amount to number to ensure proper calculation
    const billAmount = Number(billData.ntot);

    // Use a transaction to ensure data consistency
    return await db.runTransaction(async (transaction) => {
      // First, do all reads before any writes (Firestore requirement)
      const transactionsCollection = db.collection('ledgerTransactions');
      const existingTransactionQuery = transactionsCollection
        .where('billId', '==', billIdStr)
        .where('firmId', '==', firmIdStr)
        .limit(1);

      const existingTransactionSnapshot = await transaction.get(existingTransactionQuery);

      // Find the ledger for this party
      const ledgersCollection = db.collection('ledgers');
      const ledgerQuery = ledgersCollection
        .where('firmId', '==', firmIdStr)
        .where('name', '==', billData.supply)
        .limit(1);

      const ledgerSnapshot = await transaction.get(ledgerQuery);

      // Now do all writes after reads
      if (!existingTransactionSnapshot.empty) {
        // Delete the existing transaction
        const existingTransactionRef = existingTransactionSnapshot.docs[0].ref;
        transaction.delete(existingTransactionRef);
        console.log('Deleted existing ledger transaction for bill');
      }

      if (ledgerSnapshot.empty) {
        console.log('No ledger found for party, skipping ledger update');
        return { success: true, message: 'No ledger found to update' };
      }

      // Update the existing ledger by reversing the bill amount
      const ledgerRef = ledgerSnapshot.docs[0].ref;
      const ledgerData = ledgerSnapshot.docs[0].data();

      // Calculate the amount to reverse based on bill type
      let amountToReverse = 0;
      switch (billData.btype) {
        case 'PURCHASE':
        case 'DEBIT NOTE':
          // These increased the amount we owe, so subtract when deleting
          amountToReverse = -billAmount;
          break;
        case 'SALES':
        case 'CREDIT NOTE':
          // These increased the amount owed to us, so subtract when deleting
          amountToReverse = -billAmount;
          break;
        default:
          console.warn(`Unknown bill type for ledger reversal: ${billData.btype}`);
          return { success: true, message: 'Unknown bill type, no ledger update needed' };
      }

      const newBalance = ledgerData.currentBalance + amountToReverse;

      // Update the ledger balance
      transaction.update(ledgerRef, {
        currentBalance: newBalance,
        updatedAt: now
      });

      console.log(`Updated ledger for ${billData.supply}: ${ledgerData.currentBalance} -> ${newBalance}`);

      return {
        success: true,
        message: 'Ledger transaction reversed successfully',
        ledgerId: ledgerRef.id,
        oldBalance: ledgerData.currentBalance,
        newBalance: newBalance,
        amountReversed: amountToReverse
      };
    });

  } catch (error) {
    console.error('Error in createDeletionLedgerTransaction:', error);
    throw error;
  }
}
