<template>
  <div>
    <!-- Search and Filter Section -->
    <div class="bg-white rounded-lg shadow p-3 sm:p-4 mb-4 sm:mb-6">
      <div class="flex flex-wrap items-end gap-3 sm:gap-4">
        <!-- Search Field -->
        <div class="w-full sm:w-64">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
            <MagnifyingGlassIcon class="h-4 w-4 mr-1" />
            Search
          </label>
          <div class="relative">
            <input
              type="text"
              id="search"
              v-model="searchQuery"
              placeholder="Search by paid to, description..."
              class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
              @keyup.enter="applyFilters"
            />
            <MagnifyingGlassIcon class="h-4 w-4 text-gray-400 absolute left-2 top-1/2 transform -translate-y-1/2" />
          </div>
        </div>

        <!-- Date Range -->
        <div class="w-full sm:w-auto flex-grow">
          <label for="dateRange" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
            <CalendarIcon class="h-4 w-4 mr-1" />
            Date Range
          </label>
          <div class="flex gap-2">
            <div class="flex-1 relative">
              <input
                type="date"
                id="startDate"
                v-model="filters.startDate"
                class="w-full pl-8 pr-2 sm:pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                placeholder="From"
              />
              <CalendarIcon class="h-4 w-4 text-gray-400 absolute left-2 top-1/2 transform -translate-y-1/2" />
            </div>
            <div class="flex-1 relative">
              <input
                type="date"
                id="endDate"
                v-model="filters.endDate"
                class="w-full pl-8 pr-2 sm:pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 text-sm"
                placeholder="To"
              />
              <CalendarIcon class="h-4 w-4 text-gray-400 absolute left-2 top-1/2 transform -translate-y-1/2" />
            </div>
          </div>
        </div>

        <!-- Category Filter -->
        <div class="w-full sm:w-48">
          <label for="category" class="block text-sm font-medium text-gray-700 mb-1 flex items-center">
            <TagIcon class="h-4 w-4 mr-1" />
            Category
          </label>
          <div class="relative">
            <TagIcon class="h-4 w-4 text-gray-400 absolute left-2 top-1/2 transform -translate-y-1/2 z-10" />
            <select
              id="category"
              v-model="filters.category"
              class="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 appearance-none"
            >
            <option value="">All Categories</option>
            <option value="PAYMENT">Payment</option>
            <option value="RECEIPT">Receipt</option>
            <option value="TRANSFER">Transfer</option>
            <option v-for="category in uniqueCategories" :key="category" :value="category">
              {{ category }}
            </option>
          </select>
          </div>
        </div>

        <!-- Filter Actions -->
        <div class="flex gap-2 ml-auto">
          <button
            @click="applyFilters"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm flex items-center"
            :disabled="isLoading"
          >
            <span v-if="isLoading">
              <svg class="animate-spin -ml-1 mr-1 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            </span>
            <FunnelIcon v-else class="h-4 w-4 mr-1" />
            <span>Filter</span>
          </button>
          <button
            @click="resetFilters"
            class="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 text-sm flex items-center"
            :disabled="isLoading"
          >
            <ArrowPathIcon class="h-4 w-4 mr-1" />
            Reset
          </button>
        </div>
      </div>

      <!-- Advanced Filters (Collapsible) -->
      <div class="mt-4 flex justify-center">
        <button
          @click="showAdvancedFilters = !showAdvancedFilters"
          class="text-indigo-600 hover:text-indigo-800 text-sm flex items-center justify-center py-1 px-3 border border-indigo-200 rounded-md bg-indigo-50 hover:bg-indigo-100 transition-colors duration-200"
        >
          <MagnifyingGlassIcon class="h-4 w-4 mr-1" />
          <span v-if="showAdvancedFilters">Hide Advanced Filters</span>
          <span v-if="!showAdvancedFilters">Show Advanced Filters</span>
          <svg
            :class="{ 'transform rotate-180': showAdvancedFilters }"
            class="ml-1 h-4 w-4 transition-transform duration-200"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <div v-if="showAdvancedFilters" class="mt-3 sm:mt-4 flex flex-wrap items-end gap-3 sm:gap-4">
        <!-- Payment Mode Filter -->
        <div class="w-full sm:w-48">
          <label for="paymentMode" class="block text-sm font-medium text-gray-700 mb-1">Payment Mode</label>
          <select
            id="paymentMode"
            v-model="filters.paymentMode"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">All Payment Modes</option>
            <option value="cash">Cash</option>
            <option value="bank">Bank</option>
          </select>
        </div>

        <!-- Paid To Group Filter -->
        <div class="w-full sm:w-48">
          <label for="paidToGroup" class="block text-sm font-medium text-gray-700 mb-1">Paid To Group</label>
          <select
            id="paidToGroup"
            v-model="filters.paidToGroup"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">All Groups</option>
            <option v-for="group in uniquePaidToGroups" :key="group" :value="group">
              {{ group }}
            </option>
          </select>
        </div>

        <!-- Project Filter -->
        <div class="w-full sm:w-48">
          <label for="project" class="block text-sm font-medium text-gray-700 mb-1">Project</label>
          <select
            id="project"
            v-model="filters.project"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">All Projects</option>
            <option v-for="project in uniqueProjects" :key="project" :value="project">
              {{ project }}
            </option>
          </select>
        </div>

        <!-- Transfer Filter -->
        <div class="w-full sm:w-48">
          <label for="isTransfer" class="block text-sm font-medium text-gray-700 mb-1">Transaction Type</label>
          <select
            id="isTransfer"
            v-model="filters.isTransfer"
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          >
            <option value="">All Transactions</option>
            <option value="false">Regular Expenses/Receipts</option>
            <option value="true">Transfers Only</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Expenses Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden mt-4 sm:mt-6">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-indigo-600">
            <tr>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                <div class="flex items-center">
                  <CalendarIcon class="h-4 w-4 mr-1" />
                  <span>Date</span>
                </div>
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                <div class="flex items-center">
                  <UserIcon class="h-4 w-4 mr-1" />
                  <span>Paid To/From</span>
                </div>
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                <div class="flex items-center">
                  <CurrencyRupeeIcon class="h-4 w-4 mr-1" />
                  <span>Amount</span>
                </div>
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider hidden sm:table-cell">
                <div class="flex items-center">
                  <TagIcon class="h-4 w-4 mr-1" />
                  <span>Category</span>
                </div>
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider hidden md:table-cell">
                <div class="flex items-center">
                  <CreditCardIcon class="h-4 w-4 mr-1" />
                  <span>Payment Mode</span>
                </div>
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-left text-xs font-medium text-white uppercase tracking-wider hidden md:table-cell">
                <div class="flex items-center">
                  <DocumentTextIcon class="h-4 w-4 mr-1" />
                  <span>Description</span>
                </div>
              </th>
              <th scope="col" class="px-3 sm:px-6 py-2 sm:py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                <span>Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-if="isLoading" class="animate-pulse">
              <td colspan="7" class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center justify-center">
                  <svg class="animate-spin h-5 w-5 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="ml-2 text-sm text-gray-500">Loading expenses...</span>
                </div>
              </td>
            </tr>
            <tr v-else-if="filteredExpenses.length === 0">
              <td colspan="7" class="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 text-center">
                No expenses found. Try adjusting your filters.
              </td>
            </tr>
            <tr v-else-if="paginatedExpenses.length === 0">
              <td colspan="7" class="px-3 sm:px-6 py-3 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 text-center">
                No expenses on this page. Try navigating to another page.
              </td>
            </tr>
            <tr
              v-for="expense in paginatedExpenses"
              :key="expense.id"
              class="hover:bg-gray-50 transition-colors duration-150 ease-in-out"
              :class="{ 'bg-yellow-50': selectedExpenseId === expense.id }"
              tabindex="0"
              @click="selectExpense(expense.id)"
              @keydown.enter="viewExpense(expense.id)"
              @keydown.arrow-up="navigateExpense('up', expense.id)"
              @keydown.arrow-down="navigateExpense('down', expense.id)"
            >
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900">
                {{ formatDate(expense.date) }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900">
                {{ expense.paidTo }}
                <span v-if="expense.paidToGroup" class="ml-1 text-xs text-gray-500">({{ expense.paidToGroup }})</span>
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium" :class="getAmountClass(expense.amount, expense.category)">
                {{ formatCurrency(expense.amount) }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 hidden sm:table-cell">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="getCategoryClass(expense.category)"
                >
                  {{ expense.category || 'PAYMENT' }}
                </span>
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 hidden md:table-cell">
                {{ expense.paymentMode.type === 'cash' ? 'Cash' : 'Bank' }}
                <span v-if="expense.paymentMode.instrumentNo" class="ml-1 text-xs text-gray-500">({{ expense.paymentMode.instrumentNo }})</span>
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 truncate max-w-xs hidden md:table-cell">
                {{ expense.description || '-' }}
              </td>
              <td class="px-3 sm:px-6 py-2 sm:py-4 whitespace-nowrap text-right text-xs sm:text-sm font-medium">
                <div class="flex justify-end space-x-2">
                  <button
                    @click.stop="viewExpense(expense.id)"
                    class="text-indigo-600 hover:text-indigo-900 text-xs sm:text-sm flex items-center"
                  >
                    <span class="hidden sm:inline mr-1">View</span>
                    <EyeIcon class="h-5 w-5" />
                  </button>
                  <button
                    @click.stop="editExpense(expense.id)"
                    class="text-green-600 hover:text-green-900 text-xs sm:text-sm flex items-center"
                  >
                    <span class="hidden sm:inline mr-1">Edit</span>
                    <PencilSquareIcon class="h-5 w-5" />
                  </button>
                  <button
                    @click.stop="confirmDelete(expense.id)"
                    class="text-red-600 hover:text-red-900 text-xs sm:text-sm flex items-center"
                  >
                    <span class="hidden sm:inline mr-1">Delete</span>
                    <TrashIcon class="h-5 w-5" />
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination -->
      <div class="bg-white px-3 sm:px-4 py-2 sm:py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
        <div class="flex-1 flex justify-between sm:hidden">
          <button
            @click="currentPage > 1 ? currentPage-- : null"
            :disabled="currentPage <= 1"
            class="relative inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage <= 1 }"
          >
            Previous
          </button>
          <button
            @click="currentPage < totalPages ? currentPage++ : null"
            :disabled="currentPage >= totalPages"
            class="ml-3 relative inline-flex items-center px-3 sm:px-4 py-1.5 sm:py-2 border border-gray-300 text-xs sm:text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
            :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }"
          >
            Next
          </button>
        </div>
        <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
          <div>
            <p class="text-xs sm:text-sm text-gray-700">
              Showing
              <span class="font-medium">{{ startIndex + 1 }}</span>
              to
              <span class="font-medium">{{ Math.min(endIndex, filteredExpenses.length) }}</span>
              of
              <span class="font-medium">{{ filteredExpenses.length }}</span>
              results
            </p>
          </div>
          <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
              <button
                @click="currentPage > 1 ? currentPage-- : null"
                :disabled="currentPage <= 1"
                class="relative inline-flex items-center px-1.5 sm:px-2 py-1.5 sm:py-2 rounded-l-md border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage <= 1 }"
              >
                <span class="sr-only">Previous</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
              <button
                v-for="page in displayedPages"
                :key="page"
                @click="currentPage = page"
                :class="[
                  page === currentPage ? 'z-10 bg-indigo-50 border-indigo-500 text-indigo-600' : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50',
                  'relative inline-flex items-center px-2 sm:px-4 py-1.5 sm:py-2 border text-xs sm:text-sm font-medium'
                ]"
              >
                {{ page }}
              </button>
              <button
                @click="currentPage < totalPages ? currentPage++ : null"
                :disabled="currentPage >= totalPages"
                class="relative inline-flex items-center px-1.5 sm:px-2 py-1.5 sm:py-2 rounded-r-md border border-gray-300 bg-white text-xs sm:text-sm font-medium text-gray-500 hover:bg-gray-50"
                :class="{ 'opacity-50 cursor-not-allowed': currentPage >= totalPages }"
              >
                <span class="sr-only">Next</span>
                <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                  <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div v-if="showDeleteModal" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        <div class="p-4 border-b border-gray-200">
          <h3 class="text-lg font-medium text-gray-900">Confirm Delete</h3>
        </div>
        <div class="p-4">
          <p class="text-gray-700">Are you sure you want to delete this expense? This action cannot be undone.</p>
        </div>
        <div class="p-4 border-t border-gray-200 flex justify-end space-x-3">
          <button
            @click="showDeleteModal = false"
            class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            @click="deleteExpense(expenseToDelete)"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700"
          >
            Delete
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, watch, onMounted } from 'vue';
import { useExpenses } from '~/composables/expenses/useExpenses';
import {
  EyeIcon,
  PencilSquareIcon,
  TrashIcon,
  CalendarIcon,
  UserIcon,
  CurrencyRupeeIcon,
  TagIcon,
  CreditCardIcon,
  DocumentTextIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  ArrowPathIcon
} from '@heroicons/vue/24/outline';

export default {
  name: 'ExpenseList',
  components: {
    EyeIcon,
    PencilSquareIcon,
    TrashIcon,
    CalendarIcon,
    UserIcon,
    CurrencyRupeeIcon,
    TagIcon,
    CreditCardIcon,
    DocumentTextIcon,
    MagnifyingGlassIcon,
    FunnelIcon,
    ArrowPathIcon
  },

  props: {
    expenses: {
      type: Array,
      required: true
    },
    isLoading: {
      type: Boolean,
      default: false
    }
  },

  emits: ['view', 'edit', 'delete', 'filter'],

  setup(props, { emit }) {
    // Get composables
    const {
      getUniqueCategories,
      getUniqueProjects,
      getUniquePaidToGroups
    } = useExpenses();

    // State
    const searchQuery = ref('');
    const filters = ref({
      startDate: '',
      endDate: '',
      category: '',
      paymentMode: '',
      paidToGroup: '',
      project: '',
      isTransfer: ''
    });
    const showAdvancedFilters = ref(false);
    const currentPage = ref(1);
    const itemsPerPage = ref(10);
    const showDeleteModal = ref(false);
    const expenseToDelete = ref(null);
    const selectedExpenseId = ref(null);

    // Computed properties
    const uniqueCategories = computed(() => getUniqueCategories.value);
    const uniqueProjects = computed(() => getUniqueProjects.value);
    const uniquePaidToGroups = computed(() => getUniquePaidToGroups.value);

    const filteredExpenses = computed(() => {
      let result = [...props.expenses];

      // Apply search query
      if (searchQuery.value) {
        const query = searchQuery.value.toLowerCase();
        result = result.filter(expense =>
          expense.paidTo.toLowerCase().includes(query) ||
          (expense.description && expense.description.toLowerCase().includes(query))
        );
      }

      return result;
    });

    const totalPages = computed(() => {
      return Math.ceil(filteredExpenses.value.length / itemsPerPage.value);
    });

    const startIndex = computed(() => {
      return (currentPage.value - 1) * itemsPerPage.value;
    });

    const endIndex = computed(() => {
      return startIndex.value + itemsPerPage.value;
    });

    const paginatedExpenses = computed(() => {
      return filteredExpenses.value.slice(startIndex.value, endIndex.value);
    });

    const displayedPages = computed(() => {
      const pages = [];
      const maxPagesToShow = 5;

      if (totalPages.value <= maxPagesToShow) {
        // Show all pages if there are fewer than maxPagesToShow
        for (let i = 1; i <= totalPages.value; i++) {
          pages.push(i);
        }
      } else {
        // Always show first page
        pages.push(1);

        // Calculate start and end of pages to show
        let start = Math.max(2, currentPage.value - 1);
        let end = Math.min(totalPages.value - 1, currentPage.value + 1);

        // Adjust if we're at the beginning or end
        if (currentPage.value <= 2) {
          end = Math.min(totalPages.value - 1, maxPagesToShow - 1);
        } else if (currentPage.value >= totalPages.value - 1) {
          start = Math.max(2, totalPages.value - maxPagesToShow + 2);
        }

        // Add ellipsis if needed
        if (start > 2) {
          pages.push('...');
        }

        // Add middle pages
        for (let i = start; i <= end; i++) {
          pages.push(i);
        }

        // Add ellipsis if needed
        if (end < totalPages.value - 1) {
          pages.push('...');
        }

        // Always show last page
        pages.push(totalPages.value);
      }

      return pages;
    });

    // Methods
    const formatDate = (date) => {
      if (!date) return '-';
      return new Date(date).toLocaleDateString();
    };

    const formatCurrency = (amount) => {
      return new Intl.NumberFormat('en-IN', {
        style: 'currency',
        currency: 'INR',
        minimumFractionDigits: 2
      }).format(amount);
    };

    const getAmountClass = (amount, category) => {
      // Use category to determine color, not the amount sign
      if (category === 'PAYMENT') {
        return 'text-red-600'; // PAYMENT should be red
      } else if (category === 'RECEIPT') {
        return 'text-green-600'; // RECEIPT should be green
      } else if (category === 'TRANSFER') {
        return 'text-blue-600'; // TRANSFER should be blue
      } else {
        // Fallback to using amount sign
        return amount < 0 ? 'text-red-600' : 'text-green-600';
      }
    };

    const getCategoryClass = (category) => {
      switch (category) {
        case 'PAYMENT':
          return 'bg-red-100 text-red-800';
        case 'RECEIPT':
          return 'bg-green-100 text-green-800';
        case 'TRANSFER':
          return 'bg-blue-100 text-blue-800';
        default:
          return 'bg-gray-100 text-gray-800';
      }
    };

    const applyFilters = () => {
      emit('filter', { ...filters.value });
      currentPage.value = 1; // Reset to first page when filtering
    };

    const resetFilters = () => {
      filters.value = {
        startDate: '',
        endDate: '',
        category: '',
        paymentMode: '',
        paidToGroup: '',
        project: '',
        isTransfer: ''
      };
      searchQuery.value = '';
      applyFilters();
    };

    const viewExpense = (id) => {
      emit('view', id);
    };

    const editExpense = (id) => {
      emit('edit', id);
    };

    const confirmDelete = (id) => {
      expenseToDelete.value = id;
      showDeleteModal.value = true;
    };

    const deleteExpense = (id) => {
      emit('delete', id);
      showDeleteModal.value = false;
      expenseToDelete.value = null;
    };

    const selectExpense = (id) => {
      selectedExpenseId.value = id;
    };

    const navigateExpense = (direction, currentId) => {
      const currentIndex = paginatedExpenses.value.findIndex(expense => expense.id === currentId);

      if (direction === 'up' && currentIndex > 0) {
        // Navigate up within the current page
        selectedExpenseId.value = paginatedExpenses.value[currentIndex - 1].id;
      } else if (direction === 'up' && currentIndex === 0 && currentPage.value > 1) {
        // Navigate to the previous page when at the top of the current page
        currentPage.value--;
        // Select the last item on the previous page (after the page changes)
        setTimeout(() => {
          if (paginatedExpenses.value.length > 0) {
            selectedExpenseId.value = paginatedExpenses.value[paginatedExpenses.value.length - 1].id;
          }
        }, 0);
      } else if (direction === 'down' && currentIndex < paginatedExpenses.value.length - 1) {
        // Navigate down within the current page
        selectedExpenseId.value = paginatedExpenses.value[currentIndex + 1].id;
      } else if (direction === 'down' && currentIndex === paginatedExpenses.value.length - 1 && currentPage.value < totalPages.value) {
        // Navigate to the next page when at the bottom of the current page
        currentPage.value++;
        // Select the first item on the next page (after the page changes)
        setTimeout(() => {
          if (paginatedExpenses.value.length > 0) {
            selectedExpenseId.value = paginatedExpenses.value[0].id;
          }
        }, 0);
      }
    };

    // Watch for changes to expenses prop
    watch(() => props.expenses, () => {
      // Reset pagination when expenses change
      currentPage.value = 1;
    });

    // Watch for changes to search query
    watch(searchQuery, () => {
      // Reset pagination when search query changes
      currentPage.value = 1;
    });

    // Initialize
    onMounted(() => {
      // Set default date range to current month
      const today = new Date();
      const firstDay = new Date(today.getFullYear(), today.getMonth(), 1);
      const lastDay = new Date(today.getFullYear(), today.getMonth() + 1, 0);

      filters.value.startDate = firstDay.toISOString().split('T')[0];
      filters.value.endDate = lastDay.toISOString().split('T')[0];
    });

    return {
      searchQuery,
      filters,
      showAdvancedFilters,
      currentPage,
      showDeleteModal,
      expenseToDelete,
      selectedExpenseId,
      uniqueCategories,
      uniqueProjects,
      uniquePaidToGroups,
      filteredExpenses,
      paginatedExpenses,
      totalPages,
      startIndex,
      endIndex,
      displayedPages,
      formatDate,
      formatCurrency,
      getAmountClass,
      getCategoryClass,
      applyFilters,
      resetFilters,
      viewExpense,
      editExpense,
      confirmDelete,
      deleteExpense,
      selectExpense,
      navigateExpense
    };
  }
};
</script>

<style scoped>
/* Add any component-specific styles here */
.max-w-xs {
  max-width: 20rem;
}

/* Add focus styles for keyboard navigation */
tr:focus {
  outline: 2px solid #4f46e5;
  outline-offset: -2px;
}
</style>
