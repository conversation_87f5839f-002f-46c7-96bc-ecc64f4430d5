<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 mb-4">
    <h3 class="text-lg font-semibold text-gray-800 dark:text-gray-200 mb-4">Mutual Funds Summary</h3>
    
    <div v-if="isLoading" class="flex justify-center items-center py-6">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-500"></div>
    </div>
    
    <div v-else-if="error" class="text-red-500 text-center py-4">
      {{ error }}
    </div>
    
    <div v-else class="grid grid-cols-2 md:grid-cols-4 gap-4">
      <!-- Total Invested -->
      <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div class="text-sm text-gray-500 dark:text-gray-400">Total Invested</div>
        <div class="text-xl font-bold text-gray-800 dark:text-gray-200">₹{{ formatNumber(summary.totalInvested) }}</div>
      </div>
      
      <!-- Current Value -->
      <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div class="text-sm text-gray-500 dark:text-gray-400">Current Value</div>
        <div class="text-xl font-bold text-gray-800 dark:text-gray-200">₹{{ formatNumber(summary.currentValue) }}</div>
      </div>
      
      <!-- Profit/Loss -->
      <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div class="text-sm text-gray-500 dark:text-gray-400">Profit/Loss</div>
        <div class="text-xl font-bold" :class="getProfitLossClass(summary.totalProfitLoss)">
          ₹{{ formatNumber(summary.totalProfitLoss) }}
          <span class="text-sm ml-1">({{ formatPercentage(summary.profitLossPercentage) }}%)</span>
        </div>
      </div>
      
      <!-- Active SIPs -->
      <div class="bg-gray-50 dark:bg-gray-700 p-3 rounded-lg">
        <div class="text-sm text-gray-500 dark:text-gray-400">Active SIPs</div>
        <div class="text-xl font-bold text-gray-800 dark:text-gray-200">
          {{ summary.sipCount }}
          <span v-if="monthlySIP > 0" class="text-sm ml-1">(₹{{ formatNumber(monthlySIP) }}/mo)</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  summary: {
    type: Object,
    required: true,
    default: () => ({
      totalInvested: 0,
      currentValue: 0,
      totalProfitLoss: 0,
      profitLossPercentage: 0,
      fundCount: 0,
      sipCount: 0
    })
  },
  monthlySIP: {
    type: Number,
    default: 0
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
});

// Format number with commas for thousands
const formatNumber = (number) => {
  if (number === null || number === undefined) return '-';
  return number.toLocaleString('en-IN', {
    maximumFractionDigits: 2,
    minimumFractionDigits: 0
  });
};

// Format percentage
const formatPercentage = (percentage) => {
  if (percentage === null || percentage === undefined) return '-';
  return percentage.toFixed(2);
};

// Get class for profit/loss text color
const getProfitLossClass = (value) => {
  if (value > 0) return 'text-green-600 dark:text-green-400';
  if (value < 0) return 'text-red-600 dark:text-red-400';
  return 'text-gray-800 dark:text-gray-200';
};
</script>
