<template>
  <div class="container mx-auto px-4 py-4 mt-2">
    <div class="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
      <div class="flex items-center">
        <h1 class="text-2xl md:text-3xl font-bold text-gray-800">Indian Stock Market Data</h1>
      </div>

      <div class="flex flex-col sm:flex-row w-full md:w-auto gap-3">
        <!-- Stock Search Box -->
        <div class="relative w-full sm:max-w-md">
          <input
            type="text"
            v-model="searchQuery"
            @input="handleSearch"
            placeholder="Search by company name or symbol..."
            class="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
          />
          <div v-if="isSearching" class="absolute right-3 top-3">
            <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-500"></div>
          </div>
          <div v-else-if="searchQuery" class="absolute right-3 top-3 cursor-pointer" @click="clearSearch">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        </div>

        <!-- Export Button -->
        <button
          @click="showExportModal = true"
          class="flex items-center justify-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
          </svg>
          Export
        </button>
      </div>
    </div>

    <!-- Root Level Tabs -->
    <div class="mb-6">
      <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <!-- Tabs with horizontal scrolling on mobile -->
        <div class="w-full overflow-x-auto pb-2 -mb-2 sm:mb-0">
          <div class="flex border-b border-gray-200 min-w-max">
            <button
              @click="rootTab = 'live-market'"
              class="px-4 sm:px-6 py-3 text-xs sm:text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none flex items-center"
              :class="rootTab === 'live-market'
                ? 'border-b-2 border-indigo-500 text-indigo-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
            >
              <Icon name="heroicons:chart-bar" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
              <span class="hidden xs:inline whitespace-nowrap">LIVE MARKET</span>
              <span class="xs:hidden whitespace-nowrap">MARKET</span>
            </button>
            <button
              @click="rootTab = 'portfolio'"
              class="px-4 sm:px-6 py-3 text-xs sm:text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none flex items-center"
              :class="rootTab === 'portfolio'
                ? 'border-b-2 border-indigo-500 text-indigo-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
            >
              <Icon name="heroicons:briefcase" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
              <span class="whitespace-nowrap">PORTFOLIO</span>
            </button>
            <button
              @click="rootTab = 'my-investments'"
              class="px-4 sm:px-6 py-3 text-xs sm:text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none flex items-center"
              :class="rootTab === 'my-investments'
                ? 'border-b-2 border-indigo-500 text-indigo-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
            >
              <Icon name="heroicons:currency-rupee" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
              <span class="whitespace-nowrap">EQUITY</span>
            </button>
            <button
              @click="rootTab = 'contract-notes'"
              class="px-4 sm:px-6 py-3 text-xs sm:text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none flex items-center"
              :class="rootTab === 'contract-notes'
                ? 'border-b-2 border-indigo-500 text-indigo-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
            >
              <Icon name="heroicons:document-text" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
              <span class="hidden sm:inline whitespace-nowrap">MY CONTRACT NOTES</span>
              <span class="sm:hidden whitespace-nowrap">NOTES</span>
            </button>
            <button
              @click="rootTab = 'mutual-funds'"
              class="px-4 sm:px-6 py-3 text-xs sm:text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none flex items-center"
              :class="rootTab === 'mutual-funds'
                ? 'border-b-2 border-indigo-500 text-indigo-600'
                : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
            >
              <Icon name="heroicons:banknotes" class="w-4 h-4 sm:w-5 sm:h-5 mr-1 sm:mr-2" />
              <span class="hidden sm:inline whitespace-nowrap">MUTUAL FUNDS</span>
              <span class="sm:hidden whitespace-nowrap">FUNDS</span>
            </button>
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex justify-end w-full sm:w-auto">
          <!-- Add Contract Note button - only show when on contract-notes tab -->
          <button
            v-if="rootTab === 'contract-notes'"
            @click="showAddContractNoteModal = true"
            class="bg-red-600 hover:bg-red-700 text-white px-3 sm:px-4 py-2 rounded flex items-center text-sm"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 sm:w-5 sm:h-5 mr-1" />
            <span class="hidden xs:inline whitespace-nowrap">Add Contract Note</span>
            <span class="xs:hidden whitespace-nowrap">Add Note</span>
          </button>

          <!-- Add Mutual Fund button - only show when on mutual-funds tab -->
          <button
            v-if="rootTab === 'mutual-funds'"
            @click="showAddMutualFundModal = true"
            class="bg-indigo-600 hover:bg-indigo-700 text-white px-3 sm:px-4 py-2 rounded flex items-center text-sm mr-3"
          >
            <Icon name="heroicons:plus" class="w-4 h-4 sm:w-5 sm:h-5 mr-1" />
            <span class="hidden xs:inline whitespace-nowrap">Add Mutual Fund</span>
            <span class="xs:hidden whitespace-nowrap">Add Fund</span>
          </button>

          <!-- Redeem Mutual Fund button - only show when on mutual-funds tab -->
          <button
            v-if="rootTab === 'mutual-funds'"
            @click="showRedeemMutualFundModal = true"
            class="bg-orange-600 hover:bg-orange-700 text-white px-3 sm:px-4 py-2 rounded flex items-center text-sm mr-3"
          >
            <Icon name="heroicons:minus" class="w-4 h-4 sm:w-5 sm:h-5 mr-1" />
            <span class="hidden xs:inline whitespace-nowrap">Redeem Fund</span>
            <span class="xs:hidden whitespace-nowrap">Redeem</span>
          </button>

          <!-- Import Mutual Fund button - only show when on mutual-funds tab -->
          <button
            v-if="rootTab === 'mutual-funds'"
            @click="showImportMutualFundModal = true"
            class="bg-green-600 hover:bg-green-700 text-white px-3 sm:px-4 py-2 rounded flex items-center text-sm"
          >
            <Icon name="heroicons:arrow-down-tray" class="w-4 h-4 sm:w-5 sm:h-5 mr-1" />
            <span class="hidden xs:inline whitespace-nowrap">Import</span>
            <span class="xs:hidden whitespace-nowrap">Import</span>
          </button>
        </div>
      </div>
    </div>

    <!-- AI Configuration Banner -->
    <div v-if="!isAIConfigured && !aiBannerDismissed" class="mb-6 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
      <div class="flex items-start">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3 flex-1">
          <h3 class="text-sm font-medium text-blue-800">🤖 AI Features Available</h3>
          <p class="mt-1 text-sm text-blue-700">
            Configure your AI settings to unlock powerful AI analysis features for stocks and mutual funds. Use your own API key from Google, OpenAI, or Anthropic.
          </p>
          <div class="mt-3 flex items-center space-x-3">
            <button
              @click="openAISettings"
              class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
            >
              Configure AI Settings
            </button>
            <button
              @click="dismissAIBanner"
              class="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Dismiss
            </button>
          </div>
        </div>
        <div class="flex-shrink-0 ml-3">
          <button
            @click="dismissAIBanner"
            class="text-blue-400 hover:text-blue-600"
          >
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Search Results -->
    <div v-if="searchResults.length > 0" class="bg-white rounded-lg shadow-md p-4 mb-6">
      <h3 class="text-lg font-medium mb-2">Search Results</h3>
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
              <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exchange</th>
              <th class="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="result in searchResults" :key="result.fullSymbol" class="hover:bg-gray-50">
              <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ result.symbol }}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-900">{{ result.name }}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ result.exchange }}</td>
              <td class="px-4 py-2 whitespace-nowrap text-sm text-center">
                <NuxtLink :to="`/stock-market/${result.symbol}`" class="text-indigo-600 hover:text-indigo-800 hover:underline">
                  View Details
                </NuxtLink>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Loading state - only show on initial load -->
    <div v-if="isInitialLoading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline"> {{ error }}</span>
      <button @click="fetchMarketData" class="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
        Try Again
      </button>
    </div>

    <!-- Portfolio View - Combined Equity and Mutual Funds -->
    <div v-else-if="rootTab === 'portfolio'">
      <!-- Loading state -->
      <div v-if="isLoading || isMutualFundsLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="error || mutualFundsError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ error || mutualFundsError }}</span>
        <button @click="fetchCombinedData" class="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
          Try Again
        </button>
      </div>

      <div v-else>
        <!-- Combined Portfolio Summary -->
        <div class="mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Combined Portfolio Summary</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Total Invested -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-blue-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Invested</h3>
              <p class="text-2xl font-bold text-blue-600">
                {{ formatInvestmentCurrency(investmentData.summary.totalInvested + mutualFundData.summary.totalInvested) }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                {{ investmentData.summary.investmentCount + mutualFundData.summary.fundCount }} investments
              </p>
            </div>

            <!-- Current Value -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-emerald-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Current Value</h3>
              <p class="text-2xl font-bold text-emerald-600">
                {{ formatInvestmentCurrency(investmentData.summary.currentValue + mutualFundData.summary.currentValue) }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Last updated: {{ new Date().toLocaleString() }}
              </p>
            </div>

            <!-- Today's Gain/Loss -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4"
                 :class="(investmentData.summary.todayTotalPL + mutualFundData.summary.todayTotalPL) >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Today's Gain/Loss</h3>
              <p class="text-2xl font-bold"
                 :class="(investmentData.summary.todayTotalPL + mutualFundData.summary.todayTotalPL) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentCurrency(investmentData.summary.todayTotalPL + mutualFundData.summary.todayTotalPL) }}
              </p>
              <p class="text-sm mt-1"
                 :class="(investmentData.summary.todayTotalPL + mutualFundData.summary.todayTotalPL) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ (investmentData.summary.todayTotalPL + mutualFundData.summary.todayTotalPL) >= 0 ? '+' : '' }}
                {{ calculateCombinedPercentage(investmentData.summary.todayTotalPL, mutualFundData.summary.todayTotalPL,
                   investmentData.summary.currentValue, mutualFundData.summary.currentValue).toFixed(2) }}%
              </p>
            </div>

            <!-- Total Profit/Loss -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4"
                 :class="(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Profit/Loss</h3>
              <p class="text-2xl font-bold"
                 :class="(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentCurrency(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) }}
              </p>
              <p class="text-sm mt-1"
                 :class="(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ (investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? '+' : '' }}
                {{ calculateCombinedPercentage(investmentData.summary.totalProfitLoss, mutualFundData.summary.totalProfitLoss,
                   investmentData.summary.totalInvested, mutualFundData.summary.totalInvested).toFixed(2) }}%
              </p>
            </div>
          </div>
        </div>

        <!-- Portfolio Performance History -->
        <div class="mb-8">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-2xl font-semibold text-gray-800">Portfolio Performance History</h2>
            <button
              @click="fetchPortfolioPerformanceHistory"
              :disabled="portfolioPerformanceLoading"
              class="bg-gradient-to-r from-purple-600 to-indigo-600 text-white px-4 py-2 rounded-lg hover:from-purple-700 hover:to-indigo-700 disabled:opacity-50 transition-all duration-200 shadow-sm"
            >
              <span v-if="portfolioPerformanceLoading">📊 Analyzing...</span>
              <span v-else>📊 Analyze Performance</span>
            </button>
          </div>

          <!-- Performance Analysis Loading State -->
          <div v-if="portfolioPerformanceLoading" class="mb-4">
            <div class="bg-white rounded-lg shadow-md p-4">
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                  <div class="animate-spin rounded-full h-6 w-6 border-t-2 border-b-2 border-purple-500"></div>
                  <span class="text-gray-700">{{ portfolioPerformanceStatusMessage || 'Analyzing portfolio performance...' }}</span>
                </div>
                <button
                  @click="killPortfolioAnalysis"
                  class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700 transition-colors"
                >
                  🛑 Stop
                </button>
              </div>
              <div class="bg-gray-200 rounded-full h-2">
                <div
                  class="bg-purple-500 h-2 rounded-full transition-all duration-500"
                  :style="{ width: portfolioPerformanceProgress + '%' }"
                ></div>
              </div>
            </div>
          </div>

          <!-- Performance Analysis Error State -->
          <div v-else-if="portfolioPerformanceError" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
            <div class="flex items-center space-x-2">
              <span class="text-red-600 text-lg">❌</span>
              <div>
                <p class="text-red-800 font-medium">Performance Analysis Failed</p>
                <p class="text-red-600 text-sm">{{ portfolioPerformanceError }}</p>
                <button
                  @click="fetchPortfolioPerformanceHistory"
                  class="mt-2 bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700"
                >
                  Try Again
                </button>
              </div>
            </div>
          </div>

          <!-- Performance Analysis Results -->
          <div v-else-if="portfolioPerformanceData" class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Weekly Performance -->
            <div v-if="portfolioPerformanceData.weeklyPerformance" class="bg-white rounded-lg shadow-md p-4 border-t-4"
                 :class="portfolioPerformanceData.weeklyPerformance.totalPL >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-3">📅 Weekly Performance</h3>
              <p class="text-2xl font-bold mb-2"
                 :class="portfolioPerformanceData.weeklyPerformance.totalPL >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentCurrency(portfolioPerformanceData.weeklyPerformance.totalPL) }}
              </p>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Equity P&L:</span>
                  <span :class="portfolioPerformanceData.weeklyPerformance.equityPL >= 0 ? 'text-green-600' : 'text-red-600'">
                    {{ formatInvestmentCurrency(portfolioPerformanceData.weeklyPerformance.equityPL) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Mutual Fund P&L:</span>
                  <span :class="portfolioPerformanceData.weeklyPerformance.mutualFundPL >= 0 ? 'text-green-600' : 'text-red-600'">
                    {{ formatInvestmentCurrency(portfolioPerformanceData.weeklyPerformance.mutualFundPL) }}
                  </span>
                </div>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>Period:</span>
                  <span>{{ portfolioPerformanceData.weeklyPerformance.fromDate }} to {{ portfolioPerformanceData.weeklyPerformance.toDate }}</span>
                </div>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>Data Coverage:</span>
                  <span>{{ portfolioPerformanceData.weeklyPerformance.validEquityInvestments + portfolioPerformanceData.weeklyPerformance.validMutualFundInvestments }} investments</span>
                </div>
              </div>
            </div>

            <!-- Monthly Performance -->
            <div v-if="portfolioPerformanceData.monthlyPerformance" class="bg-white rounded-lg shadow-md p-4 border-t-4"
                 :class="portfolioPerformanceData.monthlyPerformance.totalPL >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-3">📅 Monthly Performance</h3>
              <p class="text-2xl font-bold mb-2"
                 :class="portfolioPerformanceData.monthlyPerformance.totalPL >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentCurrency(portfolioPerformanceData.monthlyPerformance.totalPL) }}
              </p>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Equity P&L:</span>
                  <span :class="portfolioPerformanceData.monthlyPerformance.equityPL >= 0 ? 'text-green-600' : 'text-red-600'">
                    {{ formatInvestmentCurrency(portfolioPerformanceData.monthlyPerformance.equityPL) }}
                  </span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Mutual Fund P&L:</span>
                  <span :class="portfolioPerformanceData.monthlyPerformance.mutualFundPL >= 0 ? 'text-green-600' : 'text-red-600'">
                    {{ formatInvestmentCurrency(portfolioPerformanceData.monthlyPerformance.mutualFundPL) }}
                  </span>
                </div>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>Period:</span>
                  <span>{{ portfolioPerformanceData.monthlyPerformance.fromDate }} to {{ portfolioPerformanceData.monthlyPerformance.toDate }}</span>
                </div>
                <div class="flex justify-between text-xs text-gray-500">
                  <span>Data Coverage:</span>
                  <span>{{ portfolioPerformanceData.monthlyPerformance.validEquityInvestments + portfolioPerformanceData.monthlyPerformance.validMutualFundInvestments }} investments</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Initial State -->
          <div v-else class="bg-white rounded-lg shadow-md p-4 text-center">
            <p class="text-gray-500 mb-4">Click "Analyze Performance" to get weekly and monthly P&L analysis for your combined portfolio using real market data.</p>
            <p class="text-xs text-gray-400">Analysis includes both equity and mutual fund investments with historical price data from Yahoo Finance and AMFI APIs.</p>
          </div>
        </div>

        <!-- Investment Charts Row -->
        <div class="mb-16">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Investment Analysis</h2>
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Monthly Investment Chart -->
            <div class="bg-white rounded-lg shadow-md p-4">
              <h3 class="text-lg font-medium text-gray-700 mb-3">Monthly Investment vs Current Value</h3>
              <MonthlyInvestmentChart
                :chart-data="combinedMonthlyInvestmentChartData"
                height="280px"
              />
            </div>

            <!-- Equity vs Mutual Fund Distribution Chart -->
            <div class="bg-white rounded-lg shadow-md p-4">
              <h3 class="text-lg font-medium text-gray-700 mb-3">Equity vs Mutual Fund Distribution</h3>
              <EquityMutualFundComparisonChart
                :chart-data="equityMutualFundComparisonData"
                height="280px"
              />
            </div>
          </div>
        </div>

        <!-- Combined Investments Table -->
        <div class="mb-16">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">My Investments Summary</h2>
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Invested</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Current Value</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Profit/Loss</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">P/L %</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <!-- Equity Summary Row -->
                  <tr class="bg-gray-50 font-medium">
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">Equity</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ investmentData.summary.investmentCount }} stocks</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900">{{ formatInvestmentCurrency(investmentData.summary.totalInvested) }}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900">{{ formatInvestmentCurrency(investmentData.summary.currentValue) }}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right"
                        :class="investmentData.summary.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ formatInvestmentCurrency(investmentData.summary.totalProfitLoss) }}
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right"
                        :class="investmentData.summary.profitLossPercentage >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ investmentData.summary.profitLossPercentage >= 0 ? '+' : '' }}{{ investmentData.summary.profitLossPercentage.toFixed(2) }}%
                    </td>
                  </tr>

                  <!-- Mutual Funds Summary Row -->
                  <tr class="bg-gray-50 font-medium">
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">Mutual Funds</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ mutualFundData.summary.fundCount }} funds</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900">{{ formatInvestmentCurrency(mutualFundData.summary.totalInvested) }}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900">{{ formatInvestmentCurrency(mutualFundData.summary.currentValue) }}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right"
                        :class="mutualFundData.summary.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ formatInvestmentCurrency(mutualFundData.summary.totalProfitLoss) }}
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right"
                        :class="mutualFundData.summary.profitLossPercentage >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ mutualFundData.summary.profitLossPercentage >= 0 ? '+' : '' }}{{ mutualFundData.summary.profitLossPercentage.toFixed(2) }}%
                    </td>
                  </tr>

                  <!-- Total Row -->
                  <tr class="bg-indigo-50 font-bold">
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">Total Portfolio</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-gray-900">{{ investmentData.summary.investmentCount + mutualFundData.summary.fundCount }} investments</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900">{{ formatInvestmentCurrency(investmentData.summary.totalInvested + mutualFundData.summary.totalInvested) }}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right text-gray-900">{{ formatInvestmentCurrency(investmentData.summary.currentValue + mutualFundData.summary.currentValue) }}</td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right"
                        :class="(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ formatInvestmentCurrency(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) }}
                    </td>
                    <td class="px-4 py-3 whitespace-nowrap text-sm text-right"
                        :class="(investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ (investmentData.summary.totalProfitLoss + mutualFundData.summary.totalProfitLoss) >= 0 ? '+' : '' }}
                      {{ calculateCombinedPercentage(investmentData.summary.totalProfitLoss, mutualFundData.summary.totalProfitLoss,
                         investmentData.summary.totalInvested, mutualFundData.summary.totalInvested).toFixed(2) }}%
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Data display -->
    <div v-else-if="rootTab === 'live-market'">
      <!-- Mock data notification removed -->

      <!-- Market Overview -->
      <div class="mb-8">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl sm:text-2xl font-semibold text-gray-800">Market Overview</h2>

          <!-- Last Updated Info -->
          <div class="text-xs sm:text-sm text-gray-500 flex items-center">
            <span class="hidden sm:inline">Last updated:</span>
            <span class="sm:hidden">Updated:</span>
            <span class="ml-1">{{ lastUpdated }}</span>
            <button
              @click="refreshData"
              class="ml-2 text-indigo-600 hover:text-indigo-800 focus:outline-none"
              :disabled="isLoading"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" :class="{ 'animate-spin': isLoading }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Collapsible Market Overview Section -->
        <div class="mb-4 sm:hidden">
          <button
            @click="showMarketOverview = !showMarketOverview"
            class="w-full flex justify-between items-center bg-gray-100 hover:bg-gray-200 px-4 py-2 rounded-md"
          >
            <span class="font-medium">Market Overview</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              class="h-5 w-5 transition-transform duration-200"
              :class="showMarketOverview ? 'transform rotate-180' : ''"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
          </button>
        </div>

        <div v-show="showMarketOverview || !isMobile" class="grid grid-cols-1 lg:grid-cols-12 gap-6">
          <!-- Nifty 50 Historical Chart -->
          <div class="lg:col-span-4 bg-white rounded-lg shadow-md overflow-hidden">
            <div class="bg-indigo-600 text-white px-4 py-3 flex justify-between items-center">
              <h3 class="text-lg sm:text-xl font-semibold">NIFTY 50 Historical</h3>
              <div class="flex space-x-2">
                <button
                  v-for="period in ['1W', '1M', '6M', '1Y']"
                  :key="period"
                  @click="chartPeriod = period"
                  class="text-xs px-2 py-1 rounded-md transition-colors"
                  :class="chartPeriod === period ? 'bg-white text-indigo-600' : 'text-white hover:bg-indigo-500'"
                >
                  {{ period }}
                </button>
              </div>
            </div>
            <div class="p-4" style="min-height: 300px;">
              <NiftyHistoricalChart
                symbol="^NSEI"
                height="280px"
                v-model:period="chartPeriod"
              />
            </div>
          </div>

          <!-- Market Indices, Gainers, Losers -->
          <div class="lg:col-span-8">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
              <!-- Market Indices -->
              <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-indigo-600 text-white px-4 py-3">
                  <h3 class="text-lg font-semibold">Key Indices</h3>
                </div>
                <div class="p-4">
                  <div v-if="marketData.indices && marketData.indices.length > 0">
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Index</th>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Last</th>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">% Change</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="index in keyIndices" :key="index.index">
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm font-medium text-gray-900">{{ index.indexName }}</td>
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm text-right text-gray-500">{{ formatPrice(index.last) }}</td>
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm text-right" :class="index.percentChange.color">
                              {{ index.percentChange.value }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div v-else class="text-center py-4 text-gray-500 text-sm">
                    No index data available
                  </div>
                </div>
              </div>

              <!-- Top Gainers -->
              <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-green-600 text-white px-4 py-3">
                  <h3 class="text-lg font-semibold">Top Gainers</h3>
                </div>
                <div class="p-4">
                  <div v-if="marketData.gainers && marketData.gainers.length > 0">
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">LTP</th>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">% Change</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="(gainer, i) in marketData.gainers.slice(0, 5)" :key="i">
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm font-medium">
                              <NuxtLink :to="`/stock-market/${gainer.symbol}`" class="text-gray-900 hover:text-indigo-600 hover:underline">
                                {{ gainer.symbol }}
                              </NuxtLink>
                            </td>
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm text-right text-gray-500">{{ formatPrice(gainer.lastPrice) }}</td>
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm text-right text-green-600">
                              +{{ parseFloat(gainer.pChange).toFixed(2) }}%
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div v-else class="text-center py-4 text-gray-500 text-sm">
                    No gainer data available
                  </div>
                </div>
              </div>

              <!-- Top Losers -->
              <div class="bg-white rounded-lg shadow-md overflow-hidden">
                <div class="bg-red-600 text-white px-4 py-3">
                  <h3 class="text-lg font-semibold">Top Losers</h3>
                </div>
                <div class="p-4">
                  <div v-if="marketData.losers && marketData.losers.length > 0">
                    <div class="overflow-x-auto">
                      <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                          <tr>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">LTP</th>
                            <th class="px-2 sm:px-4 py-2 sm:py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">% Change</th>
                          </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                          <tr v-for="(loser, i) in marketData.losers.slice(0, 5)" :key="i">
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm font-medium">
                              <NuxtLink :to="`/stock-market/${loser.symbol}`" class="text-gray-900 hover:text-indigo-600 hover:underline">
                                {{ loser.symbol }}
                              </NuxtLink>
                            </td>
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm text-right text-gray-500">{{ formatPrice(loser.lastPrice) }}</td>
                            <td class="px-2 sm:px-4 py-2 whitespace-nowrap text-xs sm:text-sm text-right text-red-600">
                              {{ parseFloat(loser.pChange).toFixed(2) }}%
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div v-else class="text-center py-4 text-gray-500 text-sm">
                    No loser data available
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Tabs for Nifty 50 and custom views -->
      <div class="mb-6">
        <div class="flex border-b border-gray-200 overflow-x-auto">
          <!-- Default Nifty 50 tab -->
          <button
            @click="activeTab = 'nifty50'; setActiveView('')"
            class="px-6 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none"
            :class="activeTab === 'nifty50'
              ? 'border-b-2 border-indigo-500 text-indigo-600'
              : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
          >
            Nifty 50 Stocks
          </button>

          <!-- User views tabs -->
          <button
            v-for="view in views"
            :key="view.id"
            @click="activeTab = view.id; setActiveView(view.id)"
            class="px-6 py-3 text-sm font-medium whitespace-nowrap transition-all duration-200 focus:outline-none"
            :class="activeTab === view.id
              ? 'border-b-2 border-indigo-500 text-indigo-600'
              : 'text-gray-500 hover:text-gray-700 hover:border-b-2 hover:border-gray-300'"
          >
            {{ view.name }}
          </button>

          <!-- Add new view button (only if less than 10 views) -->
          <button
            v-if="views.length < 10"
            @click="showCreateViewModal = true"
            class="px-4 py-3 text-sm font-medium text-indigo-600 hover:text-indigo-800 flex items-center ml-2"
            title="Create new view"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <span class="ml-1 whitespace-nowrap">New View</span>
          </button>
        </div>
      </div>

      <!-- Create view modal -->
      <div v-if="showCreateViewModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Create New View</h3>
          <div class="mb-4">
            <label for="viewName" class="block text-sm font-medium text-gray-700 mb-1">View Name</label>
            <input
              id="viewName"
              v-model="newViewName"
              type="text"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500"
              placeholder="Enter view name"
            />
          </div>
          <div class="flex justify-end space-x-3">
            <button
              @click="showCreateViewModal = false"
              class="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
            >
              Cancel
            </button>
            <button
              @click="createNewView"
              class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 rounded-md"
              :disabled="!newViewName.trim()"
            >
              Create
            </button>
          </div>
        </div>
      </div>

      <!-- Delete view confirmation modal -->
      <div v-if="viewToDelete" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Delete View</h3>
          <p class="mb-4 text-gray-600">Are you sure you want to delete this view? This action cannot be undone.</p>
          <div class="flex justify-end space-x-3">
            <button
              @click="viewToDelete = null"
              class="px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-100 rounded-md"
            >
              Cancel
            </button>
            <button
              @click="deleteViewConfirmed"
              class="px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-md"
            >
              Delete
            </button>
          </div>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="mt-4">

        <!-- Nifty 50 Stocks Tab -->
        <div v-if="activeTab === 'nifty50'" class="bg-white rounded-lg shadow-md p-4">
          <div class="mb-4">
            <h2 class="text-2xl font-semibold text-gray-800">Nifty 50 Stocks</h2>
          </div>

          <!-- Loading indicator - only show on initial load -->
          <div v-if="isInitialLoading" class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
            <span class="ml-2 text-gray-500">Loading stock data...</span>
          </div>

          <!-- Simple stock table -->
          <div v-else-if="stocksData.length > 0" class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
              <thead class="bg-gray-50">
                <tr>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                  <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Volume</th>
                  <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">AI Analysis</th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="stock in filteredStocks" :key="stock.symbol" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                    <NuxtLink :to="`/stock-market/${stock.symbol}`" class="text-indigo-600 hover:text-indigo-900">
                      {{ stock.symbol }}
                    </NuxtLink>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    <NuxtLink :to="`/stock-market/${stock.symbol}`" class="text-gray-600 hover:text-gray-900">
                      {{ stock.meta?.companyName || stock.symbol }}
                    </NuxtLink>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatPrice(stock.lastPrice || 0) }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm" :class="typeof stock.pChange === 'number' && stock.pChange >= 0 ? 'text-green-600' : 'text-red-600'">
                    <span v-if="typeof stock.pChange === 'number'">
                      <template v-if="stock.pChange >= 0">+{{ stock.pChange.toFixed(2) }}%</template>
                      <template v-else>{{ stock.pChange.toFixed(2) }}%</template>
                    </span>
                    <span v-else>{{ stock.pChange }}</span>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ stock.totalTradedVolume }}</td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                    <button
                      @click="openAIAnalysis(stock)"
                      class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1 rounded-md text-xs hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-sm"
                      title="Get AI Analysis"
                    >
                      🤖 AI Analysis
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>

          <!-- No data message -->
          <div v-else class="text-center py-8 text-gray-500">
            No stock data available
          </div>
        </div>



        <!-- Custom View Tabs -->
        <div v-if="views.some(v => v.id === activeTab)" class="bg-white rounded-lg shadow-md p-4">
          <div class="mb-4 flex justify-between items-center">
            <h2 class="text-2xl font-semibold text-gray-800">{{ activeView?.name }}</h2>
            <div class="flex items-center space-x-3">
              <!-- Add stock form (compact) -->
              <div class="flex items-center border border-gray-300 rounded-md overflow-hidden h-8">
                <input
                  v-model="newStockSymbol"
                  type="text"
                  placeholder="Add symbol (e.g., RELIANCE)"
                  class="w-40 px-2 py-1 text-sm focus:outline-none"
                  @keyup.enter="newStockSymbol.trim() && addStockToView()"
                />
                <button
                  @click="addStockToView"
                  class="px-2 py-1 bg-indigo-600 text-white text-sm hover:bg-indigo-700"
                  :disabled="!newStockSymbol.trim()"
                >
                  Add
                </button>
              </div>
              <p v-if="addStockError" class="text-red-500 text-xs absolute mt-8">{{ addStockError }}</p>

              <div class="text-sm text-gray-500">
                {{ activeView?.symbols?.length || 0 }} symbols
              </div>

              <button
                @click="confirmDeleteView(activeTab)"
                class="px-2 py-1 bg-red-600 text-white text-sm rounded hover:bg-red-700"
                title="Remove this view"
              >
                Remove View
              </button>
            </div>
          </div>

          <!-- Loading indicator - only show on initial load -->
          <div v-if="isInitialLoading || viewsLoading" class="flex justify-center items-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
            <span class="ml-2 text-gray-500">Loading stock data...</span>
          </div>

          <!-- Empty view message -->
          <div v-else-if="!activeView?.symbols?.length" class="text-center py-8">
            <p class="text-gray-500 mb-4">No stocks added to this view yet.</p>
            <p class="text-sm text-gray-400">Add stocks from the stock detail pages or use the form above.</p>
          </div>

          <!-- Custom view table -->
          <div v-else-if="stocksData.length > 0">

            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Company</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Change</th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Volume</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">AI Analysis</th>
                    <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="stock in customViewStocks" :key="stock.symbol" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      <NuxtLink :to="`/stock-market/${stock.symbol}`" class="text-indigo-600 hover:text-indigo-900">
                        {{ stock.symbol }}
                      </NuxtLink>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <NuxtLink :to="`/stock-market/${stock.symbol}`" class="text-gray-600 hover:text-gray-900">
                        {{ stock.meta?.companyName || stock.symbol }}
                      </NuxtLink>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ formatPrice(stock.lastPrice || 0) }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm" :class="typeof stock.pChange === 'number' && stock.pChange >= 0 ? 'text-green-600' : 'text-red-600'">
                      <span v-if="typeof stock.pChange === 'number'">
                        <template v-if="stock.pChange >= 0">+{{ stock.pChange.toFixed(2) }}%</template>
                        <template v-else>{{ stock.pChange.toFixed(2) }}%</template>
                      </span>
                      <span v-else>{{ stock.pChange }}</span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ stock.totalTradedVolume }}</td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <button
                        @click="openAIAnalysis(stock)"
                        class="bg-gradient-to-r from-blue-500 to-indigo-600 text-white px-3 py-1 rounded-md text-xs hover:from-blue-600 hover:to-indigo-700 transition-all duration-200 shadow-sm"
                        title="Get AI Analysis"
                      >
                        🤖 AI Analysis
                      </button>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                      <button
                        @click="removeSymbolFromView(activeTab, stock.symbol)"
                        class="text-red-500 hover:text-red-700"
                        title="Remove from view"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          <!-- No data message -->
          <div v-else class="text-center py-8 text-gray-500">
            No stock data available
          </div>
        </div>
      </div>

      <!-- Auto-update controls and timestamp -->
      <div class="mt-6 flex justify-between items-center text-sm text-gray-500">
        <div class="flex items-center">
          <span class="mr-2">Auto-update:</span>
          <select
            v-model="autoUpdateInterval"
            @change="handleAutoUpdateChange"
            class="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
          >
            <option value="0">Off</option>
            <option value="1">Every 1 minute</option>
            <option value="5">Every 5 minutes</option>
            <option value="10">Every 10 minutes</option>
            <option value="15">Every 15 minutes</option>
            <option value="20">Every 20 minutes</option>
            <option value="30">Every 30 minutes</option>
          </select>
        </div>
        <div class="flex items-center">
          <p>Last updated: {{ lastUpdated }}</p>
          <button @click="refreshData" class="ml-2 text-indigo-600 hover:text-indigo-800">
            Refresh Now
          </button>
        </div>
      </div>
    </div> <!-- End of LIVE MARKET tab content -->

    <!-- EQUITY tab content -->
    <div v-if="rootTab === 'my-investments'" class="mt-4">
      <!-- Loading state - only show on initial load -->
      <div v-if="isInvestmentsLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="investmentsError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ investmentsError }}</span>

        <!-- Authentication error - show login button -->
        <div v-if="investmentsError.includes('Authentication required')" class="mt-4">
          <p class="text-sm mb-2">You need to be logged in to view your investments.</p>
          <NuxtLink to="/login" class="inline-block bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded">
            Log In
          </NuxtLink>
        </div>

        <!-- Other errors - show retry button -->
        <button
          v-else
          @click="fetchInvestments"
          class="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
        >
          Try Again
        </button>
      </div>

      <!-- Investments data display -->
      <div v-else>
        <!-- Investment Summary -->
        <div class="mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Portfolio Summary</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <!-- Total Invested -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-blue-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Invested</h3>
              <p class="text-2xl font-bold text-blue-600">
                {{ formatInvestmentCurrency(investmentData.summary.totalInvested) }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                {{ investmentData.summary.investmentCount }} investments
              </p>
            </div>

            <!-- Current Value -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-emerald-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Current Value</h3>
              <p class="text-2xl font-bold text-emerald-600">
                {{ formatInvestmentCurrency(investmentData.summary.currentValue) }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Last updated: {{ new Date(investmentData.timestamp).toLocaleString() }}
              </p>
            </div>

            <!-- Today's Gain/Loss -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4"
                 :class="(investmentData.summary.todayTotalPL || 0) >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Today's Gain/Loss</h3>
              <p class="text-2xl font-bold"
                 :class="(investmentData.summary.todayTotalPL || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentCurrency(investmentData.summary.todayTotalPL || 0) }}
              </p>
              <p class="text-sm mt-1"
                 :class="formatInvestmentPercentage(investmentData.summary.todayPLPercentage).color">
                {{ formatInvestmentPercentage(investmentData.summary.todayPLPercentage).value }}
              </p>
            </div>

            <!-- Total Profit/Loss -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4" :class="(investmentData.summary.totalProfitLoss || 0) >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Profit/Loss</h3>
              <p class="text-2xl font-bold" :class="(investmentData.summary.totalProfitLoss || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentCurrency(investmentData.summary.totalProfitLoss) }}
              </p>
              <p class="text-sm mt-1" :class="(investmentData.summary.profitLossPercentage || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ formatInvestmentPercentage(investmentData.summary.profitLossPercentage).value }}
              </p>
            </div>

            <!-- Sector Diversity -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-violet-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Sector Diversity</h3>
              <p class="text-2xl font-bold text-violet-600">
                {{ investmentData.sectorAllocation.length }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Sectors in portfolio
              </p>
            </div>
          </div>
        </div>

        <!-- Monthly Investment Chart and Sector Allocation in same row -->
        <div class="mb-16">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Monthly Investment Chart -->
            <div>
              <h2 class="text-2xl font-semibold text-gray-800 mb-4">Monthly Investment vs Current Value</h2>
              <div class="bg-white rounded-lg shadow-md p-4 h-full">
                <MonthlyInvestmentChart
                  :chart-data="monthlyInvestmentChartData"
                  height="280px"
                />
              </div>
            </div>

            <!-- Sector Allocation -->
            <div>
              <h2 class="text-2xl font-semibold text-gray-800 mb-4">Sector Allocation</h2>
              <div class="bg-white rounded-lg shadow-md p-4 h-full">
                <div class="overflow-x-auto max-h-72">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="sticky top-0">
                      <tr class="bg-violet-600">
                        <th class="px-4 py-2 text-left text-xs font-medium text-white uppercase tracking-wider">Sector</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">Value</th>
                        <th class="px-4 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">Allocation</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr v-for="sector in investmentData.sectorAllocation" :key="sector.sector" class="hover:bg-gray-50">
                        <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ sector.sector }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">{{ formatInvestmentCurrency(sector.value) }}</td>
                        <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-900">
                          {{ sector.percentage.toFixed(2) }}%
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Investments Table -->
        <div class="mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">My Equity Investments</h2>
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr class="bg-blue-600">
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Symbol</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Sector</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Quantity</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Buy Price</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Current Price</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Investment</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Current Value</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Today's P/L</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Today's P/L %</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Overall P/L</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Overall P/L %</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="investment in investmentData.investments" :key="investment._id" class="hover:bg-gray-50">
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ investment.symbol }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ investment.sector }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">{{ investment.qnty }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ formatPrice(investment.price) }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ investment.cprice ? formatPrice(investment.cprice) : 'N/A' }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ formatInvestmentCurrency(investment.namt) }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ investment.cval ? formatInvestmentCurrency(investment.cval) : 'N/A' }}
                    </td>
                    <!-- Today's P/L -->
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right"
                        :class="(investment.dayPL || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ investment.dayPL !== undefined ? formatInvestmentCurrency(investment.dayPL) : 'N/A' }}
                    </td>
                    <!-- Today's P/L % -->
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right"
                        :class="(investment.dayPLPercentage || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ investment.dayPLPercentage !== undefined ? formatInvestmentPercentage(investment.dayPLPercentage).value : 'N/A' }}
                    </td>
                    <!-- Overall P/L -->
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right"
                        :class="(investment.pl || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ investment.pl ? formatInvestmentCurrency(investment.pl) : 'N/A' }}
                    </td>
                    <!-- Overall P/L % -->
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right"
                        :class="(investment.pl || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                      {{ investment.pl ? ((investment.pl / investment.namt) * 100).toFixed(2) + '%' : 'N/A' }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Auto-update controls and timestamp -->
        <div class="mt-6 flex flex-col space-y-2 text-sm text-gray-500">
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <span class="mr-2">Auto-update:</span>
              <select
                v-model="investmentsAutoUpdateInterval"
                @change="handleInvestmentsAutoUpdateChange"
                class="px-2 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-indigo-500"
              >
                <option value="0">Off</option>
                <option value="5">Every 5 minutes</option>
                <option value="15">Every 15 minutes</option>
                <option value="30">Every 30 minutes</option>
                <option value="60">Every hour</option>
              </select>

              <!-- Visual timer for next auto-refresh -->
              <div v-if="investmentsAutoUpdateInterval !== '0'" class="flex items-center ml-3">
                <div class="flex items-center bg-indigo-100 px-3 py-1.5 rounded-md border border-indigo-300">
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-600 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  <span class="text-indigo-700 font-medium">{{ investmentsTimeRemaining }}</span>
                </div>
              </div>

              <!-- Operation status indicator -->
              <div v-if="operationStatus !== 'idle'" class="flex items-center ml-2">
                <span
                  class="px-2 py-1 text-sm rounded-md"
                  :class="{
                    'bg-blue-100 text-blue-700': operationStatus === 'updating-prices',
                    'bg-green-100 text-green-700': operationStatus === 'fetching-data'
                  }"
                >
                  {{ operationStatusText }}
                </span>
              </div>
            </div>
            <div class="flex items-center">
              <p>Last updated: {{ investmentsLastUpdated }}</p>
              <button @click="refreshInvestments" class="ml-2 text-indigo-600 hover:text-indigo-800">
                Refresh Now
              </button>
            </div>
          </div>

          <!-- Data update info -->
          <div class="flex justify-between items-center">
            <div class="flex items-center">
              <span class="mr-2">Updates include:</span>
              <span class="text-green-600 mr-2">Price updates</span>
              <span class="text-blue-600">Full data refresh</span>
            </div>
            <div class="flex items-center">
              <p v-if="lastPriceUpdate">Prices last updated: {{ lastPriceUpdate }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- MY CONTRACT NOTES tab content -->
    <div v-if="rootTab === 'contract-notes'" class="mt-4">
      <!-- Loading state - only show on initial load -->
      <div v-if="isContractNotesLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="contractNotesError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ contractNotesError }}</span>

        <!-- Authentication error - show login button -->
        <div v-if="contractNotesError.includes('Authentication required')" class="mt-4">
          <p class="text-sm mb-2">You need to be logged in to view your contract notes.</p>
          <NuxtLink to="/login" class="inline-block bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded">
            Log In
          </NuxtLink>
        </div>

        <!-- Other errors - show retry button -->
        <button
          v-else
          @click="fetchContractNotes"
          class="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded"
        >
          Try Again
        </button>
      </div>

      <!-- Contract Notes data display -->
      <div v-else>
        <!-- Contract Notes Summary -->
        <div class="mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Contract Notes Summary</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
            <!-- Total Notes -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-indigo-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Notes</h3>
              <p class="text-2xl font-bold text-indigo-600">
                {{ contractNotesData.summary.totalNotes }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Contract notes
              </p>
            </div>

            <!-- Total Invested -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-purple-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Invested</h3>
              <p class="text-2xl font-bold text-purple-600">
                {{ formatContractNotesCurrency(contractNotesData.summary.totalInvested) }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Across all notes
              </p>
            </div>

            <!-- Total Stocks -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-teal-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Stocks</h3>
              <p class="text-2xl font-bold text-teal-600">
                {{ contractNotesData.summary.totalStocks }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Unique stocks
              </p>
            </div>

            <!-- Unique Brokers -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-pink-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Brokers</h3>
              <p class="text-2xl font-bold text-pink-600">
                {{ contractNotesData.summary.uniqueBrokers }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Different brokers
              </p>
            </div>

            <!-- Unique Folios -->
            <div class="bg-white rounded-lg shadow-md p-4 border-t-4 border-amber-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Folios</h3>
              <p class="text-2xl font-bold text-amber-600">
                {{ contractNotesData.summary.uniqueFolios }}
              </p>
              <p class="text-sm text-gray-500 mt-1">
                Different folios
              </p>
            </div>
          </div>
        </div>

        <!-- Broker and Monthly Distribution in same row -->
        <div class="mb-12">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Broker Distribution -->
            <div>
              <h2 class="text-2xl font-semibold text-gray-800 mb-4">Broker Distribution</h2>
              <div class="bg-white rounded-lg shadow-md p-4 h-full">
                <div class="overflow-x-auto max-h-64">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="sticky top-0">
                      <tr class="bg-indigo-600">
                        <th class="px-3 py-2 text-left text-xs font-medium text-white uppercase tracking-wider">Broker</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">Notes</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">Amount</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">%</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr v-for="broker in contractNotesData.brokerSummary" :key="broker.broker" class="hover:bg-gray-50">
                        <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ broker.broker }}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {{ broker.count }}
                        </td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {{ formatContractNotesCurrency(broker.totalInvested) }}
                        </td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-900">
                          {{ broker.percentage.toFixed(2) }}%
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Monthly Distribution -->
            <div>
              <h2 class="text-2xl font-semibold text-gray-800 mb-4">Monthly Distribution</h2>
              <div class="bg-white rounded-lg shadow-md p-4 h-full">
                <div class="overflow-x-auto max-h-64">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="sticky top-0">
                      <tr class="bg-purple-600">
                        <th class="px-3 py-2 text-left text-xs font-medium text-white uppercase tracking-wider">Month</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">Notes</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-white uppercase tracking-wider">Amount</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr v-for="month in contractNotesData.monthlyData" :key="`${month.year}-${month.month}`" class="hover:bg-gray-50">
                        <td class="px-3 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ month.displayName }}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {{ month.count }}
                        </td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                          {{ formatContractNotesCurrency(month.totalInvested) }}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Contract Notes Table -->
        <div class="mb-8">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-semibold text-gray-800">My Contract Notes</h2>
          </div>
          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="overflow-x-auto">
              <table class="min-w-full divide-y divide-gray-200">
                <thead>
                  <tr class="bg-teal-600">
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Note #</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Date</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Broker</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Type</th>
                    <th class="px-4 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">Folio</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Stocks</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Quantity</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Amount</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Other Charges</th>
                    <th class="px-4 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">Final Amount</th>
                    <th class="px-4 py-3 text-center text-xs font-medium text-white uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="note in contractNotesData.contractNotes" :key="note._id" class="hover:bg-gray-50">
                    <td class="px-4 py-2 whitespace-nowrap text-sm font-medium text-gray-900">{{ note.cn_no }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ formatContractNotesDate(note.cn_date) }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ note.broker }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ note.type }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-gray-500">{{ note.folio }}</td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ note.summary?.totalStocks || 0 }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ note.summary?.totalQuantity || 0 }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ formatContractNotesCurrency(note.summary?.totalInvested || 0) }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ formatContractNotesCurrency(note.oth_chg || 0) }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-right text-gray-500">
                      {{ formatContractNotesCurrency(note.famt || 0) }}
                    </td>
                    <td class="px-4 py-2 whitespace-nowrap text-sm text-center">
                      <button
                        @click="openContractNoteModal(note)"
                        class="text-indigo-600 hover:text-indigo-900 focus:outline-none"
                      >
                        <span class="sr-only">View</span>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                        </svg>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Last updated timestamp and refresh button -->
        <div class="mt-6 flex justify-end items-center text-sm text-gray-500">
          <p>Last updated: {{ contractNotesLastUpdated }}</p>
          <button @click="refreshContractNotes" class="ml-2 text-indigo-600 hover:text-indigo-800">
            Refresh Now
          </button>
        </div>

        <!-- Contract Note Modal -->
        <ContractNoteModal
          v-if="selectedContractNote"
          :is-open="isContractNoteModalOpen"
          :note="selectedContractNote"
          @close="closeContractNoteModal"
          @save="handleSaveContractNote"
        />

        <!-- Add Contract Note Modal -->
        <AddContractNoteModal
          :show="showAddContractNoteModal"
          @close="showAddContractNoteModal = false"
          @success="handleAddContractNoteSuccess"
        />
      </div>
    </div>

    <!-- MUTUAL FUNDS tab content -->
    <div v-if="rootTab === 'mutual-funds'" class="mt-4">
      <!-- Loading state - only show on initial load -->
      <div v-if="isMutualFundsLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="mutualFundsError" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative mb-6">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ mutualFundsError }}</span>
        <button @click="fetchMutualFunds" class="mt-2 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded">
          Try Again
        </button>
      </div>

      <div v-else>
        <!-- Mutual Fund Summary -->
        <div class="mb-8">
          <h2 class="text-2xl font-semibold text-gray-800 mb-4">Mutual Fund Portfolio Summary</h2>
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 xl:grid-cols-5 gap-6">
            <!-- Total Invested -->
            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-indigo-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Invested</h3>
              <div class="text-2xl font-bold text-gray-900">₹{{ formatPrice(mutualFundData.summary.totalInvested) }}</div>
              <div class="text-sm text-gray-500">{{ mutualFundData.summary.fundCount }} mutual funds</div>
            </div>

            <!-- Current Value -->
            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-green-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Current Value</h3>
              <div class="text-2xl font-bold text-gray-900">₹{{ formatPrice(mutualFundData.summary.currentValue) }}</div>
              <div class="text-sm text-gray-500">Last updated: {{ mutualFundsLastUpdated }}</div>
            </div>

            <!-- Today's Gain/Loss -->
            <div class="bg-white rounded-lg shadow-md p-4 border-l-4" :class="mutualFundData.summary.todayTotalPL >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Today's Gain/Loss</h3>
              <div class="text-2xl font-bold" :class="mutualFundData.summary.todayTotalPL >= 0 ? 'text-green-600' : 'text-red-600'">
                ₹{{ formatPrice(mutualFundData.summary.todayTotalPL) }}
              </div>
              <div class="text-sm" :class="mutualFundData.summary.todayPLPercentage >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ mutualFundData.summary.todayPLPercentage >= 0 ? '+' : '' }}{{ mutualFundData.summary.todayPLPercentage.toFixed(2) }}%
              </div>
            </div>

            <!-- Total Profit/Loss -->
            <div class="bg-white rounded-lg shadow-md p-4 border-l-4" :class="mutualFundData.summary.totalProfitLoss >= 0 ? 'border-green-500' : 'border-red-500'">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Total Profit/Loss</h3>
              <div class="text-2xl font-bold" :class="mutualFundData.summary.totalProfitLoss >= 0 ? 'text-green-600' : 'text-red-600'">
                ₹{{ formatPrice(mutualFundData.summary.totalProfitLoss) }}
              </div>
              <div class="text-sm" :class="mutualFundData.summary.profitLossPercentage >= 0 ? 'text-green-600' : 'text-red-600'">
                {{ mutualFundData.summary.profitLossPercentage >= 0 ? '+' : '' }}{{ mutualFundData.summary.profitLossPercentage.toFixed(2) }}%
              </div>
            </div>

            <!-- SIP Information -->
            <div class="bg-white rounded-lg shadow-md p-4 border-l-4 border-blue-500">
              <h3 class="text-lg font-medium text-gray-700 mb-2">Active SIPs</h3>
              <div class="text-2xl font-bold text-gray-900">{{ mutualFundData.summary.sipCount }}</div>
              <div class="text-sm text-gray-500">Monthly commitment: ₹{{ formatPrice(calculateMonthlySIP()) }}</div>
            </div>
          </div>
        </div>

        <!-- Charts Section -->
        <div class="mb-16">
          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Category Allocation Chart -->
            <div class="bg-white rounded-lg shadow-md p-4 h-full lg:col-span-1">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">Category Allocation</h3>
              <div v-if="isMutualFundsLoading" class="flex justify-center items-center h-64">
                <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
              </div>
              <div v-else-if="!mutualFundData.categoryAllocation || !mutualFundData.categoryAllocation.length" class="flex justify-center items-center h-64 text-gray-500">
                No category data available
              </div>
              <div v-else class="h-64 relative">
                <canvas ref="categoryChartCanvas" class="w-full h-full"></canvas>
              </div>
              <div v-if="mutualFundData.categoryAllocation && mutualFundData.categoryAllocation.length" class="mt-4">
                <div class="overflow-x-auto">
                  <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Value</th>
                        <th class="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Allocation</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <tr v-for="(item, index) in sortedCategoryAllocation" :key="index" class="hover:bg-gray-50">
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-gray-900 flex items-center">
                          <div class="w-3 h-3 rounded-full mr-2" :style="{ backgroundColor: getChartColors()[index % getChartColors().length] }"></div>
                          {{ item.category }}
                        </td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-900">₹{{ formatPrice(item.value) }}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-sm text-right text-gray-900">
                          {{ ((item.value / mutualFundData.summary.currentValue) * 100).toFixed(2) }}%
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>

            <!-- Fund House Distribution Chart -->
            <div class="bg-white rounded-lg shadow-md p-4 h-full lg:col-span-2">
              <h3 class="text-xl font-semibold text-gray-800 mb-4">Fund House Distribution</h3>
              <div v-if="isMutualFundsLoading" class="flex justify-center items-center h-64">
                <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-indigo-500"></div>
              </div>
              <div v-else-if="!mutualFundData.fundHouseAllocation || !mutualFundData.fundHouseAllocation.length" class="flex justify-center items-center h-64 text-gray-500">
                No fund house data available
              </div>
              <div v-else class="h-64 relative">
                <canvas ref="fundHouseChartCanvas" class="w-full h-full"></canvas>
              </div>
              <div v-if="mutualFundData.fundHouseAllocation && mutualFundData.fundHouseAllocation.length" class="mt-4">
                <div class="w-full">
                  <table class="w-full divide-y divide-gray-200 text-xs table-fixed">
                    <thead class="bg-gray-50">
                      <tr>
                        <th class="w-6 px-1 py-2 text-left text-xs font-medium text-gray-500 uppercase"></th>
                        <th class="px-2 py-2 text-left text-xs font-medium text-gray-500 uppercase" style="width: 25%;">Fund House</th>
                        <th class="px-1 py-2 text-right text-xs font-medium text-gray-500 uppercase" style="width: 12%;">Investment</th>
                        <th class="px-1 py-2 text-right text-xs font-medium text-gray-500 uppercase" style="width: 12%;">Value</th>
                        <th class="px-1 py-2 text-right text-xs font-medium text-gray-500 uppercase" style="width: 10%;">P/L</th>
                        <th class="px-1 py-2 text-right text-xs font-medium text-gray-500 uppercase" style="width: 8%;">P/L %</th>
                        <th class="px-1 py-2 text-right text-xs font-medium text-gray-500 uppercase" style="width: 8%;">XIRR</th>
                        <th class="px-1 py-2 text-right text-xs font-medium text-gray-500 uppercase" style="width: 10%;">Allocation</th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                      <template v-for="(item, index) in sortedFundHouseAllocation" :key="index">
                        <!-- Fund House Row -->
                        <tr class="hover:bg-gray-50 cursor-pointer" @click="toggleFundHouseDetails(item.fundHouse)">
                          <td class="px-1 py-2 text-center">
                            <button
                              class="text-gray-500 hover:text-gray-700 focus:outline-none"
                              v-if="item.folios && item.folios.length > 0"
                            >
                              <Icon
                                :name="expandedFundHouses.includes(item.fundHouse) ? 'heroicons:chevron-down' : 'heroicons:chevron-right'"
                                class="w-3 h-3"
                              />
                            </button>
                          </td>
                          <td class="px-2 py-2 text-xs text-gray-900 flex items-center min-w-0">
                            <div class="w-2 h-2 rounded-full mr-1 flex-shrink-0" :style="{ backgroundColor: getChartColors()[index % getChartColors().length] }"></div>
                            <div class="truncate" :title="item.fundHouse">{{ item.fundHouse }}</div>
                            <span v-if="item.folios && item.folios.length > 0" class="ml-1 text-xs text-gray-500 flex-shrink-0">
                              ({{ item.folios.length }})
                            </span>
                          </td>
                          <td class="px-1 py-2 text-xs text-right text-gray-900">₹{{ formatPrice(item.totalInvestment || 0) }}</td>
                          <td class="px-1 py-2 text-xs text-right text-gray-900">₹{{ formatPrice(item.value) }}</td>
                          <td class="px-1 py-2 text-xs text-right" :class="(item.totalProfitLoss || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                            ₹{{ formatPrice(item.totalProfitLoss || 0) }}
                          </td>
                          <td class="px-1 py-2 text-xs text-right" :class="(item.profitLossPercentage || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                            {{ formatPercentage(item.profitLossPercentage || 0) }}%
                          </td>
                          <td class="px-1 py-2 text-xs text-right" :class="(item.xirr || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                            {{ formatXIRR(item.xirr) }}
                          </td>
                          <td class="px-1 py-2 text-xs text-right text-gray-900">
                            {{ ((item.value / mutualFundData.summary.currentValue) * 100).toFixed(2) }}%
                          </td>
                        </tr>

                        <!-- Nested Folio Details -->
                        <tr v-if="expandedFundHouses.includes(item.fundHouse) && item.folios && item.folios.length > 0" class="bg-gray-50">
                          <td colspan="8" class="px-0 py-0">
                            <div class="border-t border-gray-200 px-2 py-2">
                              <h4 class="text-xs font-medium text-gray-700 mb-2">Folios in {{ item.fundHouse }}</h4>
                              <div class="w-full">
                                <table class="w-full divide-y divide-gray-200 text-xs table-fixed">
                                  <thead class="bg-gradient-to-r from-blue-500 to-teal-500">
                                    <tr>
                                      <th class="px-1 py-1 text-left text-xs font-medium text-white uppercase" style="width: 15%;">Folio</th>
                                      <th class="px-1 py-1 text-left text-xs font-medium text-white uppercase" style="width: 25%;">Schemes</th>
                                      <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase" style="width: 12%;">Investment</th>
                                      <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase" style="width: 12%;">Current</th>
                                      <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase" style="width: 10%;">P/L</th>
                                      <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase" style="width: 8%;">P/L %</th>
                                      <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase" style="width: 8%;">XIRR</th>
                                    </tr>
                                  </thead>
                                  <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-for="folio in item.folios" :key="folio.folioNumber" class="hover:bg-gray-50">
                                      <td class="px-1 py-1 text-xs text-gray-900">
                                        <div class="truncate" :title="folio.folioNumber">{{ folio.folioNumber }}</div>
                                        <div class="text-xs text-gray-500">
                                          {{ (folio.schemes || []).length }} scheme{{ (folio.schemes || []).length > 1 ? 's' : '' }}
                                        </div>
                                      </td>
                                      <td class="px-1 py-1 text-xs text-gray-500">
                                        <div class="space-y-1">
                                          <div v-for="scheme in (folio.schemes || [])" :key="scheme.schemeCode" class="text-xs">
                                            <div class="truncate" :title="scheme.schemeName">{{ scheme.schemeName }}</div>
                                            <div class="flex items-center gap-1 mt-0.5">
                                              <span v-if="scheme.sipFlag" class="px-1 py-0.5 bg-blue-100 text-blue-800 rounded text-xs">SIP</span>
                                              <span v-if="scheme.xirr" class="text-xs" :class="(scheme.xirr || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                                                {{ formatXIRR(scheme.xirr) }}
                                              </span>
                                            </div>
                                          </div>
                                        </div>
                                      </td>
                                      <td class="px-1 py-1 text-xs text-right text-gray-500">₹{{ formatPrice(folio.totalInvestment || 0) }}</td>
                                      <td class="px-1 py-1 text-xs text-right text-gray-500">₹{{ formatPrice(folio.totalValue || 0) }}</td>
                                      <td class="px-1 py-1 text-xs text-right" :class="(folio.totalProfitLoss || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                                        ₹{{ formatPrice(folio.totalProfitLoss || 0) }}
                                      </td>
                                      <td class="px-1 py-1 text-xs text-right" :class="(folio.profitLossPercentage || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                                        {{ formatPercentage(folio.profitLossPercentage || 0) }}%
                                      </td>
                                      <td class="px-1 py-1 text-xs text-right" :class="(folio.xirr || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                                        {{ formatXIRR(folio.xirr) }}
                                      </td>
                                    </tr>
                                  </tbody>
                                </table>
                              </div>
                            </div>
                          </td>
                        </tr>
                      </template>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Mutual Funds List -->
        <div class="mb-8">
          <div class="flex justify-between items-center mb-4">
            <h2 class="text-2xl font-semibold text-gray-800">My Mutual Funds</h2>
            <div class="flex items-center space-x-2">
              <button
                @click="refreshMutualFunds"
                class="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 px-3 py-1 rounded flex items-center text-sm"
                :disabled="isMutualFundsRefreshing"
              >
                <Icon v-if="isMutualFundsRefreshing" name="heroicons:arrow-path" class="w-4 h-4 mr-1 animate-spin" />
                <Icon v-else name="heroicons:arrow-path" class="w-4 h-4 mr-1" />
                Refresh
              </button>
              <button
                @click="updateAllMutualFundNAVs"
                class="bg-green-100 hover:bg-green-200 text-green-700 px-3 py-1 rounded flex items-center text-sm"
                :disabled="isUpdatingNAVs"
              >
                <Icon v-if="isUpdatingNAVs" name="heroicons:arrow-path" class="w-4 h-4 mr-1 animate-spin" />
                <Icon v-else name="heroicons:currency-rupee" class="w-4 h-4 mr-1" />
                Update NAVs
              </button>

              <button
                @click="showAllMutualFundDetails = !showAllMutualFundDetails"
                class="bg-blue-100 hover:bg-blue-200 text-blue-700 px-3 py-1 rounded flex items-center text-sm"
              >
                <Icon :name="showAllMutualFundDetails ? 'heroicons:minus-small' : 'heroicons:plus-small'" class="w-4 h-4 mr-1" />
                {{ showAllMutualFundDetails ? 'Collapse All' : 'Expand All' }}
              </button>
            </div>
          </div>

          <!-- Search Input -->
          <div class="mb-4">
            <div class="relative max-w-md">
              <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Icon name="heroicons:magnifying-glass" class="h-5 w-5 text-gray-400" />
              </div>
              <input
                v-model="mutualFundSearchQuery"
                type="text"
                placeholder="Search mutual funds..."
                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
              />
              <div v-if="mutualFundSearchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button
                  @click="mutualFundSearchQuery = ''"
                  class="text-gray-400 hover:text-gray-600 focus:outline-none"
                >
                  <Icon name="heroicons:x-mark" class="h-5 w-5" />
                </button>
              </div>
            </div>
            <div v-if="mutualFundSearchQuery && filteredMutualFunds.length === 0" class="mt-2 text-sm text-gray-500">
              No mutual funds found matching "{{ mutualFundSearchQuery }}"
            </div>
            <div v-else-if="mutualFundSearchQuery" class="mt-2 text-sm text-gray-500">
              Showing {{ filteredMutualFunds.length }} of {{ mutualFundData.mutualFunds.length }} mutual funds
            </div>
          </div>

          <div class="bg-white rounded-lg shadow-md overflow-hidden">
            <div class="w-full">
              <table class="w-full divide-y divide-gray-200 text-xs table-fixed">
                <thead class="bg-gradient-to-r from-indigo-600 to-purple-600">
                  <tr>
                    <th class="w-6 px-1 py-2 text-left text-xs font-medium text-white uppercase"></th>
                    <th class="px-2 py-2 text-left text-xs font-medium text-white uppercase" style="width: 25%;">Scheme</th>
                    <th class="px-1 py-2 text-left text-xs font-medium text-white uppercase" style="width: 8%;">Fund</th>
                    <th class="px-1 py-2 text-left text-xs font-medium text-white uppercase" style="width: 6%;">Cat</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 7%;">Units</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 7%;">Avg NAV</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 8%;">Curr NAV</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 8%;">Invested</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 8%;">Current</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 7%;">P/L</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 6%;">P/L%</th>
                    <th class="px-1 py-2 text-right text-xs font-medium text-white uppercase" style="width: 6%;">XIRR</th>
                    <th class="px-1 py-2 text-center text-xs font-medium text-white uppercase" style="width: 6%;" title="Actions: AI Analysis, Edit, Delete, View">Act</th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-if="isMutualFundsLoading" class="hover:bg-gray-50">
                    <td colspan="13" class="px-2 py-3 text-center text-gray-500 text-xs">
                      <div class="flex justify-center items-center">
                        <div class="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-indigo-500 mr-2"></div>
                        Loading mutual funds...
                      </div>
                    </td>
                  </tr>
                  <tr v-else-if="filteredMutualFunds.length === 0 && !mutualFundSearchQuery" class="hover:bg-gray-50">
                    <td colspan="13" class="px-2 py-3 text-center text-gray-500 text-xs">
                      No mutual funds found. Add your first mutual fund investment to get started.
                    </td>
                  </tr>
                  <tr v-else-if="filteredMutualFunds.length === 0 && mutualFundSearchQuery" class="hover:bg-gray-50">
                    <td colspan="13" class="px-2 py-3 text-center text-gray-500 text-xs">
                      No mutual funds match your search criteria. Try a different search term.
                    </td>
                  </tr>
                  <template v-for="(fund, index) in filteredMutualFunds" :key="fund._id">
                    <tr class="hover:bg-gray-50">
                      <td class="px-1 py-2 text-center">
                        <button
                          @click="toggleMutualFundDetails(fund._id)"
                          class="text-gray-500 hover:text-gray-700 focus:outline-none"
                          v-if="rawMutualFunds.filter(f => f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName).length > 1"
                        >
                          <Icon
                            :name="expandedMutualFunds.includes(fund._id) ? 'heroicons:chevron-down' : 'heroicons:chevron-right'"
                            class="w-3 h-3"
                          />
                        </button>
                      </td>
                      <td class="px-2 py-2 text-xs font-medium text-gray-900 min-w-0">
                        <div class="truncate max-w-xs" :title="fund.schemeName">{{ fund.schemeName }}</div>
                        <div class="flex items-center gap-1 mt-1">
                          <span v-if="fund.sipFlag" class="inline-flex items-center px-1 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                            SIP
                          </span>
                          <span class="text-xs text-gray-500" v-if="rawMutualFunds.filter(f => f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName).length > 1">
                            {{ rawMutualFunds.filter(f => f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName).length }}
                          </span>
                        </div>
                      </td>
                      <td class="px-1 py-2 text-xs text-gray-500 truncate max-w-16" :title="fund.fundHouse">{{ fund.fundHouse }}</td>
                      <td class="px-1 py-2 text-xs text-gray-500 truncate max-w-12" :title="fund.category">{{ fund.category }}</td>
                      <td class="px-1 py-2 text-xs text-right text-gray-500">{{ formatNumber(fund.units) }}</td>
                      <td class="px-1 py-2 text-xs text-right text-gray-500">{{ formatPrice(fund.purchaseNAV) }}</td>
                      <td class="px-1 py-2 text-xs text-right text-gray-500">
                        <div>{{ formatPrice(fund.currentNAV) }}</div>
                        <div v-if="fund.dayPLPercentage" :class="fund.dayPLPercentage >= 0 ? 'text-green-600' : 'text-red-600'" class="text-xs">
                          {{ fund.dayPLPercentage >= 0 ? '+' : '' }}{{ formatPercentage(fund.dayPLPercentage) }}%
                        </div>
                      </td>
                      <td class="px-1 py-2 text-xs text-right text-gray-500">{{ formatPrice(fund.investmentAmount) }}</td>
                      <td class="px-1 py-2 text-xs text-right text-gray-500">{{ formatPrice(fund.currentValue) }}</td>
                      <td class="px-1 py-2 text-xs text-right" :class="fund.profitLoss >= 0 ? 'text-green-600' : 'text-red-600'">
                        {{ formatPrice(fund.profitLoss) }}
                      </td>
                      <td class="px-1 py-2 text-xs text-right" :class="fund.profitLossPercentage >= 0 ? 'text-green-600' : 'text-red-600'">
                        {{ formatPercentage(fund.profitLossPercentage) }}%
                      </td>
                      <td class="px-1 py-2 text-xs text-right" :class="(fund.xirr || 0) >= 0 ? 'text-green-600' : 'text-red-600'">
                        {{ formatXIRR(fund.xirr) }}
                      </td>
                      <td class="px-1 py-2 text-center">
                        <div class="flex justify-center space-x-1">
                          <!-- AI Analysis Button -->
                          <button
                            @click="openMutualFundAIAnalysis(fund)"
                            class="text-purple-600 hover:text-purple-900"
                            title="AI Analysis"
                          >
                            <Icon name="heroicons:cpu-chip" class="w-3 h-3" />
                          </button>
                          <button
                            v-if="rawMutualFunds.filter(f => f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName).length <= 1"
                            @click="editMutualFund(fund)"
                            class="text-indigo-600 hover:text-indigo-900"
                            title="Edit"
                          >
                            <Icon name="heroicons:pencil-square" class="w-3 h-3" />
                          </button>
                          <button
                            v-if="rawMutualFunds.filter(f => f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName).length <= 1"
                            @click="confirmDeleteMutualFund(fund)"
                            class="text-red-600 hover:text-red-900"
                            title="Delete"
                          >
                            <Icon name="heroicons:trash" class="w-3 h-3" />
                          </button>
                          <button
                            v-if="rawMutualFunds.filter(f => f.schemeCode === fund.schemeCode && f.schemeName === fund.schemeName).length > 1"
                            @click="toggleMutualFundDetails(fund._id)"
                            class="text-blue-600 hover:text-blue-900"
                            title="View Details"
                          >
                            <Icon name="heroicons:eye" class="w-3 h-3" />
                          </button>
                        </div>
                      </td>
                    </tr>
                    <!-- Nested table for individual entries -->
                    <tr v-if="expandedMutualFunds.includes(fund._id) || showAllMutualFundDetails" class="bg-gray-50">
                      <td colspan="13" class="px-0 py-0">
                        <div class="border-t border-gray-200 px-2 py-2">
                          <h4 class="text-xs font-medium text-gray-700 mb-2">Individual Entries for {{ fund.schemeName }}</h4>
                          <div class="w-full">
                            <table class="w-full divide-y divide-gray-200 text-xs">
                              <thead class="bg-gradient-to-r from-blue-500 to-teal-500">
                                <tr>
                                  <th class="px-1 py-1 text-left text-xs font-medium text-white uppercase">Date</th>
                                  <th class="px-1 py-1 text-left text-xs font-medium text-white uppercase">Folio</th>
                                  <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase">Units</th>
                                  <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase">NAV</th>
                                  <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase">Invested</th>
                                  <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase">Current</th>
                                  <th class="px-1 py-1 text-right text-xs font-medium text-white uppercase">P/L</th>
                                  <th class="px-1 py-1 text-center text-xs font-medium text-white uppercase">SIP</th>
                                  <th class="px-1 py-1 text-center text-xs font-medium text-white uppercase">Act</th>
                                </tr>
                              </thead>
                              <tbody class="bg-white divide-y divide-gray-200">
                                <tr v-for="rawFund in filteredRawFunds(fund)" :key="rawFund._id" class="hover:bg-gray-50">
                                  <td class="px-1 py-1 text-xs text-gray-500">{{ formatDateDDMMYYYY(rawFund.purchaseDate) }}</td>
                                  <td class="px-1 py-1 text-xs text-gray-500 truncate max-w-16" :title="rawFund.folioNumber">{{ rawFund.folioNumber }}</td>
                                  <td class="px-1 py-1 text-xs text-right text-gray-500">{{ formatNumber(rawFund.units) }}</td>
                                  <td class="px-1 py-1 text-xs text-right text-gray-500">{{ formatPrice(rawFund.purchaseNAV) }}</td>
                                  <td class="px-1 py-1 text-xs text-right text-gray-500">{{ formatPrice(rawFund.investmentAmount) }}</td>
                                  <td class="px-1 py-1 text-xs text-right text-gray-500">{{ formatPrice(rawFund.currentNAV * rawFund.units) }}</td>
                                  <td class="px-1 py-1 text-xs text-right"
                                      :class="(rawFund.currentNAV * rawFund.units - rawFund.investmentAmount) >= 0 ? 'text-green-600' : 'text-red-600'">
                                    {{ formatPrice(rawFund.currentNAV * rawFund.units - rawFund.investmentAmount) }}
                                  </td>
                                  <td class="px-1 py-1 text-xs text-center">
                                    <span v-if="rawFund.sipFlag" class="px-1 py-0.5 inline-flex text-xs leading-4 font-semibold rounded bg-green-100 text-green-800">
                                      Y
                                    </span>
                                    <span v-else>-</span>
                                  </td>
                                  <td class="px-1 py-1 text-center">
                                    <div class="flex justify-center space-x-1">
                                      <button @click="editRawMutualFund(rawFund)" class="text-indigo-600 hover:text-indigo-900">
                                        <Icon name="heroicons:pencil-square" class="w-3 h-3" />
                                      </button>
                                      <button @click="confirmDeleteRawMutualFund(rawFund)" class="text-red-600 hover:text-red-900">
                                        <Icon name="heroicons:trash" class="w-3 h-3" />
                                      </button>
                                    </div>
                                  </td>
                                </tr>
                              </tbody>
                            </table>
                          </div>
                        </div>
                      </td>
                    </tr>
                  </template>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Smart Auto-update controls and status -->
        <div class="mt-6 bg-gray-50 rounded-lg p-4">
          <div class="flex flex-col space-y-3 text-sm">
            <!-- Smart Auto-Update Toggle -->
            <div class="flex justify-between items-center">
              <div class="flex items-center">
                <label class="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    v-model="mutualFundsAutoUpdateEnabled"
                    @change="handleSmartAutoUpdateChange"
                    class="sr-only"
                  />
                  <div class="relative">
                    <div class="block bg-gray-600 w-14 h-8 rounded-full"></div>
                    <div :class="mutualFundsAutoUpdateEnabled ? 'translate-x-6 bg-green-400' : 'translate-x-0 bg-white'" class="absolute left-1 top-1 bg-white w-6 h-6 rounded-full transition transform"></div>
                  </div>
                  <span class="ml-3 font-medium text-gray-700">Smart Daily NAV Updates</span>
                </label>
                <div class="ml-2 text-xs text-gray-500">
                  (Automatically updates NAVs once daily at 9 PM on trading days)
                </div>
              </div>

              <!-- Update Status Indicator -->
              <div class="flex items-center space-x-2">
                <div v-if="mutualFundsUpdateStatus === 'updating'" class="flex items-center text-blue-600">
                  <Icon name="heroicons:arrow-path" class="w-4 h-4 mr-1 animate-spin" />
                  <span class="text-xs">Updating...</span>
                </div>
                <div v-else-if="mutualFundsUpdateStatus === 'completed'" class="flex items-center text-green-600">
                  <Icon name="heroicons:check-circle" class="w-4 h-4 mr-1" />
                  <span class="text-xs">Updated</span>
                </div>
                <div v-else-if="mutualFundsUpdateStatus === 'failed'" class="flex items-center text-red-600">
                  <Icon name="heroicons:exclamation-circle" class="w-4 h-4 mr-1" />
                  <span class="text-xs">Failed</span>
                </div>
                <div v-else class="flex items-center text-gray-500">
                  <Icon name="heroicons:clock" class="w-4 h-4 mr-1" />
                  <span class="text-xs">Waiting</span>
                </div>
              </div>
            </div>

            <!-- Status Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 text-xs">
              <div class="flex justify-between">
                <span class="text-gray-600">Last NAV Update:</span>
                <span class="font-medium text-gray-900">{{ mutualFundsLastUpdated }}</span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Next Scheduled:</span>
                <span class="font-medium" :class="mutualFundsAutoUpdateEnabled ? 'text-blue-600' : 'text-gray-500'">
                  {{ mutualFundsNextUpdate }}
                </span>
              </div>
              <div class="flex justify-between">
                <span class="text-gray-600">Trading Day:</span>
                <span class="font-medium" :class="isTradingDay() ? 'text-green-600' : 'text-orange-600'">
                  {{ isTradingDay() ? 'Yes' : 'No (Weekend/Holiday)' }}
                </span>
              </div>
            </div>


          </div>
        </div>
      </div>

      <!-- Mutual Fund Modal -->
      <MutualFundModal
        :show="showAddMutualFundModal"
        :initial-data="selectedMutualFund"
        :is-loading="isMutualFundsLoading"
        :existing-sips="rawMutualFunds"
        @close="showAddMutualFundModal = false; selectedMutualFund = null"
        @save="handleSaveMutualFund"
        @fetch-existing-sips="fetchMutualFunds"
      />

      <!-- Mutual Fund Import Modal -->
      <MutualFundImportModal
        :show="showImportMutualFundModal"
        @close="showImportMutualFundModal = false"
      />

      <!-- Mutual Fund Redemption Modal -->
      <MutualFundRedemptionModal
        :show="showRedeemMutualFundModal"
        :existing-funds="rawMutualFunds"
        :is-loading="isMutualFundsLoading"
        @close="showRedeemMutualFundModal = false"
        @save="handleRedeemMutualFund"
      />

      <!-- NAV Update Progress Modal -->
      <div v-if="navUpdateProgress.isUpdating" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="nav-update-modal" role="dialog" aria-modal="true">
        <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <!-- Background overlay -->
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

          <!-- Modal panel -->
          <div class="inline-block align-middle bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-blue-100 sm:mx-0 sm:h-10 sm:w-10">
                  <Icon name="heroicons:arrow-path" class="h-6 w-6 text-blue-600 animate-spin" />
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left flex-1">
                  <h3 class="text-lg leading-6 font-medium text-gray-900" id="nav-update-modal">
                    Updating NAVs
                  </h3>
                  <div class="mt-4">
                    <!-- Progress Bar -->
                    <div class="w-full bg-gray-200 rounded-full h-2.5 mb-4">
                      <div
                        class="bg-blue-600 h-2.5 rounded-full transition-all duration-300"
                        :style="{ width: navUpdateProgress.total > 0 ? (navUpdateProgress.completed / navUpdateProgress.total * 100) + '%' : '0%' }"
                      ></div>
                    </div>

                    <!-- Progress Stats -->
                    <div class="grid grid-cols-4 gap-4 mb-4 text-sm">
                      <div class="text-center">
                        <div class="font-semibold text-gray-900">{{ navUpdateProgress.completed }}</div>
                        <div class="text-gray-500">Completed</div>
                      </div>
                      <div class="text-center">
                        <div class="font-semibold text-green-600">{{ navUpdateProgress.successful }}</div>
                        <div class="text-gray-500">Successful</div>
                      </div>
                      <div class="text-center">
                        <div class="font-semibold text-red-600">{{ navUpdateProgress.failed }}</div>
                        <div class="text-gray-500">Failed</div>
                      </div>
                      <div class="text-center">
                        <div class="font-semibold text-gray-900">{{ navUpdateProgress.total }}</div>
                        <div class="text-gray-500">Total</div>
                      </div>
                    </div>

                    <!-- Current Scheme -->
                    <div v-if="navUpdateProgress.currentSchemeName" class="mb-4 p-3 bg-blue-50 rounded-lg">
                      <div class="text-sm font-medium text-blue-900">Currently Processing:</div>
                      <div class="text-sm text-blue-700 truncate">{{ navUpdateProgress.currentSchemeName }}</div>
                      <div class="text-xs text-blue-600">{{ navUpdateProgress.currentScheme }}</div>
                    </div>

                    <!-- Progress Logs -->
                    <div class="max-h-40 overflow-y-auto bg-gray-50 rounded-lg p-3">
                      <div class="text-xs font-medium text-gray-700 mb-2">Progress Log:</div>
                      <div v-for="(log, index) in navUpdateProgress.logs.slice(-10)" :key="index" class="text-xs text-gray-600 mb-1">
                        {{ log }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                v-if="navUpdateProgress.canCancel && !navUpdateProgress.cancelled"
                @click="cancelNAVUpdate"
                type="button"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel Update
              </button>
              <div v-else class="text-sm text-gray-500 flex items-center">
                <Icon name="heroicons:clock" class="h-4 w-4 mr-1" />
                Please wait...
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Delete Mutual Fund Confirmation Modal -->
      <div v-if="showDeleteMutualFundConfirmation" class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
          <!-- Background overlay -->
          <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" @click="showDeleteMutualFundConfirmation = false"></div>

          <!-- Modal panel -->
          <div class="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
              <div class="sm:flex sm:items-start">
                <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                  <Icon name="heroicons:exclamation-triangle" class="h-6 w-6 text-red-600" />
                </div>
                <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  <h3 class="text-lg leading-6 font-medium text-gray-900 dark:text-gray-100" id="modal-title">
                    Delete {{ 'purchaseDate' in (mutualFundToDelete || {}) ? 'Mutual Fund Entry' : 'Mutual Fund' }}
                  </h3>
                  <div class="mt-2">
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                      Are you sure you want to delete this
                      {{ 'purchaseDate' in (mutualFundToDelete || {}) ? 'mutual fund entry' : 'mutual fund' }}?
                      {{ 'purchaseDate' in (mutualFundToDelete || {})
                        ? `This will only delete the entry dated ${formatDate(mutualFundToDelete?.purchaseDate)}.`
                        : '' }}
                      This action cannot be undone.
                    </p>
                    <div v-if="'purchaseDate' in (mutualFundToDelete || {})" class="mt-2 text-sm bg-gray-100 dark:bg-gray-700 p-2 rounded">
                      <div><span class="font-medium">Scheme:</span> {{ mutualFundToDelete?.schemeName }}</div>
                      <div><span class="font-medium">Units:</span> {{ formatNumber(mutualFundToDelete?.units) }}</div>
                      <div><span class="font-medium">Investment:</span> {{ formatPrice(mutualFundToDelete?.investmentAmount) }}</div>
                      <div><span class="font-medium">Purchase Date:</span> {{ formatDate(mutualFundToDelete?.purchaseDate) }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
              <button
                type="button"
                @click="deleteMutualFundConfirmed"
                class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm"
                :disabled="isMutualFundsLoading"
              >
                <span v-if="isMutualFundsLoading" class="animate-spin h-4 w-4 mr-2 border-2 border-white border-t-transparent rounded-full"></span>
                Delete
              </button>
              <button
                type="button"
                @click="showDeleteMutualFundConfirmation = false; mutualFundToDelete = null"
                class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 dark:border-gray-600 shadow-sm px-4 py-2 bg-white dark:bg-gray-800 text-base font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Export Data Modal -->
  <ExportDataModal
    :show="showExportModal"
    @close="showExportModal = false"
    @export-complete="handleExportComplete"
  />

  <!-- AI Stock Analysis Modal -->
  <ClientOnly>
    <component
      :is="AIStockAnalysisModal"
      v-if="showAIAnalysisModal && AIStockAnalysisModal"
      :stock="selectedStockForAI"
      @close="closeAIAnalysis"
    />
  </ClientOnly>

  <!-- AI Mutual Fund Analysis Modal -->
  <ClientOnly>
    <component
      :is="AIMutualFundAnalysisModal"
      v-if="showMutualFundAIAnalysisModal && AIMutualFundAnalysisModal"
      :fund="selectedMutualFundForAI"
      :show="showMutualFundAIAnalysisModal"
      @close="closeMutualFundAIAnalysis"
    />
  </ClientOnly>
</template>

<style scoped>
/* Ensure table fits without horizontal scroll */
.table-fixed {
  table-layout: fixed;
}

/* Responsive text sizing */
@media (max-width: 1024px) {
  .text-xs {
    font-size: 0.65rem;
  }
}

@media (max-width: 768px) {
  .text-xs {
    font-size: 0.6rem;
  }
}

/* Ensure truncation works properly */
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Compact spacing for mobile */
@media (max-width: 640px) {
  .px-1 {
    padding-left: 0.125rem;
    padding-right: 0.125rem;
  }

  .py-2 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
  }
}
</style>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useHead } from 'nuxt/app';
import ExportDataModal from '~/components/stock-market/ExportDataModal.vue';
// Dynamic import for AIStockAnalysisModal to avoid server-side Chart.js issues
// Dynamic import for AIMutualFundAnalysisModal to avoid server-side Chart.js issues
import Chart from 'chart.js/auto';
import { useStockMarket } from '~/composables/stock-market/useStockMarket';
import { useInvestments } from '~/composables/stock-market/useInvestments';
import { useContractNotes } from '~/composables/stock-market/useContractNotes';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { useAIConfig } from '~/composables/ai/useAIConfig';
import ContractNoteModal from '~/components/stock-market/ContractNoteModal.vue';
import AddContractNoteModal from '~/components/stock-market/AddContractNoteModal.vue';
import MonthlyInvestmentChart from '~/components/stock-market/MonthlyInvestmentChart.vue';
import NiftyHistoricalChart from '~/components/stock-market/NiftyHistoricalChart.vue';
import EquityMutualFundComparisonChart from '~/components/stock-market/EquityMutualFundComparisonChart.vue';
import MutualFundModal from '~/components/stock-market/MutualFundModal.vue';
import MutualFundImportModal from '~/components/stock-market/MutualFundImportModal.vue';
import MutualFundRedemptionModal from '~/components/stock-market/MutualFundRedemptionModal.vue';
import { formatXIRR } from '~/utils/xirr';

// Define interfaces for stock data
interface StockMeta {
  companyName: string;
}

interface StockData {
  symbol: string;
  meta: StockMeta;
  lastPrice?: number;
  change?: number;
  pChange?: number;
  totalTradedVolume?: number;
  open?: number;
  dayHigh?: number;
  dayLow?: number;
  previousClose?: number;
}

// Define interfaces for API responses
interface StockQuoteInfo {
  symbol: string;
  companyName: string;
  lastPrice: number;
  change: number;
  pChange: number;
  totalTradedVolume: number;
}

interface StockTradeInfo {
  open: number;
  dayHigh: number;
  dayLow: number;
  previousClose: number;
}

interface StockQuote {
  info: StockQuoteInfo;
}

interface StockDetailResponse {
  quote?: StockQuote;
  tradeInfo?: StockTradeInfo;
  error?: string;
}

// Set page metadata
useHead({
  title: 'Indian Stock Market Data',
  meta: [
    { name: 'description', content: 'Live Indian stock market data from NSE' }
  ]
});

const {
  isLoading,
  isSearching,
  error,
  marketData,
  searchResults,
  fetchMarketData,
  searchStocks,
  startAutoUpdate,
  stopAutoUpdate,
  formatPercentChange,
  formatPrice
} = useStockMarket();

// Root level tab state
const rootTab = ref('live-market'); // Default to live-market tab

// Track initial loading state separately from subsequent updates
const isInitialLoading = ref(true);

const lastUpdated = computed(() => {
  if (!marketData.value.timestamp) return new Date().toLocaleString();
  const date = new Date(marketData.value.timestamp);
  return date.toLocaleString();
});
const searchQuery = ref('');
const autoUpdateInterval = ref('1'); // Default to 1 minute
// Mock data variable removed
// Store the stock data locally
const stocksData = ref<StockData[]>([]);

// Chart period state for NiftyHistoricalChart
const chartPeriod = ref('1Y'); // Default to 1 year

// AI Configuration
const { isConfigured: isAIConfigured } = useAIConfig();
const aiBannerDismissed = ref(false);

// Import the StockViews composable
import { useStockViews } from '~/composables/stock-market/useStockViews';

// Get investments functionality
const {
  isLoading: isInvestmentsLoading,
  isUpdatingPrices,
  error: investmentsError,
  investmentData,
  lastPriceUpdate,
  fetchInvestmentData,
  updateCurrentPrices,
  startPriceUpdates,
  stopPriceUpdates,
  startAutoUpdate: startInvestmentsAutoUpdate,
  stopAutoUpdate: stopInvestmentsAutoUpdate,
  formatCurrency: formatInvestmentCurrency,
  formatPercentage: formatInvestmentPercentage,
  getMonthlyInvestmentData
} = useInvestments();

// Computed property for monthly investment chart data
const monthlyInvestmentChartData = computed(() => {
  return getMonthlyInvestmentData();
});

// Investments state
const investmentsLastUpdated = ref(new Date().toLocaleString());
const investmentsAutoUpdateInterval = ref('5'); // Default to 5 minutes

// Combined timer state
const investmentsNextTime = ref<Date | null>(new Date(Date.now() + 5 * 60000)); // 5 minutes default
const investmentsTimeRemaining = ref<string>('05:00');
let investmentsCountdownInterval: number | null = null;

// Operation status for visual feedback
const operationStatus = ref<'idle' | 'updating-prices' | 'fetching-data'>('idle');
const operationStatusText = computed(() => {
  switch (operationStatus.value) {
    case 'updating-prices': return 'Updating prices...';
    case 'fetching-data': return 'Fetching data...';
    default: return '';
  }
});

// Get contract notes functionality
const {
  isLoading: isContractNotesLoading,
  isSaving: isContractNoteSaving,
  error: contractNotesError,
  contractNotesData,
  selectedContractNote,
  isModalOpen: isContractNoteModalOpen,
  fetchContractNotesData,
  openContractNoteModal,
  closeContractNoteModal,
  saveContractNote,
  formatCurrency: formatContractNotesCurrency,
  formatDate: formatContractNotesDate
} = useContractNotes();

// Contract Notes state
const contractNotesLastUpdated = ref(new Date().toLocaleString());
const showAddContractNoteModal = ref(false);

// Get stock views functionality
const {
  views,
  activeViewId,
  isLoading: viewsLoading,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  error: viewsError, // Kept for future error handling
  fetchViews,
  createView,
  deleteView,
  addSymbolToView,
  removeSymbolFromView,
  setActiveView
} = useStockViews();

// Active tab state
const activeTab = ref('nifty50'); // Default to Nifty 50 tab
const showCreateViewModal = ref(false);
const newViewName = ref('');
const viewToDelete = ref<string | null>(null); // For delete confirmation

// Add stock form state
const newStockSymbol = ref('');
const addStockError = ref('');

// AI Stock Analysis Modal state
const showAIAnalysisModal = ref(false);
const selectedStockForAI = ref<StockData | null>(null);
const AIStockAnalysisModal = ref(null);

// AI Mutual Fund Analysis Modal state
const showMutualFundAIAnalysisModal = ref(false);
const selectedMutualFundForAI = ref(null);
const AIMutualFundAnalysisModal = ref(null);

// Computed property to get key indices (NIFTY 50, SENSEX, BANK NIFTY, etc.)
const keyIndices = computed(() => {
  if (!marketData.value?.indices || !marketData.value.indices.length) return [];

  // Filter for key indices we want to display
  const indexNames = ['NIFTY 50', 'SENSEX', 'NIFTY BANK', 'NIFTY IT', 'NIFTY AUTO', 'NIFTY MIDCAP 100', 'NIFTY SMALLCAP 100'];

  return marketData.value.indices
    .filter((index: any) => indexNames.includes(index.indexName))
    .map((index: any) => ({
      ...index,
      percentChange: formatPercentChange(index.percentChange)
    }))
    .slice(0, 7); // Limit to 7 indices to include the new ones
});

// Filtered stocks based on search query
const filteredStocks = computed(() => {
  if (!stocksData.value || stocksData.value.length === 0) return [];

  if (!searchQuery.value) return stocksData.value;

  const query = searchQuery.value.toLowerCase();
  return stocksData.value.filter(stock =>
    stock.symbol.toLowerCase().includes(query) ||
    (stock.meta?.companyName && stock.meta.companyName.toLowerCase().includes(query))
  );
});

// We're now using the formatVolume function from useStockMarket composable

// Get the active view
const activeView = computed(() => {
  if (!activeViewId.value) return null;
  return views.value.find(v => v.id === activeViewId.value) || null;
});

// Computed property for custom view stocks
const customViewStocks = computed(() => {
  console.log('Computing customViewStocks with:', {
    hasActiveView: !!activeView.value,
    activeViewId: activeViewId.value,
    activeViewSymbols: activeView.value?.symbols,
    activeViewSymbolsCount: activeView.value?.symbols?.length,
    activeViewSymbolsType: activeView.value?.symbols ? typeof activeView.value.symbols : 'undefined',
    activeViewSymbolsIsArray: activeView.value?.symbols ? Array.isArray(activeView.value.symbols) : false,
    stocksDataLength: stocksData.value?.length
  });

  if (!activeView.value || !activeView.value.symbols || !stocksData.value.length) {
    console.log('Returning empty array because:', {
      noActiveView: !activeView.value,
      noSymbols: !activeView.value?.symbols,
      noStocksData: !stocksData.value.length
    });
    return [];
  }

  // Log the symbols in the active view
  console.log('Active view symbols:', activeView.value.symbols);

  // Check if symbols is an array
  if (!Array.isArray(activeView.value.symbols)) {
    console.error('Symbols is not an array:', activeView.value.symbols);
    // Try to parse it if it's a string
    try {
      if (typeof activeView.value.symbols === 'string') {
        const parsedSymbols = JSON.parse(activeView.value.symbols);
        console.log('Parsed symbols:', parsedSymbols);
        // Use the parsed symbols
        return stocksData.value.filter(stock =>
          parsedSymbols.includes(stock.symbol)
        );
      }
    } catch (err) {
      console.error('Failed to parse symbols:', err);
    }
    return [];
  }

  // Filter stocks based on the symbols in the active view (case-insensitive and handling .NS suffix)
  const filteredStocks = stocksData.value.filter(stock => {
    // Normalize stock symbol (remove .NS if present)
    const normalizedStockSymbol = stock.symbol.replace('.NS', '');

    // Try exact match first
    const exactMatch = activeView.value?.symbols?.some(viewSymbol => {
      // Normalize view symbol (remove .NS if present)
      const normalizedViewSymbol = viewSymbol.replace('.NS', '');
      return normalizedViewSymbol === normalizedStockSymbol;
    }) || false;

    // If no exact match, try case-insensitive match
    const caseInsensitiveMatch = !exactMatch && (activeView.value?.symbols?.some(viewSymbol => {
      // Normalize view symbol (remove .NS if present)
      const normalizedViewSymbol = viewSymbol.replace('.NS', '').toLowerCase();
      return normalizedViewSymbol === normalizedStockSymbol.toLowerCase();
    }) || false);

    return exactMatch || caseInsensitiveMatch;
  });

  console.log('Filtered stocks:', {
    count: filteredStocks.length,
    symbols: filteredStocks.map(s => s.symbol)
  });

  return filteredStocks;
});

// Create a new view
const createNewView = async () => {
  if (!newViewName.value.trim()) return;

  try {
    const view = await createView(newViewName.value.trim());
    if (view) {
      activeTab.value = view.id;
      setActiveView(view.id);
      showCreateViewModal.value = false;
      newViewName.value = '';
    }
  } catch (err) {
    console.error('Failed to create view:', err);
  }
};

// Confirm delete view
const confirmDeleteView = (viewId: string) => {
  viewToDelete.value = viewId;
};

// Delete view confirmed
const deleteViewConfirmed = async () => {
  if (!viewToDelete.value) return;

  try {
    const success = await deleteView(viewToDelete.value);
    if (success) {
      // If the deleted view was active, switch to Nifty 50
      if (activeTab.value === viewToDelete.value) {
        activeTab.value = 'nifty50';
        setActiveView('');
      }
      viewToDelete.value = null;
    }
  } catch (err) {
    console.error('Failed to delete view:', err);
  }
};

// Add a stock to the current view from the input field
const addStockToView = async () => {
  if (!activeView.value || !newStockSymbol.value.trim()) return;

  try {
    addStockError.value = '';
    // Remove .NS suffix if user entered it
    let symbol = newStockSymbol.value.trim().toUpperCase();
    if (symbol.endsWith('.NS')) {
      symbol = symbol.replace('.NS', '');
    }

    // Check if the symbol already exists in the view
    if (activeView.value.symbols && activeView.value.symbols.some(s =>
      s.toUpperCase() === symbol
    )) {
      addStockError.value = `Symbol ${symbol} is already in this view`;
      return;
    }

    // Check if the symbol exists in the stock data
    const symbolExists = stocksData.value.some(stock =>
      stock.symbol.toUpperCase() === symbol
    );

    // If the symbol doesn't exist in our current data, we'll add it anyway
    // The user can manually add any symbol they want

    const result = await addSymbolToView(activeView.value.id, symbol);

    if (result) {
      // Clear the input field
      newStockSymbol.value = '';

      // Refresh views to update the UI
      await fetchViews();

      // If the symbol wasn't in our stock data, we need to fetch it
      if (!symbolExists) {
        await fetchSymbolData(symbol);
      }
    } else {
      addStockError.value = 'Failed to add stock to view';
    }
  } catch (err: unknown) {
    console.error('Failed to add stock:', err);
    if (err instanceof Error) {
      addStockError.value = err.message;
    } else {
      addStockError.value = 'Failed to add stock to view';
    }
  }
};

// Test stock function removed - no test/mock data in production

// Show debug info for the current view (for development/debugging purposes)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const showViewDebugInfo = () => {
  if (!activeView.value) return;

  console.log('=== VIEW DEBUG INFO ===');
  console.log('Active View:', activeView.value);
  console.log('View Symbols:', activeView.value.symbols);
  console.log('Symbol Type:', typeof activeView.value.symbols);
  console.log('Is Array:', Array.isArray(activeView.value.symbols));

  if (activeView.value.symbols && activeView.value.symbols.length > 0) {
    console.log('First Symbol:', activeView.value.symbols[0]);
    console.log('First Symbol Type:', typeof activeView.value.symbols[0]);
  }

  console.log('All Stock Symbols:', stocksData.value.map(s => s.symbol));

  // Check if any symbols match
  if (activeView.value.symbols && activeView.value.symbols.length > 0) {
    const viewSymbol = activeView.value.symbols[0];

    // Check for exact matches
    const exactMatches = stocksData.value.filter(s => s.symbol === viewSymbol);
    console.log(`Stocks matching symbol "${viewSymbol}" exactly:`, exactMatches);

    // Check for case-insensitive matches
    const caseInsensitiveMatches = stocksData.value.filter(s =>
      s.symbol.toLowerCase() === viewSymbol.toLowerCase()
    );
    console.log(`Case-insensitive matches for "${viewSymbol}":`, caseInsensitiveMatches);

    // Check for matches without .NS suffix
    const normalizedViewSymbol = viewSymbol.replace('.NS', '');
    const normalizedMatches = stocksData.value.filter(s =>
      s.symbol.replace('.NS', '') === normalizedViewSymbol
    );
    console.log(`Normalized matches (without .NS) for "${viewSymbol}":`, normalizedMatches);

    // Check for case-insensitive normalized matches
    const caseInsensitiveNormalizedMatches = stocksData.value.filter(s =>
      s.symbol.replace('.NS', '').toLowerCase() === normalizedViewSymbol.toLowerCase()
    );
    console.log(`Case-insensitive normalized matches for "${viewSymbol}":`, caseInsensitiveNormalizedMatches);
  }

  console.log('=== END DEBUG INFO ===');
};

// Fix the case of symbols in the view to match the stock data (utility function for maintenance)
// eslint-disable-next-line @typescript-eslint/no-unused-vars
const fixSymbolCase = async () => {
  if (!activeView.value || !activeView.value.symbols || !activeView.value.symbols.length) {
    console.log('No symbols to fix');
    return;
  }

  console.log('Fixing symbol case for view:', activeView.value.id);

  // Get all the symbols from the view
  const viewSymbols = [...activeView.value.symbols];

  // Create a map of normalized lowercase symbol to correct case symbol from stock data
  const symbolCaseMap: Record<string, string> = {};
  stocksData.value.forEach(stock => {
    // Store without .NS suffix since the API returns symbols without it
    const normalizedSymbol = stock.symbol.replace('.NS', '').toLowerCase();
    symbolCaseMap[normalizedSymbol] = stock.symbol.replace('.NS', '');
  });

  console.log('Symbol case map:', symbolCaseMap);

  // Check each symbol in the view
  let hasChanges = false;
  for (let i = 0; i < viewSymbols.length; i++) {
    const viewSymbol = viewSymbols[i];
    // Normalize the view symbol (remove .NS if present)
    const normalizedViewSymbol = viewSymbol.replace('.NS', '').toLowerCase();

    // If we have a case-insensitive match but not an exact match
    if (symbolCaseMap[normalizedViewSymbol] &&
        symbolCaseMap[normalizedViewSymbol] !== viewSymbol.replace('.NS', '')) {
      console.log(`Fixing case for symbol: ${viewSymbol} -> ${symbolCaseMap[normalizedViewSymbol]}`);

      // Remove the old symbol
      await removeSymbolFromView(activeView.value.id, viewSymbol);

      // Add the corrected symbol
      await addSymbolToView(activeView.value.id, symbolCaseMap[normalizedViewSymbol]);

      hasChanges = true;
    }
  }

  if (hasChanges) {
    console.log('Symbol case fixed, refreshing views');
    await fetchViews();
  } else {
    console.log('No symbol case issues found');
  }
};

// Function to fetch investments data
const fetchInvestments = async () => {
  try {
    const data = await fetchInvestmentData();
    if (data) {
      investmentsLastUpdated.value = new Date().toLocaleString();
    }
    return data;
  } catch (err) {
    console.error('Failed to fetch investment data:', err);
    return null;
  }
};

// Combined timer functions

// Function to update the investments countdown timer
const updateInvestmentsCountdown = () => {
  if (!investmentsNextTime.value) {
    investmentsTimeRemaining.value = '--:--';
    return;
  }

  const now = new Date();
  const timeDiff = investmentsNextTime.value.getTime() - now.getTime();

  if (timeDiff <= 0) {
    investmentsTimeRemaining.value = '00:00';

    // When timer reaches zero, trigger the combined update process
    console.log('Investments timer reached zero, starting combined update process...');
    performCombinedUpdate();
    return;
  }

  // Calculate minutes and seconds
  const minutes = Math.floor(timeDiff / 60000);
  const seconds = Math.floor((timeDiff % 60000) / 1000);

  // Format as MM:SS
  investmentsTimeRemaining.value = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// Function to calculate combined percentage for portfolio metrics
const calculateCombinedPercentage = (value1, value2, total1, total2) => {
  const combinedValue = value1 + value2;
  const combinedTotal = total1 + total2;

  if (combinedTotal === 0) return 0;
  return (combinedValue / combinedTotal) * 100;
};

// Function to fetch both investments and mutual funds data
const fetchCombinedData = async () => {
  try {
    await Promise.all([
      fetchInvestments(),
      fetchMutualFunds()
    ]);
  } catch (err) {
    console.error('Failed to fetch combined data:', err);
  }
};

// Function to get monthly mutual fund data for chart
const getMonthlyMutualFundData = () => {
  // Return empty data if no mutual funds or if mutualFundData is not properly initialized
  if (!mutualFundData.value || !mutualFundData.value.mutualFunds || mutualFundData.value.mutualFunds.length === 0 || !rawMutualFunds.value || rawMutualFunds.value.length === 0) {
    return {
      labels: [],
      investmentData: [],
      currentValueData: []
    };
  }

  // Create a map to store monthly investments
  const monthlyData = new Map();

  // Sort mutual fund entries by date (oldest first)
  const sortedMutualFunds = [...rawMutualFunds.value].sort(
    (a, b) => new Date(a.purchaseDate).getTime() - new Date(b.purchaseDate).getTime()
  );

  // Process each mutual fund entry
  sortedMutualFunds.forEach(fund => {
    const date = new Date(fund.purchaseDate);
    const monthYear = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;

    // Initialize month if not exists
    if (!monthlyData.has(monthYear)) {
      monthlyData.set(monthYear, {
        investments: [],
        label: new Date(date.getFullYear(), date.getMonth(), 1)
          .toLocaleDateString('en-US', { month: 'short', year: 'numeric' }),
        timestamp: date.getTime()
      });
    }

    // Add mutual fund to month
    monthlyData.get(monthYear).investments.push(fund);
  });

  // Convert to array and sort by date
  const monthsArray = Array.from(monthlyData.values())
    .sort((a, b) => a.timestamp - b.timestamp);

  // Calculate cumulative values
  let cumulativeInvestment = 0;
  let cumulativeCurrentValue = 0;

  const chartData = {
    labels: [],
    investmentData: [],
    currentValueData: []
  };

  // Process all months to calculate cumulative values
  monthsArray.forEach(month => {
    // Calculate month's investment total
    const monthInvestment = month.investments.reduce((sum, fund) => sum + (fund.investment || 0), 0);
    cumulativeInvestment += monthInvestment;

    // For current value, we need to recalculate the total current value of all investments up to this point
    cumulativeCurrentValue = sortedMutualFunds
      .filter(fund => new Date(fund.purchaseDate).getTime() <= month.timestamp)
      .reduce((sum, fund) => sum + (fund.currentValue || fund.investment || 0), 0);

    // Add to chart data
    chartData.labels.push(month.label);
    chartData.investmentData.push(cumulativeInvestment);
    chartData.currentValueData.push(cumulativeCurrentValue);
  });

  // Limit to the latest 12 months
  if (chartData.labels.length > 12) {
    const startIndex = chartData.labels.length - 12;
    chartData.labels = chartData.labels.slice(startIndex);
    chartData.investmentData = chartData.investmentData.slice(startIndex);
    chartData.currentValueData = chartData.currentValueData.slice(startIndex);
  }

  return chartData;
};

// Combined monthly investment chart data
const combinedMonthlyInvestmentChartData = computed(() => {
  // Check if both data sources are available
  if (!investmentData.value || !mutualFundData.value) {
    return { labels: [], investmentData: [], currentValueData: [] };
  }

  try {
    console.log('Generating combined monthly investment chart data...');

    // Get the total investment and current value from the summary data
    // These are the values shown at the top of the page
    const totalInvestmentFromSummary = investmentData.value.summary.totalInvested + mutualFundData.value.summary.totalInvested;
    const totalCurrentValueFromSummary = investmentData.value.summary.currentValue + mutualFundData.value.summary.currentValue;

    console.log('Total investment from summary:', totalInvestmentFromSummary);
    console.log('Total current value from summary:', totalCurrentValueFromSummary);

    // STEP 1: Get raw data from both sources
    // We'll rebuild the chart data from scratch using the raw investment data

    // Get raw equity investments
    const equityInvestments = investmentData.value.investments || [];

    // Get raw mutual fund investments
    const mutualFundInvestments = mutualFundData.value.mutualFunds || [];

    // Create a mapping of month abbreviations to their numeric values for sorting
    const monthMap = {
      'Jan': 0, 'Feb': 1, 'Mar': 2, 'Apr': 3, 'May': 4, 'Jun': 5,
      'Jul': 6, 'Aug': 7, 'Sep': 8, 'Oct': 9, 'Nov': 10, 'Dec': 11
    };

    // STEP 2: Create a map to store monthly data
    const monthlyData = new Map();

    // Process equity investments
    equityInvestments.forEach(investment => {
      const date = new Date(investment.pdate);
      const year = date.getFullYear();
      const month = date.getMonth();
      const monthStr = Object.keys(monthMap).find(key => monthMap[key] === month);
      const label = `${monthStr} ${year}`;

      if (!monthlyData.has(label)) {
        monthlyData.set(label, {
          label,
          year,
          month,
          timestamp: date.getTime(),
          equityInvestment: 0,
          equityCurrentValue: 0,
          mutualFundInvestment: 0,
          mutualFundCurrentValue: 0
        });
      }

      const monthData = monthlyData.get(label);
      monthData.equityInvestment += Number(investment.namt) || 0;
      monthData.equityCurrentValue += Number(investment.cval) || 0;
    });

    // Process mutual fund investments
    mutualFundInvestments.forEach(fund => {
      const date = new Date(fund.purchaseDate);
      const year = date.getFullYear();
      const month = date.getMonth();
      const monthStr = Object.keys(monthMap).find(key => monthMap[key] === month);
      const label = `${monthStr} ${year}`;

      if (!monthlyData.has(label)) {
        monthlyData.set(label, {
          label,
          year,
          month,
          timestamp: date.getTime(),
          equityInvestment: 0,
          equityCurrentValue: 0,
          mutualFundInvestment: 0,
          mutualFundCurrentValue: 0
        });
      }

      const monthData = monthlyData.get(label);
      monthData.mutualFundInvestment += Number(fund.investmentAmount) || 0;
      monthData.mutualFundCurrentValue += Number(fund.currentValue) || 0;
    });

    // STEP 3: Sort months chronologically
    const sortedMonths = Array.from(monthlyData.values())
      .sort((a, b) => a.timestamp - b.timestamp);

    console.log('Sorted months:', sortedMonths.map(m => m.label));

    // STEP 4: Create a complete timeline with all months filled in
    const completeTimeline = [];

    if (sortedMonths.length > 0) {
      // Get the first and last month in our data
      const firstMonth = sortedMonths[0];
      const lastMonth = sortedMonths[sortedMonths.length - 1];

      // Create a date object for the first month
      let currentDate = new Date(firstMonth.year, firstMonth.month, 1);
      const endDate = new Date(lastMonth.year, lastMonth.month, 1);

      // Generate all months between the first and last month
      while (currentDate <= endDate) {
        const year = currentDate.getFullYear();
        const month = currentDate.getMonth();
        const monthStr = Object.keys(monthMap).find(key => monthMap[key] === month);

        if (monthStr) {
          const label = `${monthStr} ${year}`;

          // Check if we have data for this month
          if (monthlyData.has(label)) {
            completeTimeline.push(monthlyData.get(label));
          } else {
            // Create an empty entry for this month
            completeTimeline.push({
              label,
              year,
              month,
              timestamp: currentDate.getTime(),
              equityInvestment: 0,
              equityCurrentValue: 0,
              mutualFundInvestment: 0,
              mutualFundCurrentValue: 0
            });
          }
        }

        // Move to the next month
        currentDate.setMonth(currentDate.getMonth() + 1);
      }
    }

    // STEP 5: Calculate cumulative values
    const chartData = {
      labels: [],
      investmentData: [],
      currentValueData: []
    };

    let cumulativeEquityInvestment = 0;
    let cumulativeEquityCurrentValue = 0;
    let cumulativeMutualFundInvestment = 0;
    let cumulativeMutualFundCurrentValue = 0;

    completeTimeline.forEach(month => {
      // Add equity investment for this month
      cumulativeEquityInvestment += month.equityInvestment;

      // For current value, we need to recalculate based on the latest prices
      // For equity investments up to this month
      const equityCurrentValue = equityInvestments
        .filter(inv => new Date(inv.pdate).getTime() <= month.timestamp)
        .reduce((sum, inv) => sum + (Number(inv.cval) || 0), 0);

      cumulativeEquityCurrentValue = equityCurrentValue;

      // Add mutual fund investment for this month
      cumulativeMutualFundInvestment += month.mutualFundInvestment;

      // For current value, we need to recalculate based on the latest NAVs
      // For mutual fund investments up to this month
      const mutualFundCurrentValue = mutualFundInvestments
        .filter(fund => new Date(fund.purchaseDate).getTime() <= month.timestamp)
        .reduce((sum, fund) => sum + (Number(fund.currentValue) || 0), 0);

      cumulativeMutualFundCurrentValue = mutualFundCurrentValue;

      // Calculate combined values
      const totalInvestment = cumulativeEquityInvestment + cumulativeMutualFundInvestment;
      const totalCurrentValue = cumulativeEquityCurrentValue + cumulativeMutualFundCurrentValue;

      // Add to chart data
      chartData.labels.push(month.label);
      chartData.investmentData.push(totalInvestment);
      chartData.currentValueData.push(totalCurrentValue);

      // Log for debugging
      console.log(`Month ${month.label}:`, {
        equityInvestment: cumulativeEquityInvestment,
        mutualFundInvestment: cumulativeMutualFundInvestment,
        totalInvestment,
        equityCurrentValue: cumulativeEquityCurrentValue,
        mutualFundCurrentValue: cumulativeMutualFundCurrentValue,
        totalCurrentValue
      });
    });

    // STEP 6: Limit to the latest 12 months if needed
    let finalLabels = chartData.labels;
    let finalInvestmentData = chartData.investmentData;
    let finalCurrentValueData = chartData.currentValueData;

    if (chartData.labels.length > 12) {
      const startIndex = chartData.labels.length - 12;
      finalLabels = chartData.labels.slice(startIndex);
      finalInvestmentData = chartData.investmentData.slice(startIndex);
      finalCurrentValueData = chartData.currentValueData.slice(startIndex);
    }

    // STEP 7: Ensure the final values match the summary totals
    if (finalInvestmentData.length > 0 && finalCurrentValueData.length > 0) {
      // Get the last values from the chart data
      const lastInvestmentValue = finalInvestmentData[finalInvestmentData.length - 1];
      const lastCurrentValue = finalCurrentValueData[finalCurrentValueData.length - 1];

      // If there's a discrepancy, adjust the final values
      if (Math.abs(lastInvestmentValue - totalInvestmentFromSummary) > 0.01) {
        finalInvestmentData[finalInvestmentData.length - 1] = totalInvestmentFromSummary;
      }

      if (Math.abs(lastCurrentValue - totalCurrentValueFromSummary) > 0.01) {
        finalCurrentValueData[finalCurrentValueData.length - 1] = totalCurrentValueFromSummary;
      }
    }

    // Log the final chart data
    console.log('Final combined chart data:', {
      labels: finalLabels,
      investmentData: finalInvestmentData,
      currentValueData: finalCurrentValueData
    });

    // Return the combined chart data in the format expected by MonthlyInvestmentChart component
    return {
      labels: finalLabels,
      investmentData: finalInvestmentData,
      currentValueData: finalCurrentValueData
    };
  } catch (error) {
    console.error('Error generating combined chart data:', error);
    return { labels: [], investmentData: [], currentValueData: [] };
  }
});

// Equity vs Mutual Fund comparison chart data
const equityMutualFundComparisonData = computed(() => {
  if (!investmentData.value || !mutualFundData.value) {
    return {
      labels: ['Equity', 'Mutual Funds'],
      equityValue: 0,
      mutualFundValue: 0
    };
  }

  return {
    labels: ['Equity', 'Mutual Funds'],
    equityValue: investmentData.value.summary.currentValue,
    mutualFundValue: mutualFundData.value.summary.currentValue
  };
});

// Function to start the investments countdown timer
const startInvestmentsCountdown = (intervalMinutes: number = 5) => {
  // Clear any existing interval
  if (investmentsCountdownInterval) {
    clearInterval(investmentsCountdownInterval);
    investmentsCountdownInterval = null;
  }

  // Set the next update time
  investmentsNextTime.value = new Date(Date.now() + intervalMinutes * 60000);
  console.log('Investments timer set to:', investmentsNextTime.value.toLocaleTimeString());

  // Update immediately
  updateInvestmentsCountdown();

  // Update every second
  investmentsCountdownInterval = window.setInterval(updateInvestmentsCountdown, 1000);
  console.log('Investments countdown interval started:', investmentsCountdownInterval);
};

// Function to stop the investments countdown timer
const stopInvestmentsCountdown = () => {
  if (investmentsCountdownInterval) {
    console.log('Stopping investments countdown interval:', investmentsCountdownInterval);
    clearInterval(investmentsCountdownInterval);
    investmentsCountdownInterval = null;
  }
  investmentsNextTime.value = null;
  investmentsTimeRemaining.value = '--:--';
  console.log('Investments countdown timer stopped');
};

// Function to perform the combined update process
const performCombinedUpdate = async () => {
  // Pause the timer during operations
  if (investmentsCountdownInterval) {
    clearInterval(investmentsCountdownInterval);
    investmentsCountdownInterval = null;
  }

  try {
    // Step 1: Update prices
    operationStatus.value = 'updating-prices';
    console.log('Combined update: Updating prices...');
    await updateCurrentPrices(true);

    // Short delay between operations
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 2: Fetch investment data
    operationStatus.value = 'fetching-data';
    console.log('Combined update: Fetching investment data...');
    const data = await fetchInvestmentData(true);
    if (data) {
      investmentsLastUpdated.value = new Date().toLocaleString();
    }

    console.log('Combined update completed successfully');
  } catch (err) {
    console.error('Combined update failed:', err);
  } finally {
    // Reset operation status
    operationStatus.value = 'idle';

    // Restart the timer for the next update
    const interval = parseInt(investmentsAutoUpdateInterval.value);
    if (interval > 0) {
      startInvestmentsCountdown(interval);
    }
  }
};

// Function to handle investments auto-update change
const handleInvestmentsAutoUpdateChange = () => {
  // Stop any existing auto-update and countdown
  stopInvestmentsAutoUpdate();
  stopInvestmentsCountdown();

  // Get the interval value
  const interval = parseInt(investmentsAutoUpdateInterval.value);

  // If interval is greater than 0, start the combined timer
  if (interval > 0) {
    startInvestmentsCountdown(interval);
  }
};

// Function to refresh investments data
const refreshInvestments = async () => {
  try {
    // Stop the timer during manual refresh
    stopInvestmentsCountdown();

    // Perform the combined update
    await performCombinedUpdate();

    // Restart the timer after manual refresh
    const interval = parseInt(investmentsAutoUpdateInterval.value);
    if (interval > 0) {
      startInvestmentsCountdown(interval);
    }
  } catch (err) {
    console.error('Failed to refresh investments:', err);
  }
};

// Function to manually update current prices
const manualUpdatePrices = async () => {
  try {
    // Stop the timer during manual update
    stopInvestmentsCountdown();

    // Only update prices
    operationStatus.value = 'updating-prices';
    console.log('Manual price update triggered');
    await updateCurrentPrices(true);
    operationStatus.value = 'idle';

    // Restart the timer after manual update
    const interval = parseInt(investmentsAutoUpdateInterval.value);
    if (interval > 0) {
      startInvestmentsCountdown(interval);
    }
  } catch (err) {
    console.error('Failed to update prices:', err);
    operationStatus.value = 'idle';
  }
};

// Function to fetch contract notes data
const fetchContractNotes = async () => {
  try {
    const data = await fetchContractNotesData();
    if (data) {
      contractNotesLastUpdated.value = new Date().toLocaleString();
    }
    return data;
  } catch (err) {
    console.error('Failed to fetch contract notes data:', err);
    return null;
  }
};



// Function to refresh contract notes data
const refreshContractNotes = async () => {
  try {
    // Use silentUpdate=true to avoid showing loading indicator during manual refresh
    const data = await fetchContractNotesData(true);
    if (data) {
      contractNotesLastUpdated.value = new Date().toLocaleString();
    }
  } catch (err) {
    console.error('Failed to refresh contract notes data:', err);
  }
};

// Function to handle saving contract note
const handleSaveContractNote = async (updatedNote) => {
  try {
    const success = await saveContractNote(updatedNote);
    if (success) {
      contractNotesLastUpdated.value = new Date().toLocaleString();
    }
  } catch (err) {
    console.error('Failed to save contract note:', err);
  }
};

// Function to handle successful contract note addition
const handleAddContractNoteSuccess = async () => {
  try {
    // Refresh contract notes data
    await fetchContractNotes();
    // Update last updated timestamp
    contractNotesLastUpdated.value = new Date().toLocaleString();
  } catch (err) {
    console.error('Failed to refresh contract notes after adding:', err);
  }
};

// Watch for investment data updates to refresh the chart
watch(() => investmentData.value.timestamp, (newTimestamp) => {
  if (newTimestamp) {
    investmentsLastUpdated.value = new Date().toLocaleString();
    // The chart data will automatically update via the computed property
    console.log('Investment data updated, chart will refresh automatically');
  }
});

// Watch for price updates to update the UI
watch(lastPriceUpdate, (newValue) => {
  if (newValue) {
    console.log('Prices updated, UI will refresh');
  }
});

// Watch for tab changes to load data when needed
watch(rootTab, async (newTab) => {
  if (newTab === 'my-investments') {
    // If we don't have data yet, fetch it
    if (investmentData.value.investments.length === 0) {
      await fetchInvestments();
    } else {
      // Start the investments countdown timer when switching to MY INVESTMENTS tab
      const interval = parseInt(investmentsAutoUpdateInterval.value);
      if (interval > 0) {
        stopInvestmentsCountdown();
        startInvestmentsCountdown(interval);
      }
    }
  } else if (newTab === 'contract-notes' && contractNotesData.value.contractNotes.length === 0) {
    // Only fetch if we don't have data yet
    await fetchContractNotes();
  }
});

// Fetch data on component mount
onMounted(async () => {
  try {
    console.log('Stock Market Page: Fetching data...');
    isInitialLoading.value = true; // Set initial loading state to true
    isLoading.value = true;

    // Fetch market data
    const data = await fetchMarketData();

    // Fetch user views
    await fetchViews();

    console.log('Stock Market Page: Market data fetched:', {
      hasData: !!data,
      hasNifty50: data && !!data.nifty50,
      nifty50Length: data && data.nifty50 ? data.nifty50.length : 0,
      firstStock: data && data.nifty50 && data.nifty50.length > 0 ? data.nifty50[0] : null
    });

    // Store the stock data locally
    if (data && data.nifty50 && data.nifty50.length > 0) {
      stocksData.value = [...data.nifty50];
      console.log('Stock Market Page: Stored', stocksData.value.length, 'stocks locally');

      // Log the first stock to see its structure
      if (stocksData.value.length > 0) {
        const firstStock = stocksData.value[0];
        console.log('First stock data:', {
          symbol: firstStock.symbol,
          price: firstStock.lastPrice,
          pChange: firstStock.pChange,
          pChangeType: typeof firstStock.pChange,
          volume: firstStock.totalTradedVolume
        });
      }
    }

    lastUpdated.value = new Date().toLocaleString();

    // Mock data detection removed

    // Fetch data for any symbols in the views that aren't in the predefined list
    if (views.value && views.value.length > 0) {
      // Get all unique symbols from all views
      const viewSymbols = new Set<string>();
      views.value.forEach(view => {
        if (view.symbols && view.symbols.length > 0) {
          view.symbols.forEach(symbol => {
            viewSymbols.add(symbol.toUpperCase());
          });
        }
      });

      // Check which symbols aren't in the stock data
      const missingSymbols: string[] = [];
      viewSymbols.forEach(symbol => {
        const normalizedSymbol = symbol.replace('.NS', '');
        const exists = stocksData.value.some(stock =>
          stock.symbol.toUpperCase() === normalizedSymbol
        );

        if (!exists) {
          missingSymbols.push(normalizedSymbol);
        }
      });

      // Fetch data for missing symbols
      if (missingSymbols.length > 0) {
        // Fetch data for each missing symbol
        for (const symbol of missingSymbols) {
          await fetchSymbolData(symbol);
        }
      }
    }

    // Turn off both loading states after initial load
    isLoading.value = false;
    isInitialLoading.value = false;

    // Start auto-update with the default interval (1 minute)
    const interval = parseInt(autoUpdateInterval.value);
    if (interval > 0) {
      console.log(`Starting auto-update with interval: ${interval} minute(s)`);
      startAutoUpdate(interval);
    }



    // If we're on the investments tab, fetch investment data
    if (rootTab.value === 'my-investments') {
      await fetchInvestments();
    }

    // Start investments auto-update with the default interval (5 minutes)
    const investmentsInterval = parseInt(investmentsAutoUpdateInterval.value);
    if (investmentsInterval > 0) {
      console.log(`Starting investments auto-update with interval: ${investmentsInterval} minute(s)`);
      startInvestmentsCountdown(investmentsInterval);
    }

    // If we're on the contract notes tab, fetch contract notes data
    if (rootTab.value === 'contract-notes') {
      await fetchContractNotes();
    }

    // Initialize smart NAV update system
    if (mutualFundsAutoUpdateEnabled.value) {
      console.log('🤖 Initializing smart NAV update system...');
      scheduleSmartNAVUpdate();
    }
  } catch (err) {
    console.error('Failed to fetch market data:', err);
    isLoading.value = false;
    isInitialLoading.value = false; // Also turn off initial loading on error
  }
});



// Function to refresh data
const refreshData = async () => {
  try {
    // Use silentUpdate=true to avoid showing loading indicator during manual refresh

    // Refresh market data
    const data = await fetchMarketData(true);

    // Refresh views data
    await fetchViews();

    // Update local stock data
    if (data && data.nifty50 && data.nifty50.length > 0) {
      stocksData.value = [...data.nifty50];
    }

    lastUpdated.value = new Date().toLocaleString();
  } catch (err) {
    console.error('Failed to refresh market data:', err);
  }
};

// Function to fetch data for a specific symbol
const fetchSymbolData = async (symbol: string) => {
  try {
    // Fetch the stock details from the API using the API with auth
    const api = useApiWithAuth();
    const response = await api.get(`/api/stock-market?symbol=${symbol}`);

    if (response.error) {
      console.error(`Error fetching data for ${symbol}:`, response.error);
      return false;
    }

    if (response.quote && response.quote.info && response.tradeInfo) {
      // Create a stock object in the same format as the nifty50 stocks
      const stockData: StockData = {
        symbol: response.quote.info.symbol.replace('.NS', ''),
        meta: {
          companyName: response.quote.info.companyName
        },
        lastPrice: response.quote.info.lastPrice,
        change: response.quote.info.change,
        pChange: response.quote.info.pChange,
        totalTradedVolume: response.quote.info.totalTradedVolume,
        open: response.tradeInfo.open,
        dayHigh: response.tradeInfo.dayHigh,
        dayLow: response.tradeInfo.dayLow,
        previousClose: response.tradeInfo.previousClose
      };

      // Check if the stock is already in the stocksData array
      const existingIndex = stocksData.value.findIndex(s =>
        s.symbol.toUpperCase() === stockData.symbol.toUpperCase()
      );

      if (existingIndex >= 0) {
        // Update the existing stock data
        stocksData.value[existingIndex] = stockData;
      } else {
        // Add the new stock data
        stocksData.value.push(stockData);
      }

      return true;
    }

    return false;
  } catch (err) {
    console.error(`Failed to fetch data for symbol ${symbol}:`, err);
    return false;
  }
};

// Function to handle search
const handleSearch = async () => {
  if (searchQuery.value.length < 2) return;

  try {
    const results = await searchStocks(searchQuery.value);

    // If no results found, show a message
    if (results && results.length === 0) {
      console.log('No results found for:', searchQuery.value);
    }
  } catch (err) {
    console.error('Failed to search stocks:', err);
    // Continue execution - the error is already handled in the composable
  }
};

// Function to clear search
const clearSearch = () => {
  searchQuery.value = '';
  searchResults.value = [];
};

// Function to handle auto-update change
const handleAutoUpdateChange = () => {
  // Stop any existing auto-update
  stopAutoUpdate();

  // Get the interval value
  const interval = parseInt(autoUpdateInterval.value);

  // If interval is greater than 0, start auto-update
  if (interval > 0) {
    startAutoUpdate(interval);
  }
};

// Mutual Funds State
const isMutualFundsLoading = ref(false);
const isMutualFundsRefreshing = ref(false);
const isUpdatingNAVs = ref(false);
const mutualFundsError = ref(null);
const mutualFundData = ref({
  mutualFunds: [],
  summary: {
    totalInvested: 0,
    currentValue: 0,
    totalProfitLoss: 0,
    profitLossPercentage: 0,
    fundCount: 0,
    sipCount: 0,
    todayTotalPL: 0,
    todayPLPercentage: 0
  },
  categoryAllocation: [],
  fundHouseAllocation: [],
  timestamp: null
});
const mutualFundsLastUpdated = ref('-');
const mutualFundsAutoUpdateEnabled = ref(true); // Smart daily auto-update
const mutualFundsNextUpdate = ref('');
const mutualFundsUpdateStatus = ref('waiting'); // waiting, updating, completed, failed
const mutualFundsSmartUpdateTimer = ref(null);


// NAV Update Progress Tracking
const navUpdateProgress = ref({
  isUpdating: false,
  currentScheme: '',
  currentSchemeName: '',
  completed: 0,
  total: 0,
  successful: 0,
  failed: 0,
  errors: [],
  canCancel: true,
  cancelled: false,
  startTime: null,
  estimatedTimeRemaining: null,
  logs: []
});

// Portfolio Performance History State
const portfolioPerformanceLoading = ref(false)
const portfolioPerformanceError = ref('')
const portfolioPerformanceData = ref(null)
const portfolioPerformanceProgress = ref(0)
const portfolioPerformanceStatusMessage = ref('')

// Mutual Fund Modal state
const showAddMutualFundModal = ref(false);
const showImportMutualFundModal = ref(false);
const showRedeemMutualFundModal = ref(false);
const selectedMutualFund = ref(null);
const showDeleteMutualFundConfirmation = ref(false);
const mutualFundToDelete = ref(null);

// Expanded table state for mutual funds
const rawMutualFunds = ref([]);
const expandedMutualFunds = ref([]);
const showAllMutualFundDetails = ref(false);

// Expanded fund house state for fund house distribution
const expandedFundHouses = ref([]);

// Search functionality for mutual funds
const mutualFundSearchQuery = ref('');

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  return date.toLocaleDateString('en-IN');
};

// Format date in dd-mm-yyyy format for Individual Entries
const formatDateDDMMYYYY = (dateString) => {
  if (!dateString) return '-';
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, '0');
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

// Filter raw mutual funds by scheme
const filteredRawFunds = (fund) => {
  return rawMutualFunds.value.filter(f =>
    f.schemeCode === fund.schemeCode &&
    f.schemeName === fund.schemeName
  );
};

// Computed property for filtered mutual funds based on search
const filteredMutualFunds = computed(() => {
  if (!mutualFundSearchQuery.value.trim()) {
    return mutualFundData.value.mutualFunds;
  }

  const query = mutualFundSearchQuery.value.toLowerCase().trim();
  return mutualFundData.value.mutualFunds.filter(fund =>
    fund.schemeName.toLowerCase().includes(query) ||
    fund.fundHouse.toLowerCase().includes(query) ||
    fund.category.toLowerCase().includes(query) ||
    fund.schemeCode.toLowerCase().includes(query)
  );
});

// Toggle showing individual mutual fund entries
const toggleMutualFundDetails = (fundId) => {
  const index = expandedMutualFunds.value.indexOf(fundId);
  if (index === -1) {
    expandedMutualFunds.value.push(fundId);
  } else {
    expandedMutualFunds.value.splice(index, 1);
  }
};

// Edit individual mutual fund entry
const editRawMutualFund = (fund) => {
  selectedMutualFund.value = fund;
  showAddMutualFundModal.value = true;
};

// Confirm deletion of individual mutual fund entry
const confirmDeleteRawMutualFund = (fund) => {
  mutualFundToDelete.value = fund;
  showDeleteMutualFundConfirmation.value = true;
};

// Fetch mutual funds data with raw entries
const fetchMutualFunds = async () => {
  try {
    isMutualFundsLoading.value = true;
    mutualFundsError.value = null;

    const api = useApiWithAuth();

    // First fetch raw mutual fund entries (not consolidated)
    const rawData = await api.get('/api/stock-market/mutual-funds/raw');
    if (rawData) {
      rawMutualFunds.value = rawData;
      console.log('Raw mutual funds data fetched:', rawMutualFunds.value.length, 'entries');
    }

    // Then fetch consolidated data
    const data = await api.get('/api/stock-market/mutual-funds');
    if (data) {
      mutualFundData.value = data;
      mutualFundsLastUpdated.value = new Date().toLocaleString();
      console.log('Mutual funds data fetched successfully:', data);
    }

    // Render charts after data is loaded and DOM has updated
    nextTick(() => {
      console.log('Data loaded, rendering charts');

      // Add a small delay to ensure DOM is fully updated
      setTimeout(() => {
        renderCategoryChart();
        renderFundHouseChart();
      }, 100);
    });
  } catch (err) {
    console.error('Error fetching mutual funds:', err);
    mutualFundsError.value = err.message || 'Failed to fetch mutual funds data';
  } finally {
    isMutualFundsLoading.value = false;
  }
};

// Refresh mutual funds data
const refreshMutualFunds = async () => {
  try {
    isMutualFundsRefreshing.value = true;
    await fetchMutualFunds();
  } finally {
    isMutualFundsRefreshing.value = false;
  }
};

// Smart NAV Update Utility Functions

// Check if current day is a trading day (Monday-Friday, excluding holidays)
const isTradingDay = (date = new Date()) => {
  const day = date.getDay();
  // 0 = Sunday, 6 = Saturday
  return day >= 1 && day <= 5;
};

// Get next NAV update time (9 PM on trading days)
const getNextNAVUpdateTime = () => {
  const now = new Date();
  const today = new Date(now);
  today.setHours(21, 0, 0, 0); // 9 PM today

  // If it's past 9 PM today or not a trading day, get next trading day at 9 PM
  if (now >= today || !isTradingDay(now)) {
    let nextDay = new Date(now);
    nextDay.setDate(nextDay.getDate() + 1);

    // Find next trading day
    while (!isTradingDay(nextDay)) {
      nextDay.setDate(nextDay.getDate() + 1);
    }

    nextDay.setHours(21, 0, 0, 0); // 9 PM
    return nextDay;
  }

  return today;
};

// Check if NAVs need updating (haven't been updated today)
const needsNAVUpdate = () => {
  if (!mutualFundsLastUpdated.value || mutualFundsLastUpdated.value === '-') {
    return true;
  }

  try {
    const lastUpdate = new Date(mutualFundsLastUpdated.value);
    const today = new Date();

    // Check if last update was today
    const isToday = lastUpdate.toDateString() === today.toDateString();

    // If not updated today and it's a trading day after 9 PM, needs update
    if (!isToday && isTradingDay() && today.getHours() >= 21) {
      return true;
    }

    return false;
  } catch (error) {
    console.error('Error checking NAV update status:', error);
    return true; // Default to needing update if error
  }
};

// Format next update time for display
const formatNextUpdateTime = (date) => {
  if (!date) return 'Not scheduled';

  const now = new Date();
  const diffMs = date.getTime() - now.getTime();
  const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));

  if (diffHours < 1) {
    return 'Soon';
  } else if (diffHours < 24) {
    return `In ${diffHours} hour${diffHours > 1 ? 's' : ''}`;
  } else {
    const diffDays = Math.ceil(diffHours / 24);
    return `In ${diffDays} day${diffDays > 1 ? 's' : ''}`;
  }
};

// Smart NAV update scheduler
const scheduleSmartNAVUpdate = () => {
  stopSmartNAVUpdate();

  if (!mutualFundsAutoUpdateEnabled.value) {
    mutualFundsNextUpdate.value = 'Disabled';
    return;
  }

  const nextUpdateTime = getNextNAVUpdateTime();
  mutualFundsNextUpdate.value = formatNextUpdateTime(nextUpdateTime);

  const now = new Date();
  const timeUntilUpdate = nextUpdateTime.getTime() - now.getTime();

  console.log(`📅 Next NAV update scheduled for: ${nextUpdateTime.toLocaleString()}`);
  console.log(`⏰ Time until update: ${Math.round(timeUntilUpdate / (1000 * 60))} minutes`);

  // Set timeout for the next update
  mutualFundsSmartUpdateTimer.value = setTimeout(async () => {
    if (isTradingDay() && mutualFundsAutoUpdateEnabled.value) {
      console.log('🚀 Starting scheduled NAV update...');
      mutualFundsUpdateStatus.value = 'updating';

      try {
        await updateAllMutualFundNAVs();
        mutualFundsUpdateStatus.value = 'completed';
        console.log('✅ Scheduled NAV update completed successfully');
      } catch (error) {
        console.error('❌ Scheduled NAV update failed:', error);
        mutualFundsUpdateStatus.value = 'failed';

        // Retry after 1 hour on failure
        setTimeout(() => {
          if (mutualFundsAutoUpdateEnabled.value) {
            console.log('🔄 Retrying failed NAV update...');
            updateAllMutualFundNAVs();
          }
        }, 60 * 60 * 1000); // 1 hour
      }
    }

    // Schedule next update
    scheduleSmartNAVUpdate();
  }, Math.max(timeUntilUpdate, 60000)); // Minimum 1 minute delay
};

// Stop smart NAV update scheduler
const stopSmartNAVUpdate = () => {
  if (mutualFundsSmartUpdateTimer.value) {
    clearTimeout(mutualFundsSmartUpdateTimer.value);
    mutualFundsSmartUpdateTimer.value = null;
  }
};

// Handle smart auto-update toggle
const handleSmartAutoUpdateChange = () => {
  if (mutualFundsAutoUpdateEnabled.value) {
    scheduleSmartNAVUpdate();
    console.log('✅ Smart NAV auto-update enabled');
  } else {
    stopSmartNAVUpdate();
    mutualFundsNextUpdate.value = 'Disabled';
    mutualFundsUpdateStatus.value = 'waiting';
    console.log('❌ Smart NAV auto-update disabled');
  }
};



// Helper function to fetch NAV for a single scheme with timeout
const fetchSchemeNAV = async (schemeCode, schemeName, timeout = 30000) => {
  return new Promise(async (resolve, reject) => {
    const timeoutId = setTimeout(() => {
      reject(new Error(`Timeout after ${timeout}ms`));
    }, timeout);

    try {
      console.log(`🔍 Fetching NAV for scheme: ${schemeCode} (${schemeName})`);

      const response = await fetch(`https://api.mfapi.in/mf/${schemeCode}`);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const fundData = await response.json();

      if (fundData && fundData.data && fundData.data.length > 0) {
        const latestNAV = parseFloat(fundData.data[0].nav);

        if (!isNaN(latestNAV)) {
          clearTimeout(timeoutId);
          console.log(`✅ Found NAV for ${schemeCode}: ₹${latestNAV}`);
          resolve({
            schemeCode,
            currentNAV: latestNAV,
            success: true
          });
        } else {
          throw new Error('Invalid NAV value received');
        }
      } else {
        throw new Error('No NAV data found in response');
      }
    } catch (error) {
      clearTimeout(timeoutId);
      console.error(`❌ Error fetching NAV for ${schemeCode}:`, error.message);
      reject(error);
    }
  });
};

// Helper function to process schemes in parallel with concurrency limit
const fetchNAVsInParallel = async (schemes, concurrencyLimit = 3) => {
  const results = [];
  const errors = [];

  // Process schemes in batches
  for (let i = 0; i < schemes.length; i += concurrencyLimit) {
    const batch = schemes.slice(i, i + concurrencyLimit);

    const batchPromises = batch.map(async (scheme) => {
      // Update progress
      navUpdateProgress.value.currentScheme = scheme.schemeCode;
      navUpdateProgress.value.currentSchemeName = scheme.schemeName;

      try {
        const result = await fetchSchemeNAV(scheme.schemeCode, scheme.schemeName);
        navUpdateProgress.value.successful++;
        navUpdateProgress.value.logs.push(`✅ ${scheme.schemeName}: ₹${result.currentNAV}`);
        return result;
      } catch (error) {
        navUpdateProgress.value.failed++;
        const errorMsg = `❌ ${scheme.schemeName}: ${error.message}`;
        navUpdateProgress.value.errors.push({
          schemeCode: scheme.schemeCode,
          schemeName: scheme.schemeName,
          error: error.message
        });
        navUpdateProgress.value.logs.push(errorMsg);
        return null;
      } finally {
        navUpdateProgress.value.completed++;
      }
    });

    // Wait for current batch to complete
    const batchResults = await Promise.allSettled(batchPromises);

    // Process results
    batchResults.forEach((result, index) => {
      if (result.status === 'fulfilled' && result.value) {
        results.push(result.value);
      }
    });

    // Check if cancelled
    if (navUpdateProgress.value.cancelled) {
      console.log('🛑 NAV update cancelled by user');
      break;
    }

    // Small delay between batches to be respectful to the API
    if (i + concurrencyLimit < schemes.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return { results, errors };
};

// Cancel NAV update function
const cancelNAVUpdate = () => {
  navUpdateProgress.value.cancelled = true;
  navUpdateProgress.value.canCancel = false;
  console.log('🛑 User cancelled NAV update');
};

// Reset NAV update progress
const resetNAVUpdateProgress = () => {
  navUpdateProgress.value = {
    isUpdating: false,
    currentScheme: '',
    currentSchemeName: '',
    completed: 0,
    total: 0,
    successful: 0,
    failed: 0,
    errors: [],
    canCancel: true,
    cancelled: false,
    startTime: null,
    estimatedTimeRemaining: null,
    logs: []
  };
};

// Update all mutual fund NAVs using the AMFI API with enhanced progress tracking
const updateAllMutualFundNAVs = async () => {
  try {
    console.log('🚀 Starting NAV update process...');

    if (mutualFundData.value.mutualFunds.length === 0) {
      alert('No mutual funds to update');
      return;
    }

    // Reset and initialize progress
    resetNAVUpdateProgress();
    isUpdatingNAVs.value = true;
    navUpdateProgress.value.isUpdating = true;
    navUpdateProgress.value.startTime = new Date();

    // Get unique schemes with their names
    const uniqueSchemes = [];
    const seenSchemes = new Set();

    mutualFundData.value.mutualFunds.forEach(fund => {
      const key = fund.schemeCode;
      if (!seenSchemes.has(key)) {
        seenSchemes.add(key);
        uniqueSchemes.push({
          schemeCode: fund.schemeCode,
          schemeName: fund.schemeName
        });
      }
    });

    navUpdateProgress.value.total = uniqueSchemes.length;
    navUpdateProgress.value.logs.push(`📊 Starting update for ${uniqueSchemes.length} unique schemes`);

    console.log(`📊 Found ${uniqueSchemes.length} unique schemes to update`);

    // Fetch NAVs in parallel
    const { results } = await fetchNAVsInParallel(uniqueSchemes);

    // Check if cancelled
    if (navUpdateProgress.value.cancelled) {
      navUpdateProgress.value.logs.push('🛑 Update cancelled by user');
      alert('NAV update was cancelled');
      return;
    }

    if (results.length === 0) {
      throw new Error('Could not fetch NAVs for any of your mutual funds');
    }

    navUpdateProgress.value.logs.push(`💾 Updating database for ${results.length} schemes...`);
    console.log(`💾 Updating database for ${results.length} schemes...`);

    // Update NAVs in batch using the API with auth
    const api = useApiWithAuth();
    const result = await api.post('/api/stock-market/mutual-funds/update-nav-batch', { updates: results });

    console.log('✅ NAV update result:', result);
    navUpdateProgress.value.logs.push(`✅ Database updated successfully`);

    // Refresh data after update
    navUpdateProgress.value.logs.push(`🔄 Refreshing mutual funds data...`);
    await fetchMutualFunds();

    const successMsg = `Updated NAVs for ${navUpdateProgress.value.successful} scheme(s)${navUpdateProgress.value.failed > 0 ? `, ${navUpdateProgress.value.failed} failed` : ''}`;
    navUpdateProgress.value.logs.push(`🎉 ${successMsg}`);

    // Show success message after a short delay to let user see the final progress
    setTimeout(() => {
      alert(successMsg);
    }, 1000);

  } catch (err) {
    console.error('❌ Error updating NAVs:', err);
    navUpdateProgress.value.logs.push(`❌ Error: ${err.message}`);
    alert(`Error updating NAVs: ${err.message}`);
  } finally {
    isUpdatingNAVs.value = false;
    // Keep progress modal open for a few seconds to show final results
    setTimeout(() => {
      navUpdateProgress.value.isUpdating = false;
    }, 3000);
  }
};

// Modal functions for mutual funds
const editMutualFund = (mutualFund) => {
  selectedMutualFund.value = mutualFund;
  showAddMutualFundModal.value = true;
};

const confirmDeleteMutualFund = (mutualFund) => {
  mutualFundToDelete.value = mutualFund;
  showDeleteMutualFundConfirmation.value = true;
};

const deleteMutualFundConfirmed = async () => {
  if (!mutualFundToDelete.value) return;

  try {
    isMutualFundsLoading.value = true;

    // Determine if this is a consolidated fund or an individual entry
    const isIndividualEntry = 'purchaseDate' in mutualFundToDelete.value;
    const endpoint = isIndividualEntry
      ? '/api/stock-market/mutual-funds/delete-entry'
      : '/api/stock-market/mutual-funds/delete';

    const payload = isIndividualEntry
      ? { entryId: mutualFundToDelete.value._id }
      : { mutualFundId: mutualFundToDelete.value._id };

    const api = useApiWithAuth();
    await api.post(endpoint, payload);

    // Refresh data after successful deletion
    await fetchMutualFunds();

    // Close confirmation modal
    showDeleteMutualFundConfirmation.value = false;
    mutualFundToDelete.value = null;

  } catch (err) {
    console.error('Error deleting mutual fund:', err);
    mutualFundsError.value = err.message || 'Failed to delete mutual fund';
  } finally {
    isMutualFundsLoading.value = false;
  }
};

const handleSaveMutualFund = async (mutualFundData) => {
  try {
    isMutualFundsLoading.value = true;

    const api = useApiWithAuth();
    const isEditing = !!mutualFundData._id;

    // Handle adding to existing fund
    if (mutualFundData.isAddingToExisting) {
      const endpoint = '/api/stock-market/mutual-funds/add';

      // Add extra info to indicate this is an addition to existing SIP
      const payload = {
        ...mutualFundData,
        isAddingToExisting: true,
        existingFundId: mutualFundData.existingFundId
      };

      await api.post(endpoint, payload);
    } else {
      // Regular edit or add
      const endpoint = isEditing ? '/api/stock-market/mutual-funds/update' : '/api/stock-market/mutual-funds/add';
      await api.post(endpoint, mutualFundData);
    }

    // Refresh data after successful save
    await fetchMutualFunds();

    // Close modal
    showAddMutualFundModal.value = false;
    selectedMutualFund.value = null;

  } catch (err) {
    console.error(`Error ${mutualFundData._id ? 'updating' : 'adding'} mutual fund:`, err);
    mutualFundsError.value = err.message || `Failed to ${mutualFundData._id ? 'update' : 'add'} mutual fund`;
  } finally {
    isMutualFundsLoading.value = false;
  }
};

const handleRedeemMutualFund = async (redemptionData) => {
  try {
    isMutualFundsLoading.value = true;

    const api = useApiWithAuth();

    // Use the same add endpoint but with negative values for redemption
    const endpoint = '/api/stock-market/mutual-funds/add';
    await api.post(endpoint, redemptionData);

    // Refresh data after successful redemption
    await fetchMutualFunds();

    // Close modal
    showRedeemMutualFundModal.value = false;

    // Show success message
    const method = redemptionData.redemptionMethod === 'amount' ? 'amount' : 'units';
    const value = redemptionData.redemptionMethod === 'amount'
      ? `₹${formatNumber(Math.abs(redemptionData.investmentAmount))}`
      : `${formatNumber(Math.abs(redemptionData.units))} units`;
    alert(`Successfully redeemed ${value} of ${redemptionData.schemeName} (by ${method})`);

  } catch (err) {
    console.error('Error redeeming mutual fund:', err);
    mutualFundsError.value = err.message || 'Failed to redeem mutual fund';
  } finally {
    isMutualFundsLoading.value = false;
  }
};

// Toggle fund house details expansion
const toggleFundHouseDetails = (fundHouse) => {
  const index = expandedFundHouses.value.indexOf(fundHouse);
  if (index > -1) {
    expandedFundHouses.value.splice(index, 1);
  } else {
    expandedFundHouses.value.push(fundHouse);
  }
};

// Watch for tab changes to load data when tab is selected
watch(() => rootTab.value, (newTab) => {
  if (newTab === 'mutual-funds') {
    // Always fetch data when switching to mutual funds tab to ensure charts are up to date
    fetchMutualFunds();

    // Ensure charts are rendered after the DOM has updated
    nextTick(() => {
      console.log('Initializing mutual fund charts after tab change');
      renderCategoryChart();
      renderFundHouseChart();
    });
  }

  // Load both investments and mutual funds data when switching to portfolio tab
  if (newTab === 'portfolio') {
    fetchCombinedData();
  }
});

// Watch for mutual fund data updates to refresh the charts
watch(() => mutualFundData.value, (newData, oldData) => {
  console.log('Mutual fund data updated, refreshing charts');

  // Check if category or fund house allocation data has changed
  const categoryChanged = JSON.stringify(newData.categoryAllocation) !== JSON.stringify(oldData?.categoryAllocation || []);
  const fundHouseChanged = JSON.stringify(newData.fundHouseAllocation) !== JSON.stringify(oldData?.fundHouseAllocation || []);

  console.log('Data changes detected:', { categoryChanged, fundHouseChanged });

  // Wait for DOM to update before rendering charts
  nextTick(() => {
    if (categoryChanged) {
      console.log('Rendering category chart due to data change');
      renderCategoryChart();
    }

    if (fundHouseChanged) {
      console.log('Rendering fund house chart due to data change');
      renderFundHouseChart();
    }
  });
}, { deep: true });

// Contract Notes countdown timer state
const contractNotesCountdownTimer = ref(null);

// Function to stop the contract notes countdown timer
const stopContractNotesCountdown = () => {
  if (contractNotesCountdownTimer.value) {
    clearInterval(contractNotesCountdownTimer.value);
    contractNotesCountdownTimer.value = null;
  }
};

// Handle visibility change (when user switches tabs or minimizes browser)
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // Resume auto-updates when page becomes visible again
    const marketInterval = parseInt(autoUpdateInterval.value);
    if (marketInterval > 0) {
      startAutoUpdate(marketInterval);
    }

    const investmentsInterval = parseInt(investmentsAutoUpdateInterval.value);
    if (investmentsInterval > 0) {
      startInvestmentsCountdown(investmentsInterval);
    }

    if (mutualFundsAutoUpdateEnabled.value) {
      scheduleSmartNAVUpdate();
    }
  } else {
    // Pause auto-updates when page is hidden
    stopAutoUpdate();
    stopInvestmentsAutoUpdate();
    stopInvestmentsCountdown();
    stopSmartNAVUpdate();
  }
};

// Add visibility change listener on mount
onMounted(() => {
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

// Clean up intervals when component is unmounted
onUnmounted(() => {
  console.log('Stock Market Page: Component unmounting - cleaning up all intervals and timers');

  stopAutoUpdate();
  stopInvestmentsAutoUpdate();
  stopContractNotesCountdown();
  stopSmartNAVUpdate();
  document.removeEventListener('visibilitychange', handleVisibilityChange);

  console.log('Stock Market Page: All cleanup completed');
});

const sortedCategoryAllocation = computed(() => {
  // Sort by value descending
  return [...(mutualFundData.value.categoryAllocation || [])].sort((a, b) => b.value - a.value);
});

const sortedFundHouseAllocation = computed(() => {
  // Sort by value descending
  return [...(mutualFundData.value.fundHouseAllocation || [])].sort((a, b) => b.value - a.value);
});

// Chart references
const categoryChartCanvas = ref(null);
const fundHouseChartCanvas = ref(null);
let categoryChart = null;
let fundHouseChart = null;

// Calculate monthly SIP commitment
const calculateMonthlySIP = () => {
  if (!rawMutualFunds.value || !rawMutualFunds.value.length) return 0;

  // Sum investment amounts for entries with sipFlag = true
  return rawMutualFunds.value
    .filter(fund => fund.sipFlag)
    .reduce((total, fund) => total + (fund.investmentAmount || 0), 0);
};

// Get standard chart colors
const getChartColors = () => {
  return [
    '#3B82F6', // blue-500
    '#10B981', // emerald-500
    '#F59E0B', // amber-500
    '#EF4444', // red-500
    '#8B5CF6', // violet-500
    '#EC4899', // pink-500
    '#6366F1', // indigo-500
    '#14B8A6', // teal-500
    '#F97316', // orange-500
    '#84CC16', // lime-500
    '#9333EA', // purple-600
    '#06B6D4', // cyan-500
  ];
};

// Format numbers for display
const formatNumber = (value) => {
  if (value === undefined || value === null) return '-';
  return new Intl.NumberFormat('en-IN').format(parseFloat(value).toFixed(2));
};

// Format percentages for display
const formatPercentage = (value) => {
  if (value === undefined || value === null) return '-';
  return parseFloat(value).toFixed(2);
};

// Render the category allocation pie chart
const renderCategoryChart = () => {
  if (!mutualFundData.value.categoryAllocation || !mutualFundData.value.categoryAllocation.length) {
    console.log('No category allocation data available');
    return;
  }

  const canvas = categoryChartCanvas.value;
  if (!canvas) {
    console.log('Category chart canvas not found');
    return;
  }

  // Destroy existing chart if it exists
  if (categoryChart) {
    categoryChart.destroy();
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.log('Failed to get 2D context for category chart');
    return;
  }

  const sortedData = sortedCategoryAllocation.value;
  const labels = sortedData.map(item => item.category);
  const data = sortedData.map(item => item.value);
  const colors = getChartColors().slice(0, sortedData.length);

  console.log('Rendering category chart with data:', { labels, data });

  try {
    categoryChart = new Chart(ctx, {
      type: 'doughnut', // Changed to doughnut for better visualization
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: colors,
          borderColor: colors.map(color => color), // Add border colors
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (tooltipItem) => {
                const label = tooltipItem.label || '';
                const value = tooltipItem.raw;
                const percentage = (value / sortedData.reduce((total, item) => total + item.value, 0) * 100).toFixed(2);
                return `${label}: ₹${formatPrice(value)} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
    console.log('Category chart rendered successfully');
  } catch (error) {
    console.error('Error rendering category chart:', error);
  }
};

// Render the fund house allocation pie chart
const renderFundHouseChart = () => {
  if (!mutualFundData.value.fundHouseAllocation || !mutualFundData.value.fundHouseAllocation.length) {
    console.log('No fund house allocation data available');
    return;
  }

  const canvas = fundHouseChartCanvas.value;
  if (!canvas) {
    console.log('Fund house chart canvas not found');
    return;
  }

  // Destroy existing chart if it exists
  if (fundHouseChart) {
    fundHouseChart.destroy();
  }

  const ctx = canvas.getContext('2d');
  if (!ctx) {
    console.log('Failed to get 2D context for fund house chart');
    return;
  }

  const sortedData = sortedFundHouseAllocation.value;
  const labels = sortedData.map(item => item.fundHouse);
  const data = sortedData.map(item => item.value);
  const colors = getChartColors().slice(0, sortedData.length);

  console.log('Rendering fund house chart with data:', { labels, data });

  try {
    fundHouseChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: labels,
        datasets: [{
          data: data,
          backgroundColor: colors,
          borderColor: colors.map(color => color), // Add border colors
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            display: false
          },
          tooltip: {
            callbacks: {
              label: (tooltipItem) => {
                const label = tooltipItem.label || '';
                const value = tooltipItem.raw;
                const percentage = (value / sortedData.reduce((total, item) => total + item.value, 0) * 100).toFixed(2);
                return `${label}: ₹${formatPrice(value)} (${percentage}%)`;
              }
            }
          }
        }
      }
    });
    console.log('Fund house chart rendered successfully');
  } catch (error) {
    console.error('Error rendering fund house chart:', error);
  }
};

// AI Stock Analysis Modal functions
const openAIAnalysis = async (stock: StockData) => {
  console.log('🤖 Opening AI analysis for:', stock.symbol);

  // Dynamically import the modal component to avoid server-side Chart.js issues
  if (!AIStockAnalysisModal.value) {
    try {
      const { default: ModalComponent } = await import('~/components/stock-market/AIStockAnalysisModal.vue');
      AIStockAnalysisModal.value = ModalComponent;
    } catch (error) {
      console.error('Failed to load AI Stock Analysis Modal:', error);
      return;
    }
  }

  selectedStockForAI.value = stock;
  showAIAnalysisModal.value = true;
};

const closeAIAnalysis = () => {
  console.log('🤖 Closing AI analysis modal');
  showAIAnalysisModal.value = false;
  selectedStockForAI.value = null;
};

// AI Mutual Fund Analysis Modal functions
const openMutualFundAIAnalysis = async (fund) => {
  console.log('🤖 Opening AI analysis for mutual fund:', fund.schemeName);

  // Dynamically import the modal component to avoid server-side Chart.js issues
  if (!AIMutualFundAnalysisModal.value) {
    try {
      const { default: ModalComponent } = await import('~/components/stock-market/AIMutualFundAnalysisModal.vue');
      AIMutualFundAnalysisModal.value = ModalComponent;
    } catch (error) {
      console.error('Failed to load AI Mutual Fund Analysis Modal:', error);
      return;
    }
  }

  selectedMutualFundForAI.value = fund;
  showMutualFundAIAnalysisModal.value = true;
};

const closeMutualFundAIAnalysis = () => {
  console.log('🤖 Closing mutual fund AI analysis modal');
  showMutualFundAIAnalysisModal.value = false;
  selectedMutualFundForAI.value = null;
};

// Mobile responsiveness
const isMobile = ref(false);
const showMarketOverview = ref(true);

// Export functionality
const showExportModal = ref(false);

const handleExportComplete = (result) => {
  showExportModal.value = false;
  if (result.success) {
    // Show success message
    alert('Data exported successfully');
  }
};

// Portfolio Performance History Method
const fetchPortfolioPerformanceHistory = async () => {
  try {
    portfolioPerformanceLoading.value = true
    portfolioPerformanceError.value = ''
    portfolioPerformanceData.value = null
    portfolioPerformanceProgress.value = 0
    portfolioPerformanceStatusMessage.value = 'Starting portfolio performance analysis...'

    console.log('📊 Starting portfolio performance history analysis')

    const api = useApiWithAuth()

    // Start portfolio performance analysis
    const queueResponse = await api.post('/api/stock-market/portfolio-performance-history', {})

    if (!queueResponse || !queueResponse.jobId) {
      console.error('❌ Invalid response from portfolio performance API:', queueResponse)
      throw new Error('Failed to queue portfolio performance analysis - no jobId received')
    }

    const jobId = queueResponse.jobId
    console.log('📋 Portfolio performance analysis job started with jobId:', jobId)

    // Poll for results
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse = await api.get(`/api/stock-market/ai-analysis-status/${jobId}`)

        console.log('📊 Portfolio performance job status:', statusResponse)

        if (statusResponse.status === 'completed') {
          clearInterval(pollInterval)
          portfolioPerformanceData.value = statusResponse.analysis
          portfolioPerformanceLoading.value = false
          console.log('✅ Portfolio performance analysis completed:', portfolioPerformanceData.value)
        } else if (statusResponse.status === 'failed') {
          clearInterval(pollInterval)
          throw new Error(statusResponse.error || 'Portfolio performance analysis failed')
        } else {
          // Update progress
          portfolioPerformanceProgress.value = statusResponse.progress || 0
          portfolioPerformanceStatusMessage.value = statusResponse.message || 'Analyzing portfolio performance...'
          console.log(`📊 Progress: ${statusResponse.progress}% - ${statusResponse.message}`)
        }
      } catch (pollError) {
        clearInterval(pollInterval)
        throw pollError
      }
    }, 2000)

    // Timeout after 5 minutes
    setTimeout(() => {
      clearInterval(pollInterval)
      if (portfolioPerformanceLoading.value) {
        portfolioPerformanceError.value = 'Analysis timed out. Please try again.'
        portfolioPerformanceLoading.value = false
      }
    }, 300000)

  } catch (error) {
    console.error('❌ Portfolio performance analysis error:', error)
    portfolioPerformanceError.value = error.message || 'Failed to analyze portfolio performance'
    portfolioPerformanceLoading.value = false
  }
}

// Kill Portfolio Analysis Method
const killPortfolioAnalysis = async () => {
  try {
    console.log('🛑 Killing portfolio analysis jobs...')

    const api = useApiWithAuth()
    const response = await api.post('/api/stock-market/kill-portfolio-analysis', {})

    // Reset state
    portfolioPerformanceLoading.value = false
    portfolioPerformanceError.value = 'Analysis was stopped by user'
    portfolioPerformanceProgress.value = 0
    portfolioPerformanceStatusMessage.value = ''

    console.log('🛑 Portfolio analysis jobs killed:', response)

  } catch (error) {
    console.error('❌ Error killing portfolio analysis:', error)
    portfolioPerformanceError.value = 'Failed to stop analysis'
    portfolioPerformanceLoading.value = false
  }
}

// AI Configuration Methods
const openAISettings = () => {
  // Emit event to open global settings with AI tab
  window.dispatchEvent(new CustomEvent('open-global-settings', {
    detail: { activeTab: 'ai' }
  }));
};

const dismissAIBanner = () => {
  aiBannerDismissed.value = true;
  // Optionally save to localStorage to persist dismissal
  if (process.client) {
    localStorage.setItem('ai_banner_dismissed', 'true');
  }
};

// Load AI banner dismissal state
onMounted(() => {
  if (process.client) {
    const dismissed = localStorage.getItem('ai_banner_dismissed');
    aiBannerDismissed.value = dismissed === 'true';
  }
});

// Define checkMobile function at component scope
const checkMobile = () => {
  isMobile.value = window.innerWidth < 768;
};

// Check if device is mobile
onMounted(() => {
  checkMobile();
  window.addEventListener('resize', checkMobile);
});

onUnmounted(() => {
  window.removeEventListener('resize', checkMobile);
});
</script>
