// server/middleware/auth.ts
import { H3Event, createError } from 'h3';
import { verifyToken } from '../utils/auth';

// List of public routes that don't require authentication
const publicRoutes = [
  '/api/auth/login',
  '/api/auth/register',
  '/api/auth/refresh',
  '/api/auth/forgot-password', // Password reset request - must be public
  '/api/auth/reset-password',  // Password reset confirmation - must be public
  '/api/auth/validate-password', // Password validation - used on public pages
  '/api/firms/signup',
  '/signup',
  '/api/csrf/token', // CSRF token endpoint should be public
  '/api/tools/languages', // Public language list API
  '/api/tools/translate' // Public translation API
];

// List of routes with specific public methods
const publicMethodRoutes = [
  {
    path: '/api/firms',
    methods: ['GET', 'POST']
  },
  {
    path: '/api/firms/signup',
    methods: ['POST']
  }
];

// Only apply authentication to API routes
const requiresAuth = (path: string) => path.startsWith('/api/');

export default defineEventHandler(async (event: H3Event) => {
  const path = getRequestURL(event).pathname;
  const method = event.node.req.method || '';

  console.log(`Auth middleware: ${method} ${path}`);

  // Special case for firms/signup endpoint - always skip auth
  if (path.includes('/api/firms/signup')) {
    console.log(`Skipping auth for firms/signup: ${path}`);
    return;
  }

  // Skip authentication for non-API routes and public routes
  const isPublicRoute = publicRoutes.some(route => path.startsWith(route));
  const isApiRoute = requiresAuth(path);

  if (!isApiRoute) {
    console.log(`Skipping auth for non-API route: ${path}`);
    return;
  }

  if (isPublicRoute) {
    console.log(`Skipping auth for public route: ${path}`);
    return;
  }

  // Check for routes with specific public methods
  const publicMethodRoute = publicMethodRoutes.find(route => path.startsWith(route.path));
  if (publicMethodRoute && publicMethodRoute.methods.includes(method)) {
    console.log(`Allowing public access to ${path} with method ${method}`);
    return;
  }

  try {
    // Verify token and get user
    const user = await verifyToken(event);

    // Type assertion to define user structure
    interface User {
      _id: string | { toString(): string };
    }

    // Attach user to event context for downstream handlers
    event.context.user = user;

    // Add userId to event context for easier access in API handlers
    event.context.userId = ((user as User)._id).toString();
  } catch (error: any) {
    throw createError({
      statusCode: error.statusCode || 401,
      statusMessage: error.statusMessage || 'Unauthorized'
    });
  }
});