<template>
  <div class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
    <div class="mb-6">
      <h1 class="text-2xl font-bold text-gray-900">GST Registration Management</h1>
      <p class="text-gray-600">Manage multiple GST registrations for your firm and parties</p>
    </div>

    <!-- Firm GST Registrations -->
    <div class="bg-white shadow rounded-lg mb-6">
      <div class="px-6 py-4 border-b border-gray-200">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-medium text-gray-900">Firm GST Registrations</h2>
          <button @click="showAddFirmGSTModal = true"
                  class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Add New Firm GST
          </button>
        </div>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GST Number</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Location</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">State</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="(gst, index) in firmGSTs" :key="index" :class="{ 'bg-blue-50': gst.isPrimary }">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {{ gst.gstNumber }}
                <span v-if="gst.isPrimary" class="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  Primary
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ gst.locationName }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ gst.state }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ gst.registrationType || 'Regular' }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="gst.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'"
                      class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium">
                  {{ gst.isActive ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button v-if="!gst.isPrimary" 
                        class="text-red-600 hover:text-red-900 ml-4">
                  Deactivate
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Party GST Registrations -->
    <div class="bg-white shadow rounded-lg">
      <div class="px-6 py-4 border-b border-gray-200">
        <h2 class="text-lg font-medium text-gray-900">Party GST Registrations</h2>
        <p class="text-sm text-gray-600">Parties with multiple GST registrations</p>
      </div>
      
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Party Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">GST Count</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Primary GST</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">States</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="party in partiesWithMultipleGSTs" :key="party._id">
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ party.supply }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ party.allGSTs?.length || 1 }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ party.gstin }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                <div class="flex flex-wrap gap-1">
                  <span v-for="state in getUniqueStates(party.allGSTs)" :key="state"
                        class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                    {{ state }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <button @click="viewPartyGSTs(party)"
                        class="text-blue-600 hover:text-blue-900">
                  View Details
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Firm GST Modal -->
    <div v-if="showAddFirmGSTModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-xl max-w-md w-full mx-4">
        <h3 class="text-lg font-semibold mb-4">Add New Firm GST Registration</h3>
        
        <form @submit.prevent="submitFirmGST">
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">GST Number *</label>
              <input v-model="firmGSTForm.gstNumber" 
                     type="text" 
                     placeholder="27AAAAA0000A1Z5"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md"
                     required />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Location Name *</label>
              <input v-model="firmGSTForm.locationName" 
                     type="text" 
                     placeholder="Mumbai Branch"
                     class="w-full px-3 py-2 border border-gray-300 rounded-md"
                     required />
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Address *</label>
              <textarea v-model="firmGSTForm.address" 
                        placeholder="Complete address with city and pincode"
                        class="w-full px-3 py-2 border border-gray-300 rounded-md"
                        rows="3"
                        required></textarea>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">City</label>
                <input v-model="firmGSTForm.city" 
                       type="text" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">Pincode</label>
                <input v-model="firmGSTForm.pincode" 
                       type="text" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-md" />
              </div>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">Registration Type</label>
              <select v-model="firmGSTForm.registrationType" 
                      class="w-full px-3 py-2 border border-gray-300 rounded-md">
                <option value="regular">Regular</option>
                <option value="composition">Composition</option>
                <option value="casual">Casual</option>
                <option value="sez">SEZ</option>
              </select>
            </div>
          </div>
          
          <div class="flex justify-end space-x-3 mt-6">
            <button type="button" 
                    @click="showAddFirmGSTModal = false"
                    class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
              Cancel
            </button>
            <button type="submit" 
                    :disabled="isSubmittingGST"
                    class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50">
              {{ isSubmittingGST ? 'Adding...' : 'Add GST Registration' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import useToast from '~/composables/ui/useToast';

const { success, error: showError } = useToast();

// Reactive data
const inventoryData = ref(null);
const showAddFirmGSTModal = ref(false);
const isSubmittingGST = ref(false);

const firmGSTForm = ref({
  gstNumber: '',
  locationName: '',
  address: '',
  city: '',
  pincode: '',
  registrationType: 'regular'
});

// Computed properties
const firmGSTs = computed(() => inventoryData.value?.firmGSTs || []);

const partiesWithMultipleGSTs = computed(() => {
  return inventoryData.value?.parties?.filter(party => 
    party.hasMultipleGSTs || (party.allGSTs && party.allGSTs.length > 1)
  ) || [];
});

// Methods
const fetchInventoryData = async () => {
  try {
    const response = await $fetch('/api/inventory');
    inventoryData.value = response;
  } catch (error) {
    console.error('Error fetching inventory data:', error);
    showError('Failed to load GST data');
  }
};

const submitFirmGST = async () => {
  if (isSubmittingGST.value) return;
  
  try {
    isSubmittingGST.value = true;
    
    const response = await $fetch('/api/inventory/firm-gst', {
      method: 'POST',
      body: firmGSTForm.value
    });
    
    if (response.success) {
      success('Firm GST registration added successfully!');
      showAddFirmGSTModal.value = false;
      
      // Reset form
      firmGSTForm.value = {
        gstNumber: '',
        locationName: '',
        address: '',
        city: '',
        pincode: '',
        registrationType: 'regular'
      };
      
      // Refresh data
      await fetchInventoryData();
    }
  } catch (error) {
    console.error('Error adding firm GST:', error);
    showError(error.data?.statusMessage || 'Failed to add firm GST registration');
  } finally {
    isSubmittingGST.value = false;
  }
};

const getUniqueStates = (gsts) => {
  if (!gsts) return [];
  return [...new Set(gsts.map(gst => gst.state))];
};

const viewPartyGSTs = (party) => {
  // Navigate to party details or show modal
  console.log('View GSTs for party:', party.supply);
};

// Lifecycle
onMounted(() => {
  fetchInventoryData();
});

// Page meta
useHead({
  title: 'GST Management - Inventory System'
});
</script>
