<template>
  <div v-if="show" class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity" @click="close">
        <div class="absolute inset-0 bg-gray-500 opacity-75"></div>
      </div>

      <!-- Modal panel -->
      <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full"
           @keydown.esc="close"
           @keydown.enter.prevent="handleEnter"
           tabindex="0">
        <!-- Gradient Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 px-4 py-3 rounded-t-lg shadow-md">
          <h3 class="text-lg leading-6 font-medium text-white">Add Item Narration</h3>
        </div>

        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
          <div class="sm:flex sm:items-start">
            <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
              <form @submit.prevent="submit">
                <div class="grid grid-cols-1 gap-4">
                  <!-- Narration -->
                  <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Narration</label>
                    <textarea
                      ref="textareaRef"
                      v-model="narration"
                      rows="3"
                      class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter item description or notes"
                      @keydown.enter.prevent="handleTextareaEnter">
                    </textarea>
                  </div>
                </div>

                <!-- Form Buttons -->
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                  <button type="submit"
                    class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:col-start-2 sm:text-sm">
                    Save Narration
                  </button>
                  <button type="button" @click="close"
                    class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:col-start-1 sm:text-sm">
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>

        <!-- Gradient Footer with scrolling message -->
        <div class="bg-gradient-to-r from-purple-600 to-blue-500 px-4 py-3 rounded-b-lg shadow-md overflow-hidden">
          <div class="marquee-container">
            <div class="text-sm text-white whitespace-nowrap animate-marquee">

            </div>
            <div class="text-sm text-white whitespace-nowrap animate-marquee animate-marquee2">
              For line break: Press Shift+Enter • For line break: Press Shift+Enter
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, watch, nextTick } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  initialNarration: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:show', 'submit']);

const textareaRef = ref(null);
const narration = ref('');

watch(() => props.show, (newVal) => {
  if (newVal) {
    // Set the narration from props if available
    if (props.initialNarration) {
      narration.value = props.initialNarration;
    }

    nextTick(() => {
      const modalDiv = document.querySelector('.modal-panel');
      modalDiv?.focus();
      textareaRef.value?.focus();
    });
  }
});

const handleEnter = (e) => {
  if (e.target === textareaRef.value) return;
  submit();
};

const handleTextareaEnter = (e) => {
  if (e.shiftKey) {
    narration.value += '\n';
  } else {
    submit();
  }
};

const submit = () => {
  if (narration.value.trim()) {
    emit('submit', narration.value);
  }
  close();
};

const close = () => {
  narration.value = '';
  emit('update:show', false);
};
</script>

<style scoped>
/* Marquee animation */
@keyframes marquee {
  0% { transform: translateX(100%); }
  100% { transform: translateX(-100%); }
}

.marquee-container {
  position: relative;
  width: 100%;
  overflow: hidden;
}

.animate-marquee {
  display: inline-block;
  white-space: nowrap;
  will-change: transform;
  animation: marquee 15s linear infinite;
}

.animate-marquee2 {
  position: absolute;
  top: 0;
  left: 0;
  animation-delay: 7.5s;
}

/* Modal focus styles */
[tabindex="0"] {
  outline: none;
}

/* Textarea styles */
textarea {
  min-height: 100px;
  resize: vertical;
}
</style>