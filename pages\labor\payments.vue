<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Payment System</h1>
            <p class="mt-1 text-sm text-gray-500">
              Manage advance payments to labor groups.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Payment Form -->
        <div class="md:col-span-1">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Make a Payment</h3>
            <form @submit.prevent="makePayment" class="space-y-6">
              <div>
                <label for="group" class="block text-sm font-medium text-gray-700 mb-2">
                  Select Group
                </label>
                <select
                  id="group"
                  v-model="paymentForm.groupId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a group</option>
                  <option v-for="group in groups" :key="group.id" :value="group.id">
                    {{ group.name }}
                  </option>
                </select>
              </div>
              <div>
                <label for="paymentDate" class="block text-sm font-medium text-gray-700 mb-2">
                  Payment Date
                </label>
                <input
                  type="date"
                  id="paymentDate"
                  v-model="paymentForm.paymentDate"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                  Amount
                </label>
                <input
                  type="number"
                  id="amount"
                  v-model="paymentForm.amount"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter amount"
                />
              </div>
              <div>
                <label for="project" class="block text-sm font-medium text-gray-700 mb-2">
                  Project
                </label>
                <input
                  type="text"
                  id="project"
                  v-model="paymentForm.project"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter project name"
                />
              </div>
              <div>
                <label for="paymentType" class="block text-sm font-medium text-gray-700 mb-2">
                  Payment Type
                </label>
                <select
                  id="paymentType"
                  v-model="paymentForm.paymentType"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Advance">Advance</option>
                  <option value="Misc Payment">Misc Payment</option>
                  <option value="Final Payment">Final Payment</option>
                </select>
              </div>
              <div>
                <label for="paymentMethod" class="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  v-model="paymentForm.paymentMethod"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="cash">Cash</option>
                  <option v-for="bank in bankLedgers" :key="bank.id" :value="bank.id">
                    {{ bank.name }}
                  </option>
                </select>
              </div>
              <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                  Description (Optional)
                </label>
                <textarea
                  id="description"
                  v-model="paymentForm.description"
                  rows="2"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter payment description or notes"
                ></textarea>
              </div>
              <button type="submit" class="w-full btn btn-primary" :disabled="isLoading">
                <span v-if="isLoading">Saving...</span>
                <span v-else>Submit Payment</span>
              </button>
            </form>
          </div>
        </div>

        <!-- Unpaid Amounts -->
        <div class="md:col-span-2">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Unpaid Amounts</h3>
                <div class="flex items-center space-x-4">
                  <!-- Logic Toggle -->
                  <div class="flex items-center">
                    <label class="text-sm text-gray-600 mr-2">Enhanced Logic:</label>
                    <button
                      @click="toggleLogic"
                      :class="useNewLogic ? 'bg-blue-600' : 'bg-gray-300'"
                      class="relative inline-flex h-6 w-11 items-center rounded-full transition-colors focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                    >
                      <span
                        :class="useNewLogic ? 'translate-x-6' : 'translate-x-1'"
                        class="inline-block h-4 w-4 transform rounded-full bg-white transition-transform"
                      ></span>
                    </button>
                  </div>

                  <button @click="fetchUnpaidAmounts" class="btn btn-secondary">
                      Refresh
                  </button>
                </div>
            </div>

            <!-- Validation Warnings -->
            <div v-if="validationResults && validationResults.warnings.length > 0" class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <div class="flex">
                <div class="flex-shrink-0">
                  <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </div>
                <div class="ml-3">
                  <h3 class="text-sm font-medium text-yellow-800">Data Validation Warnings</h3>
                  <div class="mt-2 text-sm text-yellow-700">
                    <ul class="list-disc pl-5 space-y-1">
                      <li v-for="warning in validationResults.warnings" :key="warning">{{ warning }}</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Range</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Payment Made</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unpaid Amount</th>
                        <th v-if="useNewLogic" scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="item in unpaidAmounts" :key="item.period.period_start" :class="item.unpaidAmount > 0 ? 'bg-red-50' : ''">
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium text-gray-900">
                            {{ item.period.period_start }} - {{ item.period.period_end }}
                          </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm text-gray-900">₹{{ item.totalPayments.toFixed(2) }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                          <div class="text-sm font-medium" :class="item.unpaidAmount > 0 ? 'text-red-600' : 'text-green-600'">
                            ₹{{ item.unpaidAmount.toFixed(2) }}
                          </div>
                        </td>
                        <td v-if="useNewLogic" class="px-6 py-4 whitespace-nowrap">
                          <span :class="getPaymentTypeBadge(item.type)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                            {{ item.type }}
                          </span>
                        </td>
                    </tr>
                    <tr v-if="!unpaidAmounts || unpaidAmounts.length === 0">
                        <td :colspan="useNewLogic ? 4 : 3" class="px-6 py-4 text-center text-gray-500">
                          <div v-if="isLoading">Loading unpaid amounts...</div>
                          <div v-else>No unpaid amounts found for the selected group.</div>
                        </td>
                    </tr>
                </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

definePageMeta({
  title: 'Payment System',
  layout: 'default'
})

// Mock data for now
const firmId = ref('507f1f77bcf86cd799439011')

const groups = ref([])
const paymentForm = ref({
  groupId: '',
  paymentDate: new Date().toISOString().split('T')[0],
  amount: '',
  project: '',
  paymentType: 'Advance',
  paymentMethod: 'cash',
  description: ''
})
const unpaidAmounts = ref([])
const unpaidDates = ref({
    fromDate: new Date().toISOString().split('T')[0],
    toDate: new Date().toISOString().split('T')[0]
})

// Enhanced state management
const useNewLogic = ref(true)
const debugMode = ref(false)
const validationResults = ref(null)
const isLoading = ref(false)
const paymentMetadata = ref(null)
import { useLedgers } from '~/composables/expenses/useLedgers'

const { bankLedgers, fetchLedgers } = useLedgers()

const loadGroups = async () => {
  try {
    const response = await $fetch('/api/labor/groups', {
      query: { firmId: firmId.value }
    })
    if (response.success) {
      groups.value = response.data
    }
  } catch (error) {
    console.error('Error loading groups:', error)
  }
}

const makePayment = async () => {
  try {
    isLoading.value = true
    const response = await $fetch('/api/labor/payments', {
      method: 'POST',
      body: { ...paymentForm.value, firmId: firmId.value }
    })
    if (response.success) {
      // Store payment metadata
      paymentMetadata.value = response.metadata

      // Reset form fields
      paymentForm.value.amount = ''
      paymentForm.value.project = ''
      paymentForm.value.description = ''

      // Refresh unpaid amounts
      await fetchUnpaidAmounts()

      // Show success message (you can add a toast notification here)
      console.log('Payment saved successfully:', response.message)
    }
  } catch (error) {
    console.error('Error making payment:', error)
    // Handle error (you can add error notification here)
  } finally {
    isLoading.value = false
  }
}

const fetchUnpaidAmounts = async () => {
    if (!paymentForm.value.groupId) return
    try {
        isLoading.value = true

        // Choose API endpoint based on logic preference
        const endpoint = useNewLogic.value ? '/api/labor/payments/unpaid-v2' : '/api/labor/payments/unpaid'

        const response = await $fetch(endpoint, {
            query: {
                firmId: firmId.value,
                groupId: paymentForm.value.groupId,
                fromDate: unpaidDates.value.fromDate,
                toDate: unpaidDates.value.toDate,
            }
        })

        if (response.success) {
            unpaidAmounts.value = response.data

            // Store validation results if using new logic
            if (useNewLogic.value && response.validation) {
                validationResults.value = response.validation
            }

            // Store metadata if available
            if (response.metadata) {
                paymentMetadata.value = response.metadata
            }
        }
    } catch (error) {
        console.error('Error fetching unpaid amounts:', error)
        // Handle error gracefully
        unpaidAmounts.value = []
    } finally {
        isLoading.value = false
    }
}

const fetchPaymentRecords = async (groupId) => {
    try {
        const response = await $fetch(`/api/labor/payments/${groupId}`, {
            query: {
                firmId: firmId.value
            }
        })
        if (response.success) {
            console.log('Payment records for group:', response.data)
        }
    } catch (error) {
        console.error('Error fetching payment records:', error)
    }
}

onMounted(() => {
  loadGroups()
  fetchLedgers('bank')
})

watch(() => paymentForm.value.groupId, (newGroupId) => {
    if (newGroupId) {
        fetchUnpaidAmounts()
        fetchPaymentRecords(newGroupId)
    }
})

const syncToFirestore = async () => {
    try {
        const response = await $fetch('/api/labor/payments/sync', {
            method: 'POST',
            body: {
                paymentId: 'some-payment-id', // This should be dynamic
                firmId: firmId.value
            }
        })
        if (response.success) {
            // Show success message
        }
    } catch (error) {
        console.error('Error syncing to Firestore:', error)
    }
}

// Debug function to compare old vs new logic
const fetchDebugData = async () => {
    if (!paymentForm.value.groupId) return
    try {
        const response = await $fetch('/api/labor/payments/debug-payments', {
            query: {
                firmId: firmId.value,
                groupId: paymentForm.value.groupId
            }
        })
        if (response.success) {
            console.log('Debug data:', response.debug)
            return response.debug
        }
    } catch (error) {
        console.error('Error fetching debug data:', error)
    }
}

// Toggle between old and new logic
const toggleLogic = async () => {
    useNewLogic.value = !useNewLogic.value
    await fetchUnpaidAmounts()
}

// Get payment type badge color
const getPaymentTypeBadge = (type) => {
    const badges = {
        'settled': 'bg-green-100 text-green-800',
        'ongoing': 'bg-blue-100 text-blue-800',
        'post-final': 'bg-yellow-100 text-yellow-800'
    }
    return badges[type] || 'bg-gray-100 text-gray-800'
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center justify-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>