<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-6">
          <div>
            <h1 class="text-3xl font-bold text-gray-900">Payment System</h1>
            <p class="mt-1 text-sm text-gray-500">
              Manage advance payments to labor groups.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Payment Form -->
        <div class="md:col-span-1">
          <div class="bg-white rounded-lg shadow p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Make a Payment</h3>
            <form @submit.prevent="makePayment" class="space-y-6">
              <div>
                <label for="group" class="block text-sm font-medium text-gray-700 mb-2">
                  Select Group
                </label>
                <select
                  id="group"
                  v-model="paymentForm.groupId"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select a group</option>
                  <option v-for="group in groups" :key="group.id" :value="group.id">
                    {{ group.name }}
                  </option>
                </select>
              </div>
              <div>
                <label for="paymentDate" class="block text-sm font-medium text-gray-700 mb-2">
                  Payment Date
                </label>
                <input
                  type="date"
                  id="paymentDate"
                  v-model="paymentForm.paymentDate"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div>
                <label for="amount" class="block text-sm font-medium text-gray-700 mb-2">
                  Amount
                </label>
                <input
                  type="number"
                  id="amount"
                  v-model="paymentForm.amount"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter amount"
                />
              </div>
              <div>
                <label for="project" class="block text-sm font-medium text-gray-700 mb-2">
                  Project
                </label>
                <input
                  type="text"
                  id="project"
                  v-model="paymentForm.project"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter project name"
                />
              </div>
              <div>
                <label for="paymentType" class="block text-sm font-medium text-gray-700 mb-2">
                  Payment Type
                </label>
                <select
                  id="paymentType"
                  v-model="paymentForm.paymentType"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="Advance">Advance</option>
                  <option value="Misc Payment">Misc Payment</option>
                  <option value="Final Payment">Final Payment</option>
                </select>
              </div>
              <div>
                <label for="paymentMethod" class="block text-sm font-medium text-gray-700 mb-2">
                  Payment Method
                </label>
                <select
                  id="paymentMethod"
                  v-model="paymentForm.paymentMethod"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="cash">Cash</option>
                  <option v-for="bank in bankLedgers" :key="bank.id" :value="bank.id">
                    {{ bank.name }}
                  </option>
                </select>
              </div>
              <button type="submit" class="w-full btn btn-primary">
                Submit Payment
              </button>
            </form>
          </div>
        </div>

        <!-- Unpaid Amounts -->
        <div class="md:col-span-2">
          <div class="bg-white rounded-lg shadow p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Unpaid Amounts</h3>
                <button @click="fetchUnpaidAmounts" class="btn btn-secondary">
                    Fetch Unpaid Amounts
                </button>
            </div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date Range</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Payment Made</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unpaid Amount</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <tr v-for="item in unpaidAmounts" :key="item.period.period_start">
                        <td class="px-6 py-4 whitespace-nowrap">{{ item.period.period_start }} - {{ item.period.period_end }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₹{{ item.totalPayments.toFixed(2) }}</td>
                        <td class="px-6 py-4 whitespace-nowrap">₹{{ item.unpaidAmount.toFixed(2) }}</td>
                    </tr>
                    <tr v-if="!unpaidAmounts || unpaidAmounts.length === 0">
                        <td colspan="3" class="px-6 py-4 text-center text-gray-500">No unpaid amounts found for the selected group.</td>
                    </tr>
                </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'

definePageMeta({
  title: 'Payment System',
  layout: 'default'
})

// Mock data for now
const firmId = ref('507f1f77bcf86cd799439011')

const groups = ref([])
const paymentForm = ref({
  groupId: '',
  paymentDate: new Date().toISOString().split('T')[0],
  amount: '',
  project: '',
  paymentType: 'Advance',
  paymentMethod: 'cash'
})
const unpaidAmounts = ref([])
const unpaidDates = ref({
    fromDate: new Date().toISOString().split('T')[0],
    toDate: new Date().toISOString().split('T')[0]
})
import { useLedgers } from '~/composables/expenses/useLedgers'

const { bankLedgers, fetchLedgers } = useLedgers()

const loadGroups = async () => {
  try {
    const response = await $fetch('/api/labor/groups', {
      query: { firmId: firmId.value }
    })
    if (response.success) {
      groups.value = response.data
    }
  } catch (error) {
    console.error('Error loading groups:', error)
  }
}

const makePayment = async () => {
  try {
    const response = await $fetch('/api/labor/payments', {
      method: 'POST',
      body: { ...paymentForm.value, firmId: firmId.value }
    })
    if (response.success) {
      // Reset form or show success message
      paymentForm.value.amount = ''
      paymentForm.value.project = ''
    }
  } catch (error) {
    console.error('Error making payment:', error)
  }
}

const fetchUnpaidAmounts = async () => {
    if (!paymentForm.value.groupId) return
    try {
        const response = await $fetch('/api/labor/payments/unpaid', {
            query: {
                firmId: firmId.value,
                groupId: paymentForm.value.groupId,
                fromDate: unpaidDates.value.fromDate,
                toDate: unpaidDates.value.toDate,
            }
        })
        if (response.success) {
            unpaidAmounts.value = response.data
        }
    } catch (error) {
        console.error('Error fetching unpaid amounts:', error)
    }
}

const fetchPaymentRecords = async (groupId) => {
    try {
        const response = await $fetch(`/api/labor/payments/${groupId}`, {
            query: {
                firmId: firmId.value
            }
        })
        if (response.success) {
            console.log('Payment records for group:', response.data)
        }
    } catch (error) {
        console.error('Error fetching payment records:', error)
    }
}

onMounted(() => {
  loadGroups()
  fetchLedgers('bank')
})

watch(() => paymentForm.value.groupId, (newGroupId) => {
    if (newGroupId) {
        fetchUnpaidAmounts()
        fetchPaymentRecords(newGroupId)
    }
})

const syncToFirestore = async () => {
    try {
        const response = await $fetch('/api/labor/payments/sync', {
            method: 'POST',
            body: {
                paymentId: 'some-payment-id', // This should be dynamic
                firmId: firmId.value
            }
        })
        if (response.success) {
            // Show success message
        }
    } catch (error) {
        console.error('Error syncing to Firestore:', error)
    }
}
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center justify-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}

.btn-secondary {
    @apply bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>