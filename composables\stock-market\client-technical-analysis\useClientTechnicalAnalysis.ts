import { ref, computed, readonly } from 'vue'
import { useUniversalAIClient } from '~/composables/ai/useUniversalAIClient'
import { calculateAllTechnicalIndicators, calculateSMASeries, calculateRSISeries, calculateMACDSeries, type TechnicalIndicators } from './technicalIndicators'

export interface ClientTechnicalAnalysisResult {
  technicalIndicators: TechnicalIndicators
  aiAnalysis: any
  historicalData: any[]
  historicalDataPoints: number
  analysisTimestamp: string
}

export const useClientTechnicalAnalysis = () => {
  // State
  const loading = ref(false)
  const error = ref('')
  const analysis = ref<ClientTechnicalAnalysisResult | null>(null)
  const progress = ref(0)
  const statusMessage = ref('')

  // Enhanced progress tracking
  const dataPointsProcessed = ref(0)
  const currentOperation = ref('')
  const operationStartTime = ref<Date | null>(null)

  // Universal AI Client - NO MORE HARDCODED PROVIDERS!
  const { callAIForJSON, isConfigured, getAIInfo } = useUniversalAIClient()

  // Fetch historical data from Yahoo Finance proxy
  const fetchHistoricalData = async (symbol: string, days: number = 600): Promise<any[]> => {
    try {
      console.log(`📊 Fetching ${days} days of historical data for ${symbol}`)

      const response = await $fetch<{
        success: boolean;
        dataPoints: number;
        data: any[];
        symbol: string;
        yahooSymbol: string;
        timestamp: string;
      }>('/api/stock-market/yahoo-finance-proxy', {
        params: {
          type: 'historical',
          symbol,
          days: days.toString(),
          interval: '1d'
        }
      })

      if (!response.success || !response.data) {
        throw new Error('Failed to fetch historical data from proxy')
      }

      console.log(`📊 Received ${response.dataPoints} data points for ${symbol}`)
      return response.data
    } catch (err: any) {
      console.error('Error fetching historical data:', err)
      throw new Error(`Failed to fetch historical data: ${err.message}`)
    }
  }

  // Generate AI analysis prompt - 100% IDENTICAL TO SERVER VERSION
  const generateAIAnalysisPrompt = (stockData: any, technicalIndicators: TechnicalIndicators, historicalDataPoints: number) => {
    return `You are a professional technical analyst with expertise in Indian stock markets.

STOCK INFORMATION:
- Symbol: ${stockData.symbol}
- Company: ${stockData.companyName || stockData.symbol}
- Current Price: ₹${stockData.currentPrice}
- Price Change: ₹${stockData.change} (${stockData.pChange}%)

TECHNICAL ANALYSIS DATA (${historicalDataPoints} trading days):
- 200-day SMA: ₹${technicalIndicators.sma200.toFixed(2)}
- 50-day SMA: ₹${technicalIndicators.sma50.toFixed(2)}
- 20-day SMA: ₹${technicalIndicators.sma20.toFixed(2)}
- 12-day EMA: ₹${technicalIndicators.ema12.toFixed(2)}
- 26-day EMA: ₹${technicalIndicators.ema26.toFixed(2)}
- RSI (14): ${technicalIndicators.rsi.toFixed(2)}
- MACD Line: ${technicalIndicators.macd.line.toFixed(4)}
- MACD Signal: ${technicalIndicators.macd.signal.toFixed(4)}
- MACD Histogram: ${technicalIndicators.macd.histogram.toFixed(4)}
- Bollinger Upper: ₹${technicalIndicators.bollingerBands.upper.toFixed(2)}
- Bollinger Middle: ₹${technicalIndicators.bollingerBands.middle.toFixed(2)}
- Bollinger Lower: ₹${technicalIndicators.bollingerBands.lower.toFixed(2)}
- ATR: ${technicalIndicators.atr.toFixed(2)}
- Support Levels: ${technicalIndicators.supportResistance.support.map(s => '₹' + s.toFixed(2)).join(', ')}
- Resistance Levels: ${technicalIndicators.supportResistance.resistance.map(r => '₹' + r.toFixed(2)).join(', ')}

ANALYSIS REQUIREMENTS:
1. MOVING AVERAGE ANALYSIS (based on ${historicalDataPoints} days of data):
   - Current price position relative to long-term SMA (200-day or available data)
   - Long-term trend direction and strength
   - Moving average crossover signals

2. TECHNICAL INDICATORS INTERPRETATION:
   - RSI overbought/oversold analysis
   - MACD trend and momentum signals
   - Bollinger Bands position and volatility
   - Support and resistance level validation

3. TREND ANALYSIS:
   - Primary trend (bullish/bearish/sideways)
   - Trend strength and sustainability
   - Potential reversal signals

4. TRADING RECOMMENDATIONS:
   - Entry points and optimal timing
   - Stop-loss levels based on technical support
   - Target prices using resistance levels
   - Risk-reward ratio assessment

5. ACTIONABLE INSIGHTS:
   - Short-term (1-2 weeks) outlook
   - Medium-term (1-3 months) outlook
   - Key levels to watch
   - Trading strategy recommendations

Return analysis in JSON format:
{
  "technicalRecommendation": "STRONG_BUY/BUY/HOLD/SELL/STRONG_SELL",
  "confidence": "High/Medium/Low",
  "trendAnalysis": "Detailed 200-day trend analysis",
  "movingAverageAnalysis": "SMA and EMA analysis with crossover signals",
  "momentumAnalysis": "RSI and MACD interpretation",
  "volatilityAnalysis": "Bollinger Bands and ATR analysis",
  "supportResistanceAnalysis": "Key levels and price action",
  "entryStrategy": "Optimal entry points and timing",
  "riskManagement": "Stop-loss and position sizing recommendations",
  "priceTargets": {
    "shortTerm": "1-2 week target price",
    "mediumTerm": "1-3 month target price",
    "stopLoss": "Recommended stop-loss level"
  },
  "keyLevelsToWatch": ["List of important price levels"],
  "tradingStrategy": "Specific trading approach and timing"
}

Focus on Indian market context and provide actionable technical analysis based on the 200-day data.`
  }

  // Universal AI Provider Call - NO MORE HARDCODED LOGIC!
  const callAIProviderDirectly = async (prompt: string): Promise<any> => {
    const aiInfo = getAIInfo()
    console.log(`🤖 Making universal AI call to ${aiInfo.provider} with model ${aiInfo.model}`)

    try {
      // Use universal AI client - works with ANY provider dynamically
      const systemPrompt = 'You are a professional technical analyst. Provide accurate, data-driven technical analysis based on chart patterns and indicators. Return only valid JSON format matching the exact structure requested.'
      return await callAIForJSON(prompt, systemPrompt)
    } catch (error: any) {
      console.error(`❌ Universal AI call failed:`, error)
      throw new Error(`AI analysis failed: ${error.message}`)
    }
  }

  // ALL HARDCODED PROVIDER FUNCTIONS REMOVED!
  // Now using Universal AI Client for complete dynamic support







  // Perform client-side technical analysis with REAL-TIME PROGRESS
  const performTechnicalAnalysis = async (stockData: any) => {
    if (!isConfigured.value) {
      throw new Error('AI configuration is not complete. Please configure your AI settings.')
    }

    loading.value = true
    error.value = ''
    analysis.value = null
    progress.value = 0
    dataPointsProcessed.value = 0
    currentOperation.value = 'Initializing'
    operationStartTime.value = new Date()
    statusMessage.value = `Starting technical analysis for ${stockData.symbol}...`

    try {
      // Step 1: Fetch historical data with real progress tracking
      progress.value = 5
      currentOperation.value = 'Fetching Historical Data'
      statusMessage.value = `Fetching 600 days of historical data for ${stockData.symbol}...`

      const historicalData = await fetchHistoricalData(stockData.symbol, 600)

      if (!historicalData || historicalData.length === 0) {
        throw new Error('No historical data available for technical analysis')
      }

      progress.value = 15
      dataPointsProcessed.value = historicalData.length
      currentOperation.value = 'Data Validation'
      statusMessage.value = `Received ${historicalData.length} data points (${historicalData[0]?.date} to ${historicalData[historicalData.length-1]?.date})`

      // Prepare price arrays for calculations
      const closes = historicalData.map(item => item.close)
      const highs = historicalData.map(item => item.high)
      const lows = historicalData.map(item => item.low)
      const volumes = historicalData.map(item => item.volume || 0)

      progress.value = 20
      currentOperation.value = 'Data Extraction'
      statusMessage.value = `Extracted ${closes.length} close prices, ${highs.length} highs, ${lows.length} lows, ${volumes.length} volumes`

      // Step 2: Calculate technical indicators with granular progress
      progress.value = 25
      currentOperation.value = 'Technical Indicators'
      statusMessage.value = 'Calculating Simple Moving Averages (SMA 20, 50, 200)...'

      const technicalIndicators = calculateAllTechnicalIndicators(historicalData)

      progress.value = 35
      currentOperation.value = 'Indicators Complete'
      statusMessage.value = 'Technical indicators calculated successfully'

      // Step 3: Calculate chart series data with detailed progress
      progress.value = 40
      currentOperation.value = 'Chart Series - SMA 20'
      statusMessage.value = 'Calculating 20-day SMA series for price chart...'
      const sma20Series = calculateSMASeries(closes, 20)

      progress.value = 45
      currentOperation.value = 'Chart Series - SMA 50'
      statusMessage.value = 'Calculating 50-day SMA series for price chart...'
      const sma50Series = calculateSMASeries(closes, 50)

      progress.value = 50
      currentOperation.value = 'Chart Series - SMA 200'
      statusMessage.value = 'Calculating 200-day SMA series for price chart...'
      const sma200Series = calculateSMASeries(closes, 200)

      progress.value = 55
      currentOperation.value = 'Chart Series - RSI'
      statusMessage.value = 'Calculating RSI series for RSI chart...'
      const rsiSeries = calculateRSISeries(closes, 14)

      progress.value = 60
      currentOperation.value = 'Chart Series - MACD'
      statusMessage.value = 'Calculating MACD series for MACD chart...'
      const macdSeries = calculateMACDSeries(closes)

      // Step 4: Enhance historical data with technical indicators for charting
      progress.value = 65
      statusMessage.value = `Enhancing ${historicalData.length} data points with technical indicators...`

      const historicalDataForChart = historicalData.map((item, index) => ({
        ...item,
        sma20: sma20Series[index],
        sma50: sma50Series[index],
        sma200: sma200Series[index],
        rsi: rsiSeries[index],
        macd: macdSeries.macd[index],
        macdSignal: macdSeries.signal[index],
        macdHistogram: macdSeries.histogram[index]
      }))

      progress.value = 70
      statusMessage.value = 'Chart data preparation complete - all indicators attached'

      // Step 5: Generate AI analysis prompt
      progress.value = 72
      statusMessage.value = 'Preparing AI analysis prompt with technical data...'

      const aiPrompt = generateAIAnalysisPrompt(stockData, technicalIndicators, historicalData.length)

      progress.value = 75
      const aiInfo = getAIInfo()
      statusMessage.value = `Sending analysis request to ${aiInfo.provider} (${aiInfo.model})...`

      // Make UNIVERSAL AI provider call - COMPLETELY DYNAMIC!
      const technicalAnalysis = await callAIProviderDirectly(aiPrompt)

      // Step 6: Validate AI response with detailed progress
      progress.value = 85
      statusMessage.value = `Received AI response, validating structure...`

      if (!technicalAnalysis || typeof technicalAnalysis !== 'object') {
        throw new Error('Invalid response from AI provider - expected JSON object')
      }

      progress.value = 95
      statusMessage.value = 'Validating AI analysis fields...'

      console.log('✅ Received parsed technical analysis result:', technicalAnalysis)

        // Validate required fields for server compatibility
        if (!technicalAnalysis.technicalRecommendation) {
          throw new Error('AI response missing required technicalRecommendation field')
        }

        progress.value = 97
        statusMessage.value = 'AI analysis validation complete'

        console.log('✅ Client-side AI analysis parsed successfully:', technicalAnalysis)

      // Step 7: Prepare final response with detailed completion
      progress.value = 98
      statusMessage.value = 'Preparing final analysis result...'

      const finalAnalysis: ClientTechnicalAnalysisResult = {
        technicalIndicators,
        aiAnalysis: technicalAnalysis,
        historicalData: historicalDataForChart,
        historicalDataPoints: historicalData.length,
        analysisTimestamp: new Date().toISOString()
      }

      progress.value = 100
      statusMessage.value = `Analysis complete! Processed ${historicalData.length} data points with ${Object.keys(technicalIndicators).length} indicators`

      analysis.value = finalAnalysis
      loading.value = false

      console.log('✅ Client-side technical analysis completed:', finalAnalysis)
      return finalAnalysis

    } catch (err: any) {
      console.error('❌ Client-side technical analysis error:', err)
      error.value = err.message
      loading.value = false
      progress.value = 0
      statusMessage.value = `Error: ${err.message}`
      throw err
    }
  }

  // Reset state
  const reset = () => {
    loading.value = false
    error.value = ''
    analysis.value = null
    progress.value = 0
    statusMessage.value = ''
    dataPointsProcessed.value = 0
    currentOperation.value = ''
    operationStartTime.value = null
  }

  return {
    // State
    loading: readonly(loading),
    error: readonly(error),
    analysis: readonly(analysis),
    progress: readonly(progress),
    statusMessage: readonly(statusMessage),

    // Enhanced progress tracking
    dataPointsProcessed: readonly(dataPointsProcessed),
    currentOperation: readonly(currentOperation),
    operationStartTime: readonly(operationStartTime),

    // Computed
    isConfigured,

    // Methods
    performTechnicalAnalysis,
    fetchHistoricalData,
    reset
  }
}
