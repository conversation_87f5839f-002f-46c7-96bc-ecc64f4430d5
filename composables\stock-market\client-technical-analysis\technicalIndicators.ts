// Client-side Technical Indicators Calculation Utilities
// Identical to server-side implementation but for client-side use

export interface TechnicalIndicators {
  sma20: number
  sma50: number
  sma200: number
  ema12: number
  ema26: number
  rsi: number
  macd: {
    line: number
    signal: number
    histogram: number
  }
  bollingerBands: {
    upper: number
    middle: number
    lower: number
  }
  atr: number
  obv: number
  supportResistance: {
    support: number[]
    resistance: number[]
  }
}

// Calculate SMA for a full series of data points for charting
export function calculateSMASeries(prices: number[], period: number): (number | null)[] {
  const smaValues: (number | null)[] = [];
  if (prices.length < period) {
    return new Array(prices.length).fill(null);
  }

  // Use a sliding window approach for efficiency
  let sum = 0;
  for (let i = 0; i < period; i++) {
    sum += prices[i];
  }

  // Fill initial nulls for periods where SMA is not available
  for (let i = 0; i < period - 1; i++) {
    smaValues.push(null);
  }
  smaValues.push(sum / period);

  // Calculate subsequent SMAs using the sliding window
  for (let i = period; i < prices.length; i++) {
    sum = sum - prices[i - period] + prices[i];
    smaValues.push(sum / period);
  }

  return smaValues;
}

// Calculate RSI for a full series of data points for charting
export function calculateRSISeries(prices: number[], period: number = 14): (number | null)[] {
  const rsiValues: (number | null)[] = [];
  
  if (prices.length < period + 1) {
    return new Array(prices.length).fill(null);
  }

  // Calculate price changes
  const changes: number[] = [];
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1]);
  }

  // Fill initial nulls
  for (let i = 0; i < period; i++) {
    rsiValues.push(null);
  }

  // Calculate RSI for each point
  for (let i = period - 1; i < changes.length; i++) {
    const slice = changes.slice(i - period + 1, i + 1);
    const gains = slice.filter(change => change > 0);
    const losses = slice.filter(change => change < 0).map(loss => Math.abs(loss));
    
    const avgGain = gains.length > 0 ? gains.reduce((sum, gain) => sum + gain, 0) / period : 0;
    const avgLoss = losses.length > 0 ? losses.reduce((sum, loss) => sum + loss, 0) / period : 0;
    
    if (avgLoss === 0) {
      rsiValues.push(100);
    } else {
      const rs = avgGain / avgLoss;
      rsiValues.push(100 - (100 / (1 + rs)));
    }
  }

  return rsiValues;
}

// Calculate MACD for a full series of data points for charting
export function calculateMACDSeries(prices: number[]): { 
  macd: (number | null)[], 
  signal: (number | null)[], 
  histogram: (number | null)[] 
} {
  const macdValues: (number | null)[] = [];
  const signalValues: (number | null)[] = [];
  const histogramValues: (number | null)[] = [];
  
  if (prices.length < 26) {
    return {
      macd: new Array(prices.length).fill(null),
      signal: new Array(prices.length).fill(null),
      histogram: new Array(prices.length).fill(null)
    };
  }

  const ema12Series = calculateEMASeries(prices, 12);
  const ema26Series = calculateEMASeries(prices, 26);

  // Calculate MACD line
  for (let i = 0; i < prices.length; i++) {
    if (ema12Series[i] !== null && ema26Series[i] !== null) {
      macdValues.push(ema12Series[i]! - ema26Series[i]!);
    } else {
      macdValues.push(null);
    }
  }

  // Calculate signal line (9-period EMA of MACD)
  const validMacdValues = macdValues.filter(val => val !== null) as number[];
  const signalSeries = calculateEMASeries(validMacdValues, 9);
  
  // Align signal series with MACD series
  let signalIndex = 0;
  for (let i = 0; i < macdValues.length; i++) {
    if (macdValues[i] !== null) {
      signalValues.push(signalSeries[signalIndex] || null);
      signalIndex++;
    } else {
      signalValues.push(null);
    }
  }

  // Calculate histogram
  for (let i = 0; i < macdValues.length; i++) {
    if (macdValues[i] !== null && signalValues[i] !== null) {
      histogramValues.push(macdValues[i]! - signalValues[i]!);
    } else {
      histogramValues.push(null);
    }
  }

  return {
    macd: macdValues,
    signal: signalValues,
    histogram: histogramValues
  };
}

// Helper function to calculate EMA series
function calculateEMASeries(prices: number[], period: number): (number | null)[] {
  const emaValues: (number | null)[] = [];
  
  if (prices.length < period) {
    return new Array(prices.length).fill(null);
  }

  const multiplier = 2 / (period + 1);
  
  // Fill initial nulls
  for (let i = 0; i < period - 1; i++) {
    emaValues.push(null);
  }

  // First EMA is SMA
  const firstSMA = prices.slice(0, period).reduce((sum, price) => sum + price, 0) / period;
  emaValues.push(firstSMA);

  // Calculate subsequent EMAs
  for (let i = period; i < prices.length; i++) {
    const ema = (prices[i] * multiplier) + (emaValues[i - 1]! * (1 - multiplier));
    emaValues.push(ema);
  }

  return emaValues;
}

// Simple Moving Average (for a single, latest value) - NO FALLBACK DATA
export function calculateSMA(prices: number[], period: number): number {
  if (prices.length === 0) {
    throw new Error(`SMA calculation failed: No price data available`)
  }

  // Only calculate SMA if we have enough data for the full period
  // This ensures accurate moving averages and prevents misleading results
  if (prices.length < period) {
    throw new Error(`SMA calculation failed: insufficient data for ${period}-day SMA. Need ${period} days, have ${prices.length} days`)
  }

  const slice = prices.slice(-period)
  console.log(`SMA calculation: ${period}-day SMA calculated with ${slice.length} data points from ${prices.length} total`)

  return slice.reduce((sum, price) => sum + price, 0) / period
}

// Exponential Moving Average - NO FALLBACK DATA
export function calculateEMA(prices: number[], period: number): number {
  if (prices.length < period) {
    throw new Error(`EMA calculation failed: insufficient data for ${period}-day EMA. Need ${period} days, have ${prices.length} days`)
  }
  
  const multiplier = 2 / (period + 1)
  
  // Start with SMA for the first EMA value
  const sma = calculateSMA(prices.slice(0, period), period)
  let ema = sma
  
  // Calculate EMA for remaining values
  for (let i = period; i < prices.length; i++) {
    ema = (prices[i] * multiplier) + (ema * (1 - multiplier))
  }
  
  return ema
}

// RSI (Relative Strength Index) - NO FALLBACK DATA
export function calculateRSI(prices: number[], period: number = 14): number {
  if (prices.length < period + 1) {
    throw new Error(`RSI calculation failed: insufficient data for ${period}-day RSI. Need ${period + 1} days, have ${prices.length} days`)
  }
  
  const changes: number[] = []
  for (let i = 1; i < prices.length; i++) {
    changes.push(prices[i] - prices[i - 1])
  }
  
  const recentChanges = changes.slice(-period)
  const gains = recentChanges.filter(change => change > 0)
  const losses = recentChanges.filter(change => change < 0).map(loss => Math.abs(loss))
  
  const avgGain = gains.length > 0 ? gains.reduce((sum, gain) => sum + gain, 0) / period : 0
  const avgLoss = losses.length > 0 ? losses.reduce((sum, loss) => sum + loss, 0) / period : 0
  
  if (avgLoss === 0) return 100
  const rs = avgGain / avgLoss
  return 100 - (100 / (1 + rs))
}

// MACD (Moving Average Convergence Divergence) - NO FALLBACK DATA
export function calculateMACD(prices: number[]): { line: number, signal: number, histogram: number } {
  if (prices.length < 26) {
    throw new Error(`MACD calculation failed: insufficient data for MACD. Need 26 days, have ${prices.length} days`)
  }
  
  const ema12 = calculateEMA(prices, 12)
  const ema26 = calculateEMA(prices, 26)
  const macdLine = ema12 - ema26
  
  // Calculate signal line (9-period EMA of MACD line)
  // For simplicity, using a basic calculation
  const signalLine = macdLine * 0.2 // Simplified signal calculation
  const histogram = macdLine - signalLine
  
  return {
    line: macdLine,
    signal: signalLine,
    histogram: histogram
  }
}

// Bollinger Bands
export function calculateBollingerBands(prices: number[], period: number = 20): { upper: number, middle: number, lower: number } {
  if (prices.length < period) {
    const lastPrice = prices[prices.length - 1] || 0
    return { upper: lastPrice, middle: lastPrice, lower: lastPrice }
  }
  
  const sma = calculateSMA(prices, period)
  const slice = prices.slice(-period)
  
  // Calculate standard deviation
  const variance = slice.reduce((sum, price) => sum + Math.pow(price - sma, 2), 0) / period
  const stdDev = Math.sqrt(variance)
  
  return {
    upper: sma + (2 * stdDev),
    middle: sma,
    lower: sma - (2 * stdDev)
  }
}

// ATR (Average True Range) - NO FALLBACK DATA
export function calculateATR(highs: number[], lows: number[], closes: number[], period: number = 14): number {
  if (highs.length < period + 1 || lows.length < period + 1 || closes.length < period + 1) {
    throw new Error(`ATR calculation failed: insufficient data for ${period}-day ATR. Need ${period + 1} days, have highs:${highs.length}, lows:${lows.length}, closes:${closes.length}`)
  }
  
  const trueRanges: number[] = []
  
  for (let i = 1; i < highs.length; i++) {
    const tr1 = highs[i] - lows[i]
    const tr2 = Math.abs(highs[i] - closes[i - 1])
    const tr3 = Math.abs(lows[i] - closes[i - 1])
    trueRanges.push(Math.max(tr1, tr2, tr3))
  }
  
  const recentTR = trueRanges.slice(-period)
  return recentTR.reduce((sum, tr) => sum + tr, 0) / period
}

// OBV (On-Balance Volume) - NO FALLBACK DATA
export function calculateOBV(closes: number[], volumes: number[]): number {
  if (closes.length < 2 || volumes.length < 2) {
    throw new Error(`OBV calculation failed: insufficient data for OBV. Need at least 2 days, have closes:${closes.length}, volumes:${volumes.length}`)
  }
  
  let obv = 0
  for (let i = 1; i < closes.length; i++) {
    if (closes[i] > closes[i - 1]) {
      obv += volumes[i]
    } else if (closes[i] < closes[i - 1]) {
      obv -= volumes[i]
    }
    // If closes[i] === closes[i - 1], OBV remains unchanged
  }
  
  return obv
}

// Support and Resistance Levels
export function calculateSupportResistance(highs: number[], lows: number[], closes: number[]): { support: number[], resistance: number[] } {
  if (highs.length < 20 || lows.length < 20 || closes.length < 20) {
    return { support: [], resistance: [] }
  }

  const support: number[] = []
  const resistance: number[] = []
  const lookback = 10 // Look for peaks/troughs within 10 periods

  // Find resistance levels (local highs)
  for (let i = lookback; i < highs.length - lookback; i++) {
    let isResistance = true
    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j !== i && highs[j] >= highs[i]) {
        isResistance = false
        break
      }
    }
    if (isResistance) {
      resistance.push(highs[i])
    }
  }

  // Find support levels (local lows)
  for (let i = lookback; i < lows.length - lookback; i++) {
    let isSupport = true
    for (let j = i - lookback; j <= i + lookback; j++) {
      if (j !== i && lows[j] <= lows[i]) {
        isSupport = false
        break
      }
    }
    if (isSupport) {
      support.push(lows[i])
    }
  }

  // Return most recent levels
  return {
    support: support.slice(-3), // Last 3 support levels
    resistance: resistance.slice(-3) // Last 3 resistance levels
  }
}

// Main function to calculate all technical indicators - NO FALLBACK DATA
export function calculateAllTechnicalIndicators(historicalData: any[]): TechnicalIndicators {
  if (!historicalData || historicalData.length === 0) {
    throw new Error('No historical data provided for technical analysis')
  }

  // Extract price arrays from historical data
  const closes = historicalData.map(item => item.close).filter(price => price && !isNaN(price))
  const highs = historicalData.map(item => item.high).filter(price => price && !isNaN(price))
  const lows = historicalData.map(item => item.low).filter(price => price && !isNaN(price))
  const volumes = historicalData.map(item => item.volume).filter(vol => vol && !isNaN(vol))

  // Log data quality for debugging
  console.log(`📊 Technical indicators data quality:
    - Raw data points: ${historicalData.length}
    - Valid closes: ${closes.length}
    - Valid highs: ${highs.length}
    - Valid lows: ${lows.length}
    - Valid volumes: ${volumes.length}
    - Date range: ${historicalData[0]?.date} to ${historicalData[historicalData.length-1]?.date}
    - Sample closes: [${closes.slice(-5).map(c => c.toFixed(2)).join(', ')}]`)

  if (closes.length === 0) {
    throw new Error('No valid close prices found in historical data')
  }

  // Calculate all indicators
  const sma20 = calculateSMA(closes, 20)
  const sma50 = calculateSMA(closes, 50)
  const sma200 = calculateSMA(closes, 200)
  const ema12 = calculateEMA(closes, 12)
  const ema26 = calculateEMA(closes, 26)
  const rsi = calculateRSI(closes)
  const macd = calculateMACD(closes)
  const bollingerBands = calculateBollingerBands(closes)
  const atr = calculateATR(highs, lows, closes)
  const obv = calculateOBV(closes, volumes)
  const supportResistance = calculateSupportResistance(highs, lows, closes)

  // Log calculated indicators for debugging
  console.log(`✅ Technical indicators calculated:
    - SMA 20: ₹${sma20.toFixed(2)}
    - SMA 50: ₹${sma50.toFixed(2)}
    - SMA 200: ₹${sma200.toFixed(2)}
    - RSI: ${rsi.toFixed(2)}
    - MACD: ${macd.line.toFixed(4)}`)

  return {
    sma20,
    sma50,
    sma200,
    ema12,
    ema26,
    rsi,
    macd,
    bollingerBands,
    atr,
    obv,
    supportResistance
  }
}
