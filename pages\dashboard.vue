<template>
  <!-- Full-screen background image that covers the entire content area -->
  <div class="fixed inset-0 -z-10 bg-[url('https://images.unsplash.com/photo-1470071459604-3b5ec3a7fe05?ixlib=rb-1.2.1&auto=format&fit=crop&w=1920&q=80')] bg-cover bg-center bg-fixed min-h-screen h-full"></div>

  <!-- Dashboard Content -->
  <div class="bg-white/30 backdrop-blur-md rounded-xl shadow-lg shadow-white/10 p-4 sm:p-6 md:p-8 transition-all duration-300 hover:shadow-xl hover:shadow-white/20 border border-white/20 w-full max-w-sm sm:max-w-2xl md:max-w-4xl lg:max-w-6xl mx-auto space-y-6 sm:space-y-8 my-4 sm:my-8 mb-16 sm:mb-20">
    <div v-if="mounted">
      <div class="flex flex-col sm:flex-row items-center mb-6 sm:mb-8 gap-4 sm:gap-6 justify-between">
        <div class="flex flex-col sm:flex-row items-center gap-3 sm:gap-4 w-full sm:w-auto">
          <div class="bg-emerald-100/50 backdrop-blur-sm p-3 sm:p-4 rounded-full transform transition-all duration-300 hover:scale-110 hover:rotate-3 hover:shadow-lg">
            <svg class="h-6 w-6 sm:h-8 sm:w-8 text-emerald-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div class="text-center sm:text-left">
            <h2 class="text-xl sm:text-2xl md:text-3xl font-bold text-emerald-600 animate-fadeIn drop-shadow-md">User Profile</h2>
            <p class="text-emerald-700 text-base sm:text-lg drop-shadow-sm">Welcome back, {{ user?.fullname || 'User' }}!</p>
          </div>
        </div>
        <button @click="handleLogout" class="w-full sm:w-auto bg-red-100/50 backdrop-blur-sm text-red-600 px-4 sm:px-6 py-2 sm:py-3 rounded-lg hover:bg-red-200/50 transition-all duration-300 transform hover:scale-105 hover:shadow-md text-sm sm:text-base font-medium">
          Logout
        </button>
      </div>

      <div class="border-t border-gray-200/50 pt-6 sm:pt-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6 md:gap-8">
          <div class="glass-panel transform-gpu transition-all duration-300 ease-out hover:translate-y-[-2px] hover:shadow-lg p-4 sm:p-6 from-teal-500/10 to-teal-500/5 bg-gradient-to-br">
            <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 gap-3">
              <h3 class="font-medium text-teal-600 text-lg sm:text-xl drop-shadow-sm">Account Information</h3>
              <button @click="showChangePasswordModal = true" class="w-full sm:w-auto bg-teal-100/50 backdrop-blur-sm text-teal-600 px-3 py-1.5 rounded-lg hover:bg-teal-200/50 transition-all duration-300 transform hover:scale-105 hover:shadow-md text-xs sm:text-sm font-medium flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
                Change Password
              </button>
            </div>
            <div class="space-y-2 sm:space-y-3">
              <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-teal-700 text-sm">Username:</span> <span class="font-medium text-teal-800 text-sm break-all">{{ user?.username }}</span></p>
              <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-teal-700 text-sm">Email:</span> <span class="font-medium text-teal-800 text-sm break-all">{{ user?.email }}</span></p>
              <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-teal-700 text-sm">Member since:</span> <span class="font-medium text-teal-800 text-sm">{{ formatDate(user?.createdAt) }}</span></p>
              <p class="flex flex-col sm:flex-row sm:justify-between gap-1">
                <span class="text-teal-700 text-sm">Role:</span>
                <span class="font-medium text-teal-800 text-sm">{{ user?.role.charAt(0).toUpperCase() + user?.role.slice(1) }}</span>
              </p>
            </div>
          </div>
          <div class="glass-panel transform-gpu transition-all duration-300 ease-out hover:translate-y-[-2px] hover:shadow-lg p-4 sm:p-6 from-blue-500/10 to-blue-500/5 bg-gradient-to-br">
            <h3 class="font-medium text-blue-600 mb-4 text-lg sm:text-xl drop-shadow-sm">Recent Activity</h3>
            <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-blue-700 text-sm">Last login:</span> <span class="font-medium text-blue-800 text-sm">{{ formatDate(user?.lastLogin) || 'Now' }}</span></p>
          </div>
        </div>
      </div>

      <!-- Firm Details Section (Only visible to managers) -->
      <div v-if="user?.role === 'manager' && currentFirm" class="mt-6 sm:mt-8">
        <div class="border-t border-gray-200/30 pt-4 sm:pt-6">
          <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-3 sm:gap-4">
            <h3 class="text-lg sm:text-xl font-bold text-indigo-600 drop-shadow-md">Your Firm Details</h3>
            <button @click="showEditFirmModal = true"
              class="w-full sm:w-auto bg-indigo-100/50 backdrop-blur-sm text-indigo-600 px-3 sm:px-4 py-2 rounded-lg hover:bg-indigo-200/50 transition-all duration-300 transform hover:scale-105 hover:shadow-md flex items-center justify-center text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
              Edit Firm
            </button>
          </div>

          <div class="glass-panel p-4 sm:p-6 from-indigo-500/10 to-indigo-500/5 bg-gradient-to-br">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 sm:gap-6">
              <div class="space-y-2 sm:space-y-3">
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Firm Name:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.name }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Firm Code:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.code }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Business Type:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.businessType }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Description:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.description || 'N/A' }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">State:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.state || 'N/A' }}</span></p>
              </div>
              <div class="space-y-2 sm:space-y-3">
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Contact Person:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.contactPerson }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Contact Number:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.contactNo }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Email:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.email }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">GST Number:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.gstNo }}</span></p>
                <p class="flex flex-col sm:flex-row sm:justify-between gap-1"><span class="text-indigo-600 font-medium text-sm">Address:</span> <span class="font-medium text-indigo-800 text-sm break-all">{{ currentFirm.address }}</span></p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Firm Users Section (Only visible to managers and admins) -->
      <div v-if="user?.role === 'manager' || user?.role === 'admin'" class="mt-6 sm:mt-8">
        <div class="border-t border-gray-200/30 pt-4 sm:pt-6">
          <div class="flex flex-col sm:flex-row justify-between items-center mb-4 gap-3 sm:gap-4">
            <h3 class="text-lg sm:text-xl font-bold text-purple-600 drop-shadow-md">Users in Your Firm</h3>
            <button @click="showAddUserModal = true; resetUserForm();"
              class="w-full sm:w-auto bg-indigo-100/50 backdrop-blur-sm text-indigo-600 px-3 sm:px-4 py-2 rounded-lg hover:bg-indigo-200/50 transition-all duration-300 transform hover:scale-105 hover:shadow-md flex items-center justify-center text-sm">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 sm:h-4 sm:w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
              </svg>
              Create New User
            </button>
          </div>

          <div v-if="firmUsers.length > 0">
            <!-- Mobile Card View (visible on small screens) -->
            <div class="block md:hidden space-y-3">
              <div v-for="firmUser in firmUsers" :key="firmUser._id || firmUser.id"
                class="glass-panel p-4 from-purple-500/10 to-purple-500/5 bg-gradient-to-br">
                <div class="space-y-2">
                  <div class="flex justify-between items-start">
                    <div class="flex-1 min-w-0">
                      <h4 class="font-medium text-purple-800 text-sm truncate">{{ firmUser.fullname }}</h4>
                      <p class="text-purple-600 text-xs truncate">@{{ firmUser.username }}</p>
                    </div>
                    <div class="flex items-center space-x-1 ml-2">
                      <span
                        :class="{
                          'bg-blue-100/50 text-blue-800': firmUser.role === 'admin',
                          'bg-green-100/50 text-green-800': firmUser.role === 'manager',
                          'bg-gray-100/50 text-gray-800': firmUser.role === 'user'
                        }"
                        class="px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm"
                      >
                        {{ firmUser.role }}
                      </span>
                    </div>
                  </div>

                  <div class="flex justify-between items-center">
                    <span
                      :class="{
                        'bg-green-100/50 text-green-800': firmUser.status === 'Approved' || firmUser.status === 'approved' || firmUser.status === 1,
                        'bg-yellow-100/50 text-yellow-800': firmUser.status === 'Pending' || firmUser.status === 'pending' || firmUser.status === 0,
                        'bg-red-100/50 text-red-800': firmUser.status === 'Rejected' || firmUser.status === 'rejected' || firmUser.status === 2
                      }"
                      class="px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm"
                    >
                      {{ getStatusText(firmUser.status) }}
                    </span>

                    <div class="flex space-x-1">
                      <!-- Approve/Reject buttons for pending users -->
                      <template v-if="showApproveRejectButtons(firmUser)">
                        <button
                          @click="approveUser(firmUser._id || firmUser.id)"
                          class="bg-green-500/60 text-white px-2 py-1 rounded text-xs hover:bg-green-600/60 backdrop-blur-sm transition-colors"
                        >
                          ✓
                        </button>
                        <button
                          @click="rejectUser(firmUser._id || firmUser.id)"
                          class="bg-red-500/60 text-white px-2 py-1 rounded text-xs hover:bg-red-600/60 backdrop-blur-sm transition-colors"
                        >
                          ✕
                        </button>
                      </template>

                      <!-- Remove button -->
                      <button
                        v-if="
                          (firmUser._id || firmUser.id) !== user?._id &&
                          (user?.role === 'manager' || user?.role === 'admin') &&
                          !(user?.role === 'manager' && firmUser.role === 'manager') &&
                          !(user?.role !== 'admin' && firmUser.role === 'admin')
                        "
                        @click="confirmRemoveUser(firmUser)"
                        class="bg-red-500/60 text-white px-2 py-1 rounded text-xs hover:bg-red-600/60 backdrop-blur-sm transition-colors"
                      >
                        🗑
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Desktop Table View (visible on medium screens and up) -->
            <div class="hidden md:block glass-table">
              <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200/30">
                  <thead>
                    <tr>
                      <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Username</th>
                      <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Full Name</th>
                      <th class="hidden lg:table-cell px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Email</th>
                      <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Role</th>
                      <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Status</th>
                      <th class="hidden lg:table-cell px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Member Since</th>
                      <th class="px-4 lg:px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider drop-shadow-md">Actions</th>
                    </tr>
                  </thead>
                  <tbody class="divide-y divide-gray-200/20">
                    <tr v-for="firmUser in firmUsers" :key="firmUser._id || firmUser.id" class="hover:bg-white/10 transition-colors duration-200">
                      <td class="px-4 lg:px-6 py-4 text-sm font-medium text-purple-800">
                        <div class="truncate max-w-[100px] lg:max-w-[120px]">{{ firmUser.username }}</div>
                      </td>
                      <td class="px-4 lg:px-6 py-4 text-sm text-purple-800">
                        <div class="truncate max-w-[120px] lg:max-w-[150px]">{{ firmUser.fullname }}</div>
                      </td>
                      <td class="hidden lg:table-cell px-4 lg:px-6 py-4 text-sm text-purple-800">
                        <div class="truncate max-w-[180px] lg:max-w-[200px]">{{ firmUser.email }}</div>
                      </td>
                      <td class="px-4 lg:px-6 py-4 text-sm">
                        <span
                          :class="{
                            'bg-blue-100/50 text-blue-800': firmUser.role === 'admin',
                            'bg-green-100/50 text-green-800': firmUser.role === 'manager',
                            'bg-gray-100/50 text-gray-800': firmUser.role === 'user'
                          }"
                          class="px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm"
                        >
                          {{ firmUser.role }}
                        </span>
                      </td>
                      <td class="px-4 lg:px-6 py-4 text-sm">
                        <span
                          :class="{
                            'bg-green-100/50 text-green-800': firmUser.status === 'Approved' || firmUser.status === 'approved' || firmUser.status === 1,
                            'bg-yellow-100/50 text-yellow-800': firmUser.status === 'Pending' || firmUser.status === 'pending' || firmUser.status === 0,
                            'bg-red-100/50 text-red-800': firmUser.status === 'Rejected' || firmUser.status === 'rejected' || firmUser.status === 2
                          }"
                          class="px-2 py-1 rounded-full text-xs font-medium backdrop-blur-sm"
                        >
                          {{ getStatusText(firmUser.status) }}
                        </span>
                      </td>
                      <td class="hidden lg:table-cell px-4 lg:px-6 py-4 text-sm text-purple-800">{{ formatDate(firmUser.createdAt) }}</td>
                      <td class="px-4 lg:px-6 py-4 text-sm font-medium">
                        <div class="flex space-x-1">
                          <!-- Approve/Reject buttons for pending users -->
                          <template v-if="showApproveRejectButtons(firmUser)">
                            <button
                              @click="approveUser(firmUser._id || firmUser.id)"
                              class="bg-green-500/60 text-white px-2 py-1 rounded text-xs hover:bg-green-600/60 backdrop-blur-sm transition-colors"
                            >
                              ✓
                            </button>
                            <button
                              @click="rejectUser(firmUser._id || firmUser.id)"
                              class="bg-red-500/60 text-white px-2 py-1 rounded text-xs hover:bg-red-600/60 backdrop-blur-sm transition-colors"
                            >
                              ✕
                            </button>
                          </template>

                          <!-- Remove button -->
                          <button
                            v-if="
                              (firmUser._id || firmUser.id) !== user?._id &&
                              (user?.role === 'manager' || user?.role === 'admin') &&
                              !(user?.role === 'manager' && firmUser.role === 'manager') &&
                              !(user?.role !== 'admin' && firmUser.role === 'admin')
                            "
                            @click="confirmRemoveUser(firmUser)"
                            class="bg-red-500/60 text-white px-2 py-1 rounded text-xs hover:bg-red-600/60 backdrop-blur-sm transition-colors"
                          >
                            🗑
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div v-else class="glass-panel p-4 text-center from-purple-500/10 to-purple-500/5 bg-gradient-to-br">
            <p class="text-purple-700">No users found in your firm.</p>
          </div>
        </div>
      </div>
    </div>
    <div v-else class="text-center">
      <p>Loading...</p>
    </div>
  </div>

  <!-- Edit Firm Modal -->
  <div v-if="showEditFirmModal" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
    <div class="glass-modal w-full max-w-2xl mx-auto p-6 from-indigo-500/10 to-indigo-500/5 bg-gradient-to-br" @click.stop>
      <h2 class="text-2xl font-bold mb-6 text-indigo-600 drop-shadow-md">Edit Firm Details</h2>
      <form @submit.prevent="updateFirm" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Firm Name</label>
          <input v-model="firmForm.name" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Firm Code</label>
          <input v-model="firmForm.code" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Business Type</label>
          <input v-model="firmForm.businessType" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Contact Person</label>
          <input v-model="firmForm.contactPerson" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Contact Number</label>
          <input v-model="firmForm.contactNo" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Email</label>
          <input v-model="firmForm.email" type="email" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">GST Number</label>
          <input v-model="firmForm.gstNo" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Address</label>
          <input v-model="firmForm.address" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="col-span-1 md:col-span-1 space-y-2">
          <label class="block text-sm font-medium text-indigo-700">State</label>
          <input v-model="firmForm.state" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="col-span-1 md:col-span-1 space-y-2">
          <label class="block text-sm font-medium text-indigo-700">Description (Optional)</label>
          <textarea v-model="firmForm.description" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300 h-24 resize-none"></textarea>
        </div>

        <!-- Multiple GST Section -->
        <div class="col-span-1 md:col-span-2 mt-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-indigo-700">GST Registrations</h3>
            <button type="button" @click="toggleGSTSection" class="px-3 py-1 bg-indigo-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-indigo-600/60 transition-all duration-300 text-sm">
              {{ showGSTSection ? 'Disable Multiple GST' : 'Enable Multiple GST' }}
            </button>
          </div>

          <div v-if="showGSTSection" class="space-y-4">
            <div class="bg-white/20 backdrop-blur-sm rounded-lg p-4 border border-white/30">
              <div class="flex items-center justify-between mb-3">
                <h4 class="font-medium text-indigo-700">Additional GST Registrations</h4>
                <button type="button" @click="openAddGSTModal" class="px-3 py-1 bg-green-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-green-600/60 transition-all duration-300 text-sm">
                  Add GST
                </button>
              </div>

              <div v-if="firmForm.additionalGSTs.length === 0" class="text-center py-4 text-indigo-600/70">
                No additional GST registrations added yet.
              </div>

              <div v-else class="space-y-2">
                <div v-for="(gst, index) in firmForm.additionalGSTs" :key="index" class="bg-white/30 backdrop-blur-sm rounded-lg p-3 border border-white/20">
                  <div class="flex items-center justify-between">
                    <div class="flex-1">
                      <div class="flex items-center space-x-4">
                        <span class="font-medium text-indigo-700">{{ gst.gstNumber }}</span>
                        <span class="text-sm text-indigo-600">{{ gst.locationName }}</span>
                        <span class="text-sm text-indigo-600">{{ gst.state }}</span>
                        <span v-if="gst.isDefault" class="px-2 py-1 bg-green-500/60 text-white text-xs rounded-full">Default</span>
                        <span :class="gst.isActive ? 'bg-green-500/60' : 'bg-red-500/60'" class="px-2 py-1 text-white text-xs rounded-full">
                          {{ gst.isActive ? 'Active' : 'Inactive' }}
                        </span>
                      </div>
                    </div>
                    <div class="flex items-center space-x-2">
                      <button type="button" @click="openEditGSTModal(index)" class="px-2 py-1 bg-blue-500/60 backdrop-blur-sm text-white rounded text-xs hover:bg-blue-600/60 transition-all duration-300">
                        Edit
                      </button>
                      <button type="button" @click="setDefaultGST(index)" :disabled="gst.isDefault" class="px-2 py-1 bg-yellow-500/60 backdrop-blur-sm text-white rounded text-xs hover:bg-yellow-600/60 transition-all duration-300 disabled:opacity-50">
                        Set Default
                      </button>
                      <button type="button" @click="removeGST(index)" class="px-2 py-1 bg-red-500/60 backdrop-blur-sm text-white rounded text-xs hover:bg-red-600/60 transition-all duration-300">
                        Remove
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="col-span-1 md:col-span-2 flex justify-end space-x-3 mt-4">
          <button type="button" @click="showEditFirmModal = false" class="px-4 py-2 bg-gray-100/50 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-200/50 transition-all duration-300">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-indigo-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-indigo-600/60 transition-all duration-300">
            Save Changes
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- GST Registration Modal -->
  <div v-if="showGSTModal" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
    <div class="glass-modal w-full max-w-3xl mx-auto p-6 from-green-500/10 to-green-500/5 bg-gradient-to-br max-h-[90vh] overflow-y-auto" @click.stop>
      <h2 class="text-2xl font-bold mb-6 text-green-600 drop-shadow-md">
        {{ editingGSTIndex === -1 ? 'Add GST Registration' : 'Edit GST Registration' }}
      </h2>
      <form @submit.prevent="saveGST" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">GST Number *</label>
          <input v-model="gstForm.gstNumber" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">Location Name *</label>
          <input v-model="gstForm.locationName" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">State *</label>
          <input v-model="gstForm.state" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">State Code *</label>
          <input v-model="gstForm.stateCode" type="number" min="1" max="38" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">City *</label>
          <input v-model="gstForm.city" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">Pincode *</label>
          <input v-model="gstForm.pincode" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="col-span-1 md:col-span-2 space-y-2">
          <label class="block text-sm font-medium text-green-700">Address *</label>
          <textarea v-model="gstForm.address" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300 h-20 resize-none" required></textarea>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">Registration Type</label>
          <select v-model="gstForm.registrationType" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300">
            <option value="regular">Regular</option>
            <option value="composition">Composition</option>
            <option value="casual">Casual</option>
            <option value="sez">SEZ</option>
          </select>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">Registration Date *</label>
          <input v-model="gstForm.registrationDate" type="date" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">Valid From *</label>
          <input v-model="gstForm.validFrom" type="date" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-green-700">Valid To (Optional)</label>
          <input v-model="gstForm.validTo" type="date" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-green-500/50 focus:ring-2 focus:ring-green-500/30 transition-all duration-300">
        </div>
        <div class="col-span-1 md:col-span-2 flex items-center space-x-6 mt-4">
          <label class="flex items-center space-x-2">
            <input v-model="gstForm.isActive" type="checkbox" class="rounded border-white/30 bg-white/30 text-green-600 focus:ring-green-500/30">
            <span class="text-sm font-medium text-green-700">Active</span>
          </label>
          <label class="flex items-center space-x-2">
            <input v-model="gstForm.isDefault" type="checkbox" class="rounded border-white/30 bg-white/30 text-green-600 focus:ring-green-500/30">
            <span class="text-sm font-medium text-green-700">Set as Default</span>
          </label>
        </div>
        <div class="col-span-1 md:col-span-2 flex justify-end space-x-3 mt-4">
          <button type="button" @click="showGSTModal = false" class="px-4 py-2 bg-gray-100/50 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-200/50 transition-all duration-300">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-green-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-green-600/60 transition-all duration-300">
            {{ editingGSTIndex === -1 ? 'Add GST' : 'Update GST' }}
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Add User Modal -->
  <div v-if="showAddUserModal" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
    <div class="glass-modal w-full max-w-2xl mx-auto p-6 from-purple-500/10 to-purple-500/5 bg-gradient-to-br" @click.stop>
      <h2 class="text-2xl font-bold mb-6 text-purple-600 drop-shadow-md">Create New User</h2>
      <form @submit.prevent="createUser" class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-purple-700">Username</label>
          <input v-model="userForm.username" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-purple-700">Full Name</label>
          <input v-model="userForm.fullname" type="text" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-purple-700">Email</label>
          <input v-model="userForm.email" type="email" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-purple-700">Password</label>
          <input v-model="userForm.password" type="password" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required minlength="6">
          <p class="text-xs text-purple-600">Password must be at least 6 characters long</p>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-purple-700">Role</label>
          <select v-model="userForm.role" class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-indigo-500/50 focus:ring-2 focus:ring-indigo-500/30 transition-all duration-300" required>
            <!-- Regular users can be created by both managers and admins -->
            <option value="user">User</option>

            <!-- Only admins can create manager users -->
            <option v-if="user?.role === 'admin'" value="manager">Manager</option>

            <!-- Only managers can create sub-contractor users -->
            <option v-if="user?.role === 'manager'" value="sub-contractor">Sub-Contractor</option>

            <!-- Only admins can create admin users -->
            <option v-if="user?.role === 'admin'" value="admin">Admin</option>
          </select>
        </div>
        <div class="col-span-1 md:col-span-2 flex justify-end space-x-3 mt-4">
          <button type="button" @click="showAddUserModal = false" class="px-4 py-2 bg-gray-100/50 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-200/50 transition-all duration-300">
            Cancel
          </button>
          <button type="submit" class="px-4 py-2 bg-indigo-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-indigo-600/60 transition-all duration-300">
            Create User
          </button>
        </div>
      </form>
    </div>
  </div>

  <!-- Remove User Confirmation Modal -->
  <div v-if="showRemoveUserModal" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
    <div class="glass-modal w-full max-w-md mx-auto p-6 from-red-500/10 to-red-500/5 bg-gradient-to-br" @click.stop>
      <h2 class="text-2xl font-bold mb-4 text-red-600 drop-shadow-md">Remove User</h2>
      <p class="mb-6 text-red-700">Are you sure you want to remove <span class="font-semibold">{{ userToRemove?.fullname }}</span>? This action cannot be undone.</p>
      <div class="flex justify-end space-x-3">
        <button type="button" @click="showRemoveUserModal = false" class="px-4 py-2 bg-gray-100/50 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-200/50 transition-all duration-300">
          Cancel
        </button>
        <button type="button" @click="removeUser" class="px-4 py-2 bg-red-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-red-600/60 transition-all duration-300">
          Remove
        </button>
      </div>
    </div>
  </div>

  <!-- Change Password Modal -->
  <div v-if="showChangePasswordModal" class="fixed inset-0 bg-black/30 backdrop-blur-sm flex items-center justify-center z-50 p-4">
    <div class="glass-modal w-full max-w-md mx-auto p-6 from-teal-500/10 to-teal-500/5 bg-gradient-to-br" @click.stop>
      <h2 class="text-2xl font-bold mb-6 text-teal-600 drop-shadow-md">Change Password</h2>
      <form @submit.prevent="updatePassword" class="space-y-4">
        <div class="space-y-2">
          <label class="block text-sm font-medium text-teal-700">Current Password</label>
          <input
            v-model="passwordForm.currentPassword"
            type="password"
            class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-teal-500/50 focus:ring-2 focus:ring-teal-500/30 transition-all duration-300"
            required
          >
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-teal-700">New Password</label>
          <input
            v-model="passwordForm.newPassword"
            type="password"
            class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-teal-500/50 focus:ring-2 focus:ring-teal-500/30 transition-all duration-300"
            required
            minlength="6"
          >
          <p class="text-xs text-teal-600">Password must be at least 6 characters long</p>
        </div>
        <div class="space-y-2">
          <label class="block text-sm font-medium text-teal-700">Confirm New Password</label>
          <input
            v-model="passwordForm.confirmPassword"
            type="password"
            class="w-full px-4 py-2 bg-white/30 backdrop-blur-sm rounded-lg border-2 border-white/30 focus:border-teal-500/50 focus:ring-2 focus:ring-teal-500/30 transition-all duration-300"
            required
          >
        </div>
        <div v-if="passwordError" class="bg-red-100/50 backdrop-blur-sm text-red-700 p-3 rounded-lg border border-red-200/50">
          {{ passwordError }}
        </div>
        <div v-if="passwordSuccess" class="bg-green-100/50 backdrop-blur-sm text-green-700 p-3 rounded-lg border border-green-200/50">
          {{ passwordSuccess }}
        </div>
        <div class="flex justify-end space-x-3 mt-6">
          <button type="button" @click="closePasswordModal" class="px-4 py-2 bg-gray-100/50 backdrop-blur-sm text-gray-700 rounded-lg hover:bg-gray-200/50 transition-all duration-300">
            Cancel
          </button>
          <button
            type="submit"
            class="px-4 py-2 bg-teal-500/60 backdrop-blur-sm text-white rounded-lg hover:bg-teal-600/60 transition-all duration-300 flex items-center"
            :disabled="isUpdatingPassword"
          >
            <svg v-if="isUpdatingPassword" class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Update Password
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useCookie } from '#app';
import type { User } from '~/types/user';
import useApiWithAuth from '~/composables/auth/useApiWithAuth';
import { usePageTitle } from '~/composables/ui/usePageTitle';
import { useLogout } from '~/composables/auth/useLogout';

// Set page title
usePageTitle('Dashboard', 'User dashboard and profile management');

definePageMeta({
  requiresAuth: true
});

const user: any = ref<User | null>(null);
const router = useRouter();
const mounted = ref(false);
const firmUsers = ref<any[]>([]);
const currentFirm = ref<any>(null);
const showEditFirmModal = ref(false);
const showAddUserModal = ref(false);
const showRemoveUserModal = ref(false);
const userToRemove = ref<any>(null);
const showChangePasswordModal = ref(false);
const isUpdatingPassword = ref(false);
const passwordError = ref('');
const passwordSuccess = ref('');
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
});
const firmForm = ref({
  name: '',
  code: '',
  description: '',
  address: '',
  state: '',
  contactPerson: '',
  contactNo: '',
  email: '',
  gstNo: '',
  businessType: '',
  hasMultipleGSTs: false,
  additionalGSTs: []
});

// Form for adding/editing additional GST registrations
const gstForm = ref({
  gstNumber: '',
  state: '',
  stateCode: '',
  locationName: '',
  address: '',
  city: '',
  pincode: '',
  registrationType: 'regular',
  registrationDate: '',
  validFrom: '',
  validTo: '',
  isActive: true,
  isDefault: false
});

const showGSTModal = ref(false);
const editingGSTIndex = ref(-1);
const showGSTSection = ref(false);

const userForm = ref({
  username: '',
  fullname: '',
  email: '',
  password: '',
  role: 'user' // Default role that both managers and admins can create
});

// Reset the user form with appropriate default role based on current user's role
function resetUserForm() {
  userForm.value = {
    username: '',
    fullname: '',
    email: '',
    password: '',
    role: 'user' // Default role that both managers and admins can create
  };
}

async function fetchDashboard() {
  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/dashboard');
    user.value = response.user;

    // If user is a manager or admin, fetch firm users
    if (user.value?.role === 'manager' || user.value?.role === 'admin') {
      fetchFirmUsers();

      // If user is a manager, fetch firm details
      if (user.value?.role === 'manager') {
        fetchCurrentFirm();
      }
    }
  } catch (error: any) {
    console.error('Error fetching dashboard:', error);
    router.push('/login');
  }
}

async function fetchFirmUsers() {
  try {
    const api = useApiWithAuth();
    const response = await api.get('/api/users/firm');
    firmUsers.value = response.users || [];
  } catch (error: any) {
    console.error('Error fetching firm users:', error);
    firmUsers.value = []; // Initialize to empty array on error
  }
}

async function approveUser(id: string) {
  try {
    const api = useApiWithAuth();
    await api.post(`/api/users/${id}/approve`);
    alert('User approved successfully');
    await fetchFirmUsers();
  } catch (error: any) {
    console.error('Error approving user:', error);
    alert(`Failed to approve user: ${error.message || 'Unknown error'}. Please try again.`);
  }
}

async function rejectUser(id: string) {
  try {
    const api = useApiWithAuth();
    await api.post(`/api/users/${id}/reject`);
    alert('User rejected successfully');
    await fetchFirmUsers();
  } catch (error: any) {
    console.error('Error rejecting user:', error);
    alert(`Failed to reject user: ${error.message || 'Unknown error'}. Please try again.`);
  }
}

async function fetchCurrentFirm() {
  try {
    const api = useApiWithAuth();
    const firm = await api.get('/api/firms/current');
    currentFirm.value = firm;

    // Initialize form with current values
    firmForm.value = {
      name: firm.name || '',
      code: firm.code || '',
      description: firm.description || '',
      address: firm.address || '',
      state: firm.state || '',
      contactPerson: firm.contactPerson || '',
      contactNo: firm.contactNo || '',
      email: firm.email || '',
      gstNo: firm.gstNo || '',
      businessType: firm.businessType || '',
      hasMultipleGSTs: firm.hasMultipleGSTs || false,
      additionalGSTs: firm.additionalGSTs || []
    };

    // Set the GST section visibility based on hasMultipleGSTs
    showGSTSection.value = firm.hasMultipleGSTs || false;
  } catch (error: any) {
    console.error('Error fetching firm details:', error);
    currentFirm.value = null; // Reset to null on error
  }
}

async function updateFirm() {
  try {
    const api = useApiWithAuth();
    await api.fetchWithAuth('/api/firms/manager-update', {
      method: 'POST',
      body: firmForm.value
    });

    // Refresh firm data
    await fetchCurrentFirm();

    // Close modal and show success message
    showEditFirmModal.value = false;
    alert('Firm details updated successfully');
  } catch (error: any) {
    console.error('Error updating firm:', error);
    alert('Failed to update firm details. Please try again.');
  }
}

// GST Management Functions
function toggleGSTSection() {
  showGSTSection.value = !showGSTSection.value;
  firmForm.value.hasMultipleGSTs = showGSTSection.value;
}

function resetGSTForm() {
  gstForm.value = {
    gstNumber: '',
    state: '',
    stateCode: '',
    locationName: '',
    address: '',
    city: '',
    pincode: '',
    registrationType: 'regular',
    registrationDate: '',
    validFrom: '',
    validTo: '',
    isActive: true,
    isDefault: false
  };
}

function openAddGSTModal() {
  resetGSTForm();
  editingGSTIndex.value = -1;
  showGSTModal.value = true;
}

function openEditGSTModal(index: number) {
  const gst = firmForm.value.additionalGSTs[index];
  gstForm.value = { ...gst };
  editingGSTIndex.value = index;
  showGSTModal.value = true;
}

function saveGST() {
  if (editingGSTIndex.value === -1) {
    // Adding new GST
    firmForm.value.additionalGSTs.push({ ...gstForm.value });
  } else {
    // Editing existing GST
    firmForm.value.additionalGSTs[editingGSTIndex.value] = { ...gstForm.value };
  }
  showGSTModal.value = false;
  resetGSTForm();
}

function removeGST(index: number) {
  if (confirm('Are you sure you want to remove this GST registration?')) {
    firmForm.value.additionalGSTs.splice(index, 1);
  }
}

function setDefaultGST(index: number) {
  // Set all GSTs to non-default first
  firmForm.value.additionalGSTs.forEach(gst => gst.isDefault = false);
  // Set the selected one as default
  firmForm.value.additionalGSTs[index].isDefault = true;
}

async function createUser() {
  try {
    const api = useApiWithAuth();

    // Add the firmId to the user data
    const userData = {
      ...userForm.value,
      firmId: currentFirm.value._id
    };

    // Create the user
    const response = await api.post('/api/users/create', userData);

    // Close the modal and reset the form
    showAddUserModal.value = false;
    resetUserForm();

    // Refresh the firm users list
    await fetchFirmUsers();

    // Show success message
    if (userForm.value.role === 'sub-contractor' && response.subsModelId) {
      alert(`Sub-contractor user created successfully and added to the Subcontractor Accounts system. User has been automatically approved.`);
    } else {
      alert('User created successfully and automatically approved.');
    }
  } catch (error) {
    console.error('Error creating user:', error);
    alert('Failed to create user');
  }
}

// Function to confirm user removal
function confirmRemoveUser(user: any) {
  userToRemove.value = user;
  showRemoveUserModal.value = true;
}

// Function to remove a user
async function removeUser() {
  try {
    if (!userToRemove.value) return;

    const api = useApiWithAuth();
    const userId = userToRemove.value._id || userToRemove.value.id;

    // Call the API to remove the user
    await api.delete(`/api/users/${userId}`);

    // Close the modal
    showRemoveUserModal.value = false;
    userToRemove.value = null;

    // Refresh the firm users list
    await fetchFirmUsers();

    // Show success message
    alert('User removed successfully');
  } catch (error) {
    console.error('Error removing user:', error);
    alert('Failed to remove user');
  }
}

// Function to update password
async function updatePassword() {
  try {
    // Reset messages
    passwordError.value = '';
    passwordSuccess.value = '';

    // Validate passwords match
    if (passwordForm.value.newPassword !== passwordForm.value.confirmPassword) {
      passwordError.value = 'New password and confirmation do not match';
      return;
    }

    // Validate password length
    if (passwordForm.value.newPassword.length < 6) {
      passwordError.value = 'New password must be at least 6 characters long';
      return;
    }

    isUpdatingPassword.value = true;

    const api = useApiWithAuth();
    await api.post('/api/users/update-password', {
      currentPassword: passwordForm.value.currentPassword,
      newPassword: passwordForm.value.newPassword
    });

    // Show success message
    passwordSuccess.value = 'Password updated successfully';

    // Reset form after a delay
    setTimeout(() => {
      closePasswordModal();
    }, 2000);
  } catch (error: any) {
    console.error('Error updating password:', error);
    passwordError.value = error.data?.statusMessage || 'Failed to update password';
  } finally {
    isUpdatingPassword.value = false;
  }
}

// Function to close password modal and reset form
function closePasswordModal() {
  showChangePasswordModal.value = false;
  passwordForm.value = {
    currentPassword: '',
    newPassword: '',
    confirmPassword: ''
  };
  passwordError.value = '';
  passwordSuccess.value = '';
}

// Use centralized logout composable
const { performLogout } = useLogout()

async function handleLogout() {
  await performLogout()
}

function formatDate(dateString: any) {
  if (!dateString) return 'N/A'
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  })
}

// Convert status code/text to display text
function getStatusText(status: any) {
  // Handle numeric status codes
  if (status === 0 || status === '0') return 'Pending';
  if (status === 1 || status === '1') return 'Approved';
  if (status === 2 || status === '2') return 'Rejected';

  // Handle text status
  if (typeof status === 'string') {
    const lowerStatus = status.toLowerCase();
    if (lowerStatus === 'approved') return 'Approved';
    if (lowerStatus === 'pending') return 'Pending';
    if (lowerStatus === 'rejected') return 'Rejected';
  }

  // Default fallback
  return status || 'Unknown';
}

function showApproveRejectButtons(firmUser: any) {

  // Check if the current user is a manager
  const isManager = user.value?.role === 'manager';

  // Check if the firm user has the 'user' role
  const isFirmUserRegularUser = firmUser.role === 'user';

  // Check if the firm user has 'Pending' status (case-insensitive)
  const isFirmUserPending =
    firmUser.status === 'Pending' ||
    firmUser.status === 'pending' ||
    firmUser.status === 0;

  const shouldShow = isManager && isFirmUserRegularUser && isFirmUserPending;

  return shouldShow;
}

onMounted(() => {
  mounted.value = true;
  fetchDashboard();
});
</script>

<style scoped>
.glass-panel {
  @apply bg-white/20 backdrop-blur-sm rounded-xl border border-white/15 shadow-lg hover:bg-white/25 transition-all duration-300 shadow-white/5;
}

.glass-table {
  @apply bg-white/15 backdrop-blur-sm rounded-xl overflow-hidden border border-white/10 shadow-white/5 from-purple-500/5 to-purple-500/10 bg-gradient-to-br;
}

.glass-table thead {
  @apply bg-gradient-to-r from-teal-500/50 to-indigo-600/50 backdrop-blur-sm;
}

.glass-modal {
  @apply bg-white/40 backdrop-blur-lg rounded-xl border border-white/20 shadow-xl hover:bg-white/45 transition-all duration-300 shadow-white/10;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}
</style>