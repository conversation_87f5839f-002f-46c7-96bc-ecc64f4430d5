<template>
  <div class="bg-white rounded-lg shadow">
    <div class="p-6">
      <h3 class="text-lg font-medium text-gray-900">Labor Profiles</h3>
      <p class="mt-1 text-sm text-gray-500">
        A list of all the workers in your firm.
      </p>
    </div>
    <div class="px-6 pb-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <input
            type="text"
            v-model="search"
            placeholder="Search by name..."
            class="w-64 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
          <select
            v-model="selectedGroup"
            class="w-48 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">All Groups</option>
            <option v-for="group in groups" :key="group.id" :value="group.id">
              {{ group.name }}
            </option>
          </select>
        </div>
        <button
          @click="$emit('add-profile')"
          class="btn btn-primary"
        >
          <Icon name="heroicons:plus-circle" class="w-5 h-5 mr-2" />
          Add Labor Profile
        </button>
      </div>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Group</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Daily Rate</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Phone</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Aadhar</th>
            <th scope="col" class="relative px-6 py-3">
              <span class="sr-only">Edit</span>
            </th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="loading">
            <td colspan="6" class="text-center py-10">
              <Icon name="heroicons:arrow-path" class="w-6 h-6 animate-spin inline-block text-blue-600" />
              <p class="mt-2 text-sm text-gray-500">Loading profiles...</p>
            </td>
          </tr>
          <tr v-else-if="!filteredProfiles.length">
            <td colspan="6" class="text-center py-10">
              <Icon name="heroicons:users" class="w-12 h-12 mx-auto text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">No labor profiles found</h3>
              <p class="mt-1 text-sm text-gray-500">Get started by creating a new labor profile.</p>
              <button
                @click="$emit('add-profile')"
                class="mt-4 btn btn-primary"
              >
                <Icon name="heroicons:plus-circle" class="w-5 h-5 mr-2" />
                Add Labor Profile
              </button>
            </td>
          </tr>
          <tr v-for="profile in filteredProfiles" :key="profile.id" class="hover:bg-gray-50">
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm font-medium text-gray-900">{{ profile.name }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <span
                v-if="profile.labor_groups"
                class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                :style="{ backgroundColor: profile.labor_groups.color + '20', color: profile.labor_groups.color }"
              >
                {{ profile.labor_groups.name }}
              </span>
              <span v-else class="text-xs text-gray-500">Unassigned</span>
            </td>
            <td class="px-6 py-4 whitespace-nowrap">
              <div class="text-sm text-gray-900">₹{{ formatCurrency(profile.daily_rate) }}</div>
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ profile.phone }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{{ profile.aadhar }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
              <button @click="$emit('edit-profile', profile)" class="text-blue-600 hover:text-blue-900">Edit Profile</button>
              <button @click="$emit('edit-group', profile.labor_groups)" class="ml-4 text-green-600 hover:text-green-900">Edit Group</button>
              <button @click="confirmDelete(profile)" class="ml-4 text-red-600 hover:text-red-900">Delete</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'

const props = defineProps({
  firmId: {
    type: String,
    required: true,
  },
  groups: {
    type: Array,
    default: () => [],
  },
})

const emit = defineEmits(['add-profile', 'edit-profile'])

const profiles = ref([])
const loading = ref(false)
const search = ref('')
const selectedGroup = ref('')

const filteredProfiles = computed(() => {
  let filtered = profiles.value

  if (search.value) {
    filtered = filtered.filter(p => p.name.toLowerCase().includes(search.value.toLowerCase()))
  }

  if (selectedGroup.value) {
    filtered = filtered.filter(p => p.group_id === selectedGroup.value)
  }

  return filtered
})

const loadProfiles = async () => {
  loading.value = true
  try {
    const response = await $fetch('/api/labor/profiles', {
      query: { firmId: props.firmId }
    })
    if (response.success) {
      profiles.value = response.data
    }
  } catch (error) {
    console.error('Error loading profiles:', error)
  } finally {
    loading.value = false
  }
}

const confirmDelete = (profile) => {
  if (confirm(`Are you sure you want to delete ${profile.name}?`)) {
    deleteProfile(profile.id)
  }
}

const deleteProfile = async (id) => {
  try {
    await $fetch(`/api/labor/profiles/${id}`, {
      method: 'DELETE',
      query: { firmId: props.firmId }
    })
    loadProfiles()
  } catch (error) {
    console.error('Error deleting profile:', error)
  }
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-IN').format(amount)
}

onMounted(() => {
  console.log('LaborProfileTable mounted with groups:', props.groups)
  loadProfiles()
})

watch(() => props.groups, (newGroups) => {
  console.log('Groups prop changed in LaborProfileTable:', newGroups)
})

watch(() => props.firmId, () => {
  loadProfiles()
})
</script>

<style scoped>
.btn {
  @apply px-4 py-2 rounded-md font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200 inline-flex items-center;
}

.btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed;
}
</style>