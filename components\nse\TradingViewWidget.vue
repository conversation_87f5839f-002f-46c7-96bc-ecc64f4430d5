<template>
  <div class="bg-white shadow rounded-lg p-6 mb-6">
    <h2 class="text-lg font-medium text-gray-900 mb-4">Market Chart</h2>
    
    <div v-if="isLoading" class="flex justify-center items-center h-96">
      <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-indigo-500"></div>
    </div>
    
    <div v-else>
      <div class="flex items-center mb-4">
        <label for="symbolSelect" class="block text-sm font-medium text-gray-700 mr-2">Symbol:</label>
        <select 
          id="symbolSelect" 
          v-model="selectedSymbol" 
          class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md"
        >
          <option v-for="symbol in availableSymbols" :key="symbol" :value="symbol">{{ symbol }}</option>
        </select>
      </div>
      
      <div class="h-96">
        <canvas ref="chartCanvas"></canvas>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import { prepareChartData } from '~/utils/chartUtils';
import Chart from 'chart.js/auto';

const props = defineProps({
  chartData: {
    type: Array,
    default: () => []
  },
  isLoading: {
    type: Boolean,
    default: false
  }
});

const chartCanvas = ref(null);
const selectedSymbol = ref('');
const chart = ref(null);

const availableSymbols = computed(() => {
  return props.chartData.map(item => item.symbol);
});

// Initialize with first symbol when data is loaded
watch(() => props.chartData, (newData) => {
  if (newData && newData.length > 0 && !selectedSymbol.value) {
    selectedSymbol.value = newData[0].symbol;
  }
}, { immediate: true });

// Update chart when symbol changes
watch(selectedSymbol, (newSymbol) => {
  if (newSymbol && props.chartData) {
    updateChart();
  }
});

function updateChart() {
  if (!chartCanvas.value) return;
  
  const symbolData = props.chartData.find(item => item.symbol === selectedSymbol.value);
  
  if (!symbolData) return;
  
  const chartJsData = prepareChartData(symbolData.history, selectedSymbol.value);
  
  if (chart.value) {
    chart.value.data = chartJsData;
    chart.value.update();
  } else {
    chart.value = new Chart(chartCanvas.value, {
      type: 'line',
      data: chartJsData,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          x: {
            title: {
              display: true,
              text: 'Date'
            }
          },
          y: {
            title: {
              display: true,
              text: 'Price (₹)'
            }
          }
        }
      }
    });
  }
}

onMounted(() => {
  if (selectedSymbol.value && props.chartData) {
    updateChart();
  }
});
</script>
