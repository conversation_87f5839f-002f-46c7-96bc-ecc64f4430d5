<!-- Transfer Funds Page -->
<template>
  <div class="container mx-auto px-4 py-8 mt-0">
    <div class="flex justify-between items-center mb-8">
      <h1 class="text-2xl font-bold text-gray-900">Inter-Account Fund Transfer</h1>

      <NuxtLink
        to="/expenses/list"
        class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
      >
        Back to List
      </NuxtLink>
    </div>

    <div v-if="isLoading" class="flex items-center justify-center py-12">
      <svg class="animate-spin h-10 w-10 text-indigo-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading...</span>
    </div>

    <div v-else>
      <TransferForm
        :is-loading="isSubmitting"
        @submit="saveTransfer"
        @cancel="goBack"
      />

      <!-- Success Message -->
      <div v-if="showSuccess" class="mt-6 bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Success!</strong>
        <span class="block sm:inline"> Transfer has been completed successfully.</span>
        <div class="mt-2 flex space-x-4">
          <button
            @click="goToList"
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          >
            View All Expenses
          </button>
          <button
            @click="addAnother"
            class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Make Another Transfer
          </button>
        </div>
      </div>

      <!-- Error Message -->
      <div v-if="error" class="mt-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded relative" role="alert">
        <strong class="font-bold">Error!</strong>
        <span class="block sm:inline"> {{ error }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
// Define page meta
definePageMeta({
  requiresAuth: true
});

import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useExpenses } from '~/composables/expenses/useExpenses';
import { useLedgers } from '~/composables/expenses/useLedgers';
import { usePageTitle } from '~/composables/ui/usePageTitle';

// Set page title
usePageTitle('Fund Transfer', 'Transfer funds between cash and bank accounts');

const router = useRouter();

// State
const isLoading = ref(true);
const isSubmitting = ref(false);
const showSuccess = ref(false);
const error = ref(null);

// Get composables
const { createTransfer } = useExpenses();
const { fetchLedgers } = useLedgers();

// Methods
const saveTransfer = async (formData) => {
  try {
    isSubmitting.value = true;
    error.value = null;

    await createTransfer(formData);

    // Show success message
    showSuccess.value = true;
  } catch (err) {
    error.value = err.message || 'Failed to complete transfer';
    console.error('Error completing transfer:', err);
  } finally {
    isSubmitting.value = false;
  }
};

const goBack = () => {
  router.push('/expenses/list');
};

const goToList = () => {
  router.push('/expenses/list');
};

const addAnother = () => {
  // Reset form and success message
  showSuccess.value = false;
  error.value = null;
};

// Initialize
onMounted(async () => {
  try {
    // Fetch ledgers for the form
    await fetchLedgers();
  } catch (err) {
    error.value = err.message || 'Failed to load ledgers';
    console.error('Error loading ledgers:', err);
  } finally {
    isLoading.value = false;
  }
});
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
