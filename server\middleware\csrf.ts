// server/middleware/csrf.ts
import { H3E<PERSON>, createError, getC<PERSON>ie, getRequestURL } from 'h3';
import { setCsrfToken, validateCsrfToken } from '../utils/csrf';

// List of routes that don't require CSRF protection
// These should be kept to an absolute minimum and carefully reviewed
const csrfExemptRoutes = [
  // Authentication endpoints need exemption because the user doesn't have a token yet
  '/api/auth/login',     // <PERSON><PERSON> needs exemption as user doesn't have a token yet
  '/api/auth/register',  // Registration needs exemption as user doesn't have a token yet
  '/api/auth/refresh',   // Token refresh needs exemption to avoid circular dependency
  '/api/auth/forgot-password', // Password reset request - public endpoint
  '/api/auth/reset-password',  // Password reset confirmation - uses secure token
  '/api/auth/validate-password', // Password validation - used on public pages

  // Public API endpoints that don't require authentication
  '/api/csrf/token',     // CSRF token endpoint must be exempt to avoid circular dependency

  // Public tools APIs
  '/api/tools/languages', // Public language list API
  '/api/tools/translate', // Public translation API

  // Initial firm signup - only exempt if absolutely necessary
  '/api/firms/signup'    // Firm signup might need exemption for first-time users
];

export default defineEventHandler(async (event: H3Event) => {
  const path = getRequestURL(event).pathname;
  const method = event.node.req.method || '';


  // Skip CSRF check for exempt routes - use exact matching for better security
  if (csrfExemptRoutes.some(route => path === route || path.startsWith(`${route}/`))) {
    return;
  }

  // For GET requests, set a new CSRF token if one doesn't exist or refresh if it's old
  if (method === 'GET') {
    const existingTokenData = getCookie(event, 'csrf_token');

    if (!existingTokenData) {
      // No token exists, create a new one
      setCsrfToken(event);
      return;
    }

    // Check if token needs refreshing (older than 2 hours)
    try {
      const [, timestampStr] = existingTokenData.split(':');
      const timestamp = parseInt(timestampStr || '0', 10);
      const now = Date.now();
      const tokenAge = now - timestamp;

      // If token is older than 2 hours (half the max age), refresh it
      if (tokenAge > 2 * 60 * 60 * 1000) {
        setCsrfToken(event);
      }
    } catch (error) {
      // If there's any error parsing the token, generate a new one
      console.error(`Error checking CSRF token age: ${error}`);
      setCsrfToken(event);
    }

    return;
  }

  // For state-changing operations (POST, PUT, DELETE, etc.), validate the CSRF token
  if (['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
    // Validate the CSRF token
    const { isValid, error } = await validateCsrfToken(event);

    if (!isValid) {
      console.error(`CSRF validation failed for ${method} ${path}: ${error}`);

      throw createError({
        statusCode: 403,
        statusMessage: error || 'Invalid or missing CSRF token'
      });
    }

  }
});
