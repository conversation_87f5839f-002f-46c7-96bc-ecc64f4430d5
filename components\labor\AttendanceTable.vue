<template>
  <div class="bg-white rounded-lg shadow">
    <div class="p-6">
      <h3 class="text-lg font-medium text-gray-900">Attendance Records</h3>
      <p class="mt-1 text-sm text-gray-500">
        Use Space to mark present, Enter to mark absent. Double-click to cycle through values.
      </p>
    </div>
    <div class="overflow-x-auto">
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="sticky left-0 bg-gray-50 z-10 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Labor Name</th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Rate</th>
            <th
              v-for="date in dateRange"
              :key="date"
              scope="col"
              class="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
            >
              {{ formatDateHeader(date) }}
            </th>
            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Days</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-if="loading">
            <td :colspan="dateRange.length + 3" class="text-center py-10">
              <Icon name="heroicons:arrow-path" class="w-6 h-6 animate-spin inline-block text-blue-600" />
              <p class="mt-2 text-sm text-gray-500">Loading attendance data...</p>
            </td>
          </tr>
          <tr v-else-if="!attendanceData.length">
            <td :colspan="dateRange.length + 3" class="text-center py-10">
              <Icon name="heroicons:users" class="w-12 h-12 mx-auto text-gray-400" />
              <h3 class="mt-2 text-sm font-medium text-gray-900">No labor profiles found for the selected criteria</h3>
              <p class="mt-1 text-sm text-gray-500">Try selecting a different group or date range.</p>
            </td>
          </tr>
          <tr v-for="(row, rowIndex) in attendanceData" :key="row.profile.id">
            <td class="sticky left-0 bg-white z-10 px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ row.profile.name }}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">₹{{ row.profile.daily_rate }}</td>
            <td
              v-for="(date, colIndex) in dateRange"
              :key="date"
              class="px-2 py-1 text-center"
              @dblclick="cycleDayValue(rowIndex, date)"
            >
              <input
                type="text"
                :value="getAttendanceValue(row, date)"
                class="w-12 h-10 text-center border rounded-md"
                :class="getAttendanceClass(getAttendanceValue(row, date))"
                @keydown.space.prevent="markAttendance(rowIndex, date, 1)"
                @keydown.enter.prevent="markAttendance(rowIndex, date, 0)"
                @keydown.tab.prevent="moveToNext(rowIndex, colIndex)"
                :ref="el => setCellRef(el, rowIndex, colIndex)"
                :disabled="isCellDisabled(row, date)"
              />
            </td>
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ calculateTotalDays(row) }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  firmId: { type: String, required: true },
  profiles: { type: Array, default: () => [] },
  groups: { type: Array, default: () => [] },
  fromDate: { type: String, required: true },
  toDate: { type: String, required: true },
})

const emit = defineEmits(['data-changed'])

const attendanceData = ref([])
const loading = ref(false)
const cellRefs = ref({})

const dateRange = computed(() => {
  const dates = []
  let currentDate = new Date(props.fromDate)
  const endDate = new Date(props.toDate)
  while (currentDate <= endDate) {
    dates.push(currentDate.toISOString().split('T')[0])
    currentDate.setDate(currentDate.getDate() + 1)
  }
  return dates
})

const setCellRef = (el, rowIndex, colIndex) => {
  if (el) {
    cellRefs.value[`${rowIndex}-${colIndex}`] = el
  }
}

const getAttendanceValue = (row, date) => {
  return row.attendance[date] || 0
}

const getAttendanceClass = (value) => {
  if (value > 0) return 'bg-green-100 text-green-800'
  if (value === 0) return 'bg-red-100 text-red-800'
  return 'bg-gray-100'
}

const isCellDisabled = (row, date) => {
  // Logic to disable cell if attendance is already saved for another period
  return false 
}

const markAttendance = (rowIndex, date, value) => {
  attendanceData.value[rowIndex].attendance[date] = value
  emit('data-changed', true)
  const colIndex = dateRange.value.indexOf(date)
  moveToNext(rowIndex, colIndex)
}

const cycleDayValue = (rowIndex, date) => {
  const values = [0, 0.5, 1, 1.5, 2]
  const currentValue = getAttendanceValue(attendanceData.value[rowIndex], date)
  const currentIndex = values.indexOf(currentValue)
  const nextValue = values[(currentIndex + 1) % values.length]
  attendanceData.value[rowIndex].attendance[date] = nextValue
  emit('data-changed', true)
}

const moveToNext = (rowIndex, colIndex) => {
  let nextRow = rowIndex
  let nextCol = colIndex + 1
  if (nextCol >= dateRange.value.length) {
    nextCol = 0
    nextRow += 1
  }
  if (nextRow >= attendanceData.value.length) {
    nextRow = 0
  }
  const nextCell = cellRefs.value[`${nextRow}-${nextCol}`]
  if (nextCell) {
    nextCell.focus()
    nextCell.select()
  }
}

const calculateTotalDays = (row) => {
  return Object.values(row.attendance).reduce((sum, days) => sum + (days || 0), 0)
}

const formatDateHeader = (dateString) => {
  const date = new Date(dateString)
  return `${date.getDate()}/${date.getMonth() + 1}`
}

const prepareAttendanceData = () => {
  loading.value = true
  attendanceData.value = props.profiles.map(profile => ({
    profile,
    attendance: profile.attendance || {},
  }))
  loading.value = false
}

watch(() => props.profiles, () => {
  prepareAttendanceData()
}, { deep: true, immediate: true })
</script>

<style scoped>
.sticky {
  position: -webkit-sticky;
  position: sticky;
}
</style>