/**
 * Labor Payment Service
 * Enhanced payment tracking and calculation logic for labor management system
 */

import { createClient } from '@supabase/supabase-js'

export class LaborPaymentService {
  constructor(supabaseUrl, supabaseKey) {
    this.supabase = createClient(supabaseUrl, supabaseKey)
  }

  /**
   * Get all payments for a group, sorted by date
   * @param {string} groupId - Labor group ID
   * @returns {Array} Array of payment records
   */
  async getGroupPayments(groupId) {
    const { data, error } = await this.supabase
      .from('payment_records')
      .select('*')
      .eq('group_id', groupId)
      .order('payment_date', { ascending: true })

    if (error) throw error
    return data || []
  }

  /**
   * Get all attendance periods for a group
   * @param {string} groupId - Labor group ID
   * @param {Date} fromDate - Start date filter
   * @returns {Array} Array of unique attendance periods
   */
  async getAttendancePeriods(groupId, fromDate = null) {
    const { data: profiles } = await this.supabase
      .from('labor_profiles')
      .select('id')
      .eq('group_id', groupId)

    const laborIds = profiles.map(p => p.id)

    let query = this.supabase
      .from('attendance_records')
      .select('period_start, period_end')
      .in('labor_id', laborIds)

    if (fromDate) {
      query = query.gte('period_start', fromDate.toISOString())
    }

    const { data: attendancePeriods } = await query

    // Get unique periods
    const uniquePeriods = [...new Set(attendancePeriods.map(p => `${p.period_start}|${p.period_end}`))]
      .map(p => {
        const [start, end] = p.split('|')
        return { period_start: start, period_end: end }
      })
      .sort((a, b) => new Date(a.period_start) - new Date(b.period_start))

    return uniquePeriods
  }

  /**
   * Calculate total earnings for a period
   * @param {string} groupId - Labor group ID
   * @param {Object} period - Period object with start and end dates
   * @returns {number} Total earnings including labor amount and site expenses
   */
  async calculatePeriodEarnings(groupId, period) {
    const { data: profiles } = await this.supabase
      .from('labor_profiles')
      .select('id')
      .eq('group_id', groupId)

    const laborIds = profiles.map(p => p.id)

    const { data: earnings } = await this.supabase
      .from('attendance_records')
      .select('amount, site_expenses')
      .in('labor_id', laborIds)
      .gte('attendance_date', period.period_start)
      .lte('attendance_date', period.period_end)

    return earnings.reduce((sum, record) => sum + record.amount + (record.site_expenses || 0), 0)
  }

  /**
   * Find final payments for each attendance period
   * @param {Array} payments - All payments for the group
   * @param {Array} periods - All attendance periods
   * @returns {Map} Map of period key to final payment
   */
  findFinalPayments(payments, periods) {
    const finalPayments = new Map()
    const finalPaymentsList = payments.filter(p => p.payment_type === 'Final Payment')
      .sort((a, b) => new Date(a.payment_date) - new Date(b.payment_date))

    periods.forEach(period => {
      const periodKey = `${period.period_start}|${period.period_end}`
      const periodStart = new Date(period.period_start)
      const periodEnd = new Date(period.period_end)

      // Find the most appropriate final payment for this period
      // Priority: 1) Final payment within period, 2) Final payment after period (closest to period end)
      let bestFinalPayment = null
      let bestScore = -1

      finalPaymentsList.forEach(finalPayment => {
        const paymentDate = new Date(finalPayment.payment_date)
        let score = 0

        if (paymentDate >= periodStart && paymentDate <= periodEnd) {
          // Final payment within period - highest priority
          score = 1000 + (periodEnd.getTime() - paymentDate.getTime()) // Prefer later dates within period
        } else if (paymentDate > periodEnd) {
          // Final payment after period - check if it could settle this period
          const daysDiff = Math.floor((paymentDate.getTime() - periodEnd.getTime()) / (1000 * 60 * 60 * 24))
          if (daysDiff <= 30) { // Within 30 days of period end
            score = 500 - daysDiff // Prefer closer dates
          }
        }

        if (score > bestScore) {
          bestScore = score
          bestFinalPayment = finalPayment
        }
      })

      if (bestFinalPayment && bestScore > 0) {
        finalPayments.set(periodKey, bestFinalPayment)
      }
    })

    return finalPayments
  }

  /**
   * Get the latest final payment date for a group
   * @param {Array} payments - All payments for the group
   * @returns {string|null} Latest final payment date or null
   */
  getLatestFinalPaymentDate(payments) {
    const finalPayments = payments.filter(p => p.payment_type === 'Final Payment')
    if (finalPayments.length === 0) return null
    
    return finalPayments
      .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))[0]
      .payment_date
  }

  /**
   * Group payments into categories based on settlement status
   * @param {Array} payments - All payments for the group
   * @param {Array} periods - All attendance periods
   * @returns {Object} Categorized payments
   */
  categorizePayments(payments, periods) {
    const latestFinalPaymentDate = this.getLatestFinalPaymentDate(payments)
    const finalPayments = this.findFinalPayments(payments, periods)
    
    const categorized = {
      settledPeriods: [],
      postFinalPayments: [],
      ongoingPeriods: []
    }

    periods.forEach(period => {
      const periodKey = `${period.period_start}|${period.period_end}`
      const finalPayment = finalPayments.get(periodKey)
      
      if (finalPayment) {
        // This period has been settled with a final payment
        categorized.settledPeriods.push({
          period,
          finalPayment,
          paymentsInPeriod: payments.filter(p => {
            const paymentDate = new Date(p.payment_date)
            const periodStart = new Date(period.period_start)
            const periodEnd = new Date(period.period_end)
            return paymentDate >= periodStart &&
                   paymentDate <= periodEnd &&
                   p.payment_type !== 'Final Payment'
          })
        })
      } else {
        // This period has no final payment - it's ongoing
        categorized.ongoingPeriods.push({
          period,
          paymentsInPeriod: payments.filter(p => {
            const paymentDate = new Date(p.payment_date)
            const periodStart = new Date(period.period_start)
            const periodEnd = new Date(period.period_end)
            return paymentDate >= periodStart && paymentDate <= periodEnd
          })
        })
      }
    })

    // Get payments made after the latest final payment that's associated with a period
    const associatedFinalPayments = Array.from(finalPayments.values())
    const latestAssociatedFinalPayment = associatedFinalPayments
      .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))[0]

    if (latestAssociatedFinalPayment) {
      categorized.postFinalPayments = payments.filter(p => {
        const paymentDate = new Date(p.payment_date)
        const latestFinalDate = new Date(latestAssociatedFinalPayment.payment_date)
        return paymentDate > latestFinalDate &&
               p.payment_type !== 'Final Payment' &&
               !associatedFinalPayments.some(fp => fp.id === p.id) // Don't include associated final payments
      })
    }

    return categorized
  }

  /**
   * Calculate unpaid amounts using the new logic
   * @param {string} groupId - Labor group ID
   * @param {Date} fromDate - Optional start date filter
   * @returns {Array} Array of unpaid amount records
   */
  async calculateUnpaidAmounts(groupId, fromDate = null) {
    try {
      // Get all data
      const payments = await this.getGroupPayments(groupId)
      const periods = await this.getAttendancePeriods(groupId, fromDate)

      if (periods.length === 0) {
        return []
      }

      // Categorize payments
      const categorized = this.categorizePayments(payments, periods)
      const results = []

      // Process settled periods (Scenario 1)
      for (const settledPeriod of categorized.settledPeriods) {
        const totalEarnings = await this.calculatePeriodEarnings(groupId, settledPeriod.period)
        const paymentsInPeriod = settledPeriod.paymentsInPeriod.reduce((sum, p) => sum + p.amount, 0)
        const finalPaymentAmount = settledPeriod.finalPayment.amount

        results.push({
          period: settledPeriod.period,
          totalPayments: paymentsInPeriod + finalPaymentAmount,
          unpaidAmount: 0, // Settled periods have 0 unpaid amount
          type: 'settled',
          totalEarnings
        })
      }

      // Process ongoing periods (Scenario 3)
      for (const ongoingPeriod of categorized.ongoingPeriods) {
        const totalEarnings = await this.calculatePeriodEarnings(groupId, ongoingPeriod.period)
        const totalPayments = ongoingPeriod.paymentsInPeriod.reduce((sum, p) => sum + p.amount, 0)
        const unpaidAmount = Math.max(0, totalEarnings - totalPayments)

        results.push({
          period: ongoingPeriod.period,
          totalPayments,
          unpaidAmount,
          type: 'ongoing',
          totalEarnings
        })
      }

      // Process post-final payments (Scenario 2)
      if (categorized.postFinalPayments.length > 0) {
        const latestFinalPaymentDate = this.getLatestFinalPaymentDate(payments)
        const totalPostFinalPayments = categorized.postFinalPayments.reduce((sum, p) => sum + p.amount, 0)

        // Create a virtual period for post-final payments
        const postFinalStartDate = new Date(latestFinalPaymentDate)
        postFinalStartDate.setDate(postFinalStartDate.getDate() + 1)

        const lastPaymentDate = categorized.postFinalPayments
          .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))[0]
          .payment_date

        results.push({
          period: {
            period_start: postFinalStartDate.toISOString().split('T')[0],
            period_end: lastPaymentDate
          },
          totalPayments: totalPostFinalPayments,
          unpaidAmount: totalPostFinalPayments, // All post-final payments are unpaid
          type: 'post-final',
          totalEarnings: 0 // No attendance recorded for post-final period
        })
      }

      return results.sort((a, b) => new Date(a.period.period_start) - new Date(b.period.period_start))

    } catch (error) {
      console.error('Error calculating unpaid amounts:', error)
      throw error
    }
  }

  /**
   * Validate payment data for consistency
   * @param {string} groupId - Labor group ID
   * @returns {Object} Validation results with detailed information
   */
  async validatePaymentData(groupId) {
    const payments = await this.getGroupPayments(groupId)
    const periods = await this.getAttendancePeriods(groupId)

    const validation = {
      isValid: true,
      warnings: [],
      errors: [],
      details: {
        orphanedPayments: [],
        multipleFinalPayments: [],
        duplicatePayments: []
      }
    }

    // Check for multiple final payments
    const finalPayments = payments.filter(p => p.payment_type === 'Final Payment')
    const finalPaymentsByDate = {}

    finalPayments.forEach(payment => {
      const date = payment.payment_date
      if (!finalPaymentsByDate[date]) {
        finalPaymentsByDate[date] = []
      }
      finalPaymentsByDate[date].push(payment)
    })

    Object.entries(finalPaymentsByDate).forEach(([date, paymentsOnDate]) => {
      if (paymentsOnDate.length > 1) {
        validation.warnings.push(`Multiple final payments found on ${date}`)
        validation.details.multipleFinalPayments.push({
          date,
          payments: paymentsOnDate
        })
      }
    })

    // Check for payments without corresponding attendance periods
    const orphanedPayments = payments.filter(payment => {
      const hasCorrespondingPeriod = periods.some(period =>
        payment.payment_date >= period.period_start &&
        payment.payment_date <= period.period_end
      )

      // Final payments can be outside periods, so exclude them
      return !hasCorrespondingPeriod && payment.payment_type !== 'Final Payment'
    })

    if (orphanedPayments.length > 0) {
      validation.warnings.push(`${orphanedPayments.length} payments found without corresponding attendance periods`)
      validation.details.orphanedPayments = orphanedPayments
    }

    // Check for potential duplicate payments (same amount, date, type)
    const paymentGroups = {}
    payments.forEach(payment => {
      const key = `${payment.payment_date}-${payment.amount}-${payment.payment_type}`
      if (!paymentGroups[key]) {
        paymentGroups[key] = []
      }
      paymentGroups[key].push(payment)
    })

    Object.entries(paymentGroups).forEach(([key, paymentsInGroup]) => {
      if (paymentsInGroup.length > 1) {
        validation.warnings.push(`Potential duplicate payments found: ${paymentsInGroup.length} payments with same date, amount, and type`)
        validation.details.duplicatePayments.push({
          key,
          payments: paymentsInGroup
        })
      }
    })

    // Set overall validation status
    validation.isValid = validation.warnings.length === 0 && validation.errors.length === 0

    return validation
  }

  /**
   * Get recommendations for fixing validation issues
   * @param {Object} validationResults - Results from validatePaymentData
   * @returns {Array} Array of recommendations
   */
  getValidationRecommendations(validationResults) {
    const recommendations = []

    if (validationResults.details?.orphanedPayments?.length > 0) {
      recommendations.push({
        type: 'orphaned_payments',
        severity: 'warning',
        title: 'Payments Without Attendance Periods',
        description: 'These payments were made outside of any recorded attendance periods.',
        actions: [
          'Create attendance records for the corresponding dates',
          'Verify if these are advance payments for future work',
          'Consider changing payment type to "Final Payment" if they settle previous periods',
          'Check if payment dates are correct'
        ],
        affectedCount: validationResults.details.orphanedPayments.length
      })
    }

    if (validationResults.details?.multipleFinalPayments?.length > 0) {
      recommendations.push({
        type: 'multiple_final_payments',
        severity: 'error',
        title: 'Multiple Final Payments on Same Date',
        description: 'Multiple final payments on the same date can cause calculation errors.',
        actions: [
          'Combine multiple final payments into a single payment',
          'Change extra final payments to "Advance" or "Misc Payment" type',
          'Verify if payments are for different periods'
        ],
        affectedCount: validationResults.details.multipleFinalPayments.length
      })
    }

    if (validationResults.details?.duplicatePayments?.length > 0) {
      recommendations.push({
        type: 'duplicate_payments',
        severity: 'warning',
        title: 'Potential Duplicate Payments',
        description: 'Payments with identical amounts, dates, and types detected.',
        actions: [
          'Review payments to confirm they are not duplicates',
          'Delete duplicate entries if confirmed',
          'Add descriptions to distinguish similar payments',
          'Verify payment amounts and dates'
        ],
        affectedCount: validationResults.details.duplicatePayments.length
      })
    }

    return recommendations
  }
}
