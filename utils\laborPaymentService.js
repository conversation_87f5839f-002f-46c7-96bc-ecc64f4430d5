/**
 * Labor Payment Service
 * Enhanced payment tracking and calculation logic for labor management system
 */

import { createClient } from '@supabase/supabase-js'

export class LaborPaymentService {
  constructor(supabaseUrl, supabaseKey) {
    this.supabase = createClient(supabaseUrl, supabaseKey)
  }

  /**
   * Get all payments for a group, sorted by date
   * @param {string} groupId - Labor group ID
   * @returns {Array} Array of payment records
   */
  async getGroupPayments(groupId) {
    const { data, error } = await this.supabase
      .from('payment_records')
      .select('*')
      .eq('group_id', groupId)
      .order('payment_date', { ascending: true })

    if (error) throw error
    return data || []
  }

  /**
   * Get all attendance periods for a group
   * @param {string} groupId - Labor group ID
   * @param {Date} fromDate - Start date filter
   * @returns {Array} Array of unique attendance periods
   */
  async getAttendancePeriods(groupId, fromDate = null) {
    const { data: profiles } = await this.supabase
      .from('labor_profiles')
      .select('id')
      .eq('group_id', groupId)

    const laborIds = profiles.map(p => p.id)

    let query = this.supabase
      .from('attendance_records')
      .select('period_start, period_end')
      .in('labor_id', laborIds)

    if (fromDate) {
      query = query.gte('period_start', fromDate.toISOString())
    }

    const { data: attendancePeriods } = await query

    // Get unique periods
    const uniquePeriods = [...new Set(attendancePeriods.map(p => `${p.period_start}|${p.period_end}`))]
      .map(p => {
        const [start, end] = p.split('|')
        return { period_start: start, period_end: end }
      })
      .sort((a, b) => new Date(a.period_start) - new Date(b.period_start))

    return uniquePeriods
  }

  /**
   * Calculate total earnings for a period
   * @param {string} groupId - Labor group ID
   * @param {Object} period - Period object with start and end dates
   * @returns {number} Total earnings including labor amount and site expenses
   */
  async calculatePeriodEarnings(groupId, period) {
    const { data: profiles } = await this.supabase
      .from('labor_profiles')
      .select('id')
      .eq('group_id', groupId)

    const laborIds = profiles.map(p => p.id)

    const { data: earnings } = await this.supabase
      .from('attendance_records')
      .select('amount, site_expenses')
      .in('labor_id', laborIds)
      .gte('attendance_date', period.period_start)
      .lte('attendance_date', period.period_end)

    return earnings.reduce((sum, record) => sum + record.amount + (record.site_expenses || 0), 0)
  }

  /**
   * Find final payments for each attendance period
   * @param {Array} payments - All payments for the group
   * @param {Array} periods - All attendance periods
   * @returns {Map} Map of period key to final payment
   */
  findFinalPayments(payments, periods) {
    const finalPayments = new Map()
    
    periods.forEach(period => {
      const periodKey = `${period.period_start}|${period.period_end}`
      
      // Look for final payments made during or after the period
      const finalPayment = payments.find(p => 
        p.payment_type === 'Final Payment' && 
        p.payment_date >= period.period_start
      )
      
      if (finalPayment) {
        finalPayments.set(periodKey, finalPayment)
      }
    })
    
    return finalPayments
  }

  /**
   * Get the latest final payment date for a group
   * @param {Array} payments - All payments for the group
   * @returns {string|null} Latest final payment date or null
   */
  getLatestFinalPaymentDate(payments) {
    const finalPayments = payments.filter(p => p.payment_type === 'Final Payment')
    if (finalPayments.length === 0) return null
    
    return finalPayments
      .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))[0]
      .payment_date
  }

  /**
   * Group payments into categories based on settlement status
   * @param {Array} payments - All payments for the group
   * @param {Array} periods - All attendance periods
   * @returns {Object} Categorized payments
   */
  categorizePayments(payments, periods) {
    const latestFinalPaymentDate = this.getLatestFinalPaymentDate(payments)
    const finalPayments = this.findFinalPayments(payments, periods)
    
    const categorized = {
      settledPeriods: [],
      postFinalPayments: [],
      ongoingPeriods: []
    }

    periods.forEach(period => {
      const periodKey = `${period.period_start}|${period.period_end}`
      const finalPayment = finalPayments.get(periodKey)
      
      if (finalPayment) {
        // This period has been settled with a final payment
        categorized.settledPeriods.push({
          period,
          finalPayment,
          paymentsInPeriod: payments.filter(p => 
            p.payment_date >= period.period_start && 
            p.payment_date <= period.period_end &&
            p.payment_type !== 'Final Payment'
          )
        })
      } else {
        // This period has no final payment - it's ongoing
        categorized.ongoingPeriods.push({
          period,
          paymentsInPeriod: payments.filter(p => 
            p.payment_date >= period.period_start && 
            p.payment_date <= period.period_end
          )
        })
      }
    })

    // Get payments made after the latest final payment (if any)
    if (latestFinalPaymentDate) {
      categorized.postFinalPayments = payments.filter(p => 
        p.payment_date > latestFinalPaymentDate &&
        p.payment_type !== 'Final Payment'
      )
    }

    return categorized
  }

  /**
   * Calculate unpaid amounts using the new logic
   * @param {string} groupId - Labor group ID
   * @param {Date} fromDate - Optional start date filter
   * @returns {Array} Array of unpaid amount records
   */
  async calculateUnpaidAmounts(groupId, fromDate = null) {
    try {
      // Get all data
      const payments = await this.getGroupPayments(groupId)
      const periods = await this.getAttendancePeriods(groupId, fromDate)

      if (periods.length === 0) {
        return []
      }

      // Categorize payments
      const categorized = this.categorizePayments(payments, periods)
      const results = []

      // Process settled periods (Scenario 1)
      for (const settledPeriod of categorized.settledPeriods) {
        const totalEarnings = await this.calculatePeriodEarnings(groupId, settledPeriod.period)
        const paymentsInPeriod = settledPeriod.paymentsInPeriod.reduce((sum, p) => sum + p.amount, 0)
        const finalPaymentAmount = settledPeriod.finalPayment.amount

        results.push({
          period: settledPeriod.period,
          totalPayments: paymentsInPeriod + finalPaymentAmount,
          unpaidAmount: 0, // Settled periods have 0 unpaid amount
          type: 'settled',
          totalEarnings
        })
      }

      // Process ongoing periods (Scenario 3)
      for (const ongoingPeriod of categorized.ongoingPeriods) {
        const totalEarnings = await this.calculatePeriodEarnings(groupId, ongoingPeriod.period)
        const totalPayments = ongoingPeriod.paymentsInPeriod.reduce((sum, p) => sum + p.amount, 0)
        const unpaidAmount = Math.max(0, totalEarnings - totalPayments)

        results.push({
          period: ongoingPeriod.period,
          totalPayments,
          unpaidAmount,
          type: 'ongoing',
          totalEarnings
        })
      }

      // Process post-final payments (Scenario 2)
      if (categorized.postFinalPayments.length > 0) {
        const latestFinalPaymentDate = this.getLatestFinalPaymentDate(payments)
        const totalPostFinalPayments = categorized.postFinalPayments.reduce((sum, p) => sum + p.amount, 0)

        // Create a virtual period for post-final payments
        const postFinalStartDate = new Date(latestFinalPaymentDate)
        postFinalStartDate.setDate(postFinalStartDate.getDate() + 1)

        const lastPaymentDate = categorized.postFinalPayments
          .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))[0]
          .payment_date

        results.push({
          period: {
            period_start: postFinalStartDate.toISOString().split('T')[0],
            period_end: lastPaymentDate
          },
          totalPayments: totalPostFinalPayments,
          unpaidAmount: totalPostFinalPayments, // All post-final payments are unpaid
          type: 'post-final',
          totalEarnings: 0 // No attendance recorded for post-final period
        })
      }

      return results.sort((a, b) => new Date(a.period.period_start) - new Date(b.period.period_start))

    } catch (error) {
      console.error('Error calculating unpaid amounts:', error)
      throw error
    }
  }

  /**
   * Validate payment data for consistency
   * @param {string} groupId - Labor group ID
   * @returns {Object} Validation results
   */
  async validatePaymentData(groupId) {
    const payments = await this.getGroupPayments(groupId)
    const periods = await this.getAttendancePeriods(groupId)

    const validation = {
      isValid: true,
      warnings: [],
      errors: []
    }

    // Check for multiple final payments for the same period
    const finalPayments = payments.filter(p => p.payment_type === 'Final Payment')
    const finalPaymentDates = finalPayments.map(p => p.payment_date)
    const uniqueFinalPaymentDates = [...new Set(finalPaymentDates)]

    if (finalPaymentDates.length !== uniqueFinalPaymentDates.length) {
      validation.warnings.push('Multiple final payments found for the same date')
    }

    // Check for payments without corresponding attendance periods
    const orphanedPayments = payments.filter(payment => {
      return !periods.some(period =>
        payment.payment_date >= period.period_start &&
        payment.payment_date <= period.period_end
      ) && payment.payment_type !== 'Final Payment'
    })

    if (orphanedPayments.length > 0) {
      validation.warnings.push(`${orphanedPayments.length} payments found without corresponding attendance periods`)
    }

    return validation
  }
}
